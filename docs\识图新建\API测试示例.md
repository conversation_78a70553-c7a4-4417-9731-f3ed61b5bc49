# OCR识图API测试示例

## 接口信息
- **URL**: `POST /api/v2/ocr/recognize`
- **Content-Type**: `multipart/form-data`
- **认证**: 需要用户登录（AppUserInterceptor）

## 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | File | 是 | 图片文件，支持jpg、png、webp等格式 |
| imageWidth | Integer | 否 | 图片宽度(px)，不传则从文件中获取 |
| imageHeight | Integer | 否 | 图片高度(px)，不传则从文件中获取 |

## 响应格式

### 成功响应
```json
{
  "success": true,
  "data": {
    "imageInfo": {
      "width": 1080,
      "height": 1920,
      "format": "jpeg"
    },
    "elements": [
      {
        "elementType": "1",
        "x": "8.466667",
        "y": "2.540000",
        "width": "23.813333",
        "height": "4.233333",
        "content": "卡装1克马卡龙",
        "textSize": "11.0",
        "bold": "false",
        "italic": "false",
        "hAlignment": "1",
        "rotationAngle": "0",
        "underline": "false",
        "strikethrough": "false",
        "wordSpace": "0.0",
        "linesSpace": "0.0",
        "fontType": "-2",
        "takePrint": "true",
        "mirrorImage": "false",
        "blackWhiteReflection": "false",
        "automaticHeightCalculation": "true",
        "lineWrap": "true",
        "flipX": "false"
      },
      {
        "elementType": "2",
        "x": "2.540000",
        "y": "12.700000",
        "width": "25.400000",
        "height": "11.430000",
        "content": "6975370866829",
        "barcodeType": "4",
        "rotationAngle": "0",
        "showText": "3",
        "textAlignment": "1",
        "horizontalAlignment": "true",
        "takePrint": "true",
        "mirrorImage": "false"
      }
    ]
  },
  "message": "识别成功"
}
```

### 错误响应
```json
{
  "success": false,
  "msg": "图片文件过大，最大支持10MB"
}
```

## 元素类型说明

### 1. 文本元素 (elementType: "1")
- **content**: 识别到的文本内容
- **textSize**: 计算出的字体大小
- **bold/italic**: 字体样式
- **x/y/width/height**: 位置和尺寸（毫米单位）

### 2. 条形码元素 (elementType: "2")
- **content**: 条码内容
- **barcodeType**: 条码类型（"1"=CODE39, "4"=CODE128, "8"=EAN13等）

### 3. 二维码元素 (elementType: "7")
- **content**: 二维码内容

### 4. 表格元素 (elementType: "10")
- **rowCount/colCount**: 行列数
- **cells**: 单元格数组
- **rowHeights/columnWidths**: 行高列宽数组

## 测试用例

### 1. 使用curl测试
```bash
curl -X POST \
  http://localhost:8090/api/v2/ocr/recognize \
  -H 'Content-Type: multipart/form-data' \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -F 'file=@test_image.jpg' \
  -F 'imageWidth=800' \
  -F 'imageHeight=600'
```

### 2. 使用Postman测试
1. 设置请求方法为POST
2. URL: `http://localhost:8090/api/v2/ocr/recognize`
3. Headers添加认证信息
4. Body选择form-data
5. 添加file字段，选择图片文件
6. 可选添加imageWidth和imageHeight参数

### 3. JavaScript测试示例
```javascript
const formData = new FormData();
formData.append('file', imageFile);
formData.append('imageWidth', '800');
formData.append('imageHeight', '600');

fetch('/api/v2/ocr/recognize', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token
  },
  body: formData
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log('识别成功:', data.data);
    // 处理识别结果
    data.data.elements.forEach(element => {
      console.log('元素类型:', element.elementType);
      console.log('内容:', element.content);
      console.log('位置:', element.x, element.y);
    });
  } else {
    console.error('识别失败:', data.msg);
  }
})
.catch(error => {
  console.error('请求失败:', error);
});
```

## 错误码说明
| 错误信息 | 说明 | 解决方案 |
|----------|------|----------|
| 请上传图片文件 | 未上传文件或文件为空 | 检查文件上传 |
| 图片文件过大，最大支持10MB | 文件超过大小限制 | 压缩图片或选择更小的文件 |
| 不支持的图片格式 | 文件格式不支持 | 使用jpg、png、webp等格式 |
| 图片文件为空 | 文件大小为0 | 检查文件是否损坏 |
| TextIn API调用失败 | 第三方服务异常 | 检查网络连接和API配置 |

## 性能指标
- **响应时间**: 通常5-15秒（取决于图片大小和复杂度）
- **文件大小限制**: 最大10MB
- **支持格式**: jpg、jpeg、png、webp、bmp
- **并发限制**: 建议不超过10个并发请求

## 注意事项
1. 需要配置TextIn API的密钥和应用ID
2. 图片质量影响识别准确率，建议使用清晰、光线充足的图片
3. 复杂表格的识别可能需要人工校验
4. 条码识别支持常见的一维码和二维码格式
5. 坐标系统使用毫米单位，适配XPrinter的标准格式

## 配置要求
在`common_config.txt`中添加以下配置：
```
textin.api.url=https://api.textin.com/ai/service/v1/recognize
textin.api.key=your_actual_api_key
textin.app.id=your_actual_app_id
```
