<!DOCTYPE html>
<!-- saved from url=(0047)https://docs.textin.com/xparse/parse-quickstart -->
<html lang="cn" class="__variable_5f106d __variable_3bbdad lg:[--scroll-mt:9.5rem] light" data-banner-state="hidden" data-page-mode="none" style="color-scheme: light;"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><link rel="preload" href="https://docs.textin.com/mintlify-assets/_next/static/media/bb3ef058b751a6ad-s.p.woff2" as="font" crossorigin="" type="font/woff2"><link rel="preload" href="https://docs.textin.com/mintlify-assets/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"><link rel="preload" as="image" href="./快速启动 - Textin 智能文档解析_files/light.png"><link rel="preload" as="image" href="./快速启动 - Textin 智能文档解析_files/dark.png"><link rel="preload" as="image" href="./快速启动 - Textin 智能文档解析_files/CN.svg"><link rel="stylesheet" href="./快速启动 - Textin 智能文档解析_files/ca797da4e9f8f21c.css" data-precedence="next"><link rel="stylesheet" href="./快速启动 - Textin 智能文档解析_files/fa46c000add4671c.css" data-precedence="next"><link rel="stylesheet" href="./快速启动 - Textin 智能文档解析_files/19e66b131dc509b0.css" data-precedence="next"><link rel="preload" as="script" fetchpriority="low" href="./快速启动 - Textin 智能文档解析_files/webpack-b369b521ae88fa3a.js.下载"><script src="./快速启动 - Textin 智能文档解析_files/87c73c54-2174f7eb4d0d8b2a.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/1902-be007fe49b2d7534.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/main-app-8e9287d8317b460f.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/7261-2b892dc828f6e161.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/9058-fc5eb8705bf7a22c.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/error-dad69ef19d740480.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/891cff7f-2ca7d0df884db9d0.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/7bf36345-5ba13855b95a82b2.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/d30757c7-56ff534f625704fe.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/cd24890f-549fb4ba2f588ca6.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/3892-8cb32e85ffab550f.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/7417-548f041b716e378a.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/1953-46fbce29c74b759e.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/9095-5e8c25cebc4b2bd6.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/9779-7bb45d52151006b8.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/3619-00dda429c6457d2a.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/2398-cdec25c076f8ff8b.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/1862-deaeb884406520f8.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/2755-2e4adf62599e58a6.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/1350-cf10a11ffacb60d0.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/layout-b596e085fd7c2d45.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/error-d4ab46b84560464d.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/271c4271-e47f34f62bcfeead.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/3122-473c6d6ad707a1ff.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/7048-68b7efbe64e44ac4.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/3386-4ac6c5d6ac47f6b6.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/3698-586441bcd6c2501a.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/3818-b06f8c87a0bcb5ea.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/3291-8e412928ddb5eb6a.js.下载" async=""></script><script src="./快速启动 - Textin 智能文档解析_files/page-82f1f84c31f01e84.js.下载" async=""></script><script type="text/javascript">(function(a,b,c){try{let d=localStorage.getItem(a);if(null==d)for(let c=0;c<localStorage.length;c++){let e=localStorage.key(c);if(e?.endsWith(`-${b}`)&&(d=localStorage.getItem(e),null!=d)){localStorage.setItem(a,d),localStorage.setItem(e,d);break}}let e=document.getElementById("banner")?.innerText,f=null==d||!!e&&d!==e;document.documentElement.setAttribute(c,f?"visible":"hidden")}catch(a){console.error(a),document.documentElement.setAttribute(c,"hidden")}})(
  "__mintlify-bannerDismissed",
  "bannerDismissed",
  "data-banner-state",
)</script><link rel="stylesheet" href="./快速启动 - Textin 智能文档解析_files/katex.min.css" integrity="sha384-Xi8rHCmBmhbuyyhbI88391ZKP2dmfnOl4rT9ZfRI7mLTdk1wblIUnrIq35nqwEvC" crossorigin="anonymous"><script src="./快速启动 - Textin 智能文档解析_files/polyfills-42372ed130431b0a.js.下载" nomodule=""></script><script src="./快速启动 - Textin 智能文档解析_files/logger-1.min.js.下载" async=""></script><script id="_mintlify-banner-script">(function m(a,b,c,d){try{let e=document.getElementById("banner"),f=e?.innerText;if(!f)return void document.documentElement.setAttribute(d,"hidden");let g=localStorage.getItem(a),h=g!==f&&g!==b;null!=g&&(h?(localStorage.removeItem(c),localStorage.removeItem(a)):(localStorage.setItem(c,f),localStorage.setItem(a,f))),document.documentElement.setAttribute(d,!g||h?"visible":"hidden")}catch(a){console.error(a),document.documentElement.setAttribute(d,"hidden")}})(
  "textin-bannerDismissed",
  undefined,
  "__mintlify-bannerDismissed",
  "data-banner-state",
)</script><script suppresshydrationwarning="true" id="_mintlify-scroll-top-script">(function(a,b,c,d){var e;let f,g="mint"===d||"linden"===d?"sidebar":"sidebar-content",h=(e=d,f="navbar-transition","maple"===e&&(f+="-maple"),"willow"===e&&(f+="-willow"),f);function i(){document.documentElement.classList.add("lg:[--scroll-mt:9.5rem]")}function j(a){document.getElementById(g)?.style.setProperty("top",`${a}rem`)}function k(a){document.getElementById(g)?.style.setProperty("height",`calc(100vh - ${a}rem)`)}function l(a,b){!a&&b||a&&!b?(i(),document.documentElement.classList.remove("lg:[--scroll-mt:12rem]")):a&&b&&(document.documentElement.classList.add("lg:[--scroll-mt:12rem]"),document.documentElement.classList.remove("lg:[--scroll-mt:9.5rem]"))}let m=document.documentElement.getAttribute("data-banner-state"),n=null!=m?"visible"===m:b;switch(d){case"mint":j(c),l(a,n);break;case"palm":case"aspen":j(c),k(c),l(a,n);break;case"linden":j(c),n&&i();break;case"almond":document.documentElement.style.setProperty("--scroll-mt","2.5rem"),j(c),k(c)}let o=function(){let a=document.createElement("style");return a.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(a),function(){window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(a)},1)}}();("requestAnimationFrame"in globalThis?requestAnimationFrame:setTimeout)(()=>{let a;a=!1,a=window.scrollY>50,document.getElementById(h)?.setAttribute("data-is-opaque",`${!!a}`),o()})})(
  true,
  false,
  (function l(a,b,c){let d=document.documentElement.getAttribute("data-banner-state"),e=2.5*!!(null!=d?"visible"===d:b),f=3*!!a,g=4,h=e+g+f;switch(c){case"mint":case"palm":break;case"aspen":f=2.5*!!a,g=3.5,h=e+f+g;break;case"linden":g=4,h=e+g;break;case"almond":g=3.5,h=e+g}return h})(true, false, "mint"),
  "mint",
)</script><script id="_mintlify-page-mode-script">document.documentElement.setAttribute('data-page-mode', 'none');</script><script suppresshydrationwarning="true" id="_mintlify-footer-and-sidebar-scroll-script">(function m(a,b){if(!document.getElementById("footer")?.classList.contains("advanced-footer")||"maple"===b||"willow"===b||"almond"===b)return;let c=document.documentElement.getAttribute("data-page-mode"),d=document.getElementById("navbar"),e=document.getElementById("sidebar"),f=document.getElementById("footer"),g=document.getElementById("table-of-contents-content");if(!f||"center"===c)return;let h=f.getBoundingClientRect().top,i=window.innerHeight-h;e&&(i>0?(e.style.top=`-${i}px`,e.style.height=`${window.innerHeight}px`):(e.style.top=`${a}rem`,e.style.height="auto")),g&&d&&(i>0?g.style.top="custom"===c?`${d.clientHeight-i}px`:`${40+d.clientHeight-i}px`:g.style.top="")})(
  (function l(a,b,c){let d=document.documentElement.getAttribute("data-banner-state"),e=2.5*!!(null!=d?"visible"===d:b),f=3*!!a,g=4,h=e+g+f;switch(c){case"mint":case"palm":break;case"aspen":f=2.5*!!a,g=3.5,h=e+f+g;break;case"linden":g=4,h=e+g;break;case"almond":g=3.5,h=e+g}return h})(true, false, "mint"),
  "mint",
)</script><meta name="viewport" content="width=device-width, initial-scale=1"><meta name="next-size-adjust" content=""><title>快速启动 - Textin 智能文档解析</title><meta name="description" content="参考示例，快速将文档解析API接入到您的系统和应用流程中。"><meta name="application-name" content="Textin 智能文档解析"><meta name="generator" content="Mintlify"><meta name="msapplication-config" content="https://mintlify.s3-us-west-1.amazonaws.com/textin/_generated/favicon/browserconfig.xml?v=3"><meta name="apple-mobile-web-app-title" content="Textin 智能文档解析"><meta name="msapplication-TileColor" content="#1a66ff"><meta name="charset" content="utf-8"><meta name="og:site_name" content="Textin 智能文档解析"><link rel="alternate" type="application/xml" href="https://docs.textin.com/sitemap.xml"><meta property="og:title" content="快速启动 - Textin 智能文档解析"><meta property="og:description" content="参考示例，快速将文档解析API接入到您的系统和应用流程中。"><meta property="og:image" content="https://textin.mintlify.app/mintlify-assets/_next/image?url=%2Fapi%2Fog%3Fdivision%3D%25E6%2596%2587%25E6%25A1%25A3%25E8%25A7%25A3%25E6%259E%2590%26title%3D%25E5%25BF%25AB%25E9%2580%259F%25E5%2590%25AF%25E5%258A%25A8%26description%3D%25E5%258F%2582%25E8%2580%2583%25E7%25A4%25BA%25E4%25BE%258B%25EF%25BC%258C%25E5%25BF%25AB%25E9%2580%259F%25E5%25B0%2586%25E6%2596%2587%25E6%25A1%25A3%25E8%25A7%25A3%25E6%259E%2590API%25E6%258E%25A5%25E5%2585%25A5%25E5%2588%25B0%25E6%2582%25A8%25E7%259A%2584%25E7%25B3%25BB%25E7%25BB%259F%25E5%2592%258C%25E5%25BA%2594%25E7%2594%25A8%25E6%25B5%2581%25E7%25A8%258B%25E4%25B8%25AD%25E3%2580%2582%26logoLight%3Dhttps%253A%252F%252Fmintlify.s3.us-west-1.amazonaws.com%252Ftextin%252Flogo%252Flight.png%26logoDark%3Dhttps%253A%252F%252Fmintlify.s3.us-west-1.amazonaws.com%252Ftextin%252Flogo%252Fdark.png%26primaryColor%3D%25231a66ff%26lightColor%3D%25231a66ff%26darkColor%3D%25231a66ff%26backgroundLight%3D%2523ffffff%26backgroundDark%3D%2523090b10&amp;w=1200&amp;q=100"><meta property="og:image:width" content="1200"><meta property="og:image:height" content="630"><meta property="og:type" content="website"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:title" content="快速启动 - Textin 智能文档解析"><meta name="twitter:description" content="参考示例，快速将文档解析API接入到您的系统和应用流程中。"><meta name="twitter:image" content="https://textin.mintlify.app/mintlify-assets/_next/image?url=%2Fapi%2Fog%3Fdivision%3D%25E6%2596%2587%25E6%25A1%25A3%25E8%25A7%25A3%25E6%259E%2590%26title%3D%25E5%25BF%25AB%25E9%2580%259F%25E5%2590%25AF%25E5%258A%25A8%26description%3D%25E5%258F%2582%25E8%2580%2583%25E7%25A4%25BA%25E4%25BE%258B%25EF%25BC%258C%25E5%25BF%25AB%25E9%2580%259F%25E5%25B0%2586%25E6%2596%2587%25E6%25A1%25A3%25E8%25A7%25A3%25E6%259E%2590API%25E6%258E%25A5%25E5%2585%25A5%25E5%2588%25B0%25E6%2582%25A8%25E7%259A%2584%25E7%25B3%25BB%25E7%25BB%259F%25E5%2592%258C%25E5%25BA%2594%25E7%2594%25A8%25E6%25B5%2581%25E7%25A8%258B%25E4%25B8%25AD%25E3%2580%2582%26logoLight%3Dhttps%253A%252F%252Fmintlify.s3.us-west-1.amazonaws.com%252Ftextin%252Flogo%252Flight.png%26logoDark%3Dhttps%253A%252F%252Fmintlify.s3.us-west-1.amazonaws.com%252Ftextin%252Flogo%252Fdark.png%26primaryColor%3D%25231a66ff%26lightColor%3D%25231a66ff%26darkColor%3D%25231a66ff%26backgroundLight%3D%2523ffffff%26backgroundDark%3D%2523090b10&amp;w=1200&amp;q=100"><meta name="twitter:image:width" content="1200"><meta name="twitter:image:height" content="630"><link rel="apple-touch-icon" href="https://mintlify.s3-us-west-1.amazonaws.com/textin/_generated/favicon/apple-touch-icon.png?v=3" type="image/png" sizes="180x180"><link rel="icon" href="https://mintlify.s3-us-west-1.amazonaws.com/textin/_generated/favicon/favicon-32x32.png?v=3" type="image/png" sizes="32x32"><link rel="icon" href="https://mintlify.s3-us-west-1.amazonaws.com/textin/_generated/favicon/favicon-16x16.png?v=3" type="image/png" sizes="16x16"><link rel="shortcut icon" href="https://mintlify.s3-us-west-1.amazonaws.com/textin/_generated/favicon/favicon.ico?v=3" type="image/x-icon"></head><body><script type="text/javascript" src="./快速启动 - Textin 智能文档解析_files/exception-autocapture.js.下载"></script><script type="text/javascript" src="./快速启动 - Textin 智能文档解析_files/exception-autocapture.js.下载"></script><script type="text/javascript" src="./快速启动 - Textin 智能文档解析_files/exception-autocapture.js.下载"></script><div hidden=""></div><script>((e,t,r,n,a,o,s,i)=>{let l=document.documentElement,u=["light","dark"];function c(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&o?a.map(e=>o[e]||e):a;r?(l.classList.remove(...n),l.classList.add(o&&o[t]?o[t]:t)):l.setAttribute(e,t)}),r=t,i&&u.includes(r)&&(l.style.colorScheme=r)}if(n)c(n);else try{let e=localStorage.getItem(t)||r,n=s&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(n)}catch(e){}})("class","isDarkMode","system",null,["dark","light","true","false","system"],{"true":"dark","false":"light","dark":"dark","light":"light"},true,true)</script><script>(self.__next_s=self.__next_s||[]).push([0,{"children":"(function m(a,b,c,d){try{let e=document.getElementById(\"banner\"),f=e?.innerText;if(!f)return void document.documentElement.setAttribute(d,\"hidden\");let g=localStorage.getItem(a),h=g!==f&&g!==b;null!=g&&(h?(localStorage.removeItem(c),localStorage.removeItem(a)):(localStorage.setItem(c,f),localStorage.setItem(a,f))),document.documentElement.setAttribute(d,!g||h?\"visible\":\"hidden\")}catch(a){console.error(a),document.documentElement.setAttribute(d,\"hidden\")}})(\n  \"textin-bannerDismissed\",\n  undefined,\n  \"__mintlify-bannerDismissed\",\n  \"data-banner-state\",\n)","id":"_mintlify-banner-script"}])</script><style>:root {
    --primary: 26 102 255;
    --primary-light: 26 102 255;
    --primary-dark: 26 102 255;
    --background-light: 255 255 255;
    --background-dark: 9 11 16;
    --gray-50: 243 245 250;
    --gray-100: 238 241 245;
    --gray-200: 223 225 230;
    --gray-300: 206 209 213;
    --gray-400: 159 161 166;
    --gray-500: 112 115 119;
    --gray-600: 80 83 87;
    --gray-700: 63 65 70;
    --gray-800: 38 40 45;
    --gray-900: 23 25 30;
    --gray-950: 10 13 17;
  }</style><div class="relative antialiased text-gray-500 dark:text-gray-400"><script>(self.__next_s=self.__next_s||[]).push([0,{"suppressHydrationWarning":true,"children":"(function(e,t,r,n){var a;let l,s=\"mint\"===n||\"linden\"===n?\"sidebar\":\"sidebar-content\",o=(l=\"navbar-transition\",\"maple\"===(a=n)&&(l+=\"-maple\"),\"willow\"===a&&(l+=\"-willow\"),l);function c(){document.documentElement.classList.add(\"lg:[--scroll-mt:9.5rem]\")}function i(e){document.getElementById(s)?.style.setProperty(\"top\",`${e}rem`)}function m(e){document.getElementById(s)?.style.setProperty(\"height\",`calc(100vh - ${e}rem)`)}function d(e,t){!e&&t||e&&!t?(c(),document.documentElement.classList.remove(\"lg:[--scroll-mt:12rem]\")):e&&t&&(document.documentElement.classList.add(\"lg:[--scroll-mt:12rem]\"),document.documentElement.classList.remove(\"lg:[--scroll-mt:9.5rem]\"))}let u=document.documentElement.getAttribute(\"data-banner-state\"),h=null!=u?\"visible\"===u:t;switch(n){case\"mint\":i(r),d(e,h);break;case\"palm\":case\"aspen\":i(r),m(r),d(e,h);break;case\"linden\":i(r),h&&c();break;case\"almond\":document.documentElement.style.setProperty(\"--scroll-mt\",\"2.5rem\"),i(r),m(r)}let p=function(){let e=document.createElement(\"style\");return e.appendChild(document.createTextNode(\"*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}\")),document.head.appendChild(e),function(){window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}}();(\"requestAnimationFrame\"in globalThis?requestAnimationFrame:setTimeout)(()=>{let e;e=!1,e=window.scrollY>50,document.getElementById(o)?.setAttribute(\"data-is-opaque\",`${!!e}`),p()})})(\n  true,\n  false,\n  (function i(e,t,r){let n=document.documentElement.getAttribute(\"data-banner-state\"),a=2.5*!!(null!=n?\"visible\"===n:t),l=3*!!e,s=4,o=a+4+l;switch(r){case\"mint\":case\"palm\":break;case\"aspen\":s=3.5,o=a+(l=2.5*!!e)+s;break;case\"linden\":o=a+(s=4);break;case\"almond\":o=a+(s=3.5)}return o})(true, false, \"mint\"),\n  \"mint\",\n)","id":"_mintlify-scroll-top-script"}])</script><div id="navbar" class="z-30 fixed lg:sticky top-0 w-full peer is-not-custom peer is-not-center peer is-not-wide peer is-not-frame"><div id="navbar-transition" class="absolute w-full h-full backdrop-blur flex-none transition-colors duration-500 border-b border-gray-500/5 dark:border-gray-300/[0.06] data-[is-opaque=true]:bg-background-light data-[is-opaque=true]:supports-backdrop-blur:bg-background-light/95 data-[is-opaque=true]:dark:bg-background-dark/75 data-[is-opaque=false]:supports-backdrop-blur:bg-background-light/60 data-[is-opaque=false]:dark:bg-transparent" data-is-opaque="false"></div><div class="max-w-8xl mx-auto relative"><div><div class="relative"><div class="flex items-center lg:px-12 h-16 min-w-0 mx-4 lg:mx-0"><div class="h-full relative flex-1 flex items-center gap-x-4 min-w-0 border-b border-gray-500/5 dark:border-gray-300/[0.06]"><div class="flex-1 flex items-center gap-x-4"><a href="https://docs.textin.com/"><span class="sr-only">Textin 智能文档解析 home page</span><img class="nav-logo w-auto h-7 relative object-contain block dark:hidden" alt="light logo" src="./快速启动 - Textin 智能文档解析_files/light.png"><img class="nav-logo w-auto h-7 relative object-contain hidden dark:block" alt="dark logo" src="./快速启动 - Textin 智能文档解析_files/dark.png"></a><div class="hidden lg:flex items-center gap-x-2"><button type="button" id="radix-_r_h_" aria-haspopup="menu" aria-expanded="false" data-state="closed" class="group bg-background-light dark:bg-background-dark disabled:pointer-events-none [&amp;&gt;span]:line-clamp-1 overflow-hidden group outline-none gap-1 group-hover:text-gray-950/70 dark:group-hover:text-white/70 text-xs text-gray-500 dark:text-gray-400 leading-5 font-semibold border border-gray-200 dark:border-gray-800 hover:border-gray-300 dark:hover:border-gray-700 rounded-full py-1 pl-2 pr-3 flex items-center whitespace-nowrap"><div class="relative w-4 h-4 rounded-full"><img class="w-full h-full rounded-full" alt="CN" src="./快速启动 - Textin 智能文档解析_files/CN.svg"><div class="absolute top-0 left-0 w-full h-full border rounded-full bg-primary-light/10 border-black/10"></div></div><p class="truncate pl-0.5 pr-0.5">简体中文</p><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down"><path d="m6 9 6 6 6-6"></path></svg></button></div></div><div class="relative hidden lg:flex items-center gap-2.5 flex-1"><button type="button" class="flex pointer-events-auto rounded-xl w-full items-center text-sm leading-6 h-9 pl-3.5 pr-3 shadow-sm text-gray-500 dark:text-white/50 bg-background-light dark:bg-background-dark dark:brightness-[1.1] dark:ring-1 dark:hover:brightness-[1.25] ring-1 ring-gray-400/20 hover:ring-gray-600/25 dark:ring-gray-600/30 dark:hover:ring-gray-500/30 focus:outline-primary justify-between truncate gap-2 min-w-[43px]" id="search-bar-entry"><div class="flex items-center gap-2 min-w-[42px]"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search min-w-4 flex-none text-gray-700 hover:text-gray-800 dark:text-gray-400 hover:dark:text-gray-200"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><div class="truncate min-w-0">搜索...</div></div><span class="flex-none text-xs font-semibold">Ctrl K</span></button><button type="button" class="flex-none hidden lg:flex items-center justify-center gap-1.5 pl-3 pr-3.5 h-9 rounded-xl shadow-sm bg-background-light dark:bg-background-dark dark:brightness-[1.1] dark:ring-1 dark:hover:brightness-[1.25] ring-1 ring-gray-400/20 hover:ring-gray-600/25 dark:ring-gray-600/30 dark:hover:ring-gray-500/30 focus:outline-primary" id="assistant-entry" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" class="w-4 h-4 shrink-0 text-gray-700 hover:text-gray-800 dark:text-gray-400 hover:dark:text-gray-200"><g fill="currentColor"><path d="M5.658,2.99l-1.263-.421-.421-1.263c-.137-.408-.812-.408-.949,0l-.421,1.263-1.263,.421c-.204,.068-.342,.259-.342,.474s.138,.406,.342,.474l1.263,.421,.421,1.263c.068,.204,.26,.342,.475,.342s.406-.138,.475-.342l.421-1.263,1.263-.421c.204-.068,.342-.259,.342-.474s-.138-.406-.342-.474Z" fill="currentColor" data-stroke="none" stroke="none"></path><polygon points="9.5 2.75 11.412 7.587 16.25 9.5 11.412 11.413 9.5 16.25 7.587 11.413 2.75 9.5 7.587 7.587 9.5 2.75" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></polygon></g></svg><span class="text-sm text-gray-500 dark:text-white/50 whitespace-nowrap">Ask AI</span></button></div><div class="flex-1 relative hidden lg:flex items-center ml-auto justify-end space-x-4"><nav class="text-sm"><ul class="flex space-x-6 items-center"><li class="navbar-link"><a href="https://www.textin.com/news/list" class="flex items-center gap-1.5 whitespace-nowrap font-medium text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" target="_blank">资讯中心</a></li><li class="navbar-link"><a href="https://www.textin.com/document/list" class="flex items-center gap-1.5 whitespace-nowrap font-medium text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" target="_blank">技术支持</a></li><li class="block lg:hidden"><a class="flex items-center gap-1.5 whitespace-nowrap font-medium text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" href="https://www.textin.com/console/recognition/robot_markdown?service=pdf_to_markdown">快速体验</a></li><li class="whitespace-nowrap hidden lg:flex" id="topbar-cta-button"><a target="_blank" class="group px-4 py-1.5 relative inline-flex items-center text-sm font-medium" href="https://www.textin.com/console/recognition/robot_markdown?service=pdf_to_markdown"><span class="absolute inset-0 bg-primary-dark rounded-full group-hover:opacity-[0.9]"></span><div class="mr-0.5 space-x-2.5 flex items-center"><span class="z-10 text-white">快速体验</span><svg width="3" height="24" viewBox="0 -9 3 24" class="h-5 rotate-0 overflow-visible text-white/90"><path d="M0 0L3 3L0 6" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path></svg></div></a></li></ul></nav><div class="flex items-center"><button class="group p-2 flex items-center justify-center" aria-label="Toggle dark mode"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" stroke="currentColor" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 block text-gray-400 dark:hidden group-hover:text-gray-600"><g clip-path="url(#clip0_2880_7340)"><path d="M8 1.11133V2.00022" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M12.8711 3.12891L12.2427 3.75735" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M14.8889 8H14" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M12.8711 12.8711L12.2427 12.2427" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M8 14.8889V14" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M3.12891 12.8711L3.75735 12.2427" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M1.11133 8H2.00022" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M3.12891 3.12891L3.75735 3.75735" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M8.00043 11.7782C10.0868 11.7782 11.7782 10.0868 11.7782 8.00043C11.7782 5.91402 10.0868 4.22266 8.00043 4.22266C5.91402 4.22266 4.22266 5.91402 4.22266 8.00043C4.22266 10.0868 5.91402 11.7782 8.00043 11.7782Z" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></g><defs><clippath id="clip0_2880_7340"><rect width="16" height="16" fill="white"></rect></clippath></defs></svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-moon h-4 w-4 hidden dark:block text-gray-500 dark:group-hover:text-gray-300"><path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path></svg></button></div></div><div class="flex lg:hidden items-center gap-3"><button type="button" class="text-gray-500 w-8 h-8 flex items-center justify-center hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300" id="search-bar-entry-mobile"><span class="sr-only">搜索...</span><svg class="h-4 w-4 bg-gray-500 dark:bg-gray-400 hover:bg-gray-600 dark:hover:bg-gray-300" style="mask-image: url(&quot;https://mintlify.b-cdn.net/v6.6.0/solid/magnifying-glass.svg&quot;); mask-repeat: no-repeat; mask-position: center center;"></svg></button><button id="assistant-entry-mobile"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" class="size-4.5 text-gray-500 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"><g fill="currentColor"><path d="M5.658,2.99l-1.263-.421-.421-1.263c-.137-.408-.812-.408-.949,0l-.421,1.263-1.263,.421c-.204,.068-.342,.259-.342,.474s.138,.406,.342,.474l1.263,.421,.421,1.263c.068,.204,.26,.342,.475,.342s.406-.138,.475-.342l.421-1.263,1.263-.421c.204-.068,.342-.259,.342-.474s-.138-.406-.342-.474Z" fill="currentColor" data-stroke="none" stroke="none"></path><polygon points="9.5 2.75 11.412 7.587 16.25 9.5 11.412 11.413 9.5 16.25 7.587 11.413 2.75 9.5 7.587 7.587 9.5 2.75" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></polygon></g></svg></button><button aria-label="More actions" class="h-7 w-5 flex items-center justify-end"><svg class="h-4 w-4 bg-gray-500 dark:bg-gray-400 hover:bg-gray-600 dark:hover:bg-gray-300" style="mask-image: url(&quot;https://mintlify.b-cdn.net/v6.6.0/solid/ellipsis-vertical.svg&quot;); mask-repeat: no-repeat; mask-position: center center;"></svg></button></div></div></div><button type="button" class="flex items-center h-14 py-4 px-5 lg:hidden focus:outline-none w-full text-left"><div class="text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300"><span class="sr-only">Navigation</span><svg class="h-4" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M0 96C0 78.3 14.3 64 32 64H416c17.7 0 32 14.3 32 32s-14.3 32-32 32H32C14.3 128 0 113.7 0 96zM0 256c0-17.7 14.3-32 32-32H416c17.7 0 32 14.3 32 32s-14.3 32-32 32H32c-17.7 0-32-14.3-32-32zM448 416c0 17.7-14.3 32-32 32H32c-17.7 0-32-14.3-32-32s14.3-32 32-32H416c17.7 0 32 14.3 32 32z"></path></svg></div><div class="ml-4 flex text-sm leading-6 whitespace-nowrap min-w-0 space-x-3 overflow-hidden"><div class="flex items-center space-x-3 flex-shrink-0"><span>文档解析</span><svg width="3" height="24" viewBox="0 -9 3 24" class="h-5 rotate-0 overflow-visible fill-gray-400"><path d="M0 0L3 3L0 6" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path></svg></div><div class="font-semibold text-gray-900 truncate dark:text-gray-200 min-w-0 flex-1">快速启动</div></div></button></div><div class="hidden lg:flex px-12 h-12"><div class="nav-tabs h-full flex text-sm gap-x-6"><a class="link nav-tabs-item group relative h-full gap-2 flex items-center hover:text-gray-800 dark:hover:text-gray-300 text-gray-800 dark:text-gray-200 font-semibold" href="https://docs.textin.com/xparse/overview">Guides<div class="absolute bottom-0 h-[1.5px] w-full bg-primary dark:bg-primary-light"></div></a><a class="link nav-tabs-item group relative h-full gap-2 flex items-center font-medium text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300" href="https://docs.textin.com/api-reference/endpoint/parse">API<div class="absolute bottom-0 h-[1.5px] w-full group-hover:bg-gray-200 dark:group-hover:bg-gray-700"></div></a></div></div></div></div></div><div class="peer-[.is-not-center]:max-w-8xl peer-[.is-center]:max-w-3xl peer-[.is-not-custom]:px-4 peer-[.is-not-custom]:mx-auto peer-[.is-not-custom]:lg:px-8 peer-[.is-wide]:[&amp;&gt;div:last-child]:max-w-6xl peer-[.is-custom]:contents peer-[.is-custom]:[&amp;&gt;div:first-child]:!hidden peer-[.is-custom]:[&amp;&gt;div:first-child]:sm:!hidden peer-[.is-custom]:[&amp;&gt;div:first-child]:md:!hidden peer-[.is-custom]:[&amp;&gt;div:first-child]:lg:!hidden peer-[.is-custom]:[&amp;&gt;div:first-child]:xl:!hidden peer-[.is-center]:[&amp;&gt;div:first-child]:!hidden peer-[.is-center]:[&amp;&gt;div:first-child]:sm:!hidden peer-[.is-center]:[&amp;&gt;div:first-child]:md:!hidden peer-[.is-center]:[&amp;&gt;div:first-child]:lg:!hidden peer-[.is-center]:[&amp;&gt;div:first-child]:xl:!hidden"><div class="z-20 hidden lg:block fixed bottom-0 right-auto w-[18rem]" id="sidebar" style="top:7rem"><div class="absolute inset-0 z-10 stable-scrollbar-gutter overflow-auto pr-8 pb-10" id="sidebar-content"><div class="relative lg:text-sm lg:leading-6"><div class="sticky top-0 h-8 z-10 bg-gradient-to-b from-background-light dark:from-background-dark"></div><div id="navigation-items"><div class=""><div class="sidebar-group-header flex items-center gap-2.5 pl-4 mb-3.5 lg:mb-2.5 font-semibold text-gray-900 dark:text-gray-200"><h5 id="sidebar-title">产品概览</h5></div><ul id="sidebar-group"><li id="/xparse/overview" class="relative scroll-m-4 first:scroll-m-20" data-title="产品简介"><a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" href="https://docs.textin.com/xparse/overview" style="padding-left: 1rem;"><div class="flex-1 flex items-center space-x-2.5"><div class="">产品简介</div></div></a></li><li id="/xparse/api-key" class="relative scroll-m-4 first:scroll-m-20" data-title="API Key"><a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" href="https://docs.textin.com/xparse/api-key" style="padding-left: 1rem;"><div class="flex-1 flex items-center space-x-2.5"><div class="">API Key</div></div></a></li><li id="/xparse/product-manual" class="relative scroll-m-4 first:scroll-m-20" data-title="使用手册"><a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" href="https://docs.textin.com/xparse/product-manual" style="padding-left: 1rem;"><div class="flex-1 flex items-center space-x-2.5"><div class="">使用手册</div></div></a></li></ul></div><div class="mt-6 lg:mt-8"><div class="sidebar-group-header flex items-center gap-2.5 pl-4 mb-3.5 lg:mb-2.5 font-semibold text-gray-900 dark:text-gray-200"><h5 id="sidebar-title">文档解析</h5></div><ul id="sidebar-group"><li id="/xparse/parse-quickstart" class="relative scroll-m-4 first:scroll-m-20" data-title="快速启动"><a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl bg-primary/10 text-primary font-semibold dark:text-primary-light dark:bg-primary-light/10" href="https://docs.textin.com/xparse/parse-quickstart" style="padding-left: 1rem;"><div class="flex-1 flex items-center space-x-2.5"><div class="">快速启动</div></div></a></li><li id="/xparse/parse-getjson" class="relative scroll-m-4 first:scroll-m-20" data-title="返回JSON结构说明"><a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" href="https://docs.textin.com/xparse/parse-getjson" style="padding-left: 1rem;"><div class="flex-1 flex items-center space-x-2.5"><div class="">返回JSON结构说明</div></div></a></li><li id="/xparse/parse-gettable" class="relative scroll-m-4 first:scroll-m-20" data-title="获取表格"><a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" href="https://docs.textin.com/xparse/parse-gettable" style="padding-left: 1rem;"><div class="flex-1 flex items-center space-x-2.5"><div class="">获取表格</div></div></a></li><li id="/xparse/parse-getcatalog" class="relative scroll-m-4 first:scroll-m-20" data-title="获取目录树"><a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" href="https://docs.textin.com/xparse/parse-getcatalog" style="padding-left: 1rem;"><div class="flex-1 flex items-center space-x-2.5"><div class="">获取目录树</div></div></a></li><li id="/xparse/parse-getimage" class="relative scroll-m-4 first:scroll-m-20" data-title="获取图片并持久化"><a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" href="https://docs.textin.com/xparse/parse-getimage" style="padding-left: 1rem;"><div class="flex-1 flex items-center space-x-2.5"><div class="">获取图片并持久化</div></div></a></li><li id="/xparse/parse-getpos" class="relative scroll-m-4 first:scroll-m-20" data-title="前端可视化：获取精确坐标"><a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" href="https://docs.textin.com/xparse/parse-getpos" style="padding-left: 1rem;"><div class="flex-1 flex items-center space-x-2.5"><div class="">前端可视化：获取精确坐标</div></div></a></li><li id="/xparse/parse-max-workers" class="relative scroll-m-4 first:scroll-m-20" data-title="多并发请求"><a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" href="https://docs.textin.com/xparse/parse-max-workers" style="padding-left: 1rem;"><div class="flex-1 flex items-center space-x-2.5"><div class="">多并发请求</div></div></a></li></ul></div><div class="mt-6 lg:mt-8"><div class="sidebar-group-header flex items-center gap-2.5 pl-4 mb-3.5 lg:mb-2.5 font-semibold text-gray-900 dark:text-gray-200"><h5 id="sidebar-title">文档抽取</h5></div><ul id="sidebar-group"><li id="/xparse/extract-quickstart" class="relative scroll-m-4 first:scroll-m-20" data-title="快速启动"><a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" href="https://docs.textin.com/xparse/extract-quickstart" style="padding-left: 1rem;"><div class="flex-1 flex items-center space-x-2.5"><div class="">快速启动</div></div></a></li><li id="/xparse/extract-getjson" class="relative scroll-m-4 first:scroll-m-20" data-title="返回JSON结构说明"><a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" href="https://docs.textin.com/xparse/extract-getjson" style="padding-left: 1rem;"><div class="flex-1 flex items-center space-x-2.5"><div class="">返回JSON结构说明</div></div></a></li><li id="/xparse/extract-gettable" class="relative scroll-m-4 first:scroll-m-20" data-title="获取表格"><a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" href="https://docs.textin.com/xparse/extract-gettable" style="padding-left: 1rem;"><div class="flex-1 flex items-center space-x-2.5"><div class="">获取表格</div></div></a></li><li id="/xparse/extract-getpos" class="relative scroll-m-4 first:scroll-m-20" data-title="前端可视化：获取精确坐标"><a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" href="https://docs.textin.com/xparse/extract-getpos" style="padding-left: 1rem;"><div class="flex-1 flex items-center space-x-2.5"><div class="">前端可视化：获取精确坐标</div></div></a></li><li id="/xparse/extract-asyncio" class="relative scroll-m-4 first:scroll-m-20" data-title="异步请求"><a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" href="https://docs.textin.com/xparse/extract-asyncio" style="padding-left: 1rem;"><div class="flex-1 flex items-center space-x-2.5"><div class="">异步请求</div></div></a></li><li id="/xparse/extract-max-concurrent" class="relative scroll-m-4 first:scroll-m-20" data-title="多并发请求"><a class="group mt-2 lg:mt-0 flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" href="https://docs.textin.com/xparse/extract-max-concurrent" style="padding-left: 1rem;"><div class="flex-1 flex items-center space-x-2.5"><div class="">多并发请求</div></div></a></li></ul></div></div></div></div></div><div id="content-container"><!--$--><!--/$--><script>(self.__next_s=self.__next_s||[]).push([0,{"children":"document.documentElement.setAttribute('data-page-mode', 'none');","id":"_mintlify-page-mode-script"}])</script><script>(self.__next_s=self.__next_s||[]).push([0,{"suppressHydrationWarning":true,"children":"(function d(e,t){if(!document.getElementById(\"footer\")?.classList.contains(\"advanced-footer\")||\"maple\"===t||\"willow\"===t||\"almond\"===t)return;let r=document.documentElement.getAttribute(\"data-page-mode\"),n=document.getElementById(\"navbar\"),a=document.getElementById(\"sidebar\"),l=document.getElementById(\"footer\"),s=document.getElementById(\"table-of-contents-content\");if(!l||\"center\"===r)return;let o=l.getBoundingClientRect().top,c=window.innerHeight-o;a&&(c>0?(a.style.top=`-${c}px`,a.style.height=`${window.innerHeight}px`):(a.style.top=`${e}rem`,a.style.height=\"auto\")),s&&n&&(c>0?s.style.top=\"custom\"===r?`${n.clientHeight-c}px`:`${40+n.clientHeight-c}px`:s.style.top=\"\")})(\n  (function i(e,t,r){let n=document.documentElement.getAttribute(\"data-banner-state\"),a=2.5*!!(null!=n?\"visible\"===n:t),l=3*!!e,s=4,o=a+4+l;switch(r){case\"mint\":case\"palm\":break;case\"aspen\":s=3.5,o=a+(l=2.5*!!e)+s;break;case\"linden\":o=a+(s=4);break;case\"almond\":o=a+(s=3.5)}return o})(true, false, \"mint\"),\n  \"mint\",\n)","id":"_mintlify-footer-and-sidebar-scroll-script"}])</script><span class="fixed inset-0 bg-background-light dark:bg-background-dark -z-10 pointer-events-none"></span><style>/* 隐藏语言选择按钮 */
#navbar
  button[data-state="closed"][aria-haspopup="menu"][aria-expanded="false"] {
  display: none;
}

/* 隐藏广告 */
#footer > :last-child {
  display: none;
}

.p-4 .p-px > .min-h-fit .overflow-auto {
  max-height: 60vh;
}
</style><div class="flex flex-row-reverse gap-12 box-border w-full pt-40 lg:pt-10"><div class="hidden xl:flex self-start sticky xl:flex-col max-w-[28rem] h-[calc(100vh-9.5rem)] top-[9.5rem]" id="content-side-layout"><div class="z-10 hidden xl:flex pl-10 box-border w-[19rem] max-h-full" id="table-of-contents-layout"><div class="text-gray-600 text-sm leading-6 w-[16.5rem] overflow-y-auto space-y-2 pb-4 -mt-10 pt-10" id="table-of-contents"><div class="text-gray-700 dark:text-gray-300 font-medium flex items-center space-x-2"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" stroke="currentColor" stroke-width="2" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3"><path d="M2.44434 12.6665H13.5554" stroke-linecap="round" stroke-linejoin="round"></path><path d="M2.44434 3.3335H13.5554" stroke-linecap="round" stroke-linejoin="round"></path><path d="M2.44434 8H7.33323" stroke-linecap="round" stroke-linejoin="round"></path></svg><span>在此页面</span></div><ul id="table-of-contents-content" class="toc"><li class="toc-item relative" data-depth="0"><a href="https://docs.textin.com/xparse/parse-quickstart#%E4%B8%BA%E4%BB%80%E4%B9%88%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3%E8%A7%A3%E6%9E%90api-%EF%BC%9F" class="py-1 block font-medium text-primary dark:text-primary-light border-primary dark:border-primary-light hover:border-primary dark:hover:border-primary-light">为什么使用文档解析API ？</a></li><li class="toc-item relative" data-depth="0"><a href="https://docs.textin.com/xparse/parse-quickstart#%E5%A6%82%E4%BD%95%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3%E8%A7%A3%E6%9E%90api-%EF%BC%9F" class="py-1 block font-medium hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300">如何使用文档解析API ？</a></li><li class="toc-item relative" data-depth="1"><a href="https://docs.textin.com/xparse/parse-quickstart#%E5%85%88%E5%86%B3%E6%9D%A1%E4%BB%B6%EF%BC%9A%E8%8E%B7%E5%8F%96api-key" class="group flex items-start py-1 whitespace-pre-wrap text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="margin-left: 1rem;">先决条件：获取API Key</a></li><li class="toc-item relative" data-depth="1"><a href="https://docs.textin.com/xparse/parse-quickstart#%E5%89%8D%E7%BD%AE%E5%87%86%E5%A4%87" class="group flex items-start py-1 whitespace-pre-wrap text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="margin-left: 1rem;">前置准备</a></li><li class="toc-item relative" data-depth="1"><a href="https://docs.textin.com/xparse/parse-quickstart#%E8%A7%A3%E6%9E%90%E5%8D%95%E4%B8%AA%E6%9C%AC%E5%9C%B0%E6%96%87%E4%BB%B6%E5%B9%B6%E4%BF%9D%E5%AD%98%E7%BB%93%E6%9E%9C" class="group flex items-start py-1 whitespace-pre-wrap text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="margin-left: 1rem;">解析单个本地文件并保存结果</a></li><li class="toc-item relative" data-depth="1"><a href="https://docs.textin.com/xparse/parse-quickstart#%E8%A7%A3%E6%9E%90%E5%A4%9A%E4%B8%AA%E6%9C%AC%E5%9C%B0%E6%96%87%E4%BB%B6%E5%B9%B6%E4%BF%9D%E5%AD%98%E7%BB%93%E6%9E%9C%E8%87%B3%E6%8C%87%E5%AE%9A%E7%9B%AE%E5%BD%95" class="group flex items-start py-1 whitespace-pre-wrap text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="margin-left: 1rem;">解析多个本地文件并保存结果至指定目录</a></li><li class="toc-item relative" data-depth="1"><a href="https://docs.textin.com/xparse/parse-quickstart#%E8%A7%A3%E6%9E%90%E4%BD%8D%E4%BA%8Eurl%E7%9A%84%E6%96%87%E4%BB%B6%E5%B9%B6%E4%BF%9D%E5%AD%98%E7%BB%93%E6%9E%9C" class="group flex items-start py-1 whitespace-pre-wrap text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="margin-left: 1rem;">解析位于URL的文件并保存结果</a></li><li class="toc-item relative" data-depth="1"><a href="https://docs.textin.com/xparse/parse-quickstart#url%E5%8F%82%E6%95%B0%E8%AF%B4%E6%98%8E" class="group flex items-start py-1 whitespace-pre-wrap text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="margin-left: 1rem;">URL参数说明</a></li><li class="toc-item relative" data-depth="1"><a href="https://docs.textin.com/xparse/parse-quickstart#%E8%BF%94%E5%9B%9E%E7%BB%93%E6%9E%9C%E7%A4%BA%E4%BE%8B" class="group flex items-start py-1 whitespace-pre-wrap text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="margin-left: 1rem;">返回结果示例</a></li><li class="toc-item relative" data-depth="1"><a href="https://docs.textin.com/xparse/parse-quickstart#%E9%94%99%E8%AF%AF%E7%A0%81%E8%AF%B4%E6%98%8E" class="group flex items-start py-1 whitespace-pre-wrap text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="margin-left: 1rem;">错误码说明</a></li></ul></div></div></div><div class="relative grow box-border flex-col w-full mx-auto px-1 lg:pl-[23.7rem] lg:-ml-12 xl:w-[calc(100%-28rem)]" id="content-area"><header id="header" class="relative"><div class="mt-0.5 space-y-2.5"><div class="eyebrow h-5 text-primary dark:text-primary-light text-sm font-semibold">文档解析</div><div class="flex flex-col sm:flex-row items-start sm:items-center relative gap-2"><h1 id="page-title" class="inline-block text-2xl sm:text-3xl font-bold text-gray-900 tracking-tight dark:text-gray-200">快速启动</h1></div></div><div class="mt-2 text-lg prose prose-gray dark:prose-invert"><p>参考示例，快速将文档解析API接入到您的系统和应用流程中。</p></div></header><div class="flex flex-col gap-8"></div><div class="mdx-content relative mt-8 prose prose-gray dark:prose-invert" data-page-title="快速启动" data-page-href="/xparse/parse-quickstart"><h2 class="flex whitespace-pre-wrap group font-semibold" id="%E4%B8%BA%E4%BB%80%E4%B9%88%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3%E8%A7%A3%E6%9E%90api-%EF%BC%9F"><div class="absolute"><a href="https://docs.textin.com/xparse/parse-quickstart#%E4%B8%BA%E4%BB%80%E4%B9%88%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3%E8%A7%A3%E6%9E%90api-%EF%BC%9F" class="-ml-10 flex items-center opacity-0 border-0 group-hover:opacity-100" aria-label="Navigate to header">​<div class="w-6 h-6 rounded-md flex items-center justify-center shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 bg-white ring-1 ring-gray-400/30 dark:ring-gray-700/25 hover:ring-gray-400/60 dark:hover:ring-white/20"><svg xmlns="http://www.w3.org/2000/svg" fill="gray" height="12px" viewBox="0 0 576 512"><path d="M0 256C0 167.6 71.6 96 160 96h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C98.1 144 48 194.1 48 256s50.1 112 112 112h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C71.6 416 0 344.4 0 256zm576 0c0 88.4-71.6 160-160 160H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c61.9 0 112-50.1 112-112s-50.1-112-112-112H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c88.4 0 160 71.6 160 160zM184 232H392c13.3 0 24 10.7 24 24s-10.7 24-24 24H184c-13.3 0-24-10.7-24-24s10.7-24 24-24z"></path></svg></div></a></div><span class="cursor-pointer">为什么使用文档解析API ？</span></h2>
<span data-as="p">大模型时代，文档(尤其是复杂文档)中蕴含着海量高价值的数据内容，借助文档解析API将其结构化为大模型更容易理解的格式(如markdown)，可以更大程度上增强大模型的能力、发挥更大价值，快速实现业务AI升级。</span>
<span data-as="p">TextIn xParse 文档解析API 是专为大模型重新设计的文档理解引擎，可以满足AI开发者的核心需求：✅ 文档结构完整保持 ✅ 语义关系准确理解 ✅ 大模型原生友好</span>
<span data-as="p">使用文档解析API解析一个或多个文档，您可以选择将输出结果作为markdown或JSON文件保存在指定的目录中，也可以对输出结果做进一步的处理以满足您的业务需求。如果您正在进行知识库、RAG、大模型原生应用、Agent等业务方向的产品建设，文档解析API会为您提供帮助。</span>
<h2 class="flex whitespace-pre-wrap group font-semibold" id="%E5%A6%82%E4%BD%95%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3%E8%A7%A3%E6%9E%90api-%EF%BC%9F"><div class="absolute"><a href="https://docs.textin.com/xparse/parse-quickstart#%E5%A6%82%E4%BD%95%E4%BD%BF%E7%94%A8%E6%96%87%E6%A1%A3%E8%A7%A3%E6%9E%90api-%EF%BC%9F" class="-ml-10 flex items-center opacity-0 border-0 group-hover:opacity-100" aria-label="Navigate to header">​<div class="w-6 h-6 rounded-md flex items-center justify-center shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 bg-white ring-1 ring-gray-400/30 dark:ring-gray-700/25 hover:ring-gray-400/60 dark:hover:ring-white/20"><svg xmlns="http://www.w3.org/2000/svg" fill="gray" height="12px" viewBox="0 0 576 512"><path d="M0 256C0 167.6 71.6 96 160 96h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C98.1 144 48 194.1 48 256s50.1 112 112 112h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C71.6 416 0 344.4 0 256zm576 0c0 88.4-71.6 160-160 160H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c61.9 0 112-50.1 112-112s-50.1-112-112-112H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c88.4 0 160 71.6 160 160zM184 232H392c13.3 0 24 10.7 24 24s-10.7 24-24 24H184c-13.3 0-24-10.7-24-24s10.7-24 24-24z"></path></svg></div></a></div><span class="cursor-pointer">如何使用文档解析API ？</span></h2>
<span data-as="p">您可以参考以下示例文件和步骤，快速验证并将文档解析API接入到您的系统和应用流程中。</span>
<div class="callout my-4 px-5 py-4 overflow-hidden rounded-2xl flex gap-3 border border-emerald-500/20 bg-emerald-50/50 dark:border-emerald-500/30 dark:bg-emerald-500/10" data-callout-type="tip"><div class="mt-0.5 w-4" data-component-part="callout-icon"><svg width="11" height="14" viewBox="0 0 11 14" fill="currentColor" xmlns="http://www.w3.org/2000/svg" class="text-emerald-600 dark:text-emerald-400/80 w-3.5 h-auto" aria-label="Tip"><path d="M3.12794 12.4232C3.12794 12.5954 3.1776 12.7634 3.27244 12.907L3.74114 13.6095C3.88471 13.8248 4.21067 14 4.46964 14H6.15606C6.41415 14 6.74017 13.825 6.88373 13.6095L7.3508 12.9073C7.43114 12.7859 7.49705 12.569 7.49705 12.4232L7.50055 11.3513H3.12521L3.12794 12.4232ZM5.31288 0C2.52414 0.00875889 0.5 2.26889 0.5 4.78826C0.5 6.00188 0.949566 7.10829 1.69119 7.95492C2.14321 8.47011 2.84901 9.54727 3.11919 10.4557C3.12005 10.4625 3.12175 10.4698 3.12261 10.4771H7.50342C7.50427 10.4698 7.50598 10.463 7.50684 10.4557C7.77688 9.54727 8.48281 8.47011 8.93484 7.95492C9.67728 7.13181 10.1258 6.02703 10.1258 4.78826C10.1258 2.15486 7.9709 0.000106649 5.31288 0ZM7.94902 7.11267C7.52078 7.60079 6.99082 8.37878 6.6077 9.18794H4.02051C3.63739 8.37878 3.10743 7.60079 2.67947 7.11294C2.11997 6.47551 1.8126 5.63599 1.8126 4.78826C1.8126 3.09829 3.12794 1.31944 5.28827 1.3126C7.2435 1.3126 8.81315 2.88226 8.81315 4.78826C8.81315 5.63599 8.50688 6.47551 7.94902 7.11267ZM4.87534 2.18767C3.66939 2.18767 2.68767 3.16939 2.68767 4.37534C2.68767 4.61719 2.88336 4.81288 3.12521 4.81288C3.36705 4.81288 3.56274 4.61599 3.56274 4.37534C3.56274 3.6515 4.1515 3.06274 4.87534 3.06274C5.11719 3.06274 5.31288 2.86727 5.31288 2.62548C5.31288 2.38369 5.11599 2.18767 4.87534 2.18767Z"></path></svg></div><div class="text-sm prose min-w-0 w-full text-emerald-900 dark:text-emerald-200" data-component-part="callout-content"><span data-as="p">这里为您提供了一份Textin官方pdf示例文件，您可以点击下载或使用该链接：<a href="https://dllf.intsig.net/download/2025/Solution/textin/sample/pdf_to_markdown/sample_02.pdf" target="_blank" rel="noreferrer" class="link">文档解析pdf示例.pdf</a></span></div></div>
<h3 class="flex whitespace-pre-wrap group font-semibold" id="%E5%85%88%E5%86%B3%E6%9D%A1%E4%BB%B6%EF%BC%9A%E8%8E%B7%E5%8F%96api-key"><div class="absolute"><a href="https://docs.textin.com/xparse/parse-quickstart#%E5%85%88%E5%86%B3%E6%9D%A1%E4%BB%B6%EF%BC%9A%E8%8E%B7%E5%8F%96api-key" class="-ml-10 flex items-center opacity-0 border-0 group-hover:opacity-100" aria-label="Navigate to header">​<div class="w-6 h-6 rounded-md flex items-center justify-center shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 bg-white ring-1 ring-gray-400/30 dark:ring-gray-700/25 hover:ring-gray-400/60 dark:hover:ring-white/20"><svg xmlns="http://www.w3.org/2000/svg" fill="gray" height="12px" viewBox="0 0 576 512"><path d="M0 256C0 167.6 71.6 96 160 96h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C98.1 144 48 194.1 48 256s50.1 112 112 112h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C71.6 416 0 344.4 0 256zm576 0c0 88.4-71.6 160-160 160H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c61.9 0 112-50.1 112-112s-50.1-112-112-112H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c88.4 0 160 71.6 160 160zM184 232H392c13.3 0 24 10.7 24 24s-10.7 24-24 24H184c-13.3 0-24-10.7-24-24s10.7-24 24-24z"></path></svg></div></a></div><span class="cursor-pointer">先决条件：获取API Key</span></h3>
<span data-as="p">使用文档解析API处理文档时，您需要先获取API Key。请先登录后前往 <a href="https://www.textin.com/console/dashboard/setting" target="_blank" rel="noreferrer" class="link">TextIn工作台 - 账号与开发者信息</a> 获取您的 x-ti-app-id 和 x-ti-secret-code 。</span>
<h3 class="flex whitespace-pre-wrap group font-semibold" id="%E5%89%8D%E7%BD%AE%E5%87%86%E5%A4%87"><div class="absolute"><a href="https://docs.textin.com/xparse/parse-quickstart#%E5%89%8D%E7%BD%AE%E5%87%86%E5%A4%87" class="-ml-10 flex items-center opacity-0 border-0 group-hover:opacity-100" aria-label="Navigate to header">​<div class="w-6 h-6 rounded-md flex items-center justify-center shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 bg-white ring-1 ring-gray-400/30 dark:ring-gray-700/25 hover:ring-gray-400/60 dark:hover:ring-white/20"><svg xmlns="http://www.w3.org/2000/svg" fill="gray" height="12px" viewBox="0 0 576 512"><path d="M0 256C0 167.6 71.6 96 160 96h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C98.1 144 48 194.1 48 256s50.1 112 112 112h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C71.6 416 0 344.4 0 256zm576 0c0 88.4-71.6 160-160 160H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c61.9 0 112-50.1 112-112s-50.1-112-112-112H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c88.4 0 160 71.6 160 160zM184 232H392c13.3 0 24 10.7 24 24s-10.7 24-24 24H184c-13.3 0-24-10.7-24-24s10.7-24 24-24z"></path></svg></div></a></div><span class="cursor-pointer">前置准备</span></h3>
<span data-as="p">您可以参考以下示例代码完成文档解析API请求的前置准备工作，替换您自己的 x-ti-app-id 和 x-ti-secret-code ，后续步骤可根据实际使用场景在main函数中插入代码。</span>
<div class="code-block mt-5 mb-8 not-prose rounded-2xl relative group text-gray-950 dark:text-gray-50 codeblock-light border border-gray-950/10 dark:border-white/10 bg-transparent dark:bg-transparent" numberoflines="44" language="python"><div class="absolute top-3 right-4 flex items-center gap-1.5"><div class="z-10 relative"><button class="h-[26px] w-[26px] flex items-center justify-center rounded-md backdrop-blur peer group/copy-button" data-testid="copy-code-button" aria-label="Copy the contents from the code block"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-400 group-hover/copy-button:text-gray-500 dark:text-white/40 dark:group-hover/copy-button:text-white/60"><path d="M14.25 5.25H7.25C6.14543 5.25 5.25 6.14543 5.25 7.25V14.25C5.25 15.3546 6.14543 16.25 7.25 16.25H14.25C15.3546 16.25 16.25 15.3546 16.25 14.25V7.25C16.25 6.14543 15.3546 5.25 14.25 5.25Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M2.80103 11.998L1.77203 5.07397C1.61003 3.98097 2.36403 2.96397 3.45603 2.80197L10.38 1.77297C11.313 1.63397 12.19 2.16297 12.528 3.00097" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></button><div aria-hidden="true" class="absolute top-11 left-1/2 transform whitespace-nowrap -translate-x-1/2 -translate-y-1/2 peer-hover:opacity-100 opacity-0 text-white rounded-lg px-1.5 py-0.5 text-xs bg-primary-dark">Copy</div></div><div class="z-10 relative"><button class="h-[26px] w-[26px] flex items-center justify-center rounded-md backdrop-blur peer group/ask-ai-button" id="ask-ai-code-block-button" aria-label="Ask AI"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-400 group-hover/ask-ai-button:text-gray-500 dark:text-white/40 dark:group-hover/ask-ai-button:text-white/60"><path d="M3.51169 1.50098L3.92087 2.72754L3.99997 2.96387L5.49997 3.46387L4.23825 3.88477L4.00095 3.96387L3.92184 4.20117L3.50095 5.46191L3.49997 5.46387H3.49899L2.99899 3.96387L1.49899 3.46387L2.99899 2.96387L3.07809 2.72754L3.4863 1.50098C3.49031 1.50045 3.49522 1.5 3.49997 1.5C3.50416 1.50002 3.50807 1.50054 3.51169 1.50098Z" stroke="currentColor"></path><path d="M9.5 2.75L11.412 7.587L16.25 9.5L11.412 11.413L9.5 16.25L7.587 11.413L2.75 9.5L7.587 7.587L9.5 2.75Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></button><div aria-hidden="true" class="absolute top-11 left-1/2 transform whitespace-nowrap -translate-x-1/2 -translate-y-1/2 peer-hover:opacity-100 opacity-0 text-white rounded-lg px-1.5 py-0.5 text-xs bg-primary-dark">Ask AI</div></div></div><div class="w-0 min-w-full max-w-full py-3.5 px-4 h-full dark:bg-codeblock relative text-sm leading-6 children:!my-0 children:!shadow-none children:!bg-transparent transition-[height] duration-300 ease-in-out [&amp;_*]:ring-0 [&amp;_*]:outline-none [&amp;_*]:focus:ring-0 [&amp;_*]:focus:outline-none [&amp;_pre&gt;code]:pr-[3rem] [&amp;_pre&gt;code&gt;span.line-highlight]:min-w-[calc(100%+3rem)] [&amp;_pre&gt;code&gt;span.line-diff]:min-w-[calc(100%+3rem)] rounded-2xl bg-white overflow-x-auto scrollbar-thin scrollbar-thumb-rounded scrollbar-thumb-black/15 hover:scrollbar-thumb-black/20 active:scrollbar-thumb-black/20 dark:scrollbar-thumb-white/20 dark:hover:scrollbar-thumb-white/25 dark:active:scrollbar-thumb-white/25" data-component-part="code-block-root" style="font-variant-ligatures: none; height: auto;"><div class="font-mono whitespace-pre leading-6"><pre class="shiki shiki-themes github-light-default dark-plus" language="python" style="background-color: transparent; --shiki-dark-bg: transparent; color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"><code language="python" numberoflines="44"><span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">import</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> json</span></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">import</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> requests</span></span>
<span class="line"></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">class</span><span style="color: rgb(149, 56, 0); --shiki-dark: #4EC9B0;"> OCRClient</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">:</span></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">    def</span><span style="color: rgb(5, 80, 174); --shiki-dark: #DCDCAA;"> __init__</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">(</span><span style="color: rgb(31, 35, 40); --shiki-dark: #9CDCFE;">self</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(31, 35, 40); --shiki-dark: #9CDCFE;">app_id</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #4EC9B0;">str</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(31, 35, 40); --shiki-dark: #9CDCFE;">secret_code</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #4EC9B0;">str</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">):</span></span>
<span class="line"><span style="color: rgb(5, 80, 174); --shiki-dark: #9cdcfe;">        self</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">.app_id </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> app_id</span></span>
<span class="line"><span style="color: rgb(5, 80, 174); --shiki-dark: #9cdcfe;">        self</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">.secret_code </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> secret_code</span></span>
<span class="line"></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">    def</span><span style="color: rgb(130, 80, 223); --shiki-dark: #DCDCAA;"> recognize</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">(</span><span style="color: rgb(31, 35, 40); --shiki-dark: #9CDCFE;">self</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(31, 35, 40); --shiki-dark: #9CDCFE;">file_content</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #4EC9B0;">bytes</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(31, 35, 40); --shiki-dark: #9CDCFE;">options</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #4EC9B0;">dict</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">) -&gt; </span><span style="color: rgb(5, 80, 174); --shiki-dark: #4EC9B0;">str</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">:</span></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">        # 构建请求参数</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">        params </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> {}</span></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">        for</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> key, value </span><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">in</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> options.items():</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">            params[key] </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(5, 80, 174); --shiki-dark: #4EC9B0;"> str</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">(value)</span></span>
<span class="line"></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">        # 设置请求头</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">        headers </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> {</span></span>
<span class="line"><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">            "x-ti-app-id"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #9cdcfe;">self</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">.app_id,</span></span>
<span class="line"><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">            "x-ti-secret-code"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #9cdcfe;">self</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">.secret_code,</span></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">            # 方式一：读取本地文件</span></span>
<span class="line"><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">            "Content-Type"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"application/octet-stream"</span></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">            # 方式二：使用URL方式</span></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">            # "Content-Type": "text/plain"</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">        }</span></span>
<span class="line"></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">        # 发送请求</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">        response </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> requests.post(</span></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">            f</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"https://api.textin.com/ai/service/v1/pdf_to_markdown"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span></span>
<span class="line"><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">            params</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">params,</span></span>
<span class="line"><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">            headers</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">headers,</span></span>
<span class="line"><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">            data</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">file_content</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">        )</span></span>
<span class="line"></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">        # 检查响应状态</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">        response.raise_for_status()</span></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">        return</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> response.text</span></span>
<span class="line"></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">def</span><span style="color: rgb(130, 80, 223); --shiki-dark: #DCDCAA;"> main</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">():</span></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">    # 创建客户端实例，需替换你的API Key</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">    client </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> OCRClient(</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"你的x-ti-app-id"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"你的x-ti-secret-code"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">)</span></span>
<span class="line"></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">	# 插入下面的示例代码</span></span>
<span class="line"></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">if</span><span style="color: rgb(5, 80, 174); --shiki-dark: #9CDCFE;"> __name__</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;"> ==</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;"> "__main__"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">:</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">    main()</span></span>
</code></pre></div></div></div>
<h3 class="flex whitespace-pre-wrap group font-semibold" id="%E8%A7%A3%E6%9E%90%E5%8D%95%E4%B8%AA%E6%9C%AC%E5%9C%B0%E6%96%87%E4%BB%B6%E5%B9%B6%E4%BF%9D%E5%AD%98%E7%BB%93%E6%9E%9C"><div class="absolute"><a href="https://docs.textin.com/xparse/parse-quickstart#%E8%A7%A3%E6%9E%90%E5%8D%95%E4%B8%AA%E6%9C%AC%E5%9C%B0%E6%96%87%E4%BB%B6%E5%B9%B6%E4%BF%9D%E5%AD%98%E7%BB%93%E6%9E%9C" class="-ml-10 flex items-center opacity-0 border-0 group-hover:opacity-100" aria-label="Navigate to header">​<div class="w-6 h-6 rounded-md flex items-center justify-center shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 bg-white ring-1 ring-gray-400/30 dark:ring-gray-700/25 hover:ring-gray-400/60 dark:hover:ring-white/20"><svg xmlns="http://www.w3.org/2000/svg" fill="gray" height="12px" viewBox="0 0 576 512"><path d="M0 256C0 167.6 71.6 96 160 96h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C98.1 144 48 194.1 48 256s50.1 112 112 112h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C71.6 416 0 344.4 0 256zm576 0c0 88.4-71.6 160-160 160H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c61.9 0 112-50.1 112-112s-50.1-112-112-112H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c88.4 0 160 71.6 160 160zM184 232H392c13.3 0 24 10.7 24 24s-10.7 24-24 24H184c-13.3 0-24-10.7-24-24s10.7-24 24-24z"></path></svg></div></a></div><span class="cursor-pointer">解析单个本地文件并保存结果</span></h3>
<span data-as="p">复制以下示例代码，粘贴至前置准备代码的main函数中；替换要解析的文件；运行脚本来解析本地目录中的文件并将结果作为markdown和JSON文件保存。</span>
<div class="callout my-4 px-5 py-4 overflow-hidden rounded-2xl flex gap-3 border border-emerald-500/20 bg-emerald-50/50 dark:border-emerald-500/30 dark:bg-emerald-500/10" data-callout-type="tip"><div class="mt-0.5 w-4" data-component-part="callout-icon"><svg width="11" height="14" viewBox="0 0 11 14" fill="currentColor" xmlns="http://www.w3.org/2000/svg" class="text-emerald-600 dark:text-emerald-400/80 w-3.5 h-auto" aria-label="Tip"><path d="M3.12794 12.4232C3.12794 12.5954 3.1776 12.7634 3.27244 12.907L3.74114 13.6095C3.88471 13.8248 4.21067 14 4.46964 14H6.15606C6.41415 14 6.74017 13.825 6.88373 13.6095L7.3508 12.9073C7.43114 12.7859 7.49705 12.569 7.49705 12.4232L7.50055 11.3513H3.12521L3.12794 12.4232ZM5.31288 0C2.52414 0.00875889 0.5 2.26889 0.5 4.78826C0.5 6.00188 0.949566 7.10829 1.69119 7.95492C2.14321 8.47011 2.84901 9.54727 3.11919 10.4557C3.12005 10.4625 3.12175 10.4698 3.12261 10.4771H7.50342C7.50427 10.4698 7.50598 10.463 7.50684 10.4557C7.77688 9.54727 8.48281 8.47011 8.93484 7.95492C9.67728 7.13181 10.1258 6.02703 10.1258 4.78826C10.1258 2.15486 7.9709 0.000106649 5.31288 0ZM7.94902 7.11267C7.52078 7.60079 6.99082 8.37878 6.6077 9.18794H4.02051C3.63739 8.37878 3.10743 7.60079 2.67947 7.11294C2.11997 6.47551 1.8126 5.63599 1.8126 4.78826C1.8126 3.09829 3.12794 1.31944 5.28827 1.3126C7.2435 1.3126 8.81315 2.88226 8.81315 4.78826C8.81315 5.63599 8.50688 6.47551 7.94902 7.11267ZM4.87534 2.18767C3.66939 2.18767 2.68767 3.16939 2.68767 4.37534C2.68767 4.61719 2.88336 4.81288 3.12521 4.81288C3.36705 4.81288 3.56274 4.61599 3.56274 4.37534C3.56274 3.6515 4.1515 3.06274 4.87534 3.06274C5.11719 3.06274 5.31288 2.86727 5.31288 2.62548C5.31288 2.38369 5.11599 2.18767 4.87534 2.18767Z"></path></svg></div><div class="text-sm prose min-w-0 w-full text-emerald-900 dark:text-emerald-200" data-component-part="callout-content"><span data-as="p">请注意：请求体的数据格式为本地文件的二进制流，非 FormData 或其他格式。文件大小不超过 500M，长宽比小于2的图片宽高需在20～20000像素范围内，其他图片的宽高需在20～10000像素范围内。</span><ul>
<li>支持的文件格式：png, jpg, jpeg, pdf, bmp, tiff, webp, doc, docx, html, mhtml, xls, xlsx, csv, ppt, pptx, txt, ofd, rtf</li>
<li>如果是xls/xlsx/csv文件，每个sheet行数不能超过2000，列数不能超过100。</li>
<li>如果是txt文件，文件大小不超过100k。</li>
</ul></div></div>
<div class="code-block mt-5 mb-8 not-prose rounded-2xl relative group text-gray-950 dark:text-gray-50 codeblock-light border border-gray-950/10 dark:border-white/10 bg-transparent dark:bg-transparent" numberoflines="32" language="python"><div class="absolute top-3 right-4 flex items-center gap-1.5"><div class="z-10 relative"><button class="h-[26px] w-[26px] flex items-center justify-center rounded-md backdrop-blur peer group/copy-button" data-testid="copy-code-button" aria-label="Copy the contents from the code block"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-400 group-hover/copy-button:text-gray-500 dark:text-white/40 dark:group-hover/copy-button:text-white/60"><path d="M14.25 5.25H7.25C6.14543 5.25 5.25 6.14543 5.25 7.25V14.25C5.25 15.3546 6.14543 16.25 7.25 16.25H14.25C15.3546 16.25 16.25 15.3546 16.25 14.25V7.25C16.25 6.14543 15.3546 5.25 14.25 5.25Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M2.80103 11.998L1.77203 5.07397C1.61003 3.98097 2.36403 2.96397 3.45603 2.80197L10.38 1.77297C11.313 1.63397 12.19 2.16297 12.528 3.00097" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></button><div aria-hidden="true" class="absolute top-11 left-1/2 transform whitespace-nowrap -translate-x-1/2 -translate-y-1/2 peer-hover:opacity-100 opacity-0 text-white rounded-lg px-1.5 py-0.5 text-xs bg-primary-dark">Copy</div></div><div class="z-10 relative"><button class="h-[26px] w-[26px] flex items-center justify-center rounded-md backdrop-blur peer group/ask-ai-button" id="ask-ai-code-block-button" aria-label="Ask AI"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-400 group-hover/ask-ai-button:text-gray-500 dark:text-white/40 dark:group-hover/ask-ai-button:text-white/60"><path d="M3.51169 1.50098L3.92087 2.72754L3.99997 2.96387L5.49997 3.46387L4.23825 3.88477L4.00095 3.96387L3.92184 4.20117L3.50095 5.46191L3.49997 5.46387H3.49899L2.99899 3.96387L1.49899 3.46387L2.99899 2.96387L3.07809 2.72754L3.4863 1.50098C3.49031 1.50045 3.49522 1.5 3.49997 1.5C3.50416 1.50002 3.50807 1.50054 3.51169 1.50098Z" stroke="currentColor"></path><path d="M9.5 2.75L11.412 7.587L16.25 9.5L11.412 11.413L9.5 16.25L7.587 11.413L2.75 9.5L7.587 7.587L9.5 2.75Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></button><div aria-hidden="true" class="absolute top-11 left-1/2 transform whitespace-nowrap -translate-x-1/2 -translate-y-1/2 peer-hover:opacity-100 opacity-0 text-white rounded-lg px-1.5 py-0.5 text-xs bg-primary-dark">Ask AI</div></div></div><div class="w-0 min-w-full max-w-full py-3.5 px-4 h-full dark:bg-codeblock relative text-sm leading-6 children:!my-0 children:!shadow-none children:!bg-transparent transition-[height] duration-300 ease-in-out [&amp;_*]:ring-0 [&amp;_*]:outline-none [&amp;_*]:focus:ring-0 [&amp;_*]:focus:outline-none [&amp;_pre&gt;code]:pr-[3rem] [&amp;_pre&gt;code&gt;span.line-highlight]:min-w-[calc(100%+3rem)] [&amp;_pre&gt;code&gt;span.line-diff]:min-w-[calc(100%+3rem)] rounded-2xl bg-white overflow-x-auto scrollbar-thin scrollbar-thumb-rounded scrollbar-thumb-black/15 hover:scrollbar-thumb-black/20 active:scrollbar-thumb-black/20 dark:scrollbar-thumb-white/20 dark:hover:scrollbar-thumb-white/25 dark:active:scrollbar-thumb-white/25" data-component-part="code-block-root" style="font-variant-ligatures: none; height: auto;"><div class="font-mono whitespace-pre leading-6"><pre class="shiki shiki-themes github-light-default dark-plus" language="python" style="background-color: transparent; --shiki-dark-bg: transparent; color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"><code language="python" numberoflines="32"><span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">    # 在main函数中插入</span></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">    # 读取本地文件</span></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">    with</span><span style="color: rgb(5, 80, 174); --shiki-dark: #DCDCAA;"> open</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">(</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"你的文件.pdf"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"rb"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">) </span><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">as</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> f:</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">        file_content </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> f.read()</span></span>
<span class="line"></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">    # 设置URL参数，可按需设置，这里已为你默认设置了一些参数</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">    options </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(5, 80, 174); --shiki-dark: #4EC9B0;"> dict</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">(</span></span>
<span class="line"><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">        dpi</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">144</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span></span>
<span class="line"><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">        get_image</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"objects"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span></span>
<span class="line"><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">        markdown_details</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">1</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span></span>
<span class="line"><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">        page_count</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">10</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span></span>
<span class="line"><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">        parse_mode</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"auto"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span></span>
<span class="line"><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">        table_flavor</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"html"</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">    )</span></span>
<span class="line"></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">    try</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">:</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">        response </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> client.recognize(file_content, options)</span></span>
<span class="line"></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">        # 保存完整的JSON响应到result.json文件</span></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">        with</span><span style="color: rgb(5, 80, 174); --shiki-dark: #DCDCAA;"> open</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">(</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"result.json"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"w"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">encoding</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"utf-8"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">) </span><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">as</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> f:</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">            f.write(response)</span></span>
<span class="line"></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">        # 解析JSON响应以提取markdown内容</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">        json_response </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> json.loads(response)</span></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">        if</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;"> "result"</span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;"> in</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> json_response </span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">and</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;"> "markdown"</span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;"> in</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> json_response[</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"result"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">]:</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">            markdown_content </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> json_response[</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"result"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">][</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"markdown"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">]</span></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">            with</span><span style="color: rgb(5, 80, 174); --shiki-dark: #DCDCAA;"> open</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">(</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"result.md"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"w"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">encoding</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"utf-8"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">) </span><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">as</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> f:</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">                f.write(markdown_content)</span></span>
<span class="line"></span>
<span class="line"><span style="color: rgb(5, 80, 174); --shiki-dark: #DCDCAA;">        print</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">(response)</span></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">    except</span><span style="color: rgb(5, 80, 174); --shiki-dark: #4EC9B0;"> Exception</span><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;"> as</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> e:</span></span>
<span class="line"><span style="color: rgb(5, 80, 174); --shiki-dark: #DCDCAA;">        print</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">(</span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">f</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"Error: </span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">{</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">e</span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">}</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">)</span></span>
</code></pre></div></div></div>
<h3 class="flex whitespace-pre-wrap group font-semibold" id="%E8%A7%A3%E6%9E%90%E5%A4%9A%E4%B8%AA%E6%9C%AC%E5%9C%B0%E6%96%87%E4%BB%B6%E5%B9%B6%E4%BF%9D%E5%AD%98%E7%BB%93%E6%9E%9C%E8%87%B3%E6%8C%87%E5%AE%9A%E7%9B%AE%E5%BD%95"><div class="absolute"><a href="https://docs.textin.com/xparse/parse-quickstart#%E8%A7%A3%E6%9E%90%E5%A4%9A%E4%B8%AA%E6%9C%AC%E5%9C%B0%E6%96%87%E4%BB%B6%E5%B9%B6%E4%BF%9D%E5%AD%98%E7%BB%93%E6%9E%9C%E8%87%B3%E6%8C%87%E5%AE%9A%E7%9B%AE%E5%BD%95" class="-ml-10 flex items-center opacity-0 border-0 group-hover:opacity-100" aria-label="Navigate to header">​<div class="w-6 h-6 rounded-md flex items-center justify-center shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 bg-white ring-1 ring-gray-400/30 dark:ring-gray-700/25 hover:ring-gray-400/60 dark:hover:ring-white/20"><svg xmlns="http://www.w3.org/2000/svg" fill="gray" height="12px" viewBox="0 0 576 512"><path d="M0 256C0 167.6 71.6 96 160 96h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C98.1 144 48 194.1 48 256s50.1 112 112 112h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C71.6 416 0 344.4 0 256zm576 0c0 88.4-71.6 160-160 160H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c61.9 0 112-50.1 112-112s-50.1-112-112-112H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c88.4 0 160 71.6 160 160zM184 232H392c13.3 0 24 10.7 24 24s-10.7 24-24 24H184c-13.3 0-24-10.7-24-24s10.7-24 24-24z"></path></svg></div></a></div><span class="cursor-pointer">解析多个本地文件并保存结果至指定目录</span></h3>
<span data-as="p">复制以下示例代码，粘贴至前置准备代码的main函数中；替换要解析的文件夹和输出结果文件夹；运行脚本来解析本地目录中的多个文件并将结果作为markdown和JSON文件保存至指定目录中。</span>
<div class="code-block mt-5 mb-8 not-prose rounded-2xl relative group text-gray-950 dark:text-gray-50 codeblock-light border border-gray-950/10 dark:border-white/10 bg-transparent dark:bg-transparent" numberoflines="41" language="python"><div class="absolute top-3 right-4 flex items-center gap-1.5"><div class="z-10 relative"><button class="h-[26px] w-[26px] flex items-center justify-center rounded-md backdrop-blur peer group/copy-button" data-testid="copy-code-button" aria-label="Copy the contents from the code block"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-400 group-hover/copy-button:text-gray-500 dark:text-white/40 dark:group-hover/copy-button:text-white/60"><path d="M14.25 5.25H7.25C6.14543 5.25 5.25 6.14543 5.25 7.25V14.25C5.25 15.3546 6.14543 16.25 7.25 16.25H14.25C15.3546 16.25 16.25 15.3546 16.25 14.25V7.25C16.25 6.14543 15.3546 5.25 14.25 5.25Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M2.80103 11.998L1.77203 5.07397C1.61003 3.98097 2.36403 2.96397 3.45603 2.80197L10.38 1.77297C11.313 1.63397 12.19 2.16297 12.528 3.00097" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></button><div aria-hidden="true" class="absolute top-11 left-1/2 transform whitespace-nowrap -translate-x-1/2 -translate-y-1/2 peer-hover:opacity-100 opacity-0 text-white rounded-lg px-1.5 py-0.5 text-xs bg-primary-dark">Copy</div></div><div class="z-10 relative"><button class="h-[26px] w-[26px] flex items-center justify-center rounded-md backdrop-blur peer group/ask-ai-button" id="ask-ai-code-block-button" aria-label="Ask AI"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-400 group-hover/ask-ai-button:text-gray-500 dark:text-white/40 dark:group-hover/ask-ai-button:text-white/60"><path d="M3.51169 1.50098L3.92087 2.72754L3.99997 2.96387L5.49997 3.46387L4.23825 3.88477L4.00095 3.96387L3.92184 4.20117L3.50095 5.46191L3.49997 5.46387H3.49899L2.99899 3.96387L1.49899 3.46387L2.99899 2.96387L3.07809 2.72754L3.4863 1.50098C3.49031 1.50045 3.49522 1.5 3.49997 1.5C3.50416 1.50002 3.50807 1.50054 3.51169 1.50098Z" stroke="currentColor"></path><path d="M9.5 2.75L11.412 7.587L16.25 9.5L11.412 11.413L9.5 16.25L7.587 11.413L2.75 9.5L7.587 7.587L9.5 2.75Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></button><div aria-hidden="true" class="absolute top-11 left-1/2 transform whitespace-nowrap -translate-x-1/2 -translate-y-1/2 peer-hover:opacity-100 opacity-0 text-white rounded-lg px-1.5 py-0.5 text-xs bg-primary-dark">Ask AI</div></div></div><div class="w-0 min-w-full max-w-full py-3.5 px-4 h-full dark:bg-codeblock relative text-sm leading-6 children:!my-0 children:!shadow-none children:!bg-transparent transition-[height] duration-300 ease-in-out [&amp;_*]:ring-0 [&amp;_*]:outline-none [&amp;_*]:focus:ring-0 [&amp;_*]:focus:outline-none [&amp;_pre&gt;code]:pr-[3rem] [&amp;_pre&gt;code&gt;span.line-highlight]:min-w-[calc(100%+3rem)] [&amp;_pre&gt;code&gt;span.line-diff]:min-w-[calc(100%+3rem)] rounded-2xl bg-white overflow-x-auto scrollbar-thin scrollbar-thumb-rounded scrollbar-thumb-black/15 hover:scrollbar-thumb-black/20 active:scrollbar-thumb-black/20 dark:scrollbar-thumb-white/20 dark:hover:scrollbar-thumb-white/25 dark:active:scrollbar-thumb-white/25" data-component-part="code-block-root" style="font-variant-ligatures: none; height: auto;"><div class="font-mono whitespace-pre leading-6"><pre class="shiki shiki-themes github-light-default dark-plus" language="python" style="background-color: transparent; --shiki-dark-bg: transparent; color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"><code language="python" numberoflines="41"><span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;"> 	# 在main函数中插入</span></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">    # 读取本地文件夹</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">    input_dir </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;"> "./tmp"</span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">  # 你可以修改为自己的文件夹</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">    output_dir </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;"> "./output"</span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">  # 输出结果的文件夹</span></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">	import</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> os</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">    os.makedirs(output_dir, </span><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">exist_ok</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(5, 80, 174); --shiki-dark: #9cdcfe;">True</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">)</span></span>
<span class="line"></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">    # 支持的文件类型</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">    exts </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> (</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">".pdf"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">".png"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">".jpg"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">".jpeg"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">".bmp"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">".tiff"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">".webp"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">".doc"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">".docx"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">".html"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">".mhtml"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">".xls"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">".xlsx"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">".csv"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">".ppt"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">".pptx"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">".txt"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">".ofd"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">".rtf"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">)</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">    files </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> [f </span><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">for</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> f </span><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">in</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> os.listdir(input_dir) </span><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">if</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> f.lower().endswith(exts)]</span></span>
<span class="line"></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">    # 设置URL参数，可按需设置，这里已为你默认设置了一些参数</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">    options </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(5, 80, 174); --shiki-dark: #4EC9B0;"> dict</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">(</span></span>
<span class="line"><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">        dpi</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">144</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span></span>
<span class="line"><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">        get_image</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"objects"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span></span>
<span class="line"><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">        markdown_details</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">1</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span></span>
<span class="line"><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">        page_count</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">10</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span></span>
<span class="line"><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">        parse_mode</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"auto"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span></span>
<span class="line"><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">        table_flavor</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"html"</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">    )</span></span>
<span class="line"></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">    #循环处理</span></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">    for</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> filename </span><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">in</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> files:</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">        file_path </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> os.path.join(input_dir, filename)</span></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">        with</span><span style="color: rgb(5, 80, 174); --shiki-dark: #DCDCAA;"> open</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">(file_path, </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"rb"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">) </span><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">as</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> f:</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">            file_content </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> f.read()</span></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">        try</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">:</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">            response </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> client.recognize(file_content, options)</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">            base_name </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> os.path.splitext(filename)[</span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">0</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">]</span></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">            # 保存JSON</span></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">            with</span><span style="color: rgb(5, 80, 174); --shiki-dark: #DCDCAA;"> open</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">(os.path.join(output_dir, </span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">f</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"</span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">{</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">base_name</span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">}</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">.json"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">), </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"w"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">encoding</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"utf-8"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">) </span><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">as</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> fw:</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">                fw.write(response)</span></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">            # 保存Markdown</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">            json_response </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> json.loads(response)</span></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">            if</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;"> "result"</span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;"> in</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> json_response </span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">and</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;"> "markdown"</span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;"> in</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> json_response[</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"result"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">]:</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">                markdown_content </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> json_response[</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"result"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">][</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"markdown"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">]</span></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">                with</span><span style="color: rgb(5, 80, 174); --shiki-dark: #DCDCAA;"> open</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">(os.path.join(output_dir, </span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">f</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"</span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">{</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">base_name</span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">}</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">.md"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">), </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"w"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">encoding</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"utf-8"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">) </span><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">as</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> fw:</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">                    fw.write(markdown_content)</span></span>
<span class="line"><span style="color: rgb(5, 80, 174); --shiki-dark: #DCDCAA;">            print</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">(</span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">f</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"</span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">{</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">filename</span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">}</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;"> 处理完成"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">)</span></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">        except</span><span style="color: rgb(5, 80, 174); --shiki-dark: #4EC9B0;"> Exception</span><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;"> as</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> e:</span></span>
<span class="line"><span style="color: rgb(5, 80, 174); --shiki-dark: #DCDCAA;">            print</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">(</span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">f</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"</span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">{</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">filename</span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">}</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;"> 处理出错: </span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">{</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">e</span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">}</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">)</span></span>
</code></pre></div></div></div>
<h3 class="flex whitespace-pre-wrap group font-semibold" id="%E8%A7%A3%E6%9E%90%E4%BD%8D%E4%BA%8Eurl%E7%9A%84%E6%96%87%E4%BB%B6%E5%B9%B6%E4%BF%9D%E5%AD%98%E7%BB%93%E6%9E%9C"><div class="absolute"><a href="https://docs.textin.com/xparse/parse-quickstart#%E8%A7%A3%E6%9E%90%E4%BD%8D%E4%BA%8Eurl%E7%9A%84%E6%96%87%E4%BB%B6%E5%B9%B6%E4%BF%9D%E5%AD%98%E7%BB%93%E6%9E%9C" class="-ml-10 flex items-center opacity-0 border-0 group-hover:opacity-100" aria-label="Navigate to header">​<div class="w-6 h-6 rounded-md flex items-center justify-center shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 bg-white ring-1 ring-gray-400/30 dark:ring-gray-700/25 hover:ring-gray-400/60 dark:hover:ring-white/20"><svg xmlns="http://www.w3.org/2000/svg" fill="gray" height="12px" viewBox="0 0 576 512"><path d="M0 256C0 167.6 71.6 96 160 96h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C98.1 144 48 194.1 48 256s50.1 112 112 112h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C71.6 416 0 344.4 0 256zm576 0c0 88.4-71.6 160-160 160H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c61.9 0 112-50.1 112-112s-50.1-112-112-112H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c88.4 0 160 71.6 160 160zM184 232H392c13.3 0 24 10.7 24 24s-10.7 24-24 24H184c-13.3 0-24-10.7-24-24s10.7-24 24-24z"></path></svg></div></a></div><span class="cursor-pointer">解析位于URL的文件并保存结果</span></h3>
<span data-as="p">您可以参考以下步骤，解析位于URL的文件。</span>
<div class="callout my-4 px-5 py-4 overflow-hidden rounded-2xl flex gap-3 border border-emerald-500/20 bg-emerald-50/50 dark:border-emerald-500/30 dark:bg-emerald-500/10" data-callout-type="tip"><div class="mt-0.5 w-4" data-component-part="callout-icon"><svg width="11" height="14" viewBox="0 0 11 14" fill="currentColor" xmlns="http://www.w3.org/2000/svg" class="text-emerald-600 dark:text-emerald-400/80 w-3.5 h-auto" aria-label="Tip"><path d="M3.12794 12.4232C3.12794 12.5954 3.1776 12.7634 3.27244 12.907L3.74114 13.6095C3.88471 13.8248 4.21067 14 4.46964 14H6.15606C6.41415 14 6.74017 13.825 6.88373 13.6095L7.3508 12.9073C7.43114 12.7859 7.49705 12.569 7.49705 12.4232L7.50055 11.3513H3.12521L3.12794 12.4232ZM5.31288 0C2.52414 0.00875889 0.5 2.26889 0.5 4.78826C0.5 6.00188 0.949566 7.10829 1.69119 7.95492C2.14321 8.47011 2.84901 9.54727 3.11919 10.4557C3.12005 10.4625 3.12175 10.4698 3.12261 10.4771H7.50342C7.50427 10.4698 7.50598 10.463 7.50684 10.4557C7.77688 9.54727 8.48281 8.47011 8.93484 7.95492C9.67728 7.13181 10.1258 6.02703 10.1258 4.78826C10.1258 2.15486 7.9709 0.000106649 5.31288 0ZM7.94902 7.11267C7.52078 7.60079 6.99082 8.37878 6.6077 9.18794H4.02051C3.63739 8.37878 3.10743 7.60079 2.67947 7.11294C2.11997 6.47551 1.8126 5.63599 1.8126 4.78826C1.8126 3.09829 3.12794 1.31944 5.28827 1.3126C7.2435 1.3126 8.81315 2.88226 8.81315 4.78826C8.81315 5.63599 8.50688 6.47551 7.94902 7.11267ZM4.87534 2.18767C3.66939 2.18767 2.68767 3.16939 2.68767 4.37534C2.68767 4.61719 2.88336 4.81288 3.12521 4.81288C3.36705 4.81288 3.56274 4.61599 3.56274 4.37534C3.56274 3.6515 4.1515 3.06274 4.87534 3.06274C5.11719 3.06274 5.31288 2.86727 5.31288 2.62548C5.31288 2.38369 5.11599 2.18767 4.87534 2.18767Z"></path></svg></div><div class="text-sm prose min-w-0 w-full text-emerald-900 dark:text-emerald-200" data-component-part="callout-content"><span data-as="p">请注意：请求体的数据格式为文本，内容为在线文件的URL链接（支持http以及https协议）。在线文件大小不超过 500M，长宽比小于2的图片宽高需在20～20000像素范围内，其他图片的宽高需在20～10000像素范围内。为了快速验证接入，这里为您提供了示例文件URL。</span></div></div>
<ul>
<li>步骤一：在前置准备代码中修改请求头设置。</li>
</ul>
<div class="code-block mt-5 mb-8 not-prose rounded-2xl relative group text-gray-950 dark:text-gray-50 codeblock-light border border-gray-950/10 dark:border-white/10 bg-transparent dark:bg-transparent" numberoflines="7" language="python"><div class="absolute top-3 right-4 flex items-center gap-1.5"><div class="z-10 relative"><button class="h-[26px] w-[26px] flex items-center justify-center rounded-md backdrop-blur peer group/copy-button" data-testid="copy-code-button" aria-label="Copy the contents from the code block"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-400 group-hover/copy-button:text-gray-500 dark:text-white/40 dark:group-hover/copy-button:text-white/60"><path d="M14.25 5.25H7.25C6.14543 5.25 5.25 6.14543 5.25 7.25V14.25C5.25 15.3546 6.14543 16.25 7.25 16.25H14.25C15.3546 16.25 16.25 15.3546 16.25 14.25V7.25C16.25 6.14543 15.3546 5.25 14.25 5.25Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M2.80103 11.998L1.77203 5.07397C1.61003 3.98097 2.36403 2.96397 3.45603 2.80197L10.38 1.77297C11.313 1.63397 12.19 2.16297 12.528 3.00097" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></button><div aria-hidden="true" class="absolute top-11 left-1/2 transform whitespace-nowrap -translate-x-1/2 -translate-y-1/2 peer-hover:opacity-100 opacity-0 text-white rounded-lg px-1.5 py-0.5 text-xs bg-primary-dark">Copy</div></div><div class="z-10 relative"><button class="h-[26px] w-[26px] flex items-center justify-center rounded-md backdrop-blur peer group/ask-ai-button" id="ask-ai-code-block-button" aria-label="Ask AI"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-400 group-hover/ask-ai-button:text-gray-500 dark:text-white/40 dark:group-hover/ask-ai-button:text-white/60"><path d="M3.51169 1.50098L3.92087 2.72754L3.99997 2.96387L5.49997 3.46387L4.23825 3.88477L4.00095 3.96387L3.92184 4.20117L3.50095 5.46191L3.49997 5.46387H3.49899L2.99899 3.96387L1.49899 3.46387L2.99899 2.96387L3.07809 2.72754L3.4863 1.50098C3.49031 1.50045 3.49522 1.5 3.49997 1.5C3.50416 1.50002 3.50807 1.50054 3.51169 1.50098Z" stroke="currentColor"></path><path d="M9.5 2.75L11.412 7.587L16.25 9.5L11.412 11.413L9.5 16.25L7.587 11.413L2.75 9.5L7.587 7.587L9.5 2.75Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></button><div aria-hidden="true" class="absolute top-11 left-1/2 transform whitespace-nowrap -translate-x-1/2 -translate-y-1/2 peer-hover:opacity-100 opacity-0 text-white rounded-lg px-1.5 py-0.5 text-xs bg-primary-dark">Ask AI</div></div></div><div class="w-0 min-w-full max-w-full py-3.5 px-4 h-full dark:bg-codeblock relative text-sm leading-6 children:!my-0 children:!shadow-none children:!bg-transparent transition-[height] duration-300 ease-in-out [&amp;_*]:ring-0 [&amp;_*]:outline-none [&amp;_*]:focus:ring-0 [&amp;_*]:focus:outline-none [&amp;_pre&gt;code]:pr-[3rem] [&amp;_pre&gt;code&gt;span.line-highlight]:min-w-[calc(100%+3rem)] [&amp;_pre&gt;code&gt;span.line-diff]:min-w-[calc(100%+3rem)] rounded-2xl bg-white overflow-x-auto scrollbar-thin scrollbar-thumb-rounded scrollbar-thumb-black/15 hover:scrollbar-thumb-black/20 active:scrollbar-thumb-black/20 dark:scrollbar-thumb-white/20 dark:hover:scrollbar-thumb-white/25 dark:active:scrollbar-thumb-white/25" data-component-part="code-block-root" style="font-variant-ligatures: none; height: auto;"><div class="font-mono whitespace-pre leading-6"><pre class="shiki shiki-themes github-light-default dark-plus" language="python" style="background-color: transparent; --shiki-dark-bg: transparent; color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"><code language="python" numberoflines="7"><span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">        # 在前置准备中修改请求头设置</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">        headers </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> {</span></span>
<span class="line"><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">            "x-ti-app-id"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #9cdcfe;">self</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">.app_id,</span></span>
<span class="line"><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">            "x-ti-secret-code"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #9cdcfe;">self</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">.secret_code,</span></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">            # 方式：使用URL方式</span></span>
<span class="line"><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">            "Content-Type"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"text/plain"</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">        }</span></span>
</code></pre></div></div></div>
<ul>
<li>步骤二：复制以下示例代码，粘贴至前置准备代码的main函数中；替换要解析的文件URL或直接使用示例URL；运行脚本来解析位于URL的文件并将结果作为markdown和JSON文件保存。</li>
</ul>
<div class="code-block mt-5 mb-8 not-prose rounded-2xl relative group text-gray-950 dark:text-gray-50 codeblock-light border border-gray-950/10 dark:border-white/10 bg-transparent dark:bg-transparent" numberoflines="31" language="python"><div class="absolute top-3 right-4 flex items-center gap-1.5"><div class="z-10 relative"><button class="h-[26px] w-[26px] flex items-center justify-center rounded-md backdrop-blur peer group/copy-button" data-testid="copy-code-button" aria-label="Copy the contents from the code block"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-400 group-hover/copy-button:text-gray-500 dark:text-white/40 dark:group-hover/copy-button:text-white/60"><path d="M14.25 5.25H7.25C6.14543 5.25 5.25 6.14543 5.25 7.25V14.25C5.25 15.3546 6.14543 16.25 7.25 16.25H14.25C15.3546 16.25 16.25 15.3546 16.25 14.25V7.25C16.25 6.14543 15.3546 5.25 14.25 5.25Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M2.80103 11.998L1.77203 5.07397C1.61003 3.98097 2.36403 2.96397 3.45603 2.80197L10.38 1.77297C11.313 1.63397 12.19 2.16297 12.528 3.00097" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></button><div aria-hidden="true" class="absolute top-11 left-1/2 transform whitespace-nowrap -translate-x-1/2 -translate-y-1/2 peer-hover:opacity-100 opacity-0 text-white rounded-lg px-1.5 py-0.5 text-xs bg-primary-dark">Copy</div></div><div class="z-10 relative"><button class="h-[26px] w-[26px] flex items-center justify-center rounded-md backdrop-blur peer group/ask-ai-button" id="ask-ai-code-block-button" aria-label="Ask AI"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-400 group-hover/ask-ai-button:text-gray-500 dark:text-white/40 dark:group-hover/ask-ai-button:text-white/60"><path d="M3.51169 1.50098L3.92087 2.72754L3.99997 2.96387L5.49997 3.46387L4.23825 3.88477L4.00095 3.96387L3.92184 4.20117L3.50095 5.46191L3.49997 5.46387H3.49899L2.99899 3.96387L1.49899 3.46387L2.99899 2.96387L3.07809 2.72754L3.4863 1.50098C3.49031 1.50045 3.49522 1.5 3.49997 1.5C3.50416 1.50002 3.50807 1.50054 3.51169 1.50098Z" stroke="currentColor"></path><path d="M9.5 2.75L11.412 7.587L16.25 9.5L11.412 11.413L9.5 16.25L7.587 11.413L2.75 9.5L7.587 7.587L9.5 2.75Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></button><div aria-hidden="true" class="absolute top-11 left-1/2 transform whitespace-nowrap -translate-x-1/2 -translate-y-1/2 peer-hover:opacity-100 opacity-0 text-white rounded-lg px-1.5 py-0.5 text-xs bg-primary-dark">Ask AI</div></div></div><div class="w-0 min-w-full max-w-full py-3.5 px-4 h-full dark:bg-codeblock relative text-sm leading-6 children:!my-0 children:!shadow-none children:!bg-transparent transition-[height] duration-300 ease-in-out [&amp;_*]:ring-0 [&amp;_*]:outline-none [&amp;_*]:focus:ring-0 [&amp;_*]:focus:outline-none [&amp;_pre&gt;code]:pr-[3rem] [&amp;_pre&gt;code&gt;span.line-highlight]:min-w-[calc(100%+3rem)] [&amp;_pre&gt;code&gt;span.line-diff]:min-w-[calc(100%+3rem)] rounded-2xl bg-white overflow-x-auto scrollbar-thin scrollbar-thumb-rounded scrollbar-thumb-black/15 hover:scrollbar-thumb-black/20 active:scrollbar-thumb-black/20 dark:scrollbar-thumb-white/20 dark:hover:scrollbar-thumb-white/25 dark:active:scrollbar-thumb-white/25" data-component-part="code-block-root" style="font-variant-ligatures: none; height: auto;"><div class="font-mono whitespace-pre leading-6"><pre class="shiki shiki-themes github-light-default dark-plus" language="python" style="background-color: transparent; --shiki-dark-bg: transparent; color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"><code language="python" numberoflines="31"><span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">    # 在main函数中插入</span></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">    # 文件URL，这里为你提供了一份真实可用的示例URL</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">    file_content </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;"> "https://dllf.intsig.net/download/2025/Solution/textin/sample/pdf_to_markdown/sample_02.pdf"</span></span>
<span class="line"></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">    # 设置URL参数，可按需设置，这里已为你默认设置了一些参数</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">    options </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(5, 80, 174); --shiki-dark: #4EC9B0;"> dict</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">(</span></span>
<span class="line"><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">        dpi</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">144</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span></span>
<span class="line"><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">        get_image</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"objects"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span></span>
<span class="line"><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">        markdown_details</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">1</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span></span>
<span class="line"><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">        page_count</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">10</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span></span>
<span class="line"><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">        parse_mode</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"auto"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">,</span></span>
<span class="line"><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">        table_flavor</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"html"</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">    )</span></span>
<span class="line"></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">    try</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">:</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">        response </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> client.recognize(file_content, options)</span></span>
<span class="line"></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">        # 保存完整的JSON响应到result.json文件</span></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">        with</span><span style="color: rgb(5, 80, 174); --shiki-dark: #DCDCAA;"> open</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">(</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"result.json"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"w"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">encoding</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"utf-8"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">) </span><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">as</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> f:</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">            f.write(response)</span></span>
<span class="line"></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">        # 解析JSON响应以提取markdown内容</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">        json_response </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> json.loads(response)</span></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">        if</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;"> "result"</span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;"> in</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> json_response </span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">and</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;"> "markdown"</span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;"> in</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> json_response[</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"result"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">]:</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">            markdown_content </span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> json_response[</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"result"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">][</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"markdown"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">]</span></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">            with</span><span style="color: rgb(5, 80, 174); --shiki-dark: #DCDCAA;"> open</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">(</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"result.md"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"w"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(149, 56, 0); --shiki-dark: #9CDCFE;">encoding</span><span style="color: rgb(207, 34, 46); --shiki-dark: #f3f7f6;">=</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"utf-8"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">) </span><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">as</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> f:</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">                f.write(markdown_content)</span></span>
<span class="line"></span>
<span class="line"><span style="color: rgb(5, 80, 174); --shiki-dark: #DCDCAA;">        print</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">(response)</span></span>
<span class="line"><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;">    except</span><span style="color: rgb(5, 80, 174); --shiki-dark: #4EC9B0;"> Exception</span><span style="color: rgb(207, 34, 46); --shiki-dark: #C586C0;"> as</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"> e:</span></span>
<span class="line"><span style="color: rgb(5, 80, 174); --shiki-dark: #DCDCAA;">        print</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">(</span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">f</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"Error: </span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">{</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">e</span><span style="color: rgb(207, 34, 46); --shiki-dark: #9cdcfe;">}</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">)</span></span>
</code></pre></div></div></div>
<h3 class="flex whitespace-pre-wrap group font-semibold" id="url%E5%8F%82%E6%95%B0%E8%AF%B4%E6%98%8E"><div class="absolute"><a href="https://docs.textin.com/xparse/parse-quickstart#url%E5%8F%82%E6%95%B0%E8%AF%B4%E6%98%8E" class="-ml-10 flex items-center opacity-0 border-0 group-hover:opacity-100" aria-label="Navigate to header">​<div class="w-6 h-6 rounded-md flex items-center justify-center shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 bg-white ring-1 ring-gray-400/30 dark:ring-gray-700/25 hover:ring-gray-400/60 dark:hover:ring-white/20"><svg xmlns="http://www.w3.org/2000/svg" fill="gray" height="12px" viewBox="0 0 576 512"><path d="M0 256C0 167.6 71.6 96 160 96h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C98.1 144 48 194.1 48 256s50.1 112 112 112h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C71.6 416 0 344.4 0 256zm576 0c0 88.4-71.6 160-160 160H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c61.9 0 112-50.1 112-112s-50.1-112-112-112H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c88.4 0 160 71.6 160 160zM184 232H392c13.3 0 24 10.7 24 24s-10.7 24-24 24H184c-13.3 0-24-10.7-24-24s10.7-24 24-24z"></path></svg></div></a></div><span class="cursor-pointer">URL参数说明</span></h3>
<span data-as="p">以下是文档解析API的URL参数，URL参数指以 参数名=参数值 形式拼接到 URL 上的键值对。它以 <code>?</code> 开头，不同参数之间使用 <code>&amp;</code> 连接，形如 <code>?p1=v1&amp;p2=v2</code>。URL参数会影响文档的解析结果和JSON输出内容，您可按需进行设置。</span>
<ul>
<li><strong>pdf_pwd</strong>：当pdf为加密文档时，需要提供密码。
<ul>
<li>备注：对前端封装该接口参数时，需要自行对密码进行安全防护。</li>
</ul>
</li>
<li><strong>page_start</strong>：当上传的是pdf时，表示从第几页开始解析，不传该参数时默认从首页开始。</li>
<li><strong>page_count</strong>：当上传的是pdf时，表示要进行解析的pdf页数。总页数不得超过1000页，默认为1000页。</li>
<li><strong>parse_mode</strong>：pdf文档的解析模式，默认为scan模式。图片不用设置，均默认按scan模式处理。
<ul>
<li>auto 综合文字识别和解析模式：对pdf电子档解析，会直接提取pdf中的文字</li>
<li>scan 仅按文字识别模式：将pdf当成图片处理</li>
</ul>
</li>
<li><strong>dpi</strong>：pdf文档的坐标基准，默认144 dpi。 与<strong>parse_mode</strong>参数联动
<ul>
<li>当parse_mode=auto时，默认动态，支持72，144，216；</li>
<li>当parse_mode=scan时，默认144，支持72，144，216。</li>
</ul>
</li>
<li><strong>apply_document_tree</strong>：<strong>markdown</strong>中是否生成标题层级，默认为1，生成标题。
<ul>
<li>0 不生成标题：同时也不会返回<strong>catalog</strong>字段</li>
<li>1 生成标题</li>
</ul>
</li>
<li><strong>table_flavor</strong>：<strong>markdown</strong>里的表格格式，默认为html，按html语法输出表格。
<ul>
<li>md 按md语法输出表格</li>
<li>html 按html语法输出表格</li>
<li>none 不进行表格识别，把表格图像当成普通文字段落来识别。</li>
</ul>
</li>
<li><strong>get_image</strong>：获取<strong>markdown</strong>里的图片，默认为none，不返回任何图像。
<ul>
<li>none 不返回任何图像</li>
<li>page 返回每一页的整页图像：即pdf页的完整页图片</li>
<li>objects 返回页面内的子图像：即pdf页内的各个子图片</li>
<li>both 返回整页图像和图像对象</li>
</ul>
</li>
<li><strong>image_output_type</strong>：指定返回的图片对象输出类型，默认返回子图片url和页图片id。
<ul>
<li>base64str 指定所有图片对象为base64字符串，适用于没有云存储的用户，但是引擎返回结果体积会很大。</li>
<li>default 指定子图片对象为图片url，页图片对象为图片id</li>
</ul>
</li>
<li><strong>paratext_mode</strong>：<strong>markdown</strong>中非正文文本内容展示模式。默认为annotation。非正文内容包括页眉页脚、子图中的文本。
<ul>
<li>none 不展示</li>
<li>annotation 以注释格式插入到markdown中。页眉页脚中的图片只保留文本，图片base64或url不保留</li>
<li>body 以正文格式插入到markdown中</li>
</ul>
</li>
<li><strong>formula_level</strong>：公式识别等级，默认为0，全识别。
<ul>
<li>0 行间公式和行内公式都识别</li>
<li>1 仅识别行间公式，行内公式不识别</li>
<li>2 不识别公式</li>
</ul>
</li>
<li><strong>apply_merge</strong>：是否进行段落合并和表格合并。默认为1，合并段落和表格。
<ul>
<li>0 不合并</li>
<li>1 合并</li>
</ul>
</li>
<li><strong>markdown_details</strong>：是否返回结果中的<strong>detail</strong>字段。默认为1，返回detail字段，保存markdown各类型元素的详细信息。
<ul>
<li>0 不返回</li>
<li>1 返回</li>
</ul>
</li>
<li><strong>page_details</strong>：是否返回结果中的<strong>pages</strong>字段。默认为1，返回pages字段，保存每一页更加详细的解析结果。
<ul>
<li>0 不返回</li>
<li>1 返回</li>
</ul>
</li>
<li><strong>raw_ocr</strong>：是否返回全部文字识别结果(包含字符坐标信息)，结果字段为<strong>raw_ocr</strong>。默认为0，不返回。与<strong>page_details</strong>参数联动，当page_details为0或false时不返回。
<ul>
<li>0 不返回</li>
<li>1 返回</li>
</ul>
</li>
<li><strong>char_details</strong>：是否返回结果中的<strong>char_pos</strong>字段（保存每个字符的位置信息）和<strong>raw_ocr</strong>中的<strong>char_</strong>相关字段。默认为0，不返回。
<ul>
<li>0 不返回</li>
<li>1 返回</li>
</ul>
</li>
<li><strong>catalog_details</strong>：是否返回结果中的<strong>catalog</strong>字段，保存目录相关信息。与<strong>apply_document_tree</strong>参数联动，当apply_document_tree为0时不返回。
<ul>
<li>0 不返回</li>
<li>1 返回</li>
</ul>
</li>
<li><strong>get_excel</strong>：是否返回excel的base64结果，结果字段为<strong>excel_base64</strong>，可以根据该字段进行后处理保存excel文件。默认为0，不返回。
<ul>
<li>0 不返回</li>
<li>1 返回</li>
</ul>
</li>
<li><strong>crop_image</strong>（<strong>切边矫正</strong>）：是否进行切边矫正预处理，默认为0，不进行切边矫正。
<ul>
<li>0 不进行切边矫正</li>
<li>1 进行切边矫正</li>
</ul>
</li>
<li><strong>remove_watermark</strong>（<strong>去水印</strong>）：是否进行去水印预处理，默认为0，不去水印。
<ul>
<li>0 不去水印</li>
<li>1 去水印</li>
</ul>
</li>
<li><strong>apply_chart</strong>（<strong>图表识别</strong>）：是否开启图表识别，开启图表识别会将识别到的图表以表格形式输出。默认为0，不进行图表识别。
<ul>
<li>0 不开启图表识别</li>
<li>1 开启图表识别</li>
</ul>
</li>
</ul>
<h3 class="flex whitespace-pre-wrap group font-semibold" id="%E8%BF%94%E5%9B%9E%E7%BB%93%E6%9E%9C%E7%A4%BA%E4%BE%8B"><div class="absolute"><a href="https://docs.textin.com/xparse/parse-quickstart#%E8%BF%94%E5%9B%9E%E7%BB%93%E6%9E%9C%E7%A4%BA%E4%BE%8B" class="-ml-10 flex items-center opacity-0 border-0 group-hover:opacity-100" aria-label="Navigate to header">​<div class="w-6 h-6 rounded-md flex items-center justify-center shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 bg-white ring-1 ring-gray-400/30 dark:ring-gray-700/25 hover:ring-gray-400/60 dark:hover:ring-white/20"><svg xmlns="http://www.w3.org/2000/svg" fill="gray" height="12px" viewBox="0 0 576 512"><path d="M0 256C0 167.6 71.6 96 160 96h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C98.1 144 48 194.1 48 256s50.1 112 112 112h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C71.6 416 0 344.4 0 256zm576 0c0 88.4-71.6 160-160 160H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c61.9 0 112-50.1 112-112s-50.1-112-112-112H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c88.4 0 160 71.6 160 160zM184 232H392c13.3 0 24 10.7 24 24s-10.7 24-24 24H184c-13.3 0-24-10.7-24-24s10.7-24 24-24z"></path></svg></div></a></div><span class="cursor-pointer">返回结果示例</span></h3>
<span data-as="p">解析后的结果数据将按照以下JSON格式返回，下面为您提供了一段节选的返回示例。如果您想了解最全面的返回结果说明，可以在<a class="link" href="https://docs.textin.com/xparse/parse-getjson">返回JSON结构说明</a>中查看，也可以在<a class="link" href="https://docs.textin.com/api-reference/endpoint/parse">API</a>中查看和调试。</span>
<div class="code-block mt-5 mb-8 not-prose rounded-2xl relative group text-gray-950 dark:text-gray-50 codeblock-light border border-gray-950/10 dark:border-white/10 bg-transparent dark:bg-transparent" numberoflines="82" language="json"><div class="absolute top-3 right-4 flex items-center gap-1.5"><div class="z-10 relative"><button class="h-[26px] w-[26px] flex items-center justify-center rounded-md backdrop-blur peer group/copy-button" data-testid="copy-code-button" aria-label="Copy the contents from the code block"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-400 group-hover/copy-button:text-gray-500 dark:text-white/40 dark:group-hover/copy-button:text-white/60"><path d="M14.25 5.25H7.25C6.14543 5.25 5.25 6.14543 5.25 7.25V14.25C5.25 15.3546 6.14543 16.25 7.25 16.25H14.25C15.3546 16.25 16.25 15.3546 16.25 14.25V7.25C16.25 6.14543 15.3546 5.25 14.25 5.25Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M2.80103 11.998L1.77203 5.07397C1.61003 3.98097 2.36403 2.96397 3.45603 2.80197L10.38 1.77297C11.313 1.63397 12.19 2.16297 12.528 3.00097" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></button><div aria-hidden="true" class="absolute top-11 left-1/2 transform whitespace-nowrap -translate-x-1/2 -translate-y-1/2 peer-hover:opacity-100 opacity-0 text-white rounded-lg px-1.5 py-0.5 text-xs bg-primary-dark">Copy</div></div><div class="z-10 relative"><button class="h-[26px] w-[26px] flex items-center justify-center rounded-md backdrop-blur peer group/ask-ai-button" id="ask-ai-code-block-button" aria-label="Ask AI"><svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-400 group-hover/ask-ai-button:text-gray-500 dark:text-white/40 dark:group-hover/ask-ai-button:text-white/60"><path d="M3.51169 1.50098L3.92087 2.72754L3.99997 2.96387L5.49997 3.46387L4.23825 3.88477L4.00095 3.96387L3.92184 4.20117L3.50095 5.46191L3.49997 5.46387H3.49899L2.99899 3.96387L1.49899 3.46387L2.99899 2.96387L3.07809 2.72754L3.4863 1.50098C3.49031 1.50045 3.49522 1.5 3.49997 1.5C3.50416 1.50002 3.50807 1.50054 3.51169 1.50098Z" stroke="currentColor"></path><path d="M9.5 2.75L11.412 7.587L16.25 9.5L11.412 11.413L9.5 16.25L7.587 11.413L2.75 9.5L7.587 7.587L9.5 2.75Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></button><div aria-hidden="true" class="absolute top-11 left-1/2 transform whitespace-nowrap -translate-x-1/2 -translate-y-1/2 peer-hover:opacity-100 opacity-0 text-white rounded-lg px-1.5 py-0.5 text-xs bg-primary-dark">Ask AI</div></div></div><div class="w-0 min-w-full max-w-full py-3.5 px-4 h-full dark:bg-codeblock relative text-sm leading-6 children:!my-0 children:!shadow-none children:!bg-transparent transition-[height] duration-300 ease-in-out [&amp;_*]:ring-0 [&amp;_*]:outline-none [&amp;_*]:focus:ring-0 [&amp;_*]:focus:outline-none [&amp;_pre&gt;code]:pr-[3rem] [&amp;_pre&gt;code&gt;span.line-highlight]:min-w-[calc(100%+3rem)] [&amp;_pre&gt;code&gt;span.line-diff]:min-w-[calc(100%+3rem)] rounded-2xl bg-white overflow-x-auto scrollbar-thin scrollbar-thumb-rounded scrollbar-thumb-black/15 hover:scrollbar-thumb-black/20 active:scrollbar-thumb-black/20 dark:scrollbar-thumb-white/20 dark:hover:scrollbar-thumb-white/25 dark:active:scrollbar-thumb-white/25" data-component-part="code-block-root" style="font-variant-ligatures: none; height: auto;"><div class="font-mono whitespace-pre leading-6"><pre class="shiki shiki-themes github-light-default dark-plus" language="json" style="background-color: transparent; --shiki-dark-bg: transparent; color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;"><code language="json" numberoflines="82"><span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">{</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">  "code"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">200</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 响应状态码，200表示成功</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">  "result"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: {</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">    "markdown"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"# 劳动人事争议仲裁申请书</span><span style="color: rgb(207, 34, 46); --shiki-dark: #D7BA7D;">\n\n</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">致:广东省劳动人事争议调解仲裁院</span><span style="color: rgb(207, 34, 46); --shiki-dark: #D7BA7D;">\n\n</span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">[完整表格内容...]"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 生成的markdown格式文档内容正文字符串</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">    "success_count"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">5</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 成功处理的页面数量</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">    "pages"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: [</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">      {</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">        "angle"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">0</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 页面旋转角度，0表示无旋转</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">        "page_id"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">1</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 页面ID</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">        "content"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: [</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">          {</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">            "pos"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: [</span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">276</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">243</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">970</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">243</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">970</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">291</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">276</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">291</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">], </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 文本行四个角点坐标</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">            "id"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">0</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 内容块ID</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">            "score"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">1</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 识别置信度分数</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">            "type"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"line"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 内容类型，line表示文本行</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">            "text"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"劳动人事争议仲裁申请书"</span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;"> // 识别的文本内容</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">          },</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">          {</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">            "pos"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: [</span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">181</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">400</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">560</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">400</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">560</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">424</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">181</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">424</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">], </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 文本行四个角点坐标</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">            "id"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">1</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 内容块ID</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">            "score"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">1</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 识别置信度分数</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">            "type"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"line"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 内容类型，line表示文本行</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">            "text"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"致:广东省劳动人事争议调解仲裁院"</span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;"> // 识别的文本内容</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">          }</span></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">          // [更多content条目...]</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">        ],</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">        "status"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"success"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 页面处理状态，success表示成功</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">        "height"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">1684</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 页面高度</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">        "structured"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: [</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">          {</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">            "pos"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: [</span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">278</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">244</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">967</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">242</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">967</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">293</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">278</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">295</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">], </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 结构化内容的位置坐标</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">            "type"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"textblock"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 结构化内容类型，textblock表示文本块</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">            "id"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">0</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 结构化内容ID</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">            "content"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: [</span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">0</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">], </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 关联的content数组索引</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">            "text"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"劳动人事争议仲裁申请书"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 结构化文本内容</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">            "outline_level"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">0</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 大纲级别，0表示顶级标题</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">            "sub_type"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"text_title"</span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;"> // 子类型，text_title表示文本标题</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">          }</span></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">          // [更多structured条目...]</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">        ],</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">        "durations"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">459.98861694336</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 页面处理耗时（毫秒）</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">        "image_id"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">""</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 页面图像ID</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">        "width"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">1191</span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;"> // 页面宽度</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">      }</span></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">      // [更多pages条目...]</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">    ],</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">    "valid_page_number"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">5</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 有效页面数量</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">    "total_page_number"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">5</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 总页面数量</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">    "total_count"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">5</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 总处理数量</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">    "detail"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: [</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">      {</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">        "paragraph_id"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">0</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 段落ID</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">        "page_id"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">1</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 所属页面ID</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">        "tags"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: [], </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 标签数组</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">        "outline_level"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">0</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 大纲级别</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">        "text"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"劳动人事争议仲裁申请书"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 段落文本内容</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">        "type"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"paragraph"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 内容类型，paragraph表示段落</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">        "position"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: [</span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">278</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">244</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">967</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">242</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">967</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">293</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">278</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">295</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">], </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 段落位置坐标</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">        "content"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">0</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 关联的content索引</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">        "sub_type"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"text_title"</span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;"> // 子类型，text_title表示文本标题</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">      }</span></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">      // [更多detail条目...]</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">    ]</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">  },</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">  "x_request_id"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"f36effda6a0141ed0583bea0d596f597"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 请求唯一标识符</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">  "metrics"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: [</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">    {</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">      "angle"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">0</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 页面旋转角度</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">      "status"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"success"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 处理状态</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">      "dpi"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">144</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 图像DPI值</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">      "image_id"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">""</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 图像ID</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">      "page_id"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">1</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 页面ID</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">      "duration"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">464.10571289062</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 处理耗时（毫秒）</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">      "page_image_width"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">1191</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 页面图像宽度</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">      "page_image_height"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">1684</span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;"> // 页面图像高度</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">    }</span></span>
<span class="line"><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">    // [更多metrics条目...]</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">  ],</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">  "duration"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(5, 80, 174); --shiki-dark: #B5CEA8;">1459</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 引擎耗时（毫秒）</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">  "message"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"success"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">, </span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;">// 响应消息</span></span>
<span class="line"><span style="color: rgb(17, 99, 41); --shiki-dark: #9CDCFE;">  "version"</span><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">: </span><span style="color: rgb(10, 48, 105); --shiki-dark: #CE9178;">"3.17.12"</span><span style="color: rgb(110, 119, 129); --shiki-dark: #6A9955;"> // 引擎版本号</span></span>
<span class="line"><span style="color: rgb(31, 35, 40); --shiki-dark: #f3f7f6;">}</span></span>
</code></pre></div></div></div>
<h3 class="flex whitespace-pre-wrap group font-semibold" id="%E9%94%99%E8%AF%AF%E7%A0%81%E8%AF%B4%E6%98%8E"><div class="absolute"><a href="https://docs.textin.com/xparse/parse-quickstart#%E9%94%99%E8%AF%AF%E7%A0%81%E8%AF%B4%E6%98%8E" class="-ml-10 flex items-center opacity-0 border-0 group-hover:opacity-100" aria-label="Navigate to header">​<div class="w-6 h-6 rounded-md flex items-center justify-center shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 bg-white ring-1 ring-gray-400/30 dark:ring-gray-700/25 hover:ring-gray-400/60 dark:hover:ring-white/20"><svg xmlns="http://www.w3.org/2000/svg" fill="gray" height="12px" viewBox="0 0 576 512"><path d="M0 256C0 167.6 71.6 96 160 96h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C98.1 144 48 194.1 48 256s50.1 112 112 112h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C71.6 416 0 344.4 0 256zm576 0c0 88.4-71.6 160-160 160H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c61.9 0 112-50.1 112-112s-50.1-112-112-112H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c88.4 0 160 71.6 160 160zM184 232H392c13.3 0 24 10.7 24 24s-10.7 24-24 24H184c-13.3 0-24-10.7-24-24s10.7-24 24-24z"></path></svg></div></a></div><span class="cursor-pointer">错误码说明</span></h3>
<table><thead><tr><th style="text-align: left;"><strong>错误码</strong></th><th style="text-align: left;"><strong>描述</strong></th></tr></thead><tbody><tr><td style="text-align: left;">40101</td><td style="text-align: left;">x-ti-app-id 或 x-ti-secret-code 为空</td></tr><tr><td style="text-align: left;">40102</td><td style="text-align: left;">x-ti-app-id 或 x-ti-secret-code 无效，验证失败</td></tr><tr><td style="text-align: left;">40103</td><td style="text-align: left;">客户端IP不在白名单</td></tr><tr><td style="text-align: left;">40003</td><td style="text-align: left;">余额不足，请充值后再使用</td></tr><tr><td style="text-align: left;">40004</td><td style="text-align: left;">参数错误，请查看技术文档，检查传参</td></tr><tr><td style="text-align: left;">40007</td><td style="text-align: left;">机器人不存在或未发布</td></tr><tr><td style="text-align: left;">40008</td><td style="text-align: left;">机器人未开通，请至市场开通后重试</td></tr><tr><td style="text-align: left;">40301</td><td style="text-align: left;">图片类型不支持</td></tr><tr><td style="text-align: left;">40302</td><td style="text-align: left;">上传文件大小不符，文件大小不超过 500M</td></tr><tr><td style="text-align: left;">40303</td><td style="text-align: left;">文件类型不支持，接口会返回实际检测到的文件类型，如“当前文件类型为.gif”</td></tr><tr><td style="text-align: left;">40304</td><td style="text-align: left;">图片尺寸不符，长宽比小于2的图片宽高需在20～20000像素范围内，其他图片的宽高需在20～10000像素范围内</td></tr><tr><td style="text-align: left;">40305</td><td style="text-align: left;">识别文件未上传</td></tr><tr><td style="text-align: left;">40422</td><td style="text-align: left;">文件损坏（The file is corrupted.）</td></tr><tr><td style="text-align: left;">40423</td><td style="text-align: left;">PDF密码错误（Password required or incorrect password.）</td></tr><tr><td style="text-align: left;">40424</td><td style="text-align: left;">页数设置超出文件范围（Page number out of range.）</td></tr><tr><td style="text-align: left;">40425</td><td style="text-align: left;">文件格式不支持（The input file format is not supported.）</td></tr><tr><td style="text-align: left;">40427</td><td style="text-align: left;">DPI参数不在支持列表中（Input DPI is not in the allowed DPIs list(72,144,216).）</td></tr><tr><td style="text-align: left;">40428</td><td style="text-align: left;">word和ppt转pdf失败或者超时（Process office file failed.）</td></tr><tr><td style="text-align: left;">50207</td><td style="text-align: left;">部分页面解析失败（Partial failed）</td></tr><tr><td style="text-align: left;">40400</td><td style="text-align: left;">无效的请求链接，请检查链接是否正确</td></tr><tr><td style="text-align: left;">30203</td><td style="text-align: left;">基础服务故障，请稍后重试</td></tr><tr><td style="text-align: left;">500</td><td style="text-align: left;">服务器内部错误</td></tr></tbody></table></div><div class="leading-6 mt-14"><div class="feedback-toolbar pb-16 w-full"><div class="flex flex-col gap-y-4 xl:flex-col xl:gap-6 min-[1400px]:flex-row md:flex-row md:justify-end"><div class="flex flex-row gap-5 items-center grow justify-between md:justify-start xl:justify-between min-[1400px]:justify-start"><p class="text-sm text-gray-600 dark:text-gray-400">此页面对您有帮助吗？</p><div class="flex flex-row gap-3 items-center"><button class="px-3.5 py-2 flex flex-row gap-3 items-center border-standard rounded-xl text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 bg-white/50 dark:bg-codeblock/50 hover:border-gray-500 hover:dark:border-gray-500"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" class="fill-current"><path d="M10.1187 1.08741C8.925 0.746789 7.67813 1.43741 7.3375 2.63116L7.15938 3.25616C7.04375 3.66241 6.83438 4.03741 6.55 4.34991L4.94688 6.11241C4.66875 6.41866 4.69062 6.89366 4.99687 7.17179C5.30312 7.44991 5.77813 7.42804 6.05625 7.12179L7.65938 5.35929C8.1 4.87491 8.42188 4.29679 8.6 3.66866L8.77812 3.04366C8.89062 2.64679 9.30625 2.41554 9.70625 2.52804C10.1063 2.64054 10.3344 3.05616 10.2219 3.45616L10.0437 4.08116C9.86562 4.70304 9.58437 5.29054 9.2125 5.81554C9.05 6.04366 9.03125 6.34366 9.15938 6.59366C9.2875 6.84366 9.54375 6.99991 9.825 6.99991H14C14.275 6.99991 14.5 7.22491 14.5 7.49991C14.5 7.71241 14.3656 7.89679 14.175 7.96866C13.9438 8.05616 13.7688 8.24992 13.7094 8.49054C13.65 8.73117 13.7125 8.98429 13.875 9.16866C13.9531 9.25616 14 9.37179 14 9.49991C14 9.74366 13.825 9.94679 13.5938 9.99054C13.3375 10.0405 13.1219 10.2187 13.0312 10.4624C12.9406 10.7062 12.9813 10.9843 13.1438 11.1905C13.2094 11.2749 13.25 11.3812 13.25 11.4999C13.25 11.7093 13.1187 11.8937 12.9312 11.9655C12.5719 12.1062 12.3781 12.4937 12.4812 12.8655C12.4937 12.9062 12.5 12.953 12.5 12.9999C12.5 13.2749 12.275 13.4999 12 13.4999H8.95312C8.55937 13.4999 8.17188 13.3843 7.84375 13.1655L5.91563 11.8812C5.57188 11.6499 5.10625 11.7437 4.875 12.0905C4.64375 12.4374 4.7375 12.8999 5.08437 13.1312L7.0125 14.4155C7.5875 14.7999 8.2625 15.003 8.95312 15.003H12C13.0844 15.003 13.9656 14.1405 14 13.0655C14.4563 12.6999 14.75 12.1374 14.75 11.503C14.75 11.3624 14.7344 11.228 14.7094 11.0968C15.1906 10.7312 15.5 10.153 15.5 9.50304C15.5 9.29991 15.4688 9.10304 15.4125 8.91866C15.775 8.55304 16 8.05304 16 7.49991C16 6.39679 15.1063 5.49991 14 5.49991H11.1156C11.2625 5.17491 11.3875 4.83741 11.4844 4.49366L11.6625 3.86866C12.0031 2.67491 11.3125 1.42804 10.1187 1.08741ZM1 5.99991C0.446875 5.99991 0 6.44679 0 6.99991V13.9999C0 14.553 0.446875 14.9999 1 14.9999H3C3.55313 14.9999 4 14.553 4 13.9999V6.99991C4 6.44679 3.55313 5.99991 3 5.99991H1Z"></path></svg><small class="text-sm font-normal leading-4 ">是</small></button><button class="px-3.5 py-2 flex flex-row gap-3 items-center border-standard rounded-xl text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 bg-white/50 dark:bg-codeblock/50 hover:border-gray-500 hover:dark:border-gray-500"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" class="fill-current"><path d="M10.1187 14.9124C8.925 15.253 7.67813 14.5624 7.3375 13.3687L7.15938 12.7437C7.04375 12.3374 6.83438 11.9624 6.55 11.6499L4.94688 9.8874C4.66875 9.58115 4.69062 9.10615 4.99687 8.82803C5.30312 8.5499 5.77813 8.57178 6.05625 8.87803L7.65938 10.6405C8.1 11.1249 8.42188 11.703 8.6 12.3312L8.77812 12.9562C8.89062 13.353 9.30625 13.5843 9.70625 13.4718C10.1063 13.3593 10.3344 12.9437 10.2219 12.5437L10.0437 11.9187C9.86562 11.2968 9.58437 10.7093 9.2125 10.1843C9.05 9.95615 9.03125 9.65615 9.15938 9.40615C9.2875 9.15615 9.54375 8.9999 9.825 8.9999H14C14.275 8.9999 14.5 8.7749 14.5 8.4999C14.5 8.2874 14.3656 8.10303 14.175 8.03115C13.9438 7.94365 13.7688 7.7499 13.7094 7.50928C13.65 7.26865 13.7125 7.01553 13.875 6.83115C13.9531 6.74365 14 6.62803 14 6.4999C14 6.25615 13.825 6.05303 13.5938 6.00928C13.3375 5.95928 13.1219 5.78115 13.0312 5.53428C12.9406 5.2874 12.9813 5.0124 13.1438 4.80615C13.2094 4.72178 13.25 4.61553 13.25 4.49678C13.25 4.2874 13.1187 4.10303 12.9312 4.03115C12.5719 3.89053 12.3781 3.50303 12.4812 3.13115C12.4937 3.09053 12.5 3.04365 12.5 2.99678C12.5 2.72178 12.275 2.49678 12 2.49678H8.95312C8.55937 2.49678 8.17188 2.6124 7.84375 2.83115L5.91563 4.11553C5.57188 4.34678 5.10625 4.25303 4.875 3.90615C4.64375 3.55928 4.7375 3.09678 5.08437 2.86553L7.0125 1.58115C7.5875 1.19678 8.2625 0.993652 8.95312 0.993652H12C13.0844 0.993652 13.9656 1.85615 14 2.93115C14.4563 3.29678 14.75 3.85928 14.75 4.49365C14.75 4.63428 14.7344 4.76865 14.7094 4.8999C15.1906 5.26553 15.5 5.84365 15.5 6.49365C15.5 6.69678 15.4688 6.89365 15.4125 7.07803C15.775 7.44678 16 7.94678 16 8.4999C16 9.60303 15.1063 10.4999 14 10.4999H11.1156C11.2625 10.8249 11.3875 11.1624 11.4844 11.5062L11.6625 12.1312C12.0031 13.3249 11.3125 14.5718 10.1187 14.9124ZM1 11.9999C0.446875 11.9999 0 11.553 0 10.9999V3.9999C0 3.44678 0.446875 2.9999 1 2.9999H3C3.55313 2.9999 4 3.44678 4 3.9999V10.9999C4 11.553 3.55313 11.9999 3 11.9999H1Z"></path></svg><small class="text-sm font-normal leading-4 ">否</small></button></div></div><div class="flex flex-row gap-3 justify-end"></div></div></div><div id="pagination" class="mb-12 px-0.5 flex items-center text-sm font-semibold text-gray-700 dark:text-gray-200"><a class="flex items-center space-x-3 group" href="https://docs.textin.com/xparse/product-manual"><svg viewBox="0 0 3 6" class="h-1.5 stroke-gray-400 overflow-visible group-hover:stroke-gray-600 dark:group-hover:stroke-gray-300"><path d="M3 0L0 3L3 6" fill="none" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></svg><span class="group-hover:text-gray-900 dark:group-hover:text-white">使用手册</span></a><a class="flex items-center ml-auto space-x-3 group" href="https://docs.textin.com/xparse/parse-getjson"><span class="group-hover:text-gray-900 dark:group-hover:text-white">返回JSON结构说明</span><svg viewBox="0 0 3 6" class="rotate-180 h-1.5 stroke-gray-400 overflow-visible group-hover:stroke-gray-600 dark:group-hover:stroke-gray-300"><path d="M3 0L0 3L3 6" fill="none" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></svg></a></div><footer id="footer" class="flex gap-12 justify-between pt-10 border-t border-gray-100 sm:flex dark:border-gray-800/50 pb-28"><div class="flex gap-6 flex-wrap"><a href="https://github.com/intsig-textin" target="_blank" class="h-fit"><span class="sr-only">github</span><svg class="w-5 h-5 bg-gray-400 dark:bg-gray-500 hover:bg-gray-500 dark:hover:bg-gray-400" style="mask-image: url(&quot;https://mintlify.b-cdn.net/v6.6.0/brands/github.svg&quot;); mask-repeat: no-repeat; mask-position: center center;"></svg></a></div><div class="flex items-center justify-between"><div class="sm:flex"><a href="https://mintlify.com/preview-request?utm_campaign=poweredBy&amp;utm_medium=referral&amp;utm_source=textin" target="_blank" rel="noreferrer" class="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-nowrap">由Mintlify提供支持</a></div></div></footer></div></div></div><div class="z-10 fixed right-0 w-[23rem] border-l border-gray-500/5 dark:border-gray-300/[0.06] bg-background-light dark:bg-background-dark h-[calc(100vh-7rem)] top-[7rem] transition-[width] duration-300 ease-in-out invisible" style="width:368px;min-width:368px;max-width:576px"><div class="absolute -left-1 top-0 bottom-0 w-1 cursor-col-resize hover:bg-gray-200/70 dark:hover:bg-white/[0.07] z-10" style="cursor:col-resize"></div><div id="chat-assistant-sheet" class="absolute inset-0 -top-px min-h-full flex flex-col overflow-hidden shrink-0 chat-assistant-sheet" aria-hidden="true"><div class="w-full flex flex-col pb-4 h-full lg:pt-3"><div class="chat-assistant-sheet-header flex items-center justify-between pb-3 px-4"><div class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" class="size-5 text-primary dark:text-primary-light"><g fill="currentColor"><path d="M5.658,2.99l-1.263-.421-.421-1.263c-.137-.408-.812-.408-.949,0l-.421,1.263-1.263,.421c-.204,.068-.342,.259-.342,.474s.138,.406,.342,.474l1.263,.421,.421,1.263c.068,.204,.26,.342,.475,.342s.406-.138,.475-.342l.421-1.263,1.263-.421c.204-.068,.342-.259,.342-.474s-.138-.406-.342-.474Z" fill="currentColor" data-stroke="none" stroke="none"></path><polygon points="9.5 2.75 11.412 7.587 16.25 9.5 11.412 11.413 9.5 16.25 7.587 11.413 2.75 9.5 7.587 7.587 9.5 2.75" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></polygon></g></svg><span class="font-semibold text-gray-900 dark:text-gray-100">助手</span></div><div class="flex items-center gap-1"><button class="group hover:bg-gray-100 dark:hover:bg-white/10 p-1.5 rounded-lg"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-maximize2 size-[13px] text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-300"><polyline points="15 3 21 3 21 9"></polyline><polyline points="9 21 3 21 3 15"></polyline><line x1="21" x2="14" y1="3" y2="10"></line><line x1="3" x2="10" y1="21" y2="14"></line></svg></button><button class="group hover:bg-gray-100 dark:hover:bg-white/10 p-1.5 rounded-lg"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="size-3.5 text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-300"><path d="M12.4444 3.55566L3.55554 12.4446" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M3.55554 3.55566L12.4444 12.4446" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></button></div></div><div id="chat-content" class="chat-assistant-sheet-content flex-1 overflow-y-auto relative px-4"><div class="mt-4 flex flex-col items-center text-sm justify-between"><div class="mx-8 text-center text-gray-400 dark:text-gray-600 text-xs chat-assistant-disclaimer-text">Responses are generated using AI and may contain mistakes.</div><div class="flex flex-col gap-4 text-gray-800 dark:text-gray-200"></div></div></div><div class="px-4"><div class=""><div class="flex items-end gap-2 relative"><textarea id="chat-assistant-textarea" autocomplete="off" placeholder="提出问题..." class="grow w-full px-3.5 pr-10 py-2.5 bg-background-light dark:bg-background-dark border border-gray-200 dark:border-gray-600/30 rounded-2xl shadow-sm focus:outline-none focus:border-primary dark:focus:border-primary-light text-gray-900 dark:text-gray-100 text-sm chat-assistant-input" style="resize: none; height: 42px !important;"></textarea><button class="absolute right-2.5 bottom-[9px] flex justify-center items-center p-1 w-6 h-6 rounded-lg bg-primary/30 dark:bg-primary-dark/30 chat-assistant-send-button" aria-label="Send message" disabled=""><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up w-3.5 h-3.5 text-white dark:text-white"><path d="m5 12 7-7 7 7"></path><path d="M12 19V5"></path></svg></button></div></div></div></div></div></div></div></div></div><script src="./快速启动 - Textin 智能文档解析_files/webpack-b369b521ae88fa3a.js.下载" id="_R_" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[47132,[],\"\"]\n3:I[55983,[\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"9058\",\"static/chunks/9058-fc5eb8705bf7a22c.js\",\"8039\",\"static/chunks/app/error-dad69ef19d740480.js\"],\"default\"]\n4:I[75082,[],\"\"]\n"])</script><script>self.__next_f.push([1,"5:I[85506,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"803\",\"static/chunks/cd24890f-549fb4ba2f588ca6.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"9779\",\"static/chunks/9779-7bb45d52151006b8.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"1350\",\"static/chunks/1350-cf10a11ffacb60d0.js\",\"5456\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/layout-b596e085fd7c2d45.js\"],\"ThemeProvider\"]\n"])</script><script>self.__next_f.push([1,"6:I[81925,[\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"9058\",\"static/chunks/9058-fc5eb8705bf7a22c.js\",\"9249\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/error-d4ab46b84560464d.js\"],\"default\"]\nf:I[71256,[],\"\"]\n:HL[\"/mintlify-assets/_next/static/media/bb3ef058b751a6ad-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/mintlify-assets/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/mintlify-assets/_next/static/css/ca797da4e9f8f21c.css\",\"style\"]\n:HL[\"/mintlify-assets/_next/static/css/fa46c000add4671c.css\",\"style\"]\n:HL[\"/mintlify-assets/_next/static/css/19e66b131dc509b0.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"oAGmX_i3TAEhpY4tCVFfA\",\"p\":\"/mintlify-assets\",\"c\":[\"\",\"_sites\",\"docs.textin.com\",\"api-reference\",\"endpoint\",\"parse\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"%5Fsites\",{\"children\":[[\"subdomain\",\"docs.textin.com\",\"d\"],{\"children\":[\"(multitenant)\",{\"topbar\":[\"children\",{\"children\":[[\"slug\",\"api-reference/endpoint/parse\",\"oc\"],{\"children\":[\"__PAGE__\",{}]}]}],\"children\":[[\"slug\",\"api-reference/endpoint/parse\",\"oc\"],{\"children\":[\"__PAGE__\",{}]}]}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/mintlify-assets/_next/static/css/ca797da4e9f8f21c.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/mintlify-assets/_next/static/css/fa46c000add4671c.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"suppressHydrationWarning\":true,\"lang\":\"en\",\"className\":\"__variable_5f106d __variable_3bbdad dark\",\"data-banner-state\":\"visible\",\"data-page-mode\":\"none\",\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"script\",null,{\"type\":\"text/javascript\",\"dangerouslySetInnerHTML\":{\"__html\":\"(function(a,b,c){try{let d=localStorage.getItem(a);if(null==d)for(let c=0;c\u003clocalStorage.length;c++){let e=localStorage.key(c);if(e?.endsWith(`-${b}`)\u0026\u0026(d=localStorage.getItem(e),null!=d)){localStorage.setItem(a,d),localStorage.setItem(e,d);break}}let e=document.getElementById(\\\"banner\\\")?.innerText,f=null==d||!!e\u0026\u0026d!==e;document.documentElement.setAttribute(c,f?\\\"visible\\\":\\\"hidden\\\")}catch(a){console.error(a),document.documentElement.setAttribute(c,\\\"hidden\\\")}})(\\n  \\\"__mintlify-bannerDismissed\\\",\\n  \\\"bannerDismissed\\\",\\n  \\\"data-banner-state\\\",\\n)\"}}],[\"$\",\"link\",null,{\"rel\":\"stylesheet\",\"href\":\"https://cdn.jsdelivr.net/npm/katex@0.16.0/dist/katex.min.css\",\"integrity\":\"sha384-Xi8rHCmBmhbuyyhbI88391ZKP2dmfnOl4rT9ZfRI7mLTdk1wblIUnrIq35nqwEvC\",\"crossOrigin\":\"anonymous\"}]]}],[\"$\",\"body\",null,{\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$3\",\"errorStyles\":[],\"errorScripts\":[],\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$L5\",null,{\"children\":[[\"$\",\"style\",null,{\"children\":\":root {\\n    --primary: 22 163 74;\\n    --primary-light: 74 222 128;\\n    --primary-dark: 22 101 52;\\n    --background-light: 255 255 255;\\n    --background-dark: 10 13 13;\\n    --gray-50: 243 247 245;\\n    --gray-100: 238 242 240;\\n    --gray-200: 223 227 224;\\n    --gray-300: 206 211 208;\\n    --gray-400: 159 163 160;\\n    --gray-500: 112 116 114;\\n    --gray-600: 80 84 82;\\n    --gray-700: 63 67 64;\\n    --gray-800: 38 42 39;\\n    --gray-900: 23 27 25;\\n    --gray-950: 10 15 12;\\n  }\"}],null,null,[\"$\",\"style\",null,{\"children\":\":root {\\n  --primary: 17 120 102;\\n  --primary-light: 74 222 128;\\n  --primary-dark: 22 101 52;\\n  --background-light: 255 255 255;\\n  --background-dark: 15 17 23;\\n}\"}],[\"$\",\"main\",null,{\"className\":\"h-screen bg-background-light dark:bg-background-dark\",\"children\":[\"$\",\"article\",null,{\"className\":\"bg-custom bg-fixed bg-center bg-cover relative flex flex-col items-center justify-center h-full\",\"children\":[\"$\",\"div\",null,{\"className\":\"w-full max-w-xl px-10\",\"children\":[[\"$\",\"span\",null,{\"className\":\"inline-flex mb-6 rounded-full px-3 py-1 text-sm font-semibold mr-4 text-white p-1 bg-primary\",\"children\":[\"Error \",404]}],[\"$\",\"h1\",null,{\"className\":\"font-semibold mb-3 text-3xl\",\"children\":\"Page not found!\"}],[\"$\",\"p\",null,{\"className\":\"text-lg text-gray-600 dark:text-gray-400 mb-6\",\"children\":\"We're sorry, we couldn't find the page you were looking for.\"}]]}]}]}]]}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]]}]]}],{\"children\":[\"%5Fsites\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[[\"subdomain\",\"docs.textin.com\",\"d\"],[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$6\",\"errorStyles\":[],\"errorScripts\":[],\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$L7\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"(multitenant)\",\"$L8\",{\"topbar\":[\"children\",\"$L9\",{\"children\":[[\"slug\",\"api-reference/endpoint/parse\",\"oc\"],\"$La\",{\"children\":[\"__PAGE__\",\"$Lb\",{},null,false]},null,false]},null,false],\"children\":[[\"slug\",\"api-reference/endpoint/parse\",\"oc\"],\"$Lc\",{\"children\":[\"__PAGE__\",\"$Ld\",{},null,false]},null,false]},null,false]},null,false]},null,false]},null,false],\"$Le\",false]],\"m\":\"$undefined\",\"G\":[\"$f\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"12:I[50700,[],\"OutletBoundary\"]\n17:I[87748,[],\"AsyncMetadataOutlet\"]\n19:I[50700,[],\"ViewportBoundary\"]\n1b:I[50700,[],\"MetadataBoundary\"]\n1c:\"$Sreact.suspense\"\n"])</script><script>self.__next_f.push([1,"7:[[\"$\",\"$L5\",null,{\"children\":[[\"$\",\"style\",null,{\"children\":\":root {\\n    --primary: 22 163 74;\\n    --primary-light: 74 222 128;\\n    --primary-dark: 22 101 52;\\n    --background-light: 255 255 255;\\n    --background-dark: 10 13 13;\\n    --gray-50: 243 247 245;\\n    --gray-100: 238 242 240;\\n    --gray-200: 223 227 224;\\n    --gray-300: 206 211 208;\\n    --gray-400: 159 163 160;\\n    --gray-500: 112 116 114;\\n    --gray-600: 80 84 82;\\n    --gray-700: 63 67 64;\\n    --gray-800: 38 42 39;\\n    --gray-900: 23 27 25;\\n    --gray-950: 10 15 12;\\n  }\"}],null,null,[\"$\",\"style\",null,{\"children\":\":root {\\n  --primary: 17 120 102;\\n  --primary-light: 74 222 128;\\n  --primary-dark: 22 101 52;\\n  --background-light: 255 255 255;\\n  --background-dark: 15 17 23;\\n}\"}],[\"$\",\"main\",null,{\"className\":\"h-screen bg-background-light dark:bg-background-dark\",\"children\":[\"$\",\"article\",null,{\"className\":\"bg-custom bg-fixed bg-center bg-cover relative flex flex-col items-center justify-center h-full\",\"children\":[\"$\",\"div\",null,{\"className\":\"w-full max-w-xl px-10\",\"children\":[[\"$\",\"span\",null,{\"className\":\"inline-flex mb-6 rounded-full px-3 py-1 text-sm font-semibold mr-4 text-white p-1 bg-primary\",\"children\":[\"Error \",404]}],[\"$\",\"h1\",null,{\"className\":\"font-semibold mb-3 text-3xl\",\"children\":\"Page not found!\"}],[\"$\",\"p\",null,{\"className\":\"text-lg text-gray-600 dark:text-gray-400 mb-6\",\"children\":\"We're sorry, we couldn't find the page you were looking for.\"}]]}]}]}]]}],[]]\n"])</script><script>self.__next_f.push([1,"8:[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/mintlify-assets/_next/static/css/19e66b131dc509b0.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],\"$L10\"]}]\n9:[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}]\na:[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}]\nb:[\"$\",\"$1\",\"c\",{\"children\":[\"$L11\",null,[\"$\",\"$L12\",null,{\"children\":[\"$L13\",\"$L14\"]}]]}]\nc:[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}]\nd:[\"$\",\"$1\",\"c\",{\"children\":[\"$L15\",null,[\"$\",\"$L12\",null,{\"children\":[\"$L16\",[\"$\",\"$L17\",null,{\"promise\":\"$@18\"}]]}]]}]\ne:[\"$\",\"$1\",\"h\",{\"children\":[null,[[\"$\",\"$L19\",null,{\"children\":\"$L1a\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]],[\"$\",\"$L1b\",null,{\"children\":[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$1c\",null,{\"fallback\":null,\"children\":\"$L1d\"}]}]}]]}]\n"])</script><script>self.__next_f.push([1,"13:null\n14:null\n"])</script><script>self.__next_f.push([1,"1a:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n16:null\n"])</script><script>self.__next_f.push([1,"1e:T516,"])</script><script>self.__next_f.push([1,"https://textin.mintlify.app/mintlify-assets/_next/image?url=%2Fapi%2Fog%3Fdivision%3D%25E6%2599%25BA%25E8%2583%25BD%25E6%2596%2587%25E6%25A1%25A3%25E8%25A7%25A3%25E6%259E%2590%26title%3D%25E6%2596%2587%25E6%25A1%25A3%25E8%25A7%25A3%25E6%259E%2590%26description%3D%25E4%25B8%258A%25E4%25BC%25A0%25E5%259B%25BE%25E7%2589%2587%252Fpdf%252Fword%252Fhtml%252Fexcel%252Fppt%252Ftxt%25EF%25BC%258C%25E8%25BF%259B%25E8%25A1%258C%25E7%2589%2588%25E9%259D%25A2%25E6%25A3%2580%25E6%25B5%258B%25EF%25BC%258C%25E6%2596%2587%25E5%25AD%2597%25E8%25AF%2586%25E5%2588%25AB%25EF%25BC%258C%25E8%25A1%25A8%25E6%25A0%25BC%25E8%25AF%2586%25E5%2588%25AB%25EF%25BC%258C%25E7%2589%2588%25E9%259D%25A2%25E5%2588%2586%25E6%259E%2590%25E7%25AD%2589%25E6%2593%258D%25E4%25BD%259C%25EF%25BC%258C%25E5%25B9%25B6%25E7%2594%259F%25E6%2588%2590markdown%25E6%2596%2587%25E6%25A1%25A3%25E5%258F%258A%25E7%25BB%2593%25E6%259E%2584%25E5%258C%2596%25E6%2595%25B0%25E6%258D%25AE%26logoLight%3Dhttps%253A%252F%252Fmintlify.s3.us-west-1.amazonaws.com%252Ftextin%252Flogo%252Flight.png%26logoDark%3Dhttps%253A%252F%252Fmintlify.s3.us-west-1.amazonaws.com%252Ftextin%252Flogo%252Fdark.png%26primaryColor%3D%25231a66ff%26lightColor%3D%25231a66ff%26darkColor%3D%25231a66ff%26backgroundLight%3D%2523ffffff%26backgroundDark%3D%2523090b10\u0026w=1200\u0026q=100"])</script><script>self.__next_f.push([1,"1f:T516,"])</script><script>self.__next_f.push([1,"https://textin.mintlify.app/mintlify-assets/_next/image?url=%2Fapi%2Fog%3Fdivision%3D%25E6%2599%25BA%25E8%2583%25BD%25E6%2596%2587%25E6%25A1%25A3%25E8%25A7%25A3%25E6%259E%2590%26title%3D%25E6%2596%2587%25E6%25A1%25A3%25E8%25A7%25A3%25E6%259E%2590%26description%3D%25E4%25B8%258A%25E4%25BC%25A0%25E5%259B%25BE%25E7%2589%2587%252Fpdf%252Fword%252Fhtml%252Fexcel%252Fppt%252Ftxt%25EF%25BC%258C%25E8%25BF%259B%25E8%25A1%258C%25E7%2589%2588%25E9%259D%25A2%25E6%25A3%2580%25E6%25B5%258B%25EF%25BC%258C%25E6%2596%2587%25E5%25AD%2597%25E8%25AF%2586%25E5%2588%25AB%25EF%25BC%258C%25E8%25A1%25A8%25E6%25A0%25BC%25E8%25AF%2586%25E5%2588%25AB%25EF%25BC%258C%25E7%2589%2588%25E9%259D%25A2%25E5%2588%2586%25E6%259E%2590%25E7%25AD%2589%25E6%2593%258D%25E4%25BD%259C%25EF%25BC%258C%25E5%25B9%25B6%25E7%2594%259F%25E6%2588%2590markdown%25E6%2596%2587%25E6%25A1%25A3%25E5%258F%258A%25E7%25BB%2593%25E6%259E%2584%25E5%258C%2596%25E6%2595%25B0%25E6%258D%25AE%26logoLight%3Dhttps%253A%252F%252Fmintlify.s3.us-west-1.amazonaws.com%252Ftextin%252Flogo%252Flight.png%26logoDark%3Dhttps%253A%252F%252Fmintlify.s3.us-west-1.amazonaws.com%252Ftextin%252Flogo%252Fdark.png%26primaryColor%3D%25231a66ff%26lightColor%3D%25231a66ff%26darkColor%3D%25231a66ff%26backgroundLight%3D%2523ffffff%26backgroundDark%3D%2523090b10\u0026w=1200\u0026q=100"])</script><script>self.__next_f.push([1,"18:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"文档解析 - Textin 智能文档解析\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"上传图片/pdf/word/html/excel/ppt/txt，进行版面检测，文字识别，表格识别，版面分析等操作，并生成markdown文档及结构化数据\"}],[\"$\",\"meta\",\"2\",{\"name\":\"application-name\",\"content\":\"Textin 智能文档解析\"}],[\"$\",\"meta\",\"3\",{\"name\":\"generator\",\"content\":\"Mintlify\"}],[\"$\",\"meta\",\"4\",{\"name\":\"msapplication-config\",\"content\":\"https://mintlify.s3-us-west-1.amazonaws.com/textin/_generated/favicon/browserconfig.xml?v=3\"}],[\"$\",\"meta\",\"5\",{\"name\":\"apple-mobile-web-app-title\",\"content\":\"Textin 智能文档解析\"}],[\"$\",\"meta\",\"6\",{\"name\":\"msapplication-TileColor\",\"content\":\"#1a66ff\"}],[\"$\",\"meta\",\"7\",{\"name\":\"charset\",\"content\":\"utf-8\"}],[\"$\",\"meta\",\"8\",{\"name\":\"og:site_name\",\"content\":\"Textin 智能文档解析\"}],[\"$\",\"link\",\"9\",{\"rel\":\"alternate\",\"type\":\"application/xml\",\"href\":\"/sitemap.xml\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:title\",\"content\":\"文档解析 - Textin 智能文档解析\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:description\",\"content\":\"上传图片/pdf/word/html/excel/ppt/txt，进行版面检测，文字识别，表格识别，版面分析等操作，并生成markdown文档及结构化数据\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:image\",\"content\":\"$1e\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:image:height\",\"content\":\"630\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:title\",\"content\":\"文档解析 - Textin 智能文档解析\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:description\",\"content\":\"上传图片/pdf/word/html/excel/ppt/txt，进行版面检测，文字识别，表格识别，版面分析等操作，并生成markdown文档及结构化数据\"}],[\"$\",\"meta\",\"19\",{\"name\":\"twitter:image\",\"content\":\"$1f\"}],\"$L20\",\"$L21\",\"$L22\",\"$L23\",\"$L24\",\"$L25\",\"$L26\"],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"1d:\"$18:metadata\"\n"])</script><script>self.__next_f.push([1,"27:I[74780,[],\"IconMark\"]\n"])</script><script>self.__next_f.push([1,"28:I[4400,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"3122\",\"static/chunks/3122-473c6d6ad707a1ff.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"7048\",\"static/chunks/7048-68b7efbe64e44ac4.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"3386\",\"static/chunks/3386-4ac6c5d6ac47f6b6.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"3698\",\"static/chunks/3698-586441bcd6c2501a.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"3818\",\"static/chunks/3818-b06f8c87a0bcb5ea.js\",\"3291\",\"static/chunks/3291-8e412928ddb5eb6a.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-82f1f84c31f01e84.js\"],\"TopBar\",1]\n"])</script><script>self.__next_f.push([1,"29:I[71197,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"3122\",\"static/chunks/3122-473c6d6ad707a1ff.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"7048\",\"static/chunks/7048-68b7efbe64e44ac4.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"3386\",\"static/chunks/3386-4ac6c5d6ac47f6b6.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"3698\",\"static/chunks/3698-586441bcd6c2501a.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"3818\",\"static/chunks/3818-b06f8c87a0bcb5ea.js\",\"3291\",\"static/chunks/3291-8e412928ddb5eb6a.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-82f1f84c31f01e84.js\"],\"ApiReferenceProvider\",1]\n"])</script><script>self.__next_f.push([1,"20:[\"$\",\"meta\",\"20\",{\"name\":\"twitter:image:width\",\"content\":\"1200\"}]\n21:[\"$\",\"meta\",\"21\",{\"name\":\"twitter:image:height\",\"content\":\"630\"}]\n22:[\"$\",\"link\",\"22\",{\"rel\":\"apple-touch-icon\",\"href\":\"https://mintlify.s3-us-west-1.amazonaws.com/textin/_generated/favicon/apple-touch-icon.png?v=3\",\"type\":\"image/png\",\"sizes\":\"180x180\"}]\n23:[\"$\",\"link\",\"23\",{\"rel\":\"icon\",\"href\":\"https://mintlify.s3-us-west-1.amazonaws.com/textin/_generated/favicon/favicon-32x32.png?v=3\",\"type\":\"image/png\",\"sizes\":\"32x32\"}]\n24:[\"$\",\"link\",\"24\",{\"rel\":\"icon\",\"href\":\"https://mintlify.s3-us-west-1.amazonaws.com/textin/_generated/favicon/favicon-16x16.png?v=3\",\"type\":\"image/png\",\"sizes\":\"16x16\"}]\n25:[\"$\",\"link\",\"25\",{\"rel\":\"shortcut icon\",\"href\":\"https://mintlify.s3-us-west-1.amazonaws.com/textin/_generated/favicon/favicon.ico?v=3\",\"type\":\"image/x-icon\",\"sizes\":\"$undefined\"}]\n26:[\"$\",\"$L27\",\"26\",{}]\n11:[\"$\",\"$L28\",null,{\"className\":\"peer is-not-custom peer is-not-center peer is-not-wide peer is-not-frame\",\"pageMetadata\":{\"title\":\"文档解析\",\"openapi\":\"api-reference/parse-1.0.1.openapi.yml post /ai/service/v1/pdf_to_markdown\",\"description\":\"上传图片/pdf/word/html/excel/ppt/txt，进行版面检测，文字识别，表格识别，版面分析等操作，并生成markdown文档及结构化数据\",\"href\":\"/api-reference/endpoint/parse\"}}]\n"])</script><script>self.__next_f.push([1,"15:[\"$\",\"$L29\",null,{\"value\":{\"apiReferenceData\":{\"endpoint\":{\"title\":\"文档解析\",\"description\":\"上传图片/pdf/word/html/excel/ppt/txt，进行版面检测，文字识别，表格识别，版面分析等操作，并生成markdown文档及结构化数据\",\"path\":\"/ai/service/v1/pdf_to_markdown\",\"method\":\"post\",\"servers\":[{\"url\":\"https://api.textin.com\",\"description\":\"Production server\"}],\"request\":{\"security\":[{\"title\":\"AppIdAuth \u0026 SecretCodeAuth\",\"parameters\":{\"query\":{},\"header\":{\"x-ti-app-id\":{\"type\":\"apiKey\",\"description\":\"请[登录Textin](https://www.textin.com/console/dashboard/setting)后前往 \\\"工作台-账号设置-开发者信息\\\" 查看 x-ti-app-id\"},\"x-ti-secret-code\":{\"type\":\"apiKey\",\"description\":\"请[登录Textin](https://www.textin.com/console/dashboard/setting)后前往 \\\"工作台-账号设置-开发者信息\\\" 查看 x-ti-secret-code\"}},\"cookie\":{}}}],\"parameters\":{\"path\":{},\"query\":{\"pdf_pwd\":{\"schema\":[{\"type\":\"string\",\"required\":false,\"description\":\"当pdf为加密文档时，需要提供密码。 \\n备注：对前端封装该接口时，需要自行对密码进行安全防护\\n\"}]},\"page_start\":{\"schema\":[{\"type\":\"integer\",\"required\":false,\"description\":\"当上传的是pdf时，表示从第几页开始解析，不传该参数时默认从首页开始\\n\",\"default\":0}]},\"page_count\":{\"schema\":[{\"type\":\"integer\",\"required\":false,\"description\":\"当上传的是pdf时，page_count 表示要进行转换的pdf页数，总页数不得超过1000页，默认为1000页\\n\",\"default\":1000}]},\"parse_mode\":{\"schema\":[{\"type\":\"enum\u003cstring\u003e\",\"enum\":[\"auto\",\"scan\"],\"required\":false,\"description\":\"pdf文档的解析模式，默认为scan模式。图片不用设置，均默认按scan模式处理。\\n- auto 综合文字识别和解析模式：对pdf电子档解析，会直接提取pdf中的文字\\n- scan 仅按文字识别模式：将pdf当成图片处理\\n\",\"default\":\"scan\"}]},\"dpi\":{\"schema\":[{\"type\":\"enum\u003cinteger\u003e\",\"enum\":[72,144,216],\"required\":false,\"description\":\"pdf文档的坐标基准，默认144dpi，与parse_mode参数联动：\\n- 当parse_mode=auto时，默认动态，支持72，144，216；\\n- 当parse_mode=scan时，默认144，支持72，144，216；\\n\",\"default\":144}]},\"apply_document_tree\":{\"schema\":[{\"type\":\"enum\u003cinteger\u003e\",\"enum\":[0,1],\"required\":false,\"description\":\"markdown中是否生成标题层级，默认为1，生成标题。\\n- 0 不生成标题，同时也不会返回catalog字段\\n- 1 生成标题\\n\",\"default\":1}]},\"table_flavor\":{\"schema\":[{\"type\":\"enum\u003cstring\u003e\",\"enum\":[\"md\",\"html\",\"none\"],\"required\":false,\"description\":\"markdown里的表格格式，默认为html，按html语法输出表格 \\n- md 按md语法输出表格\\n- html 按html语法输出表格\\n- none 不进行表格识别，把表格图像当成普通文字段落来识别\\n\",\"default\":\"html\"}]},\"get_image\":{\"schema\":[{\"type\":\"enum\u003cstring\u003e\",\"enum\":[\"none\",\"page\",\"objects\",\"both\"],\"required\":false,\"description\":\"获取markdown里的图片，默认为none，不返回任何图像\\n- none 不返回任何图像\\n- page 返回每一页的整页图像：即pdf页的完整页图片   \\n- objects 返回页面内的子图像：即pdf页内的各个子图片\\n- both 返回整页图像和图像对象\\n\",\"default\":\"objects\"}]},\"image_output_type\":{\"schema\":[{\"type\":\"enum\u003cstring\u003e\",\"enum\":[\"base64str\",\"default\"],\"required\":false,\"description\":\"指定引擎返回的图片对象输出类型，默认返回子图片url和页图片id\\n- base64str 指定所有图片对象为base64字符串，适用于没有云存储的用户，但是引擎返回结果体积会很大。识别页数page_count超过1000页时，不支持base64返回，只会以default格式返回。\\n- default 指定子图片对象为图片url,页图片对象为图片id\\n\",\"default\":\"default\"}]},\"paratext_mode\":{\"schema\":[{\"type\":\"enum\u003cstring\u003e\",\"enum\":[\"none\",\"annotation\",\"body\"],\"required\":false,\"description\":\"markdown中非正文文本内容展示模式。默认为annotation。非正文内容包括页眉页脚，子图中的文本。\\n- none 不展示\\n- annotation 以注释格式插入到markdown中。页眉页脚中的图片只保留文本，图片base64或url不保留。\\n- body 以正文格式插入到markdown中\\n\"}]},\"formula_level\":{\"schema\":[{\"type\":\"enum\u003cinteger\u003e\",\"enum\":[0,1,2],\"required\":false,\"description\":\"公式识别等级，默认为0，全识别。\\n- 0 全识别\\n- 1 仅识别行间公式，行内公式不识别\\n- 2 不识别\\n\",\"default\":0}]},\"apply_merge\":{\"schema\":[{\"type\":\"enum\u003cinteger\u003e\",\"enum\":[0,1],\"required\":false,\"description\":\"是否进行段落合并和表格合并。默认为1，合并段落和表格。\\n- 0 不合并\\n- 1 合并\\n\",\"default\":1}]},\"markdown_details\":{\"schema\":[{\"type\":\"enum\u003cinteger\u003e\",\"enum\":[0,1],\"required\":false,\"description\":\"是否返回结果中的detail字段。默认为1，返回detail字段，保存markdown各类型元素的详细信息。\\n- 0 不生成\\n- 1 生成\\n\",\"default\":1}]},\"page_details\":{\"schema\":[{\"type\":\"enum\u003cinteger\u003e\",\"enum\":[0,1],\"required\":false,\"description\":\"是否返回结果中的pages字段。默认为1，返回pages字段，保存每一页更加详细的解析结果。\\n\",\"default\":1}]},\"raw_ocr\":{\"schema\":[{\"type\":\"enum\u003cinteger\u003e\",\"enum\":[0,1],\"required\":false,\"description\":\"是否返回全部文字识别结果(包含字符坐标信息)，结果字段为raw_ocr。默认为0，不返回。与page_details参数联动，当page_details为0或false时不返回。\\n- 0 不返回\\n- 1 返回\\n\",\"default\":0}]},\"char_details\":{\"schema\":[{\"type\":\"enum\u003cinteger\u003e\",\"enum\":[0,1],\"required\":false,\"description\":\"是否返回结果中的char_pos字段（保存每个字符的位置信息）和raw_ocr中的char_相关字段。默认为0，不返回。\\n- 0 不返回\\n- 1 返回\\n\",\"default\":0}]},\"catalog_details\":{\"schema\":[{\"type\":\"enum\u003cinteger\u003e\",\"enum\":[0,1],\"required\":false,\"description\":\"是否返回结果中的catalog字段，保存目录相关信息。与apply_document_tree参数联动，当apply_document_tree为0时不返回。\\n- 0 不返回\\n- 1 返回\\n\",\"default\":0}]},\"get_excel\":{\"schema\":[{\"type\":\"enum\u003cinteger\u003e\",\"enum\":[0,1],\"required\":false,\"description\":\"是否返回excel的base64结果，结果字段为excel_base64，可以根据该字段进行后处理保存excel文件。默认为0，不返回。\\n- 0 不返回\\n- 1 返回\\n\",\"default\":0}]},\"crop_dewarp\":{\"schema\":[{\"type\":\"enum\u003cinteger\u003e\",\"enum\":[0,1],\"required\":false,\"description\":\"是否进行切边矫正处理，默认为0，不进行切边矫正\\n- 0 不进行切边矫正\\n- 1 进行切边矫正\\n\",\"default\":0}]},\"remove_watermark\":{\"schema\":[{\"type\":\"enum\u003cinteger\u003e\",\"enum\":[0,1],\"required\":false,\"description\":\"是否进行去水印处理，默认为0，不去水印\\n- 0 不去水印\\n- 1 去水印\\n\",\"default\":0}]},\"apply_chart\":{\"schema\":[{\"type\":\"enum\u003cinteger\u003e\",\"enum\":[0,1],\"required\":false,\"description\":\"是否开启图表识别，开启图表识别会将识别到的图表以表格形式输出。默认为0，不进行图表识别。\\n- 0 不开启图表识别\\n- 1 开启图表识别\\n\",\"default\":0}]}},\"header\":{},\"cookie\":{}},\"body\":{\"application/octet-stream\":{\"schemaArray\":[{\"type\":\"file\",\"contentEncoding\":\"binary\",\"required\":true}],\"examples\":{\"example\":{}},\"description\":\"支持以下两种请求格式：\\n\\n1. Content-Type: application/octet-stream\\n\\n   支持的文件格式：png, jpg, jpeg, pdf, bmp, tiff, webp, doc, docx, html, mhtml, xls, xlsx, csv, ppt, pptx, txt, ofd, rtf。\\n   - 如果是xls/xlsx/csv文件，每个sheet行数不能超过2000，列数不能超过100。\\n   - 如果是txt文件，文件大小不超过100k。\\n   - 请求体为本地文件的二进制流，非 FormData 或其他格式。\\n   - 文件大小不超过500M。\\n   - 长宽比小于2的图片宽高需在20～20000像素范围内，其他图片的宽高需在20～10000像素范围内。\\n\\n2. Content-Type: text/plain\\n\\n   请求体为文本，内容为在线文件的URL链接（支持http以及https协议）。\\n   - 在线文件大小不超过500M。\\n   - 长宽比小于2的图片宽高需在20～20000像素范围内，其他图片的宽高需在20～10000像素范围内。\\n\"},\"text/plain\":{\"schemaArray\":[{\"type\":\"string\",\"required\":true}],\"examples\":{\"example\":{\"value\":\"\u003cstring\u003e\"}},\"description\":\"支持以下两种请求格式：\\n\\n1. Content-Type: application/octet-stream\\n\\n   支持的文件格式：png, jpg, jpeg, pdf, bmp, tiff, webp, doc, docx, html, mhtml, xls, xlsx, csv, ppt, pptx, txt, ofd, rtf。\\n   - 如果是xls/xlsx/csv文件，每个sheet行数不能超过2000，列数不能超过100。\\n   - 如果是txt文件，文件大小不超过100k。\\n   - 请求体为本地文件的二进制流，非 FormData 或其他格式。\\n   - 文件大小不超过500M。\\n   - 长宽比小于2的图片宽高需在20～20000像素范围内，其他图片的宽高需在20～10000像素范围内。\\n\\n2. Content-Type: text/plain\\n\\n   请求体为文本，内容为在线文件的URL链接（支持http以及https协议）。\\n   - 在线文件大小不超过500M。\\n   - 长宽比小于2的图片宽高需在20～20000像素范围内，其他图片的宽高需在20～10000像素范围内。\\n\"}}},\"response\":{\"200\":{\"application/json\":{\"schemaArray\":[{\"type\":\"object\",\"properties\":{\"code\":{\"allOf\":[{\"type\":\"integer\",\"default\":200,\"description\":\"状态码\\n- 200: Success\\n- 40101: x-ti-app-id 或 x-ti-secret-code 为空\\n- 40102: x-ti-app-id 或 x-ti-secret-code 无效，验证失败\\n- 40103: 客户端IP不在白名单\\n- 40003: 余额不足，请充值后再使用\\n- 40004: 参数错误，请查看技术文档，检查传参\\n- 40007: 机器人不存在或未发布\\n- 40008: 机器人未开通，请至市场开通后重试\\n- 40301: 图片类型不支持\\n- 40302: 上传文件大小不符，文件大小不超过 500M\\n- 40303: 文件类型不支持，接口会返回实际检测到的文件类型，如“当前文件类型为.gif”\\n- 40304\\t图片尺寸不符，长宽比小于2的图片宽高需在20～20000像素范围内，其他图片的宽高需在20～10000像素范围内\\n- 40305\\t识别文件未上传\\n- 40422\\t文件损坏（The file is corrupted.）\\n- 40423\\tPDF密码错误（Password required or incorrect password.）\\n- 40424\\t页数设置超出文件范围（Page number out of range.）\\n- 40425\\t文件格式不支持（The input file format is not supported.）\\n- 40427\\tDPI参数不在支持列表中（Input DPI is not in the allowed DPIs list(72,144,216).）\\n- 40428\\tword和ppt转pdf失败或者超时（Process office file failed.）\\n- 50207\\t部分页面解析失败（Partial failed）\\n- 40400\\t无效的请求链接，请检查链接是否正确\\n- 30203\\t基础服务故障，请稍后重试\\n- 500\\t服务器内部错误\\n\",\"enum\":[200,40101,40102,40103,40003,40004,40007,40008,40301,40302,40303,40304,40305,40422,40423,40424,40425,40427,40428,50207,40400,30203,500]}]},\"message\":{\"allOf\":[{\"type\":\"string\",\"description\":\"错误信息\",\"example\":\"success\"}]},\"result\":{\"allOf\":[{\"required\":[\"markdown\"],\"type\":\"object\",\"properties\":{\"markdown\":{\"type\":\"string\",\"description\":\"完整 markdown 正文文本。\",\"example\":\"# hello markdown\"},\"detail\":{\"type\":\"array\",\"description\":\"markdown分块后各类型元素详细信息， 入参markdown_details=1时返回（1式返回（默认））\",\"items\":{\"$ref\":\"#/components/schemas/markdown_details\"}},\"pages\":{\"type\":\"array\",\"description\":\"文档按页为单位展开时，存储每一页的详情和状态（适用于PDF），部分信息与metrics字段重复。入参page_details=1式返回（默认）\",\"items\":{\"$ref\":\"#/components/schemas/pages_detail\"}},\"catalog\":{\"type\":\"object\",\"description\":\"目录树结构。受参数catalog_details和apply_document_tree影响。\",\"properties\":{\"toc\":{\"type\":\"array\",\"description\":\"返回的table of contents\\n\",\"items\":{\"type\":\"object\",\"properties\":{\"sub_type\":{\"type\":\"string\",\"description\":\"标题类型 text_title、 image_title、 table_title\"},\"hierarchy\":{\"type\":\"integer\",\"description\":\"标题层级， 1 是 1级标题, 2 是 2级标题，依次类推\"},\"title\":{\"type\":\"string\",\"description\":\"标题内容\"},\"page_id\":{\"type\":\"integer\",\"description\":\"标题所在页码 （最小页码为 1)\"},\"pos\":{\"type\":\"array\",\"description\":\"目录区域的四个角点坐标，依次为左上，右上，右下，左下\",\"maxItems\":8,\"minItems\":8,\"items\":{\"type\":\"integer\",\"example\":[10,10,100,10,100,50,10,50]}}},\"required\":[\"hierarchy\",\"title\",\"page_id\",\"pos\"],\"example\":[{\"hierarchy\":2,\"title\":\"1.公司简介和主要财务指标\",\"page_id\":3,\"pos\":[10,10,100,10,100,50,10,50]},{\"hierarchy\":3,\"title\":\"1.1 公司简介\",\"page_id\":4,\"pos\":[10,10,100,10,100,50,10,50]}]}}}},\"total_page_number\":{\"type\":\"integer\",\"description\":\"输入PDF时， 返回文档的总页数\",\"example\":10},\"valid_page_number\":{\"type\":\"integer\",\"description\":\"记录本次解析成功的总页数\",\"example\":3},\"excel_base64\":{\"type\":\"string\",\"description\":\"excel的base64结果，仅当get_excel=1时返回。\",\"example\":\"\"}}}]},\"version\":{\"allOf\":[{\"type\":\"string\",\"description\":\"doc_restore 引擎版本号\",\"example\":\"2.1.0\"}]},\"duration\":{\"allOf\":[{\"type\":\"integer\",\"description\":\"引擎耗时 （毫秒）\",\"example\":999}]},\"metrics\":{\"allOf\":[{\"type\":\"array\",\"description\":\"每一页的信息\",\"items\":{\"type\":\"object\",\"description\":\"状态情况\",\"properties\":{\"page_image_width\":{\"type\":\"integer\",\"description\":\"当前段落所在页的图片宽或者pdf转成的图片宽\",\"example\":1024},\"page_image_height\":{\"type\":\"integer\",\"description\":\"当前段落所在页的图片高或者pdf转成的图片高\",\"example\":768},\"dpi\":{\"type\":\"integer\",\"description\":\"当前pdf页转成图片所用的dpi\",\"example\":72},\"durations\":{\"description\":\"当前页总耗时\",\"type\":\"number\",\"format\":\"float\"},\"status\":{\"description\":\"当前页状态\",\"type\":\"string\"},\"page_id\":{\"description\":\"当前页码\",\"type\":\"number\"},\"angle\":{\"type\":\"integer\",\"description\":\"图像角度，\\n定义0度为人类阅读文字的图像方向，称为正置图像，\\n本字段表示输入图像是正置图像进行顺时针若干角度的旋转所得。\\n- 0: ▲\\n- 90: ▶\\n- 180: ▼\\n- 270: ◀\\n\",\"enum\":[0,90,180,270],\"example\":90},\"image_id\":{\"description\":\"当前页图片id （下载方式：https://api.textin.com/ocr_image/download?image_id=xxx, 需要在headers里添加appid和key, 有效期30天） \\\\\\n例如使用curl下载: \\\\\\ncurl 'https://api.textin.com/ocr_image/download?image_id=xxx' \\\\\\n--header 'x-ti-app-id: c81f*************************e9ff' \\\\\\n--header 'x-ti-secret-code: 5508***********************1c17'\\n\",\"type\":\"string\"}},\"required\":[\"page_image_width\",\"page_image_height\",\"durations\",\"status\",\"page_id\",\"angle\"]}}]}},\"description\":\"返回markdown及结构化数据\",\"refIdentifier\":\"#/components/schemas/image_to_markdown_output\",\"requiredProperties\":[\"code\",\"message\",\"result\",\"version\",\"duration\",\"metrics\"]}],\"examples\":{\"example\":{\"value\":{\"code\":200,\"message\":\"success\",\"result\":{\"markdown\":\"# hello markdown\",\"detail\":[{\"page_id\":1,\"paragraph_id\":123,\"outline_level\":-1,\"text\":\"hello markdown\",\"position\":[217,390,1336,390,1336,460,217,460],\"origin_position\":[217,390,1336,390,1336,460,217,460],\"content\":0,\"type\":\"paragraph\",\"sub_type\":\"catalog\",\"image_url\":\"\u003cstring\u003e\",\"tags\":[\"formula\",\"handwritten\"],\"caption_id\":{\"page_id\":123,\"paragraph_id\":123},\"cells\":[{\"row\":123,\"col\":123,\"row_span\":123,\"col_span\":123,\"position\":[10,10,100,10,100,50,10,50],\"origin_position\":[123],\"text\":\"\u003cstring\u003e\",\"type\":\"\u003cstring\u003e\"}],\"split_section_page_ids\":[1,2,3],\"split_section_positions\":[[0,0,100,100,100,200,0,200],[0,0,100,100,100,200,0,200],[0,0,100,100,100,200,0,200]]}],\"pages\":[{\"status\":\"success\",\"page_id\":0,\"durations\":612.5,\"image_id\":\"90u12adcad08r2\",\"origin_image_id\":\"90u12adcad08r2\",\"base64\":\"\u003cstring\u003e\",\"origin_base64\":\"\u003cstring\u003e\",\"width\":123,\"height\":123,\"angle\":123,\"content\":[{\"id\":123,\"type\":\"line\",\"text\":\"\u003cstring\u003e\",\"angle\":0,\"pos\":[123],\"origin_position\":[123],\"sub_type\":\"handwriting\",\"direction\":123,\"score\":0.5,\"char_pos\":[[123]]}],\"raw_ocr\":[{\"text\":\"这是一个例子。\",\"score\":0.99,\"type\":\"text\",\"position\":[10,10,100,10,100,50,10,50],\"angle\":123,\"direction\":1,\"handwritten\":1,\"char_scores\":[0.99,0.98,0.95,0.95,0.99,0.93,0.87],\"char_centers\":[[20,10],[30,10],[40,10],[50,10],[60,10],[70,10],[80,10]],\"char_positions\":[[18,8,22,8,22,12,18,12],[28,88,32,8,32,12,28,12],[38,88,42,8,42,12,38,12],[48,88,52,8,52,12,48,12],[58,88,62,8,62,12,58,12],[68,88,72,8,72,12,68,12],[78,88,82,8,82,12,78,12]],\"char_candidates\":[[\"这\"],[\"是\"],[\"一\",\"-\"],[\"个\"],[\"例\"],[\"子\"],[\"。\",\"O\"]],\"char_candidates_score\":[[0.99],[0.99],[0.95,0.05],[0.99],[0.99],[0.99],[0.89,0.11]]}],\"structured\":[{\"type\":\"textblock\",\"pos\":[123],\"origin_position\":[123],\"content\":[0,1,2],\"sub_type\":\"text\",\"continue\":true,\"next_page_id\":2,\"next_para_id\":1,\"text\":\"\u003cstring\u003e\",\"outline_level\":123}]}],\"catalog\":{\"toc\":[[{\"hierarchy\":2,\"title\":\"1.公司简介和主要财务指标\",\"page_id\":3,\"pos\":[10,10,100,10,100,50,10,50]},{\"hierarchy\":3,\"title\":\"1.1 公司简介\",\"page_id\":4,\"pos\":[10,10,100,10,100,50,10,50]}]]},\"total_page_number\":10,\"valid_page_number\":3,\"excel_base64\":\"\"},\"version\":\"2.1.0\",\"duration\":999,\"metrics\":[{\"page_image_width\":1024,\"page_image_height\":768,\"dpi\":72,\"durations\":123,\"status\":\"\u003cstring\u003e\",\"page_id\":123,\"angle\":90,\"image_id\":\"\u003cstring\u003e\"}]}}},\"description\":\"解析结果\"}}},\"deprecated\":false,\"type\":\"path\"},\"metadata\":{\"id\":325765,\"subdomain\":\"textin\",\"filename\":\"parse-1.0.1.openapi\",\"eTag\":\"\\\"fac26eb3313c4823fe26664c566d0d0e\\\"\",\"location\":null,\"originalFileLocation\":\"api-reference/parse-1.0.1.openapi.yml\",\"uploadId\":null,\"uuid\":\"8087bda6-2d2c-451b-8d28-4df814c190e6\",\"versionId\":null,\"source\":\"LOCAL_FILE\",\"createdAt\":\"2025-07-24T08:32:11.769Z\",\"updatedAt\":\"2025-07-24T08:51:04.802Z\",\"deletedAt\":null},\"componentSchemas\":{\"pages_detail\":{\"type\":\"object\",\"description\":\"每一页的详细信息\",\"properties\":{\"status\":{\"description\":\"表示当前页的引擎输出状态，或者error_message\",\"type\":\"string\",\"example\":\"success\"},\"page_id\":{\"description\":\"当前页码 (若为流式文件, 页码置为0)\",\"type\":\"number\",\"example\":0},\"durations\":{\"description\":\"当前页总耗时\",\"type\":\"number\",\"format\":\"float\",\"example\":612.5},\"image_id\":{\"description\":\"当前页图片id （下载方式：https://api.textin.com/ocr_image/download?image_id=xxx ,需要在headers里添加appid和key）。当输入参数image_output_type=default且get_image=page/both时返回。\\n例如使用curl下载\\\\\\ncurl 'https://api.textin.com/ocr_image/download?image_id=xxx' \\\\\\n--header 'x-ti-app-id: c81f*************************e9ff' \\\\\\n--header 'x-ti-secret-code: 5508********************1c17'\\n\",\"type\":\"string\",\"example\":\"90u12adcad08r2\"},\"origin_image_id\":{\"description\":\"切边或去水印前的原始页图片，仅当开启切边或去水印，image_output_type=default且get_image=page/both时返回。下载方式同image_id\\n\",\"type\":\"string\",\"example\":\"90u12adcad08r2\"},\"base64\":{\"description\":\"当前页图片的base64字符串，当输入参数image_output_type=base64str且get_image=page/both时返回。\\n\",\"type\":\"string\"},\"origin_base64\":{\"description\":\"切边或去水印前的原始页图片base64字符串，仅当开启切边或去水印，image_output_type=base64str且get_image=page/both时返回\\n\",\"type\":\"string\"},\"width\":{\"type\":\"integer\",\"description\":\"文档页宽度\"},\"height\":{\"type\":\"integer\",\"description\":\"文档页高度\"},\"angle\":{\"type\":\"integer\",\"description\":\"图像（中文字）的角度（当输入为图像时，默认为0， 可选值0, 90, 180, 270)\"},\"content\":{\"type\":\"array\",\"description\":\"基础数据: 文字行, 图像中的其中一种，请参考textline和image的说明\",\"items\":{\"oneOf\":[{\"$ref\":\"#/components/schemas/textline\"},{\"$ref\":\"#/components/schemas/image\"}]}},\"raw_ocr\":{\"type\":\"array\",\"description\":\"全部文字识别结果，只包含文字结果。受URL参数page_details和raw_ocr影响，默认不返回\",\"items\":{\"$ref\":\"#/components/schemas/ocrline\"}},\"structured\":{\"type\":\"array\",\"description\":\"结构化数据, 为textblock, table, imageblock, footer, header中的一种\",\"items\":{\"oneOf\":[{\"$ref\":\"#/components/schemas/textblock\"},{\"$ref\":\"#/components/schemas/table\"},{\"$ref\":\"#/components/schemas/imageblock\"},{\"$ref\":\"#/components/schemas/footer\"},{\"$ref\":\"#/components/schemas/header\"}]}}}},\"markdown_details\":{\"type\":\"object\",\"description\":\"markdown各类型元素详细信息\",\"properties\":{\"page_id\":{\"type\":\"integer\",\"description\":\"当前元素所在页码，例如”1”\",\"example\":1},\"paragraph_id\":{\"type\":\"integer\",\"description\":\"当前元素id\"},\"outline_level\":{\"type\":\"integer\",\"description\":\"标题级别(最多支持5级标题) -1表示正文，0表示一级标题，1表示二级标题 …\\n\",\"default\":-1,\"enum\":[-1,0,1,2,3,4]},\"text\":{\"type\":\"string\",\"description\":\"文本\",\"example\":\"hello markdown\"},\"position\":{\"type\":\"array\",\"description\":\"以长度为8的整型数组表示四边形，8个数两两一组为一个点的横纵坐标，分别是左上，右上，右下，左下。\\n当输入是PDF时, 此坐标是基于72dpi的;当输入是图片时，此坐标是原图里的坐标。\\n单位：像素\\n\",\"items\":{\"type\":\"integer\"},\"example\":[217,390,1336,390,1336,460,217,460]},\"origin_position\":{\"type\":\"array\",\"description\":\"仅当打开切边时返回，表示该段落在原图中的坐标。格式同position。\\n\",\"items\":{\"type\":\"integer\"},\"example\":[217,390,1336,390,1336,460,217,460]},\"content\":{\"type\":\"integer\",\"description\":\"内容类型\\n- 0 正文(段落、图片、表格)\\n- 1 非正文(页眉、页脚、侧边栏)\\n\",\"default\":0,\"enum\":[\"0 正文(段落、图片、表格)\",\"1 非正文(页眉、页脚、侧边栏)\"]},\"type\":{\"type\":\"string\",\"description\":\"类型, paragraph（段落类型，包括正文、标题、公式等文字信息）、image（图片类型）、table（表格类型）\",\"default\":\"paragraph\",\"enum\":[\"image\",\"table\",\"paragraph\"]},\"sub_type\":{\"type\":\"string\",\"description\":\"子类型。当type为paragraph时，取值范围为catalog(目录),header(页眉),footer(页脚),sidebar(侧边栏),text(正文普通文本),text_title(文本标题),image_title(图片标题),table_title(表格标题)；当type是image时，取值范围为stamp(印章),chart(图表),qrcode(二维码),barcode(条形码)；当type为table时，取值范围为bordered(有线表), borderless(无线表)。\\n\",\"example\":\"catalog\",\"enum\":[\"catalog\",\"header\",\"footer\",\"sidebar\",\"text\",\"text_title\",\"image_title\",\"table_title\",\"stamp\",\"chart\",\"qrcode\",\"barcode\",\"bordered\",\"borderless\"]},\"image_url\":{\"type\":\"string\",\"description\":\"图片链接，仅在type为image时返回，当get_image = objects 时，返回图片的公共连接,图片默认保存30天，如需长久保存，请在有效期内下载图片并保存;或者使用image_output_type=base64str,图片以base64的方式返回\"},\"tags\":{\"type\":\"array\",\"description\":\"表示段落内是否存在特殊文本，类型包括公式formula和手写体handwritten\",\"items\":{\"type\":\"string\"},\"example\":[\"formula\",\"handwritten\"]},\"caption_id\":{\"type\":\"object\",\"description\":\"表格或图片的标题id，仅在type为image或table时返回\",\"properties\":{\"page_id\":{\"type\":\"integer\",\"description\":\"标题所在页码\"},\"paragraph_id\":{\"type\":\"integer\",\"description\":\"标题所在段落id\"}}},\"cells\":{\"type\":\"array\",\"description\":\"单元格数组, 仅在type为table时返回\",\"items\":{\"type\":\"object\",\"description\":\"单元格数据\",\"required\":[\"row\",\"col\"],\"properties\":{\"row\":{\"type\":\"integer\",\"description\":\"单元格行号\"},\"col\":{\"type\":\"integer\",\"description\":\"单元格列号\"},\"row_span\":{\"type\":\"integer\",\"description\":\"单元格行跨度,默认为1\"},\"col_span\":{\"type\":\"integer\",\"description\":\"单元格列跨度,默认为1\"},\"position\":{\"type\":\"array\",\"description\":\"单元格的四个角点坐标，依次为左上，右上，右下，左下\",\"maxItems\":8,\"minItems\":8,\"items\":{\"type\":\"integer\"},\"example\":[10,10,100,10,100,50,10,50]},\"origin_position\":{\"type\":\"array\",\"description\":\"受URL参数切边矫正或去水印影响，仅当打开切边或去水印时返回，表示该单元格在原图中的坐标，格式同position。\\n\",\"items\":{\"type\":\"integer\"}},\"text\":{\"type\":\"string\",\"description\":\"单元格文本内容\"},\"type\":{\"type\":\"string\",\"description\":\"类型，固定为cell，表示单元格\"}}}},\"split_section_page_ids\":{\"type\":\"array\",\"description\":\"当表格/段落有合并时，记录合并前各个子表格/段落所在的页的id\",\"example\":[1,2,3],\"items\":{\"type\":\"integer\"}},\"split_section_positions\":{\"type\":\"array\",\"description\":\"当表格/段落有合并时，记录合并前各个子表格/段落所在页的位置，位置所属的页码与split_section_page_ids按索引一一对应，如split_section_positions[2]所属的页码为split_section_page_ids[2]\",\"items\":{\"type\":\"array\",\"items\":{\"maxItems\":8,\"minItems\":8,\"type\":\"integer\"}},\"example\":[[0,0,100,100,100,200,0,200],[0,0,100,100,100,200,0,200],[0,0,100,100,100,200,0,200]]}},\"required\":[\"page_id\",\"paragraph_id\",\"outline_level\",\"text\",\"type\",\"content\",\"position\"]},\"textline\":{\"type\":\"object\",\"description\":\"文本行数据\",\"required\":[\"id\",\"type\",\"pos\",\"text\"],\"properties\":{\"id\":{\"type\":\"integer\",\"description\":\"数据id(页内唯一)\"},\"type\":{\"type\":\"string\",\"description\":\"数据类型,line\",\"enum\":[\"line\",\"image\"],\"example\":\"line\"},\"text\":{\"type\":\"string\",\"description\":\"文本行文字内容, 当sub_type=stamp时， text为印章上的文字.\"},\"angle\":{\"type\":\"integer\",\"description\":\"文本行文字方向, 默认为0(angle为0时， json中可能不包含angle属性).\",\"enum\":[0,90,180,270]},\"pos\":{\"type\":\"array\",\"description\":\"文本行四个角点坐标\",\"maxItems\":8,\"minItems\":8,\"items\":{\"type\":\"integer\"}},\"origin_position\":{\"type\":\"array\",\"description\":\"仅当打开切边时返回，表示文本行在原图中的坐标。格式同pos。\\n\",\"items\":{\"type\":\"integer\"}},\"sub_type\":{\"type\":\"string\",\"description\":\"子类型, 当type为line时，有handwriting, formula; 当type为image, 取值范围为stamp(印章),chart(图表),qrcode(二维码),barcode(条形码)\",\"enum\":[\"handwriting\",\"formula\",\"stamp\",\"chart\",\"qrcode\",\"barcode\"]},\"direction\":{\"type\":\"integer\",\"description\":\"文字方向, 默认为0. 0:横向文本; 1:竖向文本; 2:横向右往左文本（如阿拉伯语）\"},\"score\":{\"type\":\"number\",\"description\":\"文本行内每个字符的置信度(仅当输入图像做ocr时)\",\"maximum\":1,\"minimum\":0},\"char_pos\":{\"type\":\"array\",\"description\":\"文本行内每个字符的坐标,每个item是一个由八个整数组成的数组，分别表示，左上，右上，右下，左下四个点的（x,y)坐标\",\"items\":{\"type\":\"array\",\"maxItems\":8,\"minItems\":8,\"items\":{\"type\":\"integer\"}}}}},\"image\":{\"type\":\"object\",\"description\":\"图像数据\",\"required\":[\"id\",\"type\",\"pos\",\"data\"],\"properties\":{\"id\":{\"type\":\"integer\",\"description\":\"数据id\"},\"type\":{\"type\":\"string\",\"description\":\"数据类型, image\",\"example\":\"image\"},\"pos\":{\"type\":\"array\",\"description\":\"图像四个角点坐标\",\"maxItems\":8,\"minItems\":8,\"items\":{\"type\":\"integer\"}},\"sub_type\":{\"type\":\"string\",\"description\":\"子类型, 包括stamp, chart, qrcode, barcode\"},\"size\":{\"description\":\"图像大小[width, height]\",\"type\":\"array\",\"maxItems\":2,\"minItems\":2,\"items\":{\"type\":\"integer\"}},\"data\":{\"type\":\"object\",\"description\":\"图像内容\",\"$ref\":\"#/components/schemas/imagedata\"}}},\"imageblock\":{\"type\":\"object\",\"description\":\"图像块\",\"required\":[\"type\",\"pos\",\"content\"],\"properties\":{\"type\":{\"description\":\"图像块类型， 值为 image\",\"type\":\"string\",\"example\":\"image\"},\"pos\":{\"type\":\"array\",\"description\":\"文本行四个角点坐标\",\"maxItems\":8,\"minItems\":8,\"items\":{\"type\":\"integer\"}},\"origin_position\":{\"type\":\"array\",\"description\":\"仅当打开切边时返回，表示该子图在原图中的坐标。格式同pos。\\n\",\"items\":{\"type\":\"integer\"}},\"lines\":{\"type\":\"array\",\"description\":\"图像包含的文本行id,\",\"items\":{\"type\":\"integer\"},\"example\":[1]},\"content\":{\"type\":\"array\",\"items\":{\"type\":\"integer\"},\"description\":\"图像资源数据id数组\",\"maxItems\":1},\"caption_id\":{\"type\":\"object\",\"description\":\"图片的标题id\",\"properties\":{\"page_id\":{\"type\":\"integer\",\"description\":\"标题所在页码\"},\"paragraph_id\":{\"type\":\"integer\",\"description\":\"标题所在段落id\"}}},\"text\":{\"type\":\"string\",\"description\":\"子图片识别得到的文本内容\"}}},\"textblock\":{\"type\":\"object\",\"description\":\"段落块\",\"required\":[\"type\",\"pos\",\"content\"],\"properties\":{\"type\":{\"description\":\"段落块类型， 固定为 textblock\",\"type\":\"string\",\"example\":\"textblock\"},\"pos\":{\"type\":\"array\",\"description\":\"文本行四个角点\",\"maxItems\":8,\"minItems\":8,\"items\":{\"type\":\"integer\"}},\"origin_position\":{\"type\":\"array\",\"description\":\"仅当打开切边时返回，表示该段落在原图中的坐标。格式同pos。\\n\",\"items\":{\"type\":\"integer\"}},\"content\":{\"description\":\"段落块内文本行id数据组\",\"type\":\"array\",\"items\":{\"type\":\"integer\"},\"example\":[0,1,2]},\"sub_type\":{\"description\":\"段落块字类型，包括catalog(目录),text(正文普通文本),text_title(文本标题),image_title(图片标题),table_title(表格标题)\",\"type\":\"string\",\"enum\":[\"catalog\",\"text\",\"text_title\",\"image_title\",\"table_title\"],\"example\":\"text\"},\"continue\":{\"description\":\"段落块连续属性，用于判断完整的段落块是否被页面或栏分割，为true表示该段落块和下一个段落块连续（即两个段落块可合成一个逻辑段落块）。\",\"type\":\"boolean\",\"example\":true},\"next_page_id\":{\"description\":\"当且仅当continue为true时有值。表示下一个段落块的page_id。\",\"type\":\"integer\",\"example\":2},\"next_para_id\":{\"description\":\"当且仅当continue为true时有值。表示下一个段落块的paragraph_id。\",\"type\":\"integer\",\"example\":1},\"text\":{\"type\":\"string\",\"description\":\"段落块文本内容\"},\"outline_level\":{\"type\":\"integer\",\"description\":\"标题级别: (最多支持5级标题)\\n  -1.正文 0.一级标题 1.二级标题 ...\\n\",\"default\":-1}}},\"table\":{\"type\":\"object\",\"description\":\"表格块\",\"required\":[\"type\",\"pos\",\"rows\",\"cols\",\"columns_width\",\"rows_height\",\"cells\"],\"properties\":{\"type\":{\"description\":\"表格块类型, 固定为table\",\"type\":\"string\",\"example\":\"table\"},\"sub_type\":{\"description\":\"表格子属性，取值为bordered(有线表)或borderless(无线表)，默认为bordered(即json中无该字段时，默认值为bordered)\",\"type\":\"string\",\"enum\":[\"bordered\",\"borderless\"],\"example\":\"bordered\"},\"pos\":{\"type\":\"array\",\"description\":\"文本行四个角点坐标\",\"maxItems\":8,\"minItems\":8,\"items\":{\"type\":\"integer\"}},\"origin_position\":{\"type\":\"array\",\"description\":\"仅当打开切边时返回，表示该表格在原图中的坐标。格式同pos。\\n\",\"items\":{\"type\":\"integer\"}},\"rows\":{\"type\":\"integer\",\"description\":\"表格行数\"},\"cols\":{\"type\":\"integer\",\"description\":\"表格列数\"},\"columns_width\":{\"type\":\"array\",\"description\":\"表格列宽度列表\",\"items\":{\"type\":\"integer\"}},\"rows_height\":{\"type\":\"array\",\"description\":\"表格行高度列表\",\"items\":{\"type\":\"integer\"}},\"text\":{\"type\":\"string\",\"description\":\"表格文本内容，以html或md格式展示\"},\"continue\":{\"description\":\"当前表格与后一表格连续，用来判断一个表格是否被页面分割（如果 continue为true 且该表格位于本页结尾，该表格可与下一页开头表格组合为一个表格）\",\"type\":\"boolean\",\"example\":true},\"caption_id\":{\"type\":\"object\",\"description\":\"表格的标题id\",\"properties\":{\"page_id\":{\"type\":\"integer\",\"description\":\"标题所在页码\"},\"paragraph_id\":{\"type\":\"integer\",\"description\":\"标题所在段落id\"}}},\"cells\":{\"description\":\"单元格数组\",\"type\":\"array\",\"items\":{\"type\":\"object\",\"description\":\"单元格数据\",\"required\":[\"row\",\"col\"],\"properties\":{\"row\":{\"type\":\"integer\",\"description\":\"单元格行号\"},\"col\":{\"type\":\"integer\",\"description\":\"单元格列号\"},\"row_span\":{\"type\":\"integer\",\"description\":\"单元格行跨度,默认为1\"},\"col_span\":{\"type\":\"integer\",\"description\":\"单元格列跨度,默认为1\"},\"pos\":{\"type\":\"array\",\"description\":\"单元格的四个角点坐标，依次left-top,right-top,right-bottom,left-bottom.\",\"maxItems\":8,\"minItems\":8,\"items\":{\"type\":\"integer\"},\"example\":[10,10,100,10,100,50,10,50]},\"content\":{\"type\":\"array\",\"description\":\"单元格内容\",\"items\":{\"oneOf\":[{\"$ref\":\"#/components/schemas/textblock\"},{\"$ref\":\"#/components/schemas/imageblock\"}]}}}}}}},\"footer\":{\"type\":\"object\",\"description\":\"页脚\",\"required\":[\"type\",\"pos\",\"blocks\"],\"properties\":{\"type\":{\"description\":\"页脚块类型，固定为 footer\",\"type\":\"string\",\"example\":\"footer\"},\"pos\":{\"type\":\"array\",\"description\":\"文本行四个角点坐标\",\"maxItems\":8,\"minItems\":8,\"items\":{\"type\":\"integer\"}},\"blocks\":{\"type\":\"array\",\"description\":\"footer段落内容，为textblock, imageblock, table中其中的一种\",\"items\":{\"oneOf\":[{\"$ref\":\"#/components/schemas/textblock\"},{\"$ref\":\"#/components/schemas/imageblock\"},{\"$ref\":\"#/components/schemas/table\"}]}}}},\"header\":{\"type\":\"object\",\"description\":\"页眉\",\"required\":[\"type\",\"pos\",\"blocks\"],\"properties\":{\"type\":{\"description\":\"页眉块类型，固定为 header\",\"type\":\"string\",\"example\":\"header\"},\"pos\":{\"type\":\"array\",\"description\":\"文本行四个角点坐标\",\"maxItems\":8,\"minItems\":8,\"items\":{\"type\":\"integer\"}},\"blocks\":{\"type\":\"array\",\"description\":\"header段落内容，为textblock, imageblock, table中的其中一种\",\"items\":{\"oneOf\":[{\"$ref\":\"#/components/schemas/textblock\"},{\"$ref\":\"#/components/schemas/imageblock\"},{\"$ref\":\"#/components/schemas/table\"}]}}}},\"imagedata\":{\"type\":\"object\",\"description\":\"图像数据, 下面3种方式之一\",\"properties\":{\"base64\":{\"description\":\"图像文件(jpg, png)的base64字符串\",\"type\":\"string\"},\"region\":{\"description\":\"图像在页图像中的区域（四边形4个点坐标）\",\"type\":\"array\",\"maxItems\":8,\"minItems\":8,\"items\":{\"type\":\"integer\"}},\"path\":{\"description\":\"图像文件路径（如在zip包中的路径）\",\"type\":\"string\"}}},\"ocrline\":{\"type\":\"object\",\"description\":\"orc文字识别数据\",\"required\":[\"text\",\"score\",\"type\",\"position\",\"angle\",\"direction\",\"handwritten\"],\"properties\":{\"text\":{\"type\":\"string\",\"description\":\"识别内容字符串\\n\",\"example\":\"这是一个例子。\"},\"score\":{\"type\":\"number\",\"format\":\"float\",\"minimum\":0,\"maximum\":1,\"description\":\"识别置信度（0 \u003c= x \u003c= 1）\\n\",\"example\":0.99},\"type\":{\"type\":\"string\",\"description\":\"文本类型，用于表示文字的形态。\\n当前版本下，文本类型包括：\\n- text(文本)\\n- formula(公式)\\n\",\"enum\":[\"text\",\"formula\"],\"example\":\"text\"},\"position\":{\"type\":\"array\",\"description\":\"文本行的四个角点坐标，依次为左上，右上，右下，左下\",\"maxItems\":8,\"minItems\":8,\"items\":{\"type\":\"integer\"},\"example\":[10,10,100,10,100,50,10,50]},\"angle\":{\"type\":\"integer\",\"description\":\"图像（中文字）的角度（当输入为图像时，默认为0， 可选值0, 90, 180, 270)\"},\"direction\":{\"type\":\"integer\",\"description\":\"文字阅读方向。\\n- -1: 其他\\n- 0: 单字\\n- 1: 横向\\n- 2: 纵向\\n\",\"enum\":[-1,0,1,2],\"example\":1},\"handwritten\":{\"type\":\"integer\",\"description\":\"文字是否手写所得。\\n- -1: 未知\\n- 0: 非手写文字, 一般为印刷文字\\n- 1: 文字手写, 一般具备明显的书写特征\\n\",\"enum\":[-1,0,1],\"example\":1},\"char_scores\":{\"type\":\"array\",\"description\":\"字符置信度，值域范围0-1。\\n设置char_details=1时输出。\\n\",\"items\":{\"type\":\"number\",\"format\":\"float\",\"example\":0.98},\"example\":[0.99,0.98,0.95,0.95,0.99,0.93,0.87]},\"char_centers\":{\"type\":\"array\",\"description\":\"字符中心点。\\n设置character=1时输出。\\n\",\"items\":{\"type\":\"array\",\"minItems\":2,\"maxItems\":2,\"items\":{\"type\":\"integer\",\"example\":10}},\"example\":[[20,10],[30,10],[40,10],[50,10],[60,10],[70,10],[80,10]]},\"char_positions\":{\"type\":\"array\",\"description\":\"字符四边形点坐标，以顺时针构成闭合区域。\\n设置char_details=1时输出。\\n\",\"items\":{\"type\":\"array\",\"minItems\":8,\"maxItems\":8,\"items\":{\"type\":\"integer\",\"example\":18}},\"example\":[[18,8,22,8,22,12,18,12],[28,88,32,8,32,12,28,12],[38,88,42,8,42,12,38,12],[48,88,52,8,52,12,48,12],[58,88,62,8,62,12,58,12],[68,88,72,8,72,12,68,12],[78,88,82,8,82,12,78,12]]},\"char_candidates\":{\"type\":\"array\",\"description\":\"候选字数组，表示每一个字符的候选，与候选置信度配套使用。\\n设置char_details=1时输出。\\n\",\"items\":{\"type\":\"array\",\"items\":{\"type\":\"string\",\"example\":\"这\"},\"example\":[\"这\"]},\"example\":[[\"这\"],[\"是\"],[\"一\",\"-\"],[\"个\"],[\"例\"],[\"子\"],[\"。\",\"O\"]]},\"char_candidates_score\":{\"type\":\"array\",\"description\":\"候选字置信度数组，表示每一个候选字符的置信度，与候选字符配套使用。\\n设置char_details=1时输出。\\n\",\"items\":{\"type\":\"array\",\"items\":{\"type\":\"number\",\"format\":\"float\",\"example\":0.99}},\"example\":[[0.99],[0.99],[0.95,0.05],[0.99],[0.99],[0.99],[0.89,0.11]]}}}}}},\"children\":\"$L2a\"}]\n"])</script><script>self.__next_f.push([1,"2b:I[71197,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"3122\",\"static/chunks/3122-473c6d6ad707a1ff.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"7048\",\"static/chunks/7048-68b7efbe64e44ac4.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"3386\",\"static/chunks/3386-4ac6c5d6ac47f6b6.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"3698\",\"static/chunks/3698-586441bcd6c2501a.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"3818\",\"static/chunks/3818-b06f8c87a0bcb5ea.js\",\"3291\",\"static/chunks/3291-8e412928ddb5eb6a.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-82f1f84c31f01e84.js\"],\"PageProvider\",1]\n"])</script><script>self.__next_f.push([1,"2c:I[44760,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"3122\",\"static/chunks/3122-473c6d6ad707a1ff.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"7048\",\"static/chunks/7048-68b7efbe64e44ac4.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"3386\",\"static/chunks/3386-4ac6c5d6ac47f6b6.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"3698\",\"static/chunks/3698-586441bcd6c2501a.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"3818\",\"static/chunks/3818-b06f8c87a0bcb5ea.js\",\"3291\",\"static/chunks/3291-8e412928ddb5eb6a.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-82f1f84c31f01e84.js\"],\"\"]\n"])</script><script>self.__next_f.push([1,"2d:I[99543,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"3122\",\"static/chunks/3122-473c6d6ad707a1ff.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"7048\",\"static/chunks/7048-68b7efbe64e44ac4.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"3386\",\"static/chunks/3386-4ac6c5d6ac47f6b6.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"3698\",\"static/chunks/3698-586441bcd6c2501a.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"3818\",\"static/chunks/3818-b06f8c87a0bcb5ea.js\",\"3291\",\"static/chunks/3291-8e412928ddb5eb6a.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-82f1f84c31f01e84.js\"],\"FooterAndSidebarScrollScript\",1]\n"])</script><script>self.__next_f.push([1,"2e:I[35319,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"3122\",\"static/chunks/3122-473c6d6ad707a1ff.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"7048\",\"static/chunks/7048-68b7efbe64e44ac4.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"3386\",\"static/chunks/3386-4ac6c5d6ac47f6b6.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"3698\",\"static/chunks/3698-586441bcd6c2501a.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"3818\",\"static/chunks/3818-b06f8c87a0bcb5ea.js\",\"3291\",\"static/chunks/3291-8e412928ddb5eb6a.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-82f1f84c31f01e84.js\"],\"MDXContentProvider\",1]\n"])</script><script>self.__next_f.push([1,"2f:I[86022,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"3122\",\"static/chunks/3122-473c6d6ad707a1ff.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"7048\",\"static/chunks/7048-68b7efbe64e44ac4.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"3386\",\"static/chunks/3386-4ac6c5d6ac47f6b6.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"3698\",\"static/chunks/3698-586441bcd6c2501a.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"3818\",\"static/chunks/3818-b06f8c87a0bcb5ea.js\",\"3291\",\"static/chunks/3291-8e412928ddb5eb6a.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-82f1f84c31f01e84.js\"],\"ContainerWrapper\"]\n"])</script><script>self.__next_f.push([1,"30:I[79413,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"3122\",\"static/chunks/3122-473c6d6ad707a1ff.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"7048\",\"static/chunks/7048-68b7efbe64e44ac4.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"3386\",\"static/chunks/3386-4ac6c5d6ac47f6b6.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"3698\",\"static/chunks/3698-586441bcd6c2501a.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"3818\",\"static/chunks/3818-b06f8c87a0bcb5ea.js\",\"3291\",\"static/chunks/3291-8e412928ddb5eb6a.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-82f1f84c31f01e84.js\"],\"SidePanel\",1]\n"])</script><script>self.__next_f.push([1,"31:I[17552,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"3122\",\"static/chunks/3122-473c6d6ad707a1ff.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"7048\",\"static/chunks/7048-68b7efbe64e44ac4.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"3386\",\"static/chunks/3386-4ac6c5d6ac47f6b6.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"3698\",\"static/chunks/3698-586441bcd6c2501a.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"3818\",\"static/chunks/3818-b06f8c87a0bcb5ea.js\",\"3291\",\"static/chunks/3291-8e412928ddb5eb6a.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-82f1f84c31f01e84.js\"],\"PageHeader\",1]\n"])</script><script>self.__next_f.push([1,"32:I[98959,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"3122\",\"static/chunks/3122-473c6d6ad707a1ff.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"7048\",\"static/chunks/7048-68b7efbe64e44ac4.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"3386\",\"static/chunks/3386-4ac6c5d6ac47f6b6.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"3698\",\"static/chunks/3698-586441bcd6c2501a.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"3818\",\"static/chunks/3818-b06f8c87a0bcb5ea.js\",\"3291\",\"static/chunks/3291-8e412928ddb5eb6a.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-82f1f84c31f01e84.js\"],\"MdxPanel\",1]\n"])</script><script>self.__next_f.push([1,"33:I[32907,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"3122\",\"static/chunks/3122-473c6d6ad707a1ff.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"7048\",\"static/chunks/7048-68b7efbe64e44ac4.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"3386\",\"static/chunks/3386-4ac6c5d6ac47f6b6.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"3698\",\"static/chunks/3698-586441bcd6c2501a.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"3818\",\"static/chunks/3818-b06f8c87a0bcb5ea.js\",\"3291\",\"static/chunks/3291-8e412928ddb5eb6a.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-82f1f84c31f01e84.js\"],\"Api\",1]\n"])</script><script>self.__next_f.push([1,"34:I[26385,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"3122\",\"static/chunks/3122-473c6d6ad707a1ff.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"7048\",\"static/chunks/7048-68b7efbe64e44ac4.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"3386\",\"static/chunks/3386-4ac6c5d6ac47f6b6.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"3698\",\"static/chunks/3698-586441bcd6c2501a.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"3818\",\"static/chunks/3818-b06f8c87a0bcb5ea.js\",\"3291\",\"static/chunks/3291-8e412928ddb5eb6a.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-82f1f84c31f01e84.js\"],\"default\",1]\n"])</script><script>self.__next_f.push([1,"2a:[\"$\",\"$L2b\",null,{\"value\":{\"pageMetadata\":{\"title\":\"文档解析\",\"openapi\":\"api-reference/parse-1.0.1.openapi.yml post /ai/service/v1/pdf_to_markdown\",\"description\":\"上传图片/pdf/word/html/excel/ppt/txt，进行版面检测，文字识别，表格识别，版面分析等操作，并生成markdown文档及结构化数据\",\"href\":\"/api-reference/endpoint/parse\"},\"description\":{\"compiledSource\":\"\\\"use strict\\\";\\nconst {jsx: _jsx} = arguments[0];\\nconst {useMDXComponents: _provideComponents} = arguments[0];\\nfunction _createMdxContent(props) {\\n  const _components = {\\n    p: \\\"p\\\",\\n    ..._provideComponents(),\\n    ...props.components\\n  };\\n  return _jsx(_components.p, {\\n    children: \\\"上传图片/pdf/word/html/excel/ppt/txt，进行版面检测，文字识别，表格识别，版面分析等操作，并生成markdown文档及结构化数据\\\"\\n  });\\n}\\nfunction MDXContent(props = {}) {\\n  const {wrapper: MDXLayout} = {\\n    ..._provideComponents(),\\n    ...props.components\\n  };\\n  return MDXLayout ? _jsx(MDXLayout, {\\n    ...props,\\n    children: _jsx(_createMdxContent, {\\n      ...props\\n    })\\n  }) : _createMdxContent(props);\\n}\\nreturn {\\n  default: MDXContent\\n};\\n\",\"frontmatter\":{},\"scope\":{}},\"mdxExtracts\":{\"tableOfContents\":[],\"codeExamples\":{}},\"pageType\":\"$undefined\",\"panelMdxSource\":\"$undefined\",\"panelMdxSourceWithNoJs\":\"$undefined\"},\"children\":[[\"$\",\"$L2c\",null,{\"id\":\"_mintlify-page-mode-script\",\"strategy\":\"beforeInteractive\",\"dangerouslySetInnerHTML\":{\"__html\":\"document.documentElement.setAttribute('data-page-mode', 'none');\"}}],[\"$\",\"$L2d\",null,{\"theme\":\"mint\"}],[[\"$\",\"span\",null,{\"className\":\"fixed inset-0 bg-background-light dark:bg-background-dark -z-10 pointer-events-none\"}],null,false,false],[[\"$\",\"style\",\"0\",{\"dangerouslySetInnerHTML\":{\"__html\":\"/* 隐藏语言选择按钮 */\\n#navbar\\n  button[data-state=\\\"closed\\\"][aria-haspopup=\\\"menu\\\"][aria-expanded=\\\"false\\\"] {\\n  display: none;\\n}\\n\\n/* 隐藏广告 */\\n#footer \u003e :last-child {\\n  display: none;\\n}\\n\\n.p-4 .p-px \u003e .min-h-fit .overflow-auto {\\n  max-height: 60vh;\\n}\\n\"}}]],[],[[\"$\",\"$L2e\",\"api-reference/endpoint/parse\",{\"children\":[\"$\",\"$L2f\",null,{\"isCustom\":false,\"children\":[[\"$\",\"$L30\",null,{}],[\"$\",\"div\",null,{\"className\":\"relative grow box-border flex-col w-full mx-auto px-1 lg:pl-[23.7rem] lg:-ml-12 xl:w-[calc(100%-28rem)]\",\"id\":\"content-area\",\"children\":[[\"$\",\"$L31\",null,{}],[\"$\",\"$L32\",null,{\"mobile\":true}],[\"$\",\"$L33\",null,{}],[\"$\",\"div\",null,{\"className\":\"mdx-content relative mt-8 prose prose-gray dark:prose-invert\",\"data-page-title\":\"文档解析\",\"data-page-href\":\"/api-reference/endpoint/parse\",\"children\":[[\"$\",\"$L34\",null,{\"pageData\":{\"pageMetadata\":\"$2a:props:value:pageMetadata\",\"docsConfig\":{\"theme\":\"mint\",\"$schema\":\"https://mintlify.com/docs.json\",\"name\":\"Textin 智能文档解析\",\"colors\":{\"primary\":\"#1a66ff\",\"light\":\"#1a66ff\",\"dark\":\"#1a66ff\"},\"logo\":{\"light\":\"https://mintlify.s3.us-west-1.amazonaws.com/textin/logo/light.png\",\"dark\":\"https://mintlify.s3.us-west-1.amazonaws.com/textin/logo/dark.png\"},\"favicon\":\"/favicon.png\",\"api\":{\"playground\":{\"display\":\"simple\",\"proxy\":false}},\"navbar\":{\"links\":[{\"label\":\"资讯中心\",\"href\":\"https://www.textin.com/news/list\"},{\"label\":\"技术支持\",\"href\":\"https://www.textin.com/document/list\"}],\"primary\":{\"type\":\"button\",\"label\":\"快速体验\",\"href\":\"https://www.textin.com/console/recognition/robot_markdown?service=pdf_to_markdown\"}},\"navigation\":{\"global\":{\"languages\":[{\"language\":\"cn\",\"default\":true,\"href\":\"https://mintlify.com/docs\"},{\"language\":\"en\",\"href\":\"https://mintlify.com/docs\"}],\"anchors\":[]},\"tabs\":[{\"tab\":\"Guides\",\"groups\":[{\"group\":\"产品概览\",\"pages\":[\"xparse/overview\",\"xparse/api-key\",\"xparse/product-manual\"]},{\"group\":\"文档解析\",\"pages\":[\"xparse/parse-quickstart\",\"xparse/parse-getjson\",\"xparse/parse-gettable\",\"xparse/parse-getcatalog\",\"xparse/parse-getimage\",\"xparse/parse-getpos\",\"xparse/parse-max-workers\"]},{\"group\":\"文档抽取\",\"pages\":[\"xparse/extract-quickstart\",\"xparse/extract-getjson\",\"xparse/extract-gettable\",\"xparse/extract-getpos\",\"xparse/extract-asyncio\",\"xparse/extract-max-concurrent\"]}]},{\"tab\":\"API\",\"groups\":[{\"group\":\"智能文档解析\",\"pages\":[\"api-reference/endpoint/parse\",\"api-reference/endpoint/extract\"]}]}]},\"footer\":{\"socials\":{\"github\":\"https://github.com/intsig-textin\"}}},\"apiReferenceData\":\"$15:props:value:apiReferenceData\"},\"org\":{\"plan\":\"pro\",\"createdAt\":\"2025-07-08T05:52:18.531Z\"},\"mdxSource\":{\"compiledSource\":\"\\\"use strict\\\";\\nconst {Fragment: _Fragment, jsx: _jsx} = arguments[0];\\nconst {useMDXComponents: _provideComponents} = arguments[0];\\nfunction _createMdxContent(props) {\\n  return _jsx(_Fragment, {});\\n}\\nfunction MDXContent(props = {}) {\\n  const {wrapper: MDXLayout} = {\\n    ..._provideComponents(),\\n    ...props.components\\n  };\\n  return MDXLayout ? _jsx(MDXLayout, {\\n    ...props,\\n    children: _jsx(_createMdxContent, {\\n      ...props\\n    })\\n  }) : _createMdxContent(props);\\n}\\nreturn {\\n  default: MDXContent\\n};\\n\",\"frontmatter\":{},\"scope\":{\"config\":{\"theme\":\"mint\",\"$schema\":\"https://mintlify.com/docs.json\",\"name\":\"Textin 智能文档解析\",\"colors\":{\"primary\":\"#1a66ff\",\"light\":\"#1a66ff\",\"dark\":\"#1a66ff\"},\"logo\":{\"light\":\"https://mintlify.s3.us-west-1.amazonaws.com/textin/logo/light.png\",\"dark\":\"https://mintlify.s3.us-west-1.amazonaws.com/textin/logo/dark.png\"},\"favicon\":\"/favicon.png\",\"api\":{\"playground\":{\"display\":\"simple\",\"proxy\":false}},\"navbar\":{\"links\":[{\"label\":\"资讯中心\",\"href\":\"https://www.textin.com/news/list\"},{\"label\":\"技术支持\",\"href\":\"https://www.textin.com/document/list\"}],\"primary\":{\"type\":\"button\",\"label\":\"快速体验\",\"href\":\"https://www.textin.com/console/recognition/robot_markdown?service=pdf_to_markdown\"}},\"navigation\":{\"global\":{\"languages\":[{\"language\":\"cn\",\"default\":true,\"href\":\"https://mintlify.com/docs\"},{\"language\":\"en\",\"href\":\"https://mintlify.com/docs\"}],\"anchors\":[]},\"tabs\":[{\"tab\":\"Guides\",\"groups\":[{\"group\":\"产品概览\",\"pages\":[\"xparse/overview\",\"xparse/api-key\",\"xparse/product-manual\"]},{\"group\":\"文档解析\",\"pages\":[\"xparse/parse-quickstart\",\"xparse/parse-getjson\",\"xparse/parse-gettable\",\"xparse/parse-getcatalog\",\"xparse/parse-getimage\",\"xparse/parse-getpos\",\"xparse/parse-max-workers\"]},{\"group\":\"文档抽取\",\"pages\":[\"xparse/extract-quickstart\",\"xparse/extract-getjson\",\"xparse/extract-gettable\",\"xparse/extract-getpos\",\"xparse/extract-asyncio\",\"xparse/extract-max-concurrent\"]}]},{\"tab\":\"API\",\"groups\":[{\"group\":\"智能文档解析\",\"pages\":[\"api-reference/endpoint/parse\",\"api-reference/endpoint/extract\"]}]}]},\"footer\":{\"socials\":{\"github\":\"https://github.com/intsig-textin\"}}},\"pageMetadata\":{\"title\":\"文档解析\",\"openapi\":\"api-reference/parse-1.0.1.openapi.yml post /ai/service/v1/pdf_to_markdown\",\"description\":\"上传图片/pdf/word/html/excel/ppt/txt，进行版面检测，文字识别，表格识别，版面分析等操作，并生成markdown文档及结构化数据\",\"href\":\"/api-reference/endpoint/parse\"}}},\"mdxSourceWithNoJs\":{\"compiledSource\":\"\\\"use strict\\\";\\nconst {Fragment: _Fragment, jsx: _jsx} = arguments[0];\\nconst {useMDXComponents: _provideComponents} = arguments[0];\\nfunction _createMdxContent(props) {\\n  return _jsx(_Fragment, {});\\n}\\nfunction MDXContent(props = {}) {\\n  const {wrapper: MDXLayout} = {\\n    ..._provideComponents(),\\n    ...props.components\\n  };\\n  return MDXLayout ? _jsx(MDXLayout, {\\n    ...props,\\n    children: _jsx(_createMdxContent, {\\n      ...props\\n    })\\n  }) : _createMdxContent(props);\\n}\\nreturn {\\n  default: MDXContent\\n};\\n\",\"frontmatter\":{},\"scope\":{\"config\":{\"theme\":\"mint\",\"$schema\":\"https://mintlify.com/docs.json\",\"name\":\"Textin 智能文档解析\",\"colors\":{\"primary\":\"#1a66ff\",\"light\":\"#1a66ff\",\"dark\":\"#1a66ff\"},\"logo\":{\"light\":\"https://mintlify.s3.us-west-1.amazonaws.com/textin/logo/light.png\",\"dark\":\"https://mintlify.s3.us-west-1.amazonaws.com/textin/logo/dark.png\"},\"favicon\":\"/favicon.png\",\"api\":{\"playground\":{\"display\":\"simple\",\"proxy\":false}},\"navbar\":{\"links\":[{\"label\":\"资讯中心\",\"href\":\"https://www.textin.com/news/list\"},{\"label\":\"技术支持\",\"href\":\"https://www.textin.com/document/list\"}],\"primary\":{\"type\":\"button\",\"label\":\"快速体验\",\"href\":\"https://www.textin.com/console/recognition/robot_markdown?service=pdf_to_markdown\"}},\"navigation\":{\"global\":{\"languages\":[{\"language\":\"cn\",\"default\":true,\"href\":\"https://mintlify.com/docs\"},{\"language\":\"en\",\"href\":\"https://mintlify.com/docs\"}],\"anchors\":[]},\"tabs\":[{\"tab\":\"Guides\",\"groups\":[{\"group\":\"产品概览\",\"pages\":[\"xparse/overview\",\"xparse/api-key\",\"xparse/product-manual\"]},{\"group\":\"文档解析\",\"pages\":[\"xparse/parse-quickstart\",\"xparse/parse-getjson\",\"xparse/parse-gettable\",\"xparse/parse-getcatalog\",\"xparse/parse-getimage\",\"xparse/parse-getpos\",\"xparse/parse-max-workers\"]},{\"group\":\"文档抽取\",\"pages\":[\"xparse/extract-quickstart\",\"xparse/extract-getjson\",\"xparse/extract-gettable\",\"xparse/extract-getpos\",\"xparse/extract-asyncio\",\"xparse/extract-max-concurrent\"]}]},{\"tab\":\"API\",\"groups\":[{\"group\":\"智能文档解析\",\"pages\":[\"api-reference/endpoint/parse\",\"api-reference/endpoint/extract\"]}]}]},\"footer\":{\"socials\":{\"github\":\"https://github.com/intsig-textin\"}}},\"pageMetadata\":{\"title\":\"文档解析\",\"openapi\":\"api-reference/parse-1.0.1.openapi.yml post /ai/service/v1/pdf_to_markdown\",\"description\":\"上传图片/pdf/word/html/excel/ppt/txt，进行版面检测，文字识别，表格识别，版面分析等操作，并生成markdown文档及结构化数据\",\"href\":\"/api-reference/endpoint/parse\"}}}}],\"$L35\",\"$undefined\"]}],\"$L36\"]}]]}]}]]]}]\n"])</script><script>self.__next_f.push([1,"37:I[1514,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"3122\",\"static/chunks/3122-473c6d6ad707a1ff.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"7048\",\"static/chunks/7048-68b7efbe64e44ac4.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"3386\",\"static/chunks/3386-4ac6c5d6ac47f6b6.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"3698\",\"static/chunks/3698-586441bcd6c2501a.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"3818\",\"static/chunks/3818-b06f8c87a0bcb5ea.js\",\"3291\",\"static/chunks/3291-8e412928ddb5eb6a.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-82f1f84c31f01e84.js\"],\"default\",1]\n"])</script><script>self.__next_f.push([1,"38:I[7247,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"3122\",\"static/chunks/3122-473c6d6ad707a1ff.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"7048\",\"static/chunks/7048-68b7efbe64e44ac4.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"3386\",\"static/chunks/3386-4ac6c5d6ac47f6b6.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"3698\",\"static/chunks/3698-586441bcd6c2501a.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"3818\",\"static/chunks/3818-b06f8c87a0bcb5ea.js\",\"3291\",\"static/chunks/3291-8e412928ddb5eb6a.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-82f1f84c31f01e84.js\"],\"UserFeedback\",1]\n"])</script><script>self.__next_f.push([1,"39:I[52604,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"3122\",\"static/chunks/3122-473c6d6ad707a1ff.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"7048\",\"static/chunks/7048-68b7efbe64e44ac4.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"3386\",\"static/chunks/3386-4ac6c5d6ac47f6b6.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"3698\",\"static/chunks/3698-586441bcd6c2501a.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"3818\",\"static/chunks/3818-b06f8c87a0bcb5ea.js\",\"3291\",\"static/chunks/3291-8e412928ddb5eb6a.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-82f1f84c31f01e84.js\"],\"Pagination\",1]\n"])</script><script>self.__next_f.push([1,"3a:I[87681,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"3122\",\"static/chunks/3122-473c6d6ad707a1ff.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"7048\",\"static/chunks/7048-68b7efbe64e44ac4.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"3386\",\"static/chunks/3386-4ac6c5d6ac47f6b6.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"3698\",\"static/chunks/3698-586441bcd6c2501a.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"3818\",\"static/chunks/3818-b06f8c87a0bcb5ea.js\",\"3291\",\"static/chunks/3291-8e412928ddb5eb6a.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-82f1f84c31f01e84.js\"],\"Footer\",1]\n"])</script><script>self.__next_f.push([1,"3b:I[63792,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"803\",\"static/chunks/cd24890f-549fb4ba2f588ca6.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"9779\",\"static/chunks/9779-7bb45d52151006b8.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"1350\",\"static/chunks/1350-cf10a11ffacb60d0.js\",\"5456\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/layout-b596e085fd7c2d45.js\"],\"default\"]\n"])</script><script>self.__next_f.push([1,"3c:I[71197,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"3122\",\"static/chunks/3122-473c6d6ad707a1ff.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"7048\",\"static/chunks/7048-68b7efbe64e44ac4.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"3386\",\"static/chunks/3386-4ac6c5d6ac47f6b6.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"3698\",\"static/chunks/3698-586441bcd6c2501a.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"3818\",\"static/chunks/3818-b06f8c87a0bcb5ea.js\",\"3291\",\"static/chunks/3291-8e412928ddb5eb6a.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-82f1f84c31f01e84.js\"],\"AuthProvider\",1]\n"])</script><script>self.__next_f.push([1,"3d:I[71197,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"3122\",\"static/chunks/3122-473c6d6ad707a1ff.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"7048\",\"static/chunks/7048-68b7efbe64e44ac4.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"3386\",\"static/chunks/3386-4ac6c5d6ac47f6b6.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"3698\",\"static/chunks/3698-586441bcd6c2501a.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"3818\",\"static/chunks/3818-b06f8c87a0bcb5ea.js\",\"3291\",\"static/chunks/3291-8e412928ddb5eb6a.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-82f1f84c31f01e84.js\"],\"DeploymentMetadataProvider\",1]\n"])</script><script>self.__next_f.push([1,"3e:I[71197,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"3122\",\"static/chunks/3122-473c6d6ad707a1ff.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"7048\",\"static/chunks/7048-68b7efbe64e44ac4.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"3386\",\"static/chunks/3386-4ac6c5d6ac47f6b6.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"3698\",\"static/chunks/3698-586441bcd6c2501a.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"3818\",\"static/chunks/3818-b06f8c87a0bcb5ea.js\",\"3291\",\"static/chunks/3291-8e412928ddb5eb6a.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-82f1f84c31f01e84.js\"],\"DocsConfigProvider\",1]\n"])</script><script>self.__next_f.push([1,"35:[\"$\",\"$L37\",null,{}]\n36:[\"$\",\"div\",null,{\"className\":\"leading-6 mt-14\",\"children\":[[\"$\",\"$L38\",null,{}],[\"$\",\"$L39\",null,{}],[\"$\",\"$L3a\",null,{}]]}]\n"])</script><script>self.__next_f.push([1,"10:[\"$\",\"$L5\",null,{\"appearance\":\"$undefined\",\"children\":[false,[\"$\",\"$L2c\",null,{\"id\":\"_mintlify-banner-script\",\"strategy\":\"beforeInteractive\",\"dangerouslySetInnerHTML\":{\"__html\":\"(function m(a,b,c,d){try{let e=document.getElementById(\\\"banner\\\"),f=e?.innerText;if(!f)return void document.documentElement.setAttribute(d,\\\"hidden\\\");let g=localStorage.getItem(a),h=g!==f\u0026\u0026g!==b;null!=g\u0026\u0026(h?(localStorage.removeItem(c),localStorage.removeItem(a)):(localStorage.setItem(c,f),localStorage.setItem(a,f))),document.documentElement.setAttribute(d,!g||h?\\\"visible\\\":\\\"hidden\\\")}catch(a){console.error(a),document.documentElement.setAttribute(d,\\\"hidden\\\")}})(\\n  \\\"textin-bannerDismissed\\\",\\n  undefined,\\n  \\\"__mintlify-bannerDismissed\\\",\\n  \\\"data-banner-state\\\",\\n)\"}}],[\"$\",\"$L3b\",null,{\"appId\":\"$undefined\",\"autoBoot\":true,\"children\":[\"$\",\"$L3c\",null,{\"value\":{\"auth\":\"$undefined\",\"userAuth\":\"$undefined\"},\"children\":[\"$\",\"$L3d\",null,{\"value\":{\"subdomain\":\"textin\",\"actualSubdomain\":\"textin\",\"gitSource\":{\"type\":\"github\",\"owner\":\"intsig-textin\",\"repo\":\"textin-docs\",\"deployBranch\":\"main\",\"contentDirectory\":\"\",\"isPrivate\":true},\"inkeep\":\"$undefined\",\"trieve\":{\"datasetId\":\"5626a97b-a337-48d0-b14f-93f12d021c7b\"},\"feedback\":{\"thumbs\":true},\"entitlements\":{\"AI_CHAT\":{\"status\":\"ENABLED\"}},\"buildId\":\"6886f87e71293608a90bc1d2:success\",\"clientVersion\":\"0.0.1517\"},\"children\":[\"$\",\"$L3e\",null,{\"value\":{\"mintConfig\":\"$undefined\",\"docsConfig\":{\"theme\":\"mint\",\"$schema\":\"https://mintlify.com/docs.json\",\"name\":\"Textin 智能文档解析\",\"colors\":{\"primary\":\"#1a66ff\",\"light\":\"#1a66ff\",\"dark\":\"#1a66ff\"},\"logo\":{\"light\":\"https://mintlify.s3.us-west-1.amazonaws.com/textin/logo/light.png\",\"dark\":\"https://mintlify.s3.us-west-1.amazonaws.com/textin/logo/dark.png\"},\"favicon\":\"/favicon.png\",\"api\":{\"playground\":{\"display\":\"simple\",\"proxy\":false}},\"navbar\":{\"links\":[{\"label\":\"资讯中心\",\"href\":\"https://www.textin.com/news/list\"},{\"label\":\"技术支持\",\"href\":\"https://www.textin.com/document/list\"}],\"primary\":{\"type\":\"button\",\"label\":\"快速体验\",\"href\":\"https://www.textin.com/console/recognition/robot_markdown?service=pdf_to_markdown\"}},\"navigation\":{\"global\":{\"languages\":[{\"language\":\"cn\",\"default\":true,\"href\":\"https://mintlify.com/docs\"},{\"language\":\"en\",\"href\":\"https://mintlify.com/docs\"}],\"anchors\":[]},\"tabs\":[{\"tab\":\"Guides\",\"groups\":[{\"group\":\"产品概览\",\"pages\":[\"xparse/overview\",\"xparse/api-key\",\"xparse/product-manual\"]},{\"group\":\"文档解析\",\"pages\":[\"xparse/parse-quickstart\",\"xparse/parse-getjson\",\"xparse/parse-gettable\",\"xparse/parse-getcatalog\",\"xparse/parse-getimage\",\"xparse/parse-getpos\",\"xparse/parse-max-workers\"]},{\"group\":\"文档抽取\",\"pages\":[\"xparse/extract-quickstart\",\"xparse/extract-getjson\",\"xparse/extract-gettable\",\"xparse/extract-getpos\",\"xparse/extract-asyncio\",\"xparse/extract-max-concurrent\"]}]},{\"tab\":\"API\",\"groups\":[{\"group\":\"智能文档解析\",\"pages\":[\"api-reference/endpoint/parse\",\"api-reference/endpoint/extract\"]}]}]},\"footer\":{\"socials\":{\"github\":\"https://github.com/intsig-textin\"}},\"styling\":{\"codeblocks\":\"system\"}},\"docsNavWithMetadata\":{\"global\":{\"languages\":[{\"language\":\"cn\",\"default\":true,\"href\":\"https://mintlify.com/docs\"},{\"language\":\"en\",\"href\":\"https://mintlify.com/docs\"}],\"anchors\":[]},\"tabs\":[{\"tab\":\"Guides\",\"groups\":[{\"group\":\"产品概览\",\"pages\":[{\"title\":\"产品简介\",\"description\":\"TextIn xParse for ETL  \",\"href\":\"/xparse/overview\"},{\"title\":\"API Key\",\"description\":\"快速获取您的 x-ti-app-id 和 x-ti-secret-code\",\"href\":\"/xparse/api-key\"},{\"title\":\"使用手册\",\"description\":\"智能文档解析产品使用手册，在线快速体验感受能力效果\",\"href\":\"/xparse/product-manual\"}]},{\"group\":\"文档解析\",\"pages\":[{\"title\":\"快速启动\",\"description\":\"参考示例，快速将文档解析API接入到您的系统和应用流程中。\",\"href\":\"/xparse/parse-quickstart\"},{\"title\":\"返回JSON结构说明\",\"description\":null,\"href\":\"/xparse/parse-getjson\"},{\"title\":\"获取表格\",\"description\":null,\"href\":\"/xparse/parse-gettable\"},{\"title\":\"获取目录树\",\"description\":null,\"href\":\"/xparse/parse-getcatalog\"},{\"title\":\"获取图片并持久化\",\"description\":null,\"href\":\"/xparse/parse-getimage\"},{\"title\":\"前端可视化：获取精确坐标\",\"description\":null,\"href\":\"/xparse/parse-getpos\"},{\"title\":\"多并发请求\",\"description\":null,\"href\":\"/xparse/parse-max-workers\"}]},{\"group\":\"文档抽取\",\"pages\":[{\"title\":\"快速启动\",\"description\":\"参考示例，快速将文档抽取API接入到您的系统和应用流程中。\",\"href\":\"/xparse/extract-quickstart\"},{\"title\":\"返回JSON结构说明\",\"description\":null,\"href\":\"/xparse/extract-getjson\"},{\"title\":\"获取表格\",\"description\":null,\"href\":\"/xparse/extract-gettable\"},{\"title\":\"前端可视化：获取精确坐标\",\"description\":null,\"href\":\"/xparse/extract-getpos\"},{\"title\":\"异步请求\",\"description\":null,\"href\":\"/xparse/extract-asyncio\"},{\"title\":\"多并发请求\",\"description\":null,\"href\":\"/xparse/extract-max-concurrent\"}]}]},{\"tab\":\"API\",\"groups\":[{\"group\":\"智能文档解析\",\"pages\":[{\"title\":\"文档解析\",\"openapi\":\"api-reference/parse-1.0.1.openapi.yml post /ai/service/v1/pdf_to_markdown\",\"description\":\"上传图片/pdf/word/html/excel/ppt/txt，进行版面检测，文字识别，表格识别，版面分析等操作，并生成markdown文档及结构化数据\",\"href\":\"/api-reference/endpoint/parse\"},{\"title\":\"智能抽取\",\"openapi\":\"api-reference/extract-1.0.2.openapi.yaml post /ai/service/v2/entity_extraction\",\"description\":\"智能文档抽取-API\\n\",\"href\":\"/api-reference/endpoint/extract\"}]}]}]},\"legacyThemeSettings\":{\"isSidePrimaryNav\":false,\"isSolidSidenav\":false,\"isTopbarGradient\":false,\"isSearchAtSidebar\":false,\"shouldUseTabsInTopNav\":false,\"sidebarStyle\":\"container\",\"rounded\":\"default\"}},\"children\":\"$L3f\"}]}]}]}]]}]\n"])</script><script>self.__next_f.push([1,"40:I[57862,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"803\",\"static/chunks/cd24890f-549fb4ba2f588ca6.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"9779\",\"static/chunks/9779-7bb45d52151006b8.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"1350\",\"static/chunks/1350-cf10a11ffacb60d0.js\",\"5456\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/layout-b596e085fd7c2d45.js\"],\"LoginButtonProvider\",1]\n"])</script><script>self.__next_f.push([1,"41:I[93351,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"803\",\"static/chunks/cd24890f-549fb4ba2f588ca6.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"9779\",\"static/chunks/9779-7bb45d52151006b8.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"1350\",\"static/chunks/1350-cf10a11ffacb60d0.js\",\"5456\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/layout-b596e085fd7c2d45.js\"],\"NavigationContextController\",1]\n"])</script><script>self.__next_f.push([1,"42:I[80976,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"803\",\"static/chunks/cd24890f-549fb4ba2f588ca6.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"9779\",\"static/chunks/9779-7bb45d52151006b8.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"1350\",\"static/chunks/1350-cf10a11ffacb60d0.js\",\"5456\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/layout-b596e085fd7c2d45.js\"],\"BannerProvider\"]\n"])</script><script>self.__next_f.push([1,"43:I[99543,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"3122\",\"static/chunks/3122-473c6d6ad707a1ff.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"7048\",\"static/chunks/7048-68b7efbe64e44ac4.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"3386\",\"static/chunks/3386-4ac6c5d6ac47f6b6.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"3698\",\"static/chunks/3698-586441bcd6c2501a.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"3818\",\"static/chunks/3818-b06f8c87a0bcb5ea.js\",\"3291\",\"static/chunks/3291-8e412928ddb5eb6a.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-82f1f84c31f01e84.js\"],\"ScrollTopScript\",1]\n"])</script><script>self.__next_f.push([1,"44:I[25327,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"803\",\"static/chunks/cd24890f-549fb4ba2f588ca6.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"9779\",\"static/chunks/9779-7bb45d52151006b8.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"1350\",\"static/chunks/1350-cf10a11ffacb60d0.js\",\"5456\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/layout-b596e085fd7c2d45.js\"],\"LocalStorageAndAnalyticsProviders\",1]\n"])</script><script>self.__next_f.push([1,"45:I[71476,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"803\",\"static/chunks/cd24890f-549fb4ba2f588ca6.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"9779\",\"static/chunks/9779-7bb45d52151006b8.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"1350\",\"static/chunks/1350-cf10a11ffacb60d0.js\",\"5456\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/layout-b596e085fd7c2d45.js\"],\"SearchProvider\",1]\n"])</script><script>self.__next_f.push([1,"46:I[46826,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"803\",\"static/chunks/cd24890f-549fb4ba2f588ca6.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"9779\",\"static/chunks/9779-7bb45d52151006b8.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"1350\",\"static/chunks/1350-cf10a11ffacb60d0.js\",\"5456\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/layout-b596e085fd7c2d45.js\"],\"NavScroller\"]\n"])</script><script>self.__next_f.push([1,"47:I[59672,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"803\",\"static/chunks/cd24890f-549fb4ba2f588ca6.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"9779\",\"static/chunks/9779-7bb45d52151006b8.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"1350\",\"static/chunks/1350-cf10a11ffacb60d0.js\",\"5456\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/layout-b596e085fd7c2d45.js\"],\"MainContentLayout\",1]\n"])</script><script>self.__next_f.push([1,"48:I[9855,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4129\",\"static/chunks/7bf36345-5ba13855b95a82b2.js\",\"1725\",\"static/chunks/d30757c7-56ff534f625704fe.js\",\"803\",\"static/chunks/cd24890f-549fb4ba2f588ca6.js\",\"7261\",\"static/chunks/7261-2b892dc828f6e161.js\",\"3892\",\"static/chunks/3892-8cb32e85ffab550f.js\",\"7417\",\"static/chunks/7417-548f041b716e378a.js\",\"1953\",\"static/chunks/1953-46fbce29c74b759e.js\",\"9095\",\"static/chunks/9095-5e8c25cebc4b2bd6.js\",\"9779\",\"static/chunks/9779-7bb45d52151006b8.js\",\"3619\",\"static/chunks/3619-00dda429c6457d2a.js\",\"2398\",\"static/chunks/2398-cdec25c076f8ff8b.js\",\"1862\",\"static/chunks/1862-deaeb884406520f8.js\",\"2755\",\"static/chunks/2755-2e4adf62599e58a6.js\",\"1350\",\"static/chunks/1350-cf10a11ffacb60d0.js\",\"5456\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/layout-b596e085fd7c2d45.js\"],\"ChatAssistantSheet\",1]\n"])</script><script>self.__next_f.push([1,"3f:[\"$\",\"$L40\",null,{\"children\":[\"$\",\"$L41\",null,{\"children\":[null,[[\"$\",\"style\",null,{\"children\":\":root {\\n    --primary: 26 102 255;\\n    --primary-light: 26 102 255;\\n    --primary-dark: 26 102 255;\\n    --background-light: 255 255 255;\\n    --background-dark: 9 11 16;\\n    --gray-50: 243 245 250;\\n    --gray-100: 238 241 245;\\n    --gray-200: 223 225 230;\\n    --gray-300: 206 209 213;\\n    --gray-400: 159 161 166;\\n    --gray-500: 112 115 119;\\n    --gray-600: 80 83 87;\\n    --gray-700: 63 65 70;\\n    --gray-800: 38 40 45;\\n    --gray-900: 23 25 30;\\n    --gray-950: 10 13 17;\\n  }\"}],null,[\"$\",\"div\",null,{\"className\":\"relative antialiased text-gray-500 dark:text-gray-400\",\"children\":[\"$\",\"$L42\",null,{\"initialBanner\":null,\"config\":\"$undefined\",\"subdomain\":\"textin\",\"children\":[[\"$\",\"$L43\",null,{\"theme\":\"mint\"}],[\"$\",\"$L44\",null,{\"subdomain\":\"textin\",\"internalAnalyticsWriteKey\":\"phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW\",\"org\":{\"plan\":\"pro\",\"createdAt\":\"2025-07-08T05:52:18.531Z\"},\"children\":[\"$\",\"$L45\",null,{\"subdomain\":\"textin\",\"hasChatPermissions\":true,\"assistantConfig\":\"$undefined\",\"children\":[[\"$\",\"$L46\",null,{}],[[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"topbar\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}],[\"$\",\"$L47\",null,{\"children\":[[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}],[\"$\",\"$L48\",null,{}]]}]]]}]}]]}]}]]]}]}]\n"])</script><textarea tabindex="-1" aria-hidden="true" style="min-height: 0px !important; max-height: none !important; height: 0px !important; visibility: hidden !important; overflow: hidden !important; position: absolute !important; z-index: -1000 !important; top: 0px !important; right: 0px !important; display: block !important; border-width: 1px; box-sizing: border-box; font-family: Inter, &quot;Inter Fallback&quot;; font-size: 14px; font-style: normal; font-weight: 400; letter-spacing: normal; line-height: 20px; padding: 10px 40px 10px 14px; tab-size: 4; text-indent: 0px; text-rendering: auto; text-transform: none; width: 335px; word-break: normal; word-spacing: 0px; scrollbar-gutter: auto;"></textarea><next-route-announcer style="position: absolute;"><template shadowrootmode="open"><div aria-live="assertive" id="__next-route-announcer__" role="alert" style="position: absolute; border: 0px; height: 1px; margin: -1px; padding: 0px; width: 1px; clip: rect(0px, 0px, 0px, 0px); overflow: hidden; white-space: nowrap; overflow-wrap: normal;">快速启动 - Textin 智能文档解析</div></template></next-route-announcer><div data-rmiz-portal=""></div></body></html>