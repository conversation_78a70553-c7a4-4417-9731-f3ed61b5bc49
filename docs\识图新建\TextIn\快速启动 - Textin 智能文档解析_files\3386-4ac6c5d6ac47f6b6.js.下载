!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="ec49c5a8-1d33-4186-affc-19beb2329adb",e._sentryDebugIdIdentifier="sentry-dbid-ec49c5a8-1d33-4186-affc-19beb2329adb")}catch(e){}}();"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3386],{6578:(e,t,r)=>{r.d(t,{DynamicMermaid:()=>a});let a=(0,r(91039).default)(()=>Promise.all([r.e(3220),r.e(5419),r.e(547)]).then(r.bind(r,60547)).then(e=>e.Mermaid),{loadableGenerated:{webpack:()=>[60547]},ssr:!1})},7421:(e,t,r)=>{r.d(t,{Check:()=>p,Danger:()=>m,Info:()=>l,Note:()=>d,Tip:()=>c,Warning:()=>o});var a=r(54568),n=r(31467),i=r(16600);function s({children:e,icon:t,className:r,childrenClassName:s,calloutType:l}){return(0,a.jsxs)("div",{className:(0,i.cn)(n.x.Callout,"my-4 px-5 py-4 overflow-hidden rounded-2xl flex gap-3",r),"data-callout-type":l,children:[(0,a.jsx)("div",{className:"mt-0.5 w-4","data-component-part":"callout-icon",children:t}),(0,a.jsx)("div",{className:(0,i.cn)("text-sm prose min-w-0 w-full",s),"data-component-part":"callout-content",children:e})]})}function l({children:e}){return(0,a.jsx)(s,{calloutType:"info",icon:(0,a.jsx)("svg",{viewBox:"0 0 20 20",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",className:"flex-none w-5 h-5 text-zinc-400 dark:text-zinc-300","aria-label":"Info",children:(0,a.jsx)("path",{d:"M8 0C3.58125 0 0 3.58125 0 8C0 12.4187 3.58125 16 8 16C12.4187 16 16 12.4187 16 8C16 3.58125 12.4187 0 8 0ZM8 14.5C4.41563 14.5 1.5 11.5841 1.5 8C1.5 4.41594 4.41563 1.5 8 1.5C11.5844 1.5 14.5 4.41594 14.5 8C14.5 11.5841 11.5844 14.5 8 14.5ZM9.25 10.5H8.75V7.75C8.75 7.3375 8.41563 7 8 7H7C6.5875 7 6.25 7.3375 6.25 7.75C6.25 8.1625 6.5875 8.5 7 8.5H7.25V10.5H6.75C6.3375 10.5 6 10.8375 6 11.25C6 11.6625 6.3375 12 6.75 12H9.25C9.66406 12 10 11.6641 10 11.25C10 10.8359 9.66563 10.5 9.25 10.5ZM8 6C8.55219 6 9 5.55219 9 5C9 4.44781 8.55219 4 8 4C7.44781 4 7 4.44687 7 5C7 5.55313 7.44687 6 8 6Z"})}),className:"border border-zinc-500/20 bg-zinc-50/50 dark:border-zinc-500/30 dark:bg-zinc-500/10",childrenClassName:"text-zinc-900 dark:text-zinc-200",children:e})}function o({children:e}){return(0,a.jsx)(s,{calloutType:"warning",icon:(0,a.jsx)("svg",{className:"flex-none w-5 h-5 text-amber-400 dark:text-amber-300/80",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:2,"aria-label":"Warning",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})}),className:"border border-amber-500/20 bg-amber-50/50 dark:border-amber-500/30 dark:bg-amber-500/10",childrenClassName:"text-amber-900 dark:text-amber-200",children:e})}function d({children:e}){return(0,a.jsx)(s,{calloutType:"note",icon:(0,a.jsx)("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",className:"w-4 h-4 text-sky-500","aria-label":"Note",children:(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 1.3C10.14 1.3 12.7 3.86 12.7 7C12.7 10.14 10.14 12.7 7 12.7C5.48908 12.6974 4.0408 12.096 2.97241 11.0276C1.90403 9.9592 1.30264 8.51092 1.3 7C1.3 3.86 3.86 1.3 7 1.3ZM7 0C3.14 0 0 3.14 0 7C0 10.86 3.14 14 7 14C10.86 14 14 10.86 14 7C14 3.14 10.86 0 7 0ZM8 3H6V8H8V3ZM8 9H6V11H8V9Z"})}),className:"border border-sky-500/20 bg-sky-50/50 dark:border-sky-500/30 dark:bg-sky-500/10",childrenClassName:"text-sky-900 dark:text-sky-200",children:e})}function c({children:e}){return(0,a.jsx)(s,{calloutType:"tip",icon:(0,a.jsx)("svg",{width:"11",height:"14",viewBox:"0 0 11 14",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",className:"text-emerald-600 dark:text-emerald-400/80 w-3.5 h-auto","aria-label":"Tip",children:(0,a.jsx)("path",{d:"M3.12794 12.4232C3.12794 12.5954 3.1776 12.7634 3.27244 12.907L3.74114 13.6095C3.88471 13.8248 4.21067 14 4.46964 14H6.15606C6.41415 14 6.74017 13.825 6.88373 13.6095L7.3508 12.9073C7.43114 12.7859 7.49705 12.569 7.49705 12.4232L7.50055 11.3513H3.12521L3.12794 12.4232ZM5.31288 0C2.52414 0.00875889 0.5 2.26889 0.5 4.78826C0.5 6.00188 0.949566 7.10829 1.69119 7.95492C2.14321 8.47011 2.84901 9.54727 3.11919 10.4557C3.12005 10.4625 3.12175 10.4698 3.12261 10.4771H7.50342C7.50427 10.4698 7.50598 10.463 7.50684 10.4557C7.77688 9.54727 8.48281 8.47011 8.93484 7.95492C9.67728 7.13181 10.1258 6.02703 10.1258 4.78826C10.1258 2.15486 7.9709 0.000106649 5.31288 0ZM7.94902 7.11267C7.52078 7.60079 6.99082 8.37878 6.6077 9.18794H4.02051C3.63739 8.37878 3.10743 7.60079 2.67947 7.11294C2.11997 6.47551 1.8126 5.63599 1.8126 4.78826C1.8126 3.09829 3.12794 1.31944 5.28827 1.3126C7.2435 1.3126 8.81315 2.88226 8.81315 4.78826C8.81315 5.63599 8.50688 6.47551 7.94902 7.11267ZM4.87534 2.18767C3.66939 2.18767 2.68767 3.16939 2.68767 4.37534C2.68767 4.61719 2.88336 4.81288 3.12521 4.81288C3.36705 4.81288 3.56274 4.61599 3.56274 4.37534C3.56274 3.6515 4.1515 3.06274 4.87534 3.06274C5.11719 3.06274 5.31288 2.86727 5.31288 2.62548C5.31288 2.38369 5.11599 2.18767 4.87534 2.18767Z"})}),className:"border border-emerald-500/20 bg-emerald-50/50 dark:border-emerald-500/30 dark:bg-emerald-500/10",childrenClassName:"text-emerald-900 dark:text-emerald-200",children:e})}function p({children:e}){return(0,a.jsx)(s,{calloutType:"check",icon:(0,a.jsx)("svg",{className:"text-green-600 dark:text-green-400/80 w-3.5 h-auto",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512","aria-label":"Check",children:(0,a.jsx)("path",{d:"M438.6 105.4C451.1 117.9 451.1 138.1 438.6 150.6L182.6 406.6C170.1 419.1 149.9 419.1 137.4 406.6L9.372 278.6C-3.124 266.1-3.124 245.9 9.372 233.4C21.87 220.9 42.13 220.9 54.63 233.4L159.1 338.7L393.4 105.4C405.9 92.88 426.1 92.88 438.6 105.4H438.6z"})}),className:"border border-emerald-500/20 bg-emerald-50/50 dark:border-emerald-500/30 dark:bg-emerald-500/10",childrenClassName:"text-emerald-900 dark:text-emerald-200",children:e})}function m({children:e}){return(0,a.jsx)(s,{calloutType:"danger",icon:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",fill:"currentColor",className:"text-red-600 dark:text-red-400/80 w-4 h-4","aria-label":"Danger",children:(0,a.jsx)("path",{d:"M17.1 292c-12.9-22.3-12.9-49.7 0-72L105.4 67.1c12.9-22.3 36.6-36 62.4-36l176.6 0c25.7 0 49.5 13.7 62.4 36L494.9 220c12.9 22.3 12.9 49.7 0 72L406.6 444.9c-12.9 22.3-36.6 36-62.4 36l-176.6 0c-25.7 0-49.5-13.7-62.4-36L17.1 292zm41.6-48c-4.3 7.4-4.3 16.6 0 24l88.3 152.9c4.3 7.4 12.2 12 20.8 12l176.6 0c8.6 0 16.5-4.6 20.8-12L453.4 268c4.3-7.4 4.3-16.6 0-24L365.1 91.1c-4.3-7.4-12.2-12-20.8-12l-176.6 0c-8.6 0-16.5 4.6-20.8 12L58.6 244zM256 128c13.3 0 24 10.7 24 24l0 112c0 13.3-10.7 24-24 24s-24-10.7-24-24l0-112c0-13.3 10.7-24 24-24zM224 352a32 32 0 1 1 64 0 32 32 0 1 1 -64 0z"})}),className:"border border-red-500/20 bg-red-50/50 dark:border-red-500/30 dark:bg-red-500/10",childrenClassName:"text-red-900 dark:text-red-200",children:e})}},7949:(e,t,r)=>{r.d(t,{Frame:()=>s});var a=r(54568);r(7620);var n=r(31467),i=r(16600);function s({as:e="div",caption:t,style:r,className:s,containerClassName:l,children:o,hint:d}){return(0,a.jsxs)("div",{className:l,children:[void 0!==d&&(0,a.jsxs)("div",{className:"not-prose mb-4 flex items-center space-x-2",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",className:"flex-none w-4 h-4 fill-gray-400 dark:fill-gray-300",children:(0,a.jsx)("path",{d:"M224 320c0 17.69 14.33 32 32 32h64c17.67 0 32-14.31 32-32s-14.33-32-32-32h-64C238.3 288 224 302.3 224 320zM267.6 256H352c17.67 0 32-14.31 32-32s-14.33-32-32-32h-80v40C272 240.5 270.3 248.5 267.6 256zM272 160H480c17.67 0 32-14.31 32-32s-14.33-32-32-32h-208.8C271.5 98.66 272 101.3 272 104V160zM320 416c0-17.69-14.33-32-32-32H224c-17.67 0-32 14.31-32 32s14.33 32 32 32h64C305.7 448 320 433.7 320 416zM202.1 355.8C196 345.6 192 333.3 192 320c0-5.766 1.08-11.24 2.51-16.55C157.4 300.6 128 269.9 128 232V159.1C128 151.2 135.2 144 143.1 144S160 151.2 159.1 159.1l0 69.72C159.1 245.2 171.3 271.1 200 271.1C222.1 271.1 240 254.1 240 232v-128C240 81.91 222.1 64 200 64H136.6C103.5 64 72.03 80 52.47 106.8L26.02 143.2C9.107 166.5 0 194.5 0 223.3V312C0 387.1 60.89 448 136 448h32.88C163.4 438.6 160 427.7 160 416C160 388.1 178 364.6 202.1 355.8z"})}),(0,a.jsx)("p",{className:"text-gray-700 text-sm font-medium dark:text-gray-200",children:d})]}),(0,a.jsxs)(e,{style:r,"data-name":"frame",className:(0,i.cn)(n.x.Frame,"p-2 not-prose relative bg-gray-50/50 rounded-2xl overflow-hidden dark:bg-gray-800/25"),children:[(0,a.jsx)("div",{style:{backgroundPosition:"10px 10px"},className:"absolute inset-0 bg-grid-neutral-200/20 [mask-image:linear-gradient(0deg,#fff,rgba(255,255,255,0.6))] dark:bg-grid-white/5 dark:[mask-image:linear-gradient(0deg,rgba(255,255,255,0.1),rgba(255,255,255,0.5))]"}),(0,a.jsx)("div",{className:(0,i.cn)("relative rounded-xl overflow-hidden flex justify-center",s),children:o}),t&&(0,a.jsx)("div",{className:(0,i.cn)("relative rounded-2xl flex justify-center mt-3 pt-0 px-8 pb-2 text-sm text-gray-700 dark:text-gray-400",s),contentEditable:!1,"data-component-part":"frame-caption",children:(0,a.jsx)("p",{children:t})}),(0,a.jsx)("div",{className:(0,i.cn)("absolute inset-0 pointer-events-none border border-black/5 rounded-2xl dark:border-white/5")})]})]})}},9506:(e,t,r)=>{r.d(t,{Expandable:()=>x});var a=r(54568),n=r(7620),i=r(79460),s=r(88374);let l="user_toggled_expandables",o=()=>{try{let e=sessionStorage.getItem(l);return e?JSON.parse(e):{}}catch(e){return{}}};var d=r(31467),c=r(89619),p=r(16600),m=r(43059);let u="expandable-content";function x({title:e,defaultOpen:t=!1,onChange:r,lazy:x,children:h,uniqueParamId:f}){let{hasScrolledToAnchorRef:g}=(0,n.useContext)(i.m$),y=!!f,{isExpanded:b,onManualToggle:v}=((e,t=!1)=>{let r=(0,n.useRef)(null),[a,i]=(0,n.useState)(!1),[s,d]=(0,n.useState)(!1),[c,p]=(0,n.useState)(t);return(0,n.useEffect)(()=>{if(!e)return;let t=o();e in t&&(p(!!t[e]),d(!0))},[e]),(0,n.useEffect)(()=>{let e=new IntersectionObserver(e=>{e[0]&&(i(e[0].isIntersecting),e[0].isIntersecting&&!s&&t&&p(!0))},{rootMargin:"100px",threshold:0}),a=r.current;return a&&e.observe(a),()=>{a&&e.unobserve(a)}},[s,t]),{ref:r,isVisible:a,isExpanded:c,onManualToggle:(0,n.useCallback)(t=>{p(t),d(!0),e&&((e,t)=>{try{let r={...o(),[e]:t};sessionStorage.setItem(l,JSON.stringify(r))}catch(e){}})(e,t)},[e])}})(f||"",t),[j,k]=(0,n.useState)(t),w=y?b:j,[N,O]=(0,n.useState)(w||!x),C=(0,s.p)("docs.expandable.open"),$=(0,s.p)("docs.expandable.close"),S=(0,n.useRef)(null);return(0,n.useEffect)(()=>{let e=()=>{(0,m.h)({shouldReturnEarly:!!g?.current,checkIfShouldScroll:e=>!!S.current&&S.current.contains(e),preScrollCallback(e){y?v(!0):k(!0);let t=e.closest(`.${u}`);for(;t;){let e=t.previousElementSibling;"false"===e.getAttribute("aria-expanded")&&e.click(),t=t.parentElement?.closest(`.${u}`)??null}},postScrollCallback(){g&&(g.current=!0)}})};return e(),window.addEventListener("hashchange",e),()=>{window.removeEventListener("hashchange",e)}},[y,v,g]),(0,a.jsxs)("details",{ref:S,role:"listitem",open:w,onToggle:t=>{let a=t.currentTarget.open;a!==w&&(y?v(a):k(a),O(!0),r&&r(a),a?C({title:e}):$({title:e}))},className:(0,p.cn)(d.x.Expandable,"mt-4 border-standard rounded-xl"),children:[(0,a.jsxs)("summary",{className:(0,p.cn)("not-prose text-sm flex flex-row items-center content-center w-full cursor-pointer","text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-gray-200 py-3 px-3.5 hover:bg-gray-50/50 dark:hover:bg-white/5 rounded-t-xl","list-none [&::-webkit-details-marker]:hidden",!w&&"rounded-b-xl"),"aria-controls":"Children attributes","aria-expanded":w,"data-component-part":"expandable-button",children:[(0,a.jsx)(c.Ay,{icon:"angle-right",className:(0,p.cn)("h-2.5 w-2.5 bg-zinc-400 transition-transform",w&&"rotate-90"),"data-component-part":"expandable-icon"}),(0,a.jsx)("div",{className:"ml-3 leading-tight text-left",children:(0,a.jsxs)("p",{className:"m-0",contentEditable:!1,children:[w?"Hide":"Show"," ",e||"child attributes"]})})]}),(0,a.jsx)("div",{id:e+"Children",className:(0,p.cn)(u,"mx-3 px-2 border-t border-gray-100 dark:border-white/10"),"data-component-part":u,children:N&&h})]},e)}},17809:(e,t,r)=>{r.d(t,{default:()=>n});var a=r(54568);let n=(0,r(91039).default)(()=>Promise.resolve().then(r.bind(r,49580)),{loadableGenerated:{webpack:()=>[49580]},loading:()=>(0,a.jsx)("p",{children:"Loading..."}),ssr:!1})},19043:(e,t,r)=>{r.d(t,{DynamicCustomCode:()=>a});let a=(0,r(91039).default)(()=>Promise.all([r.e(7043),r.e(7401),r.e(8389),r.e(529)]).then(r.bind(r,60529)).then(e=>e.CustomCode),{loadableGenerated:{webpack:()=>[60529]},ssr:!1})},19637:(e,t,r)=>{r.d(t,{O:()=>i});var a=r(7620),n=r(35319);let i=()=>(0,a.useContext)(n.MDXContentContext)},22968:(e,t,r)=>{r.d(t,{T:()=>s});var a=r(7620),n=r(71197),i=r(40113);function s(e){let{docsConfig:t}=(0,a.useContext)(n.H6),[r,s]=(0,a.useState)(),l=e?e.top:void 0,o=t?.theme==="almond";return(0,a.useEffect)(()=>{let e;if(void 0!==l){if(o){let t=document.getElementById(i.V.ContentContainer);t&&(e=l+t.scrollTop)}else e=l+window.pageYOffset;e&&e!==r&&s(e)}},[l,r,o]),r}},25575:(e,t,r)=>{r.d(t,{Tooltip:()=>o});var a=r(54568),n=r(60654),i=r(7620),s=r(31467),l=r(16600);function o({tip:e,children:t}){var r;return t?(0,a.jsx)(n.Kq,{delayDuration:0,children:(0,a.jsxs)(n.bL,{children:[(0,a.jsx)(n.l9,{children:(r=t,(0,i.isValidElement)(r)?r:(0,a.jsx)("span",{className:(0,l.cn)(s.x.Tooltip,"underline decoration-dotted decoration-2 underline-offset-4 decoration-gray-400 dark:decoration-gray-500"),children:r}))}),(0,a.jsx)(n.ZL,{children:(0,a.jsx)(n.UC,{collisionPadding:8,className:"z-50 bg-tooltip max-w-[16rem] text-center text-gray-50 text-xs px-1.5 py-1 rounded-lg border border-gray-50 dark:border-gray-500","data-component-part":"tooltip-content",children:e})})]})}):null}},27592:(e,t,r)=>{r.d(t,{D:()=>n});var a=r(83501);function n(e,t,r){return"object"!=typeof e.additionalProperties||Array.isArray(e.additionalProperties)?e.additionalProperties:(0,a.EW)(e.additionalProperties,t,void 0,r)}},28346:(e,t,r)=>{r.d(t,{ZoomImage:()=>s});var a=r(54568),n=r(91039);r(7620);let i=(0,n.default)(()=>r.e(2603).then(r.bind(r,42603)).then(e=>e.default),{loadableGenerated:{webpack:()=>[42603]}}),s=({children:e,noZoom:t})=>t?(0,a.jsx)(a.Fragment,{children:e}):(0,a.jsx)(i,{wrapElement:(()=>{let e=window.navigator.userAgent;return/Safari/.test(e)&&!/Chrome/.test(e)&&!/Chromium/.test(e)})()?"div":"span",zoomMargin:50,children:e})},29267:(e,t,r)=>{r.d(t,{w:()=>s});var a=r(63656),n=r(75448);let i=(e,t,r,a=0)=>{let s=!1,l=r?" | null":"";switch(e.type){case"any":return"any";case"null":return"null";case"object":if(e.title)return`${e.title} \xb7 object`;return"object";case"array":let o=(0,n.r)(e,t,void 0);if(o.some(e=>"array"===e.type)&&a>=5)return s=!0,`array${l}`;let d=new Set;o.forEach(e=>d.add(i(e,t,!1,a+1)));let c=Array.from(d);if(s)return`array${l}`;if(1===c.length&&"object"===c[0]&&e.title)return`${e.title} \xb7 object[]${l}`;if(1===c.length)return`${c[0]}[]${l}`;if(c.length>1&&e.title)return`${e.title} \xb7 array`;if(c.every(e=>"object"!==e&&"array"!==e))return`(${c.join(" | ")})[]${l}`;return`array${l}`;default:let p="format"in e&&"string"==typeof e.format?`${e.type}<${e.format}>`:e.type;return`${p}${l}`}},s=(e,t,r,n,s)=>{if(e.length<=1)return[i(e[0],t,n)];if(r)return(0,a.H)(e);let l=e.map(e=>i(e,t,n,0));return new Set(l).size===l.length?l:s?l.map((t,r)=>"any"===t?t:(e[r]?.title??`Option ${r+1}`)+" \xb7 "+t):l.map((t,r)=>(e[r]?.title??`Option ${r+1}`)+" \xb7 "+t)}},32498:(e,t,r)=>{r.d(t,{ae:()=>V,Ls:()=>G,zP:()=>Z,ib:()=>W,Yj:()=>B,z$:()=>D});var a=r(54568),n=r(46346),i=r(79460),s=r(78161),l=r(7620),o=r(44411),d=r(95876);let c=e=>{let t=(0,d.j)();return(0,l.useMemo)(()=>(0,o.e)(e,t),[e,t])};var p=r(9506),m=r(82026),u=r(71197);let x=()=>{let{mintConfig:e,docsConfig:t}=(0,l.useContext)(u.H6),r=e?.api?.paramFields?.expanded;return t?.api?.params?.expanded??r};var h=r(76949),f=r(32894);let g=()=>{let e=(0,l.useRef)(null),[t,r]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{let t=new IntersectionObserver(e=>{e[0]&&r(e[0].isIntersecting)},{rootMargin:"100px",threshold:0}),a=e.current;return a&&t.observe(a),()=>{a&&t.unobserve(a)}},[]),{ref:e,isVisible:t}};var y=r(27592),b=r(34049);let v=e=>{let t=(0,d.j)(),{location:r}=(0,i.ad)(),[a,n]=(0,l.useState)(!0),[s,o]=(0,l.useState)(void 0),[c,p]=(0,l.useState)(void 0);return(0,l.useEffect)(()=>{let a=!0;if(!e||"object"!==e.type){o(void 0),p(void 0),n(!1);return}return Promise.resolve().then(()=>{try{let i=(0,b.c)(e,t,r),s=(0,y.D)(e,t,r);a&&(o(i),p(s),n(!1))}catch(e){a&&(console.error("Error generating properties:",e),n(!1))}}),()=>{a=!1}},[e,t,r]),[a,s,c]};var j=r(75448),k=r(16600),w=r(44664),N=r(83897);let O=({options:e,selectedIndex:t,onSelectOption:r})=>(0,a.jsx)("div",{className:"-mt-2",children:(0,a.jsx)(N.Tabs,{defaultTabIndex:t,onClickTab:r,className:"!border-gray-100 !mb-4 dark:!border-white/10",children:e.map(e=>(0,a.jsx)(N.Tab,{title:e},e))})}),C=({schema:e,description:t})=>(0,a.jsx)(a.Fragment,{children:(void 0!==e.minItems||void 0!==e.maxItems)&&(0,a.jsx)("div",{className:(0,k.cn)("prose prose-sm prose-gray dark:prose-invert",t&&"mt-6"),children:(0,a.jsx)($,{minItems:e.minItems,maxItems:e.maxItems})})}),$=({minItems:e,maxItems:t})=>e===t&&void 0!==e?(0,a.jsxs)(a.Fragment,{children:["Required array length: ",(0,a.jsx)("code",{children:e})," element",e>1?"s":""]}):e&&t?(0,a.jsxs)(a.Fragment,{children:["Required array length: ",(0,a.jsx)("code",{children:`${e} - ${t}`})," element",t>1?"s":""]}):e?(0,a.jsxs)(a.Fragment,{children:["Minimum length: ",(0,a.jsx)("code",{children:e})]}):t?(0,a.jsxs)(a.Fragment,{children:["Maximum length: ",(0,a.jsx)("code",{children:t})]}):"";function S({example:e}){return(0,a.jsx)(w.V,{className:"overflow-wrap-anywhere text-[13px] [&_*]:text-[13px]",markdown:"object"==typeof e&&null!==e?`\`\`\`json
${function(e,t=45){return function e(t,r,a,n){return null===t?"null":void 0===t?"undefined":"string"==typeof t?JSON.stringify(t):"number"==typeof t||"boolean"==typeof t?String(t):Array.isArray(t)?function(t,r,a,n){if(0===t.length)return"[]";let i="["+t.map(t=>e(t,r,a,n)).join(", ")+"]";if(i.length<=r)return i;a++;let s="[\n"+t.map(t=>n.repeat(a)+e(t,r,a,n)).join(",\n")+"\n"+n.repeat(a-1)+"]";return a--,s}(t,r,a,n):"object"==typeof t&&null!==t?function(t,r,a,n){let i=Object.keys(t);if(0===i.length)return"{}";let s="{ "+i.map(i=>`${JSON.stringify(i)}: ${e(t[i],r,a,n)}`).join(", ")+" }";if(s.length<=r)return s;a++;let l="{\n"+i.map(i=>n.repeat(a)+`${JSON.stringify(i)}: ${e(t[i],r,a,n)}`).join(",\n")+"\n"+n.repeat(a-1)+"}";return a--,l}(t,r,a,n):String(t)}(e,t,0,"  ")}(e)}
\`\`\``:`\`${"null"===e?null:JSON.stringify(e,void 0,2)}\``})}let I=({schema:e})=>{let t=e.example,r=void 0!==e.examples?Array.isArray(e.examples)?e.examples:[e.examples]:void 0;return void 0!==t&&(0,a.jsxs)("div",{className:(0,k.cn)("flex prose prose-sm prose-gray dark:prose-invert mt-6 gap-1.5",("object"==typeof t||"object"==typeof r?.[0])&&"flex-col gap-y-3"),children:[(0,a.jsxs)("span",{children:["Example",r&&r.length?"s":"",":"]}),void 0!==r?(0,a.jsx)("div",{className:(0,k.cn)("*:my-2","object"!=typeof r[0]&&"inline *:mr-2 [&_*]:inline"),children:r.map((e,t)=>(0,a.jsx)(S,{example:e},t))}):(0,a.jsx)(S,{example:t})]})},A=({schema:e})=>{let t=e.exclusiveMinimum||void 0!==e.exclusiveMinimum,r=e.exclusiveMaximum||void 0!==e.exclusiveMaximum,n="";return(void 0!==e.maximum&&(n=`x ${r?"<":"<="} ${e.maximum}`),void 0!==e.minimum&&(n=`x ${t?">":">="} ${e.minimum}`),void 0!==e.maximum&&void 0!==e.minimum&&(n=`${e.minimum} ${t?"<":"<="} x ${r?"<":"<="} ${e.maximum}`),""===n&&void 0===e.multipleOf)?null:(0,a.jsxs)("div",{className:"prose prose-sm prose-gray dark:prose-invert mt-6",children:[n&&(0,a.jsxs)(a.Fragment,{children:["Required range: ",(0,a.jsx)("code",{children:n})]}),e.multipleOf&&(0,a.jsxs)(a.Fragment,{children:["Must be a multiple of ",(0,a.jsx)("code",{children:e.multipleOf})]})]})},M=({schema:e,description:t})=>(0,a.jsxs)(a.Fragment,{children:[void 0!==e.const&&(0,a.jsxs)("div",{className:(0,k.cn)("prose prose-sm prose-gray dark:prose-invert",t&&"mt-6"),children:["Allowed value: ",(0,a.jsx)("code",{children:`"${e.const}"`})]}),(void 0!==e.minLength||void 0!==e.maxLength)&&(0,a.jsx)("div",{className:(0,k.cn)("prose prose-sm prose-gray dark:prose-invert",t&&"mt-6"),children:(0,a.jsx)(T,{minLength:e.minLength,maxLength:e.maxLength})})]}),T=({minLength:e,maxLength:t})=>e===t&&void 0!==e?(0,a.jsxs)(a.Fragment,{children:["Required string length: ",(0,a.jsx)("code",{children:e})]}):e&&t?(0,a.jsxs)(a.Fragment,{children:["Required string length: ",(0,a.jsx)("code",{children:`${e} - ${t}`})]}):e?(0,a.jsxs)(a.Fragment,{children:["Minimum length: ",(0,a.jsx)("code",{children:e})]}):t?(0,a.jsxs)(a.Fragment,{children:["Maximum length: ",(0,a.jsx)("code",{children:t})]}):"",E=({fieldType:e,schema:t,name:r,location:n,typeOptions:i,selectedIndex:s,onSelectTypeOption:l,parentName:o,paramId:d,style:c,explode:p})=>{let u=i[s??0],x=t.description??(void 0===r?`The ${e} is of type \`${u}\`.`:void 0);return(0,a.jsxs)("div",{className:"py-6",children:[(0,a.jsx)(m.ob,{id:d,name:r,typeLabel:u,location:n,fieldType:e,required:t.required,deprecated:t.deprecated,defaultValue:t.default,typeOptions:i,selectedTypeOptionIndex:s,onSelectTypeOption:l,parentName:o,style:c,explode:p}),(0,a.jsxs)("div",{className:(0,k.cn)({"mt-4":x||L(t)}),children:[x&&(0,a.jsx)(w.V,{markdown:x}),L(t)&&(0,a.jsx)(R,{schema:t}),_(t)&&(0,a.jsx)(M,{schema:t,description:x}),P(t)&&(0,a.jsx)(A,{schema:t}),(0,a.jsx)(I,{schema:t})]})]})},L=e=>"enum<integer>"===e.type||"enum<number>"===e.type||"enum<string>"===e.type,R=({schema:e})=>0===e.enum.length?null:(0,a.jsxs)("div",{className:"whitespace-pre-wrap prose-sm mt-6",children:["Available options:"," ",e.enum.map((t,r)=>(0,a.jsxs)("div",{className:"inline-block",children:[(0,a.jsx)("code",{children:t}),r!==e.enum.length-1&&","," "]},`${t}-${r}`))]}),_=e=>"string"===e.type,P=e=>"number"===e.type||"integer"===e.type,q=({fieldType:e,schema:t,typeOptions:r,name:s,location:o,selectedIndex:u,onSelectTypeOption:y,parentName:b,depth:N=0,renderedRefs:$,circularRefs:S,paramId:A,shouldRenderChildren:M})=>{let T,{ref:E,isVisible:_}=g(),{anchor:P}=(0,i.ad)(),q=!!(A&&P?.includes(A)),F=M||_||q,z=(0,d.j)(),H=r[u??0],G=(0,h.U)(t),Z=x(),{originalIndices:J,typeOptionLabels:U,allObjects:Y}=c(G),[X,Q]=(0,l.useState)(0),K=J[X]??0;(0,f.H)(U.map(e=>(0,n.A)(e)),Q);let ee=(0,l.useCallback)(e=>{Q(e)},[]),et=Y&&U.length>1,er=(0,l.useMemo)(()=>{let e=({children:e})=>et?(0,a.jsxs)("div",{className:"mt-4 rounded-xl border border-gray-100 px-4 pb-4 pt-2 dark:border-white/10",children:[(0,a.jsx)(O,{options:U,onSelectOption:ee}),e]}):(0,a.jsx)(a.Fragment,{children:e});return e.displayName="PropFieldsContainer",e},[et,U,ee]),ea=G[K],[en,ei,es]=v(ea);if(void 0===ea)return null;let el=ea.refIdentifier??ea.title,eo=el&&S?.includes(el),ed=Object.entries(ei??{}),ec=eo?D({properties:ed,fieldType:e,parentName:b,name:s,depth:N}):B({currentSchemaRef:el,properties:ed,fieldType:e,parentName:b,name:s,depth:N,refs:new Set($),hideParentName:!0}),ep=G.length>1&&!G.every(e=>e.type===ea.type);switch(!0){case ep:ec.push((0,a.jsx)(V,{fieldType:"parameter",schemaArray:G,parentName:"",depth:N+1,renderedRefs:new Set($),name:""},"nestedArray"));break;case L(ea):ec.push((0,a.jsx)(V,{fieldType:"parameter",schemaArray:[ea],parentName:"",depth:N+1,renderedRefs:new Set($),name:""},"nestedArray"));break;case"array"===ea.type:T=function e(t,r,n){let i=t.props.schemaArray,s=i[0],l=t.props.depth??0,o=t.props.renderedRefs??new Set;if(i.length>1||"object"===s.type)return t;if("array"!==s.type)return;if(void 0!==s.minItems||void 0!==s.maxItems||void 0!==s.uniqueItems){let e=!Object.keys(s.items).length,t=(0,a.jsx)(V,{fieldType:"parameter",schemaArray:[s],parentName:"",depth:l+1,renderedRefs:new Set(o),name:""},"nestedArray");return e?(0,a.jsx)(p.Expandable,{defaultOpen:!1,lazy:l>=5,children:t}):t}let d=(0,j.r)(s,r,n);return e((0,a.jsx)(V,{fieldType:"parameter",schemaArray:d,parentName:"",depth:l+1,renderedRefs:new Set(o),name:null},"nestedArray"),r,n)}((0,a.jsx)(V,{fieldType:"parameter",schemaArray:[ea],parentName:"",depth:N+1,renderedRefs:new Set($),name:""},"nestedArray"),z,o),T?.props?.schemaArray?.[0]?.type==="array"&&(ec.push(T),T=void 0);break;case"object"==typeof es:ec.push((0,a.jsx)(V,{fieldType:e,schemaArray:es,name:"{key}",parentName:W(s,b),depth:N+1,renderedRefs:new Set($)},"{key}"))}if(void 0===s)return et||ec.length?(0,a.jsxs)(a.Fragment,{children:[t.description&&(0,a.jsx)("div",{className:"pt-6 pb-4",children:(0,a.jsx)(w.V,{markdown:t.description})}),(0,a.jsx)(er,{children:ec})]}):(0,a.jsxs)("div",{className:"pb-5 mb-5 border-gray-100 dark:border-gray-800 border-b last:border-b-0",children:[(0,a.jsx)(w.V,{markdown:t.description??`The ${e} is of type \`${H}\`.`}),L(ea)&&(0,a.jsx)(R,{schema:ea})]});let em=ec.length>0,eu=en||!F?(0,a.jsx)(p.Expandable,{}):(0,a.jsx)(p.Expandable,{defaultOpen:"all"===Z||q,lazy:N>=5,uniqueParamId:A,children:ec},A);return(0,a.jsxs)("div",{ref:E,className:"py-6",children:[(0,a.jsx)(m.ob,{id:A,name:s,location:o,typeLabel:H,required:t.required,fieldType:e,deprecated:t.deprecated,typeOptions:r,selectedTypeOptionIndex:u,onSelectTypeOption:y,parentName:b}),(0,a.jsxs)("div",{className:(0,k.cn)({"mt-4":t.description||et||L(ea)||!!ec.length}),children:[t.description&&(0,a.jsx)(w.V,{markdown:t.description}),(0,a.jsx)(C,{schema:t,description:t.description}),(0,a.jsxs)(er,{children:[ea.description&&!em&&(0,a.jsx)(w.V,{markdown:ea.description}),em?(0,a.jsx)("div",{className:(0,k.cn)(et?"object"===ea.type&&!ea.description&&"-mt-4":""),children:eu}):T&&(0,a.jsx)("div",{className:(0,k.cn)(("array"!==ea.type||T.props?.schemaArray?.[0]?.type==="object")&&"-mt-4"),children:T})]}),(0,a.jsx)(I,{schema:t})]})]})},F=({fieldType:e,schema:t,name:r,nullable:n,allObjects:s,location:o,selectedIndex:d,typeOptions:c,onSelectTypeOption:u,parentName:h,depth:f=0,hideDescription:y=!1,renderedRefs:b,circularRefs:j,paramId:N,shouldRenderChildren:C,style:$,explode:S})=>{let{ref:A,isVisible:M}=g(),{anchor:T}=(0,i.ad)(),E=!!(N&&T?.includes(N)),L=C||M||E,R=(s??!1)&&c.length>1,_=x(),P=s?`object${n?" | null":""}`:c[d??0],[q,F,z]=v(t),H=[...F?Object.entries(F).sort(([e],[r])=>{if(!t.requiredProperties?.length)return 0;let a=t.requiredProperties.includes(e);return a===t.requiredProperties.includes(r)?0:a?-1:1}):[]],G=t.refIdentifier??t.title,J=G&&j?.includes(G)?D({properties:H,fieldType:e,depth:f}):B({currentSchemaRef:G,properties:H,fieldType:e,parentName:h,name:r,depth:f,refs:new Set(b),style:$,explode:S});"object"==typeof z&&J.push((0,a.jsx)(V,{fieldType:e,schemaArray:z,name:"{key}",parentName:"deepObject"===$&&S?Z(r,h):W(r,h),depth:f+1,renderedRefs:new Set(b)},"{key}"));let U=(0,l.useCallback)(e=>{u?.(e)},[u]);if(void 0===r){let r=J.length>0?t.description:t.description??`The ${e} is of type \`${P}\`.`;return(0,a.jsxs)(a.Fragment,{children:[r&&!y&&(0,a.jsx)("div",{className:"pt-6 pb-4",children:(0,a.jsx)(w.V,{markdown:r})}),J.length>0&&J]})}let Y=J.length>0,X=q||!L?(0,a.jsx)(p.Expandable,{}):(0,a.jsx)(p.Expandable,{defaultOpen:"all"===_||E,lazy:f>=5,uniqueParamId:N,children:J},N);return(0,a.jsxs)("div",{ref:A,className:"py-6",children:[(0,a.jsx)(m.ob,{id:N,name:r,location:o,required:t.required,deprecated:t.deprecated,typeLabel:P,fieldType:e,typeOptions:s?[]:c,selectedTypeOptionIndex:d,onSelectTypeOption:U,parentName:h,style:$,explode:S}),(0,a.jsxs)("div",{className:(0,k.cn)({"mt-4":t.description||R||Y}),children:[r&&t.description&&(0,a.jsx)(w.V,{markdown:t.description}),(0,a.jsxs)("div",{className:(0,k.cn)({"mt-4 rounded-xl border border-gray-100 px-4 pb-4 pt-2 dark:border-white/10":R}),children:[R&&(0,a.jsx)(O,{selectedIndex:d,options:c,onSelectOption:U}),!r&&t.description&&(0,a.jsx)(w.V,{markdown:t.description}),Y&&(0,a.jsx)("div",{className:(0,k.cn)(R&&(!r&&!t.description||!!r)&&"-mt-4"),children:X}),(0,a.jsx)(I,{schema:t})]})]})]})},z=({fieldType:e,schemaArray:t,name:r,location:n,parentName:i,depth:s=0,hideDescription:o=!1,renderedRefs:d=new Set,circularRefs:p,paramId:m,style:u,explode:x})=>{let h,{originalIndices:f,typeOptionLabels:g,nullable:y,allObjects:b}=c(t),[v,j]=(0,l.useState)(0),k=t[f[v]??0];if(!k)return null;let w=m??G(e,r,i);switch(k.type){case"array":h=(0,a.jsx)(q,{paramId:w,fieldType:e,schema:k,name:r,location:n,typeOptions:g,selectedIndex:v,onSelectTypeOption:j,parentName:i,depth:s,renderedRefs:d,circularRefs:p});break;case"object":h=(0,a.jsx)(F,{paramId:w,fieldType:e,schema:k,name:r,nullable:y,allObjects:b,location:n,typeOptions:g,selectedIndex:v,onSelectTypeOption:j,parentName:i,depth:s,hideDescription:o,renderedRefs:d,circularRefs:p,style:u,explode:x});break;default:h=(0,a.jsx)(E,{paramId:w,fieldType:e,schema:k,name:r,location:n,typeOptions:g,selectedIndex:v,onSelectTypeOption:j,parentName:i,style:u,explode:x})}return(0,a.jsx)("div",{className:"border-gray-100 dark:border-gray-800 border-b last:border-b-0",children:h})},H=({fieldType:e,schemaArray:t,name:r,location:n,parentName:i,depth:s=0,hideDescription:o=!1,renderedRefs:d=new Set,circularRefs:p,paramId:m,style:u,explode:x})=>{let h,{ref:f,isVisible:y}=g(),{originalIndices:b,typeOptionLabels:v,nullable:j,allObjects:k}=c(t),[w,N]=(0,l.useState)(0),O=t[b[w]??0];if(!O)return null;let C="array"!==O.type&&"object"!==O.type,$=m??G(e,r,i);return C?(0,a.jsx)("div",{className:"border-gray-100 dark:border-gray-800 border-b last:border-b-0",children:(0,a.jsx)(E,{paramId:$,fieldType:e,schema:O,name:r,location:n,typeOptions:v,selectedIndex:w,onSelectTypeOption:N,parentName:i,style:u,explode:x})}):("array"===O.type&&(h=(0,a.jsx)(q,{paramId:$,fieldType:e,schema:O,name:r,location:n,typeOptions:v,selectedIndex:w,onSelectTypeOption:N,parentName:i,depth:s,renderedRefs:d,circularRefs:p,shouldRenderChildren:y})),"object"===O.type&&(h=(0,a.jsx)(F,{paramId:$,fieldType:e,schema:O,name:r,nullable:j,allObjects:k,location:n,typeOptions:v,selectedIndex:w,onSelectTypeOption:N,parentName:i,depth:s,hideDescription:o,renderedRefs:d,circularRefs:p,shouldRenderChildren:y,style:u,explode:x})),(0,a.jsx)("div",{ref:f,className:"border-gray-100 dark:border-gray-800 border-b last:border-b-0",children:h}))},V=({fieldType:e,schemaArray:t,name:r,location:n,parentName:s,depth:l=0,hideDescription:o=!1,renderedRefs:d=new Set,circularRefs:c,paramId:p,style:m,explode:u})=>{let{anchor:x}=(0,i.ad)();return(0,a.jsx)(x?z:H,{fieldType:e,schemaArray:t,name:r,location:n,parentName:s,depth:l,style:m,explode:u,hideDescription:o,renderedRefs:d,circularRefs:c,paramId:p})},B=({currentSchemaRef:e,properties:t,fieldType:r,parentName:n,name:i,depth:l,refs:o,style:d,explode:c,hideParentName:p})=>{let m=new Set(o);return(e&&m.add(e),m.size>0)?t.map(e=>{let[t,o]=e,u=o[0],x=(0,s.Y)(u),h=G(r,i,n,t),f=p?"":i,g=[];return Object.values(x).some(e=>"string"==typeof e&&m.has(e))&&(g=Object.values(x).filter(e=>"string"==typeof e&&m.has(e))),(0,a.jsx)(V,{paramId:h,fieldType:r,schemaArray:o,name:t,parentName:"deepObject"===d&&c?Z(f,n):W(f,n),depth:l+1,renderedRefs:new Set(m),circularRefs:g},t)}):t.map(([e,t])=>{let s=G(r,i,n,e),o=p?"":i;return(0,a.jsx)(V,{paramId:s,fieldType:r,schemaArray:t,name:e,parentName:"deepObject"===d&&c?Z(o,n):W(o,n),depth:l+1,renderedRefs:new Set,style:d,explode:c},e)})},D=({properties:e,fieldType:t,parentName:r,name:n,depth:i})=>e.map(e=>{let[s,l]=e,o=l[0];"array"===o.type?o.items=[{type:"any"}]:"object"===o.type&&(o.properties={});let d=G(t,n,r),c=`${d}-${s}`;return(0,a.jsx)(V,{fieldType:t,schemaArray:[o],name:s,paramId:c,depth:i+1,renderedRefs:new Set},s)}),G=(e,t,r,a)=>(0,n.A)(`${e?`${e}-`:""}${r?`${r}-`:""}${t||""}-${a||""}`,{decamelize:!0}),W=(e,t)=>e?`${t?`${t}`:""}${e}.`:"",Z=(e,t)=>e?t?`${t}${e}][`:`${e}[`:""},32894:(e,t,r)=>{r.d(t,{H:()=>n});var a=r(7620);let n=(e,t)=>{(0,a.useEffect)(()=>{let r=window.location.hash.substring(1);if(!r)return;let a=e.indexOf(r);if(-1!==a)return void t(a);setTimeout(()=>{(e=>{let t=document.getElementById(e);if(t)return t.click();for(let t of document.querySelectorAll("[data-child-tab-ids]")){let r=t.getAttribute("data-child-tab-ids");if(r)try{let a=JSON.parse(r);if(Array.isArray(a)&&a.includes(e))return t.click(),setTimeout(()=>{let t=document.getElementById(e);t&&t.click()}),!0}catch(e){continue}}})(r)})},[e,t])}},33227:(e,t,r)=>{r.d(t,{D:()=>p,H:()=>c});var a=r(54568);let n={fonts:{body:{family:"Google Sans"},heading:{family:"Google Sans"}}},i={fonts:{body:{family:"Geist Mono"},heading:{family:"Geist Mono"}}},s={title:"font-semibold",headings:"font-semibold"};function l({font:e}){return e?e.source?(0,a.jsx)("style",{children:`@font-face {
  font-family: '${e.family}';
  src: url('${e.source}') format('${e.format}');
  font-weight: ${e.weight};
}`}):(0,a.jsx)("link",{href:`https://fonts.googleapis.com/css2?family=${e.family.replace(/\s+/g,"+")}:wght@${e.weight??"400;500;600;700;800"}&display=block`,rel:"stylesheet"}):null}function o({heading:e,body:t}){return(0,a.jsx)("style",{children:`:root {
  ${e?.family?`--font-family-headings-custom: "${e.family}";`:""}
  ${e?.weight?`--font-weight-headings-custom: ${e.weight};`:""}
  ${t?.family?`--font-family-body-custom: "${t.family}";`:""}
  ${t?.weight?`--font-weight-body-custom: ${t.weight};`:""}
}`})}var d=r(69394);function c(e){return"maple"===e?s:"linden"===e?i:"almond"===e?n:{}}function p({theme:e,fonts:t}){let r=c(e).fonts;if(!t&&!r)return null;let n=t??r,i=(0,d.W)(n,"heading"),s=(0,d.W)(n,"body");return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l,{font:i}),(0,a.jsx)(l,{font:s}),(0,a.jsx)(o,{heading:i,body:s})]})}},34049:(e,t,r)=>{r.d(t,{c:()=>n});var a=r(83501);function n(e,t,r){return Object.fromEntries(Object.entries(e.properties).sort(([t],[r])=>{let a=e.requiredProperties?.includes(t)??!1;return a===(e.requiredProperties?.includes(r)??!1)?0:a?-1:1}).map(([n,i])=>Array.isArray(i)?[n,i]:[n,(0,a.EW)(i,t,e.requiredProperties?.includes(n),r)]))}},35319:(e,t,r)=>{r.r(t),r.d(t,{MDXContentContext:()=>c,MDXContentProvider:()=>p,MDXContentReducer:()=>l,initialState:()=>s,useInitializeMDXContentReducer:()=>d,useMDXContentReducer:()=>o});var a=r(54568),n=r(7620),i=r(71197);let s={headings:[],apiBaseIndex:0,codeExamples:{},pageMetadata:{},selectedBodyContentType:void 0,selectedResponseContentType:void 0,tableOfContents:[],changelogFilters:[]},l=(e,t)=>{switch(t.type){case"set_state":return{...e,...t.payload};case"register_heading":let{id:r,top:a,isAtRootLevel:n}=t.payload;return{...e,headings:[...e.headings.filter(e=>r!==e.id),{id:r,top:a,isAtRootLevel:n}]};case"unregister_heading":return{...e,headings:e.headings.filter(e=>t.payload!==e.id)};case"set_selected_body_content_type":return{...e,selectedBodyContentType:t.payload};case"set_selected_response_content_type":return{...e,selectedResponseContentType:t.payload};case"set_api_base_index":return{...e,apiBaseIndex:t.payload};case"toggle_changelog_filter":let i=e.changelogFilters.findIndex(e=>e.tag===t.payload.tag);if(-1!==i){let r=[...e.changelogFilters];return r[i]=t.payload,{...e,changelogFilters:r}}return{...e,changelogFilters:[...e.changelogFilters,t.payload]}}},o=e=>{let t={...s,...e};return(0,n.useReducer)(l,t)};function d(){let{pageMetadata:e,mdxExtracts:t}=(0,n.useContext)(i.NQ),r=o({pageMetadata:e,selectedBodyContentType:Object.keys(t?.endpoint?.request.body??{})[0],...t});return(0,n.useMemo)(()=>r,[r])}let c=(0,n.createContext)([s,()=>null]);function p({children:e}){let t=d();return(0,a.jsx)(c.Provider,{value:t,children:e})}c.displayName="MDXContentContext"},36401:(e,t,r)=>{r.d(t,{ResponseField:()=>i});var a=r(54568),n=r(82026);function i({name:e,type:t,hidden:r,default:i,required:s,deprecated:l,children:o,id:d,pre:c,post:p}){return(0,a.jsx)(n.t1,{name:e,type:t,hidden:r,defaultValue:i,required:s,deprecated:l,id:d,pre:c,post:p,children:o})}},42649:(e,t,r)=>{r.d(t,{Update:()=>u});var a=r(54568),n=r(7620),i=r(50139),s=r(19637),l=r(22968),o=r(31467),d=r(60710),c=r(16600),p=r(26997),m=r(43059);let u=(0,n.forwardRef)(({children:e,label:t,id:r,description:u,tags:x,className:h,...f},g)=>{let[y,b]=(0,s.O)(),[v,j]=(0,n.useState)(null),[k]=(0,i.y)(j),w=(0,l.T)(v),N=x?.map(e=>e.trim()).filter(Boolean),O=!!y;(0,n.useEffect)(()=>{if(O)return void 0!==w&&b({type:"register_heading",payload:{id:r,top:w,isAtRootLevel:!0}}),()=>{b({type:"unregister_heading",payload:r})}},[w,r,O,b]);let C=(0,n.useCallback)(()=>{(0,p.l)(`https://${window.location.host}${window.location.pathname}#${r}`),window.location.hash=r,(0,m.h)({id:r})},[r]);return(0,n.useMemo)(()=>{if(!y.changelogFilters.length)return!0;let e=y.changelogFilters.filter(e=>e.active);return 0===e.length||N?.some(t=>e.some(e=>e.tag===t))},[y.changelogFilters,N])?(0,a.jsxs)("div",{className:(0,c.cn)(o.x.Update,"flex flex-col relative items-start w-full lg:flex-row gap-2 lg:gap-6 py-8 update-container",h),ref:g,id:r,...f,children:[(0,a.jsxs)("div",{className:"lg:sticky top-[var(--scroll-mt)] group flex flex-col w-full lg:w-[160px] items-start flex-shrink-0 justify-start",children:[(0,a.jsx)("div",{className:"absolute",children:(0,a.jsxs)("a",{href:`#${r}`,className:"-ml-10 flex items-center opacity-0 border-0 group-hover:opacity-100","aria-label":"Navigate to changelog",children:["​",(0,a.jsx)("div",{className:"w-6 h-6 rounded-md flex items-center justify-center shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 bg-white ring-1 ring-gray-400/30 dark:ring-gray-700/25 hover:ring-gray-400/60 dark:hover:ring-white/20",children:(0,a.jsx)(d.qY,{})})]})}),(0,a.jsx)("div",{className:"cursor-pointer px-2 py-1 rounded-lg text-sm flex items-center flex-grow-0 justify-center font-medium bg-primary/10 text-primary dark:text-primary-light",onClick:C,contentEditable:!1,"data-component-part":"update-label",children:t}),N?.length&&(0,a.jsx)("div",{className:"px-1 flex flex-wrap gap-2 text-secondary dark:text-secondary-light mt-3 text-sm","data-component-part":"update-tag-list",children:N.map(e=>(0,a.jsx)("span",{className:"inline-block rounded-lg text-sm font-medium","data-component-part":"update-tag",children:e},e))}),u&&(0,a.jsx)("div",{className:"px-1 text-secondary dark:text-secondary-light mt-3 text-sm max-w-[160px] break-words",contentEditable:!1,"data-component-part":"update-description",children:u})]}),(0,a.jsx)("div",{className:"flex-1 overflow-hidden px-0.5 max-w-full",ref:k,children:(0,a.jsx)("div",{className:"prose-sm","data-component-part":"update-content",children:e})})]}):null});u.displayName="Update"},43059:(e,t,r)=>{r.d(t,{h:()=>a});function a({id:e,shouldReturnEarly:t,element:r,getElement:n,checkIfShouldScroll:i,preScrollCallback:s,postScrollCallback:l}={}){if(t)return;let o=window.location.hash.substring(1);if(!o&&!e)return;let d=r??n?.(e??o)??document.getElementById(e??o);d&&(i?.(d,o)??!0)&&(s?.(d),requestAnimationFrame(()=>{d.scrollIntoView(),l?.()}))}},44411:(e,t,r)=>{r.d(t,{e:()=>n});var a=r(29267);let n=(e,t)=>{if(e.every(({type:e})=>"null"===e))return{typeOptionLabels:["null"],originalIndices:[0],nullable:!0,allObjects:!1};let r=e.map((e,t)=>({schema:e,originalIndex:t})).filter(({schema:e})=>"null"!==e.type),n=e.some(e=>"null"===e.type),i=r.every(({schema:e})=>"object"===e.type);return{typeOptionLabels:(0,a.w)(r.map(({schema:e})=>e),t,i,n),originalIndices:r.map(({originalIndex:e})=>e),nullable:n,allObjects:i}}},44664:(e,t,r)=>{r.d(t,{$:()=>l,V:()=>s});var a=r(54568),n=r(79321),i=r(16600);let s=({markdown:e,className:t})=>(0,a.jsx)("div",{className:(0,i.cn)("prose prose-sm prose-gray dark:prose-invert",t),children:(0,a.jsx)(n.o,{children:e})}),l=({markdown:e,className:t})=>(0,a.jsx)("div",{className:(0,i.cn)("prose prose-sm prose-gray dark:prose-invert",t),children:(0,a.jsx)(n.w,{children:e})})},47181:(e,t,r)=>{r.d(t,{a:()=>a,x:()=>n});let a={query:{},path:{},header:{},body:{},cookie:{}};function n(e,t,r){a[e][t]=r}},47457:(e,t,r)=>{r.d(t,{CodeGroup:()=>s,SnippetGroup:()=>l});var a=r(54568),n=r(68629),i=r(88374);function s({dropdown:e,children:t,isSmallText:r,noMargins:s}){let l=(0,i.p)("docs.code_block.copy");return(0,a.jsx)(n.l,{dropdown:e,isSmallText:r,noMargins:s,onCopied:(e,t)=>l({code:t}),children:t})}function l(e){return(0,a.jsx)(s,{...e})}},49580:(e,t,r)=>{r.r(t),r.d(t,{default:()=>i});var a=r(54568),n=r(63649);r(7620);let i=({children:e})=>"object"==typeof e&&"props"in e&&e.props?.dangerouslySetInnerHTML?(0,a.jsx)(a.Fragment,{children:e.props.dangerouslySetInnerHTML.__html?(0,a.jsx)(n.A,{html:e.props.dangerouslySetInnerHTML.__html}):e}):null},50443:(e,t,r)=>{r.d(t,{Step:()=>o,Steps:()=>d});var a=r(54568),n=r(31467),i=r(89619),s=r(16600);let l=["p","h2","h3","h4"],o=({stepNumber:e=1,icon:t,iconType:r,title:o,children:d,titleSize:c="p",isLast:p=!1})=>{let m=l.includes(c)?c:"p",u="string"==typeof t?(0,a.jsx)(i.VE,{icon:t,iconType:r,className:"h-3 w-3 bg-gray-900 dark:bg-gray-50",overrideColor:!0}):void 0===t?Number(e):t;return(0,a.jsxs)("div",{role:"listitem",className:(0,s.cn)(n.x.Step,"relative flex items-start pb-5"),children:[(0,a.jsx)("div",{className:(0,s.cn)("absolute w-px h-[calc(100%-2.75rem)] top-[2.75rem]",p?"bg-transparent bg-gradient-to-b from-gray-200 dark:from-white/10 via-80% to-transparent":"bg-gray-200/70 dark:bg-white/10"),contentEditable:!1}),(0,a.jsx)("div",{className:"absolute ml-[-13px] py-2","data-component-part":"step-number",contentEditable:!1,children:(0,a.jsx)("div",{className:"size-7 shrink-0 rounded-full bg-gray-50 dark:bg-white/10 text-xs text-gray-900 dark:text-gray-50 font-semibold flex items-center justify-center",children:u})}),(0,a.jsxs)("div",{className:"w-full overflow-hidden pl-8 pr-px",children:[{p:(0,a.jsx)("p",{className:"mt-2 font-semibold prose dark:prose-invert text-gray-900 dark:text-gray-200",contentEditable:!1,"data-component-part":"step-title",children:o}),h2:(0,a.jsx)("h2",{className:"mt-2",contentEditable:!1,"data-component-part":"step-title",children:o}),h3:(0,a.jsx)("h3",{className:"mt-2",contentEditable:!1,"data-component-part":"step-title",children:o}),h4:(0,a.jsx)("h4",{className:"mt-2",contentEditable:!1,"data-component-part":"step-title",children:o})}[m],(0,a.jsx)("div",{"data-component-part":"step-content",className:"prose dark:prose-invert",children:d})]})]})},d=({children:e,titleSize:t})=>(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{role:"list",className:(0,s.cn)(n.x.Steps,"ml-3.5 mt-10 mb-6"),children:Array.isArray(e)?e.map(({props:r},n)=>(0,a.jsx)(o,{...r,stepNumber:r.stepNumber??n+1,...t&&{titleSize:t},isLast:n===e.length-1},`${r.title} + ${n}`)):e})})},50816:(e,t,r)=>{r.d(t,{CodeBlock:()=>s});var a=r(54568),n=r(88638),i=r(88374);function s({filename:e,children:t,expandable:r,...s}){let l=(0,i.p)("docs.code_block.copy");return(0,a.jsx)(n.NG,{filename:e,expandable:r?.toLowerCase()==="true",onCopied:(e,t)=>l({code:t}),...s,children:t})}},55595:(e,t,r)=>{r.d(t,{Accordion:()=>g});var a=r(54568),n=r(46346),i=r(92012),s=r.n(i),l=r(7620),o=r(88374),d=r(31467),c=r(89619),p=r(16600),m=r(26997),u=r(43059),x=r(80976),h=r(93351);let f=function({id:e,title:t,description:r,open:n,icon:i,coverClass:s}){let{divisions:o}=(0,l.useContext)(h._),{banner:d}=(0,l.useContext)(x.y),m=o.tabs.length>0,u=!!d;return(0,a.jsxs)("summary",{className:(0,p.cn)("relative not-prose flex flex-row items-center content-center w-full cursor-pointer","list-none [&::-webkit-details-marker]:hidden",s),"aria-controls":e+" accordion children","aria-expanded":n,"data-component-part":"accordion-button",children:[(0,a.jsx)("div",{id:e,className:(0,p.cn)("absolute",u&&m?"-top-[10.5rem]":u?"-top-[7rem]":m?"-top-[8rem]":"-top-[4.5rem]")}),(0,a.jsx)("div",{className:"mr-0.5","data-component-part":"accordion-caret-right",children:(0,a.jsx)(c.Ay,{icon:"caret-right",iconType:"solid",className:(0,p.cn)("h-3 w-3 transition bg-gray-700 dark:bg-gray-400",n&&"duration-200 rotate-90 -mt-1",!n&&"duration-75")})}),i?(0,a.jsx)("div",{className:"h-4 w-4 fill-gray-800 dark:fill-gray-100 text-gray-800 dark:text-gray-100","data-component-part":"accordion-icon",children:i}):null,(0,a.jsxs)("div",{className:"leading-tight text-left",contentEditable:!1,"data-component-part":"accordion-title-container",children:[(0,a.jsx)("p",{className:"m-0 font-medium text-gray-900 dark:text-gray-200","data-component-part":"accordion-title",children:t}),r?(0,a.jsx)("p",{className:"m-0 text-gray-900 dark:text-gray-200","data-component-part":"accordion-description",children:r}):null]})]})};function g({title:e,description:t,defaultOpen:r=!1,icon:n,iconType:i,children:s,_disabled:l}){let d=(0,o.p)("docs.accordion.open"),p=(0,o.p)("docs.accordion.close"),m="string"==typeof n?(0,a.jsx)(c.VE,{icon:n,iconType:i,className:"w-4 h-4"}):n;return(0,a.jsx)(b,{title:e,description:t,defaultOpen:r,onChange:t=>{t?d({title:e}).catch(console.error):p({title:e}).catch(console.error)},icon:m,_disabled:l,children:s})}let y=(0,l.createContext)({parentIds:[]});function b({title:e,description:t,defaultOpen:r=!1,icon:i,onChange:o,variant:c="rounded",children:x,_disabled:h}){let g="string"==typeof e?(0,n.A)(e.replace(":","-"),{decamelize:!1}):void 0,b=window.location.hash.substring(1).split(":"),v=(0,l.useContext)(y),[j,k]=(0,l.useState)(()=>{var e;return!!g&&(e=v.parentIds,!b||0===b.length||r?r:!!s()(e,b.slice(0,b.indexOf(g)))&&b.indexOf(g)===e.length)});(0,l.useEffect)(()=>{g&&j&&b&&b[b.length-1]===g&&s()(v.parentIds,b.slice(0,b.indexOf(g)))&&(0,u.h)({id:g,preScrollCallback(){history.scrollRestoration="manual"}})},[]);let w=e=>`${window.location.pathname}${e?`#${e}`:""}`;function N(e,t){if(h||"undefined"==typeof document||!document.hasFocus())return;let r=w(e.join(t));(0,m.l)(r),window.history.replaceState({...window.history.state,as:r,url:r},"",r)}let{parentClass:O,coverClass:C,contentClass:$}="minimalist"===c?{parentClass:"",coverClass:"[&>div]:ml-2 py-1 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200",contentClass:"mt-2 pt-1 mb-4 mx-[7px] px-4 border-l border-gray-100 dark:border-gray-800/50"}:{parentClass:"border-standard rounded-2xl mb-3 overflow-hidden bg-background-light dark:bg-codeblock",coverClass:"py-4 px-5 space-x-2 hover:bg-gray-100 hover:dark:bg-gray-800 rounded-t-xl",contentClass:"mt-2 mb-4 mx-6"};return(0,a.jsx)(y.Provider,{value:{...v,parentIds:[...v.parentIds,...void 0!=g?[g]:[]]},children:(0,a.jsxs)("details",{role:"button",open:j,onToggle:e=>{let t=e.currentTarget.open;if(t!==j){k(t);if(k(t),!h){if(t&&g)N([...v.parentIds,g],":");else if(v.parentIds.length>0)N(v.parentIds,":");else{let e=w("");window.history.replaceState({...window.history.state,as:e,url:e},"",e)}o&&o(t)}}},className:(0,p.cn)(d.x.Accordion,O,"cursor-default"),children:[(0,a.jsx)(f,{id:g,title:e,description:t,open:j,icon:i,coverClass:C}),(0,a.jsx)("div",{id:g+" accordion children",role:"contentinfo",className:(0,p.cn)($,"prose prose-gray dark:prose-invert overflow-x-auto cursor-default"),"data-component-part":"accordion-content",children:x})]},"string"==typeof e?e:"accordion")})}},55849:(e,t,r)=>{r.d(t,{Icon:()=>l});var a=r(54568),n=r(31467),i=r(89619),s=r(16600);function l({icon:e,iconType:t,color:r,size:l}){return(0,a.jsx)(i.VE,{icon:e,iconType:t,color:r,style:{width:l||16,height:l||16,display:"inline-block",verticalAlign:"middle"},className:(0,s.cn)(n.x.Icon,!r&&"bg-primary dark:bg-primary-light","inline")})}},56486:(e,t,r)=>{r.d(t,{b:()=>l});var a=r(54568),n=r(31467),i=r(89619),s=r(16600);let l=({options:e,selectedIndex:t,onSelectOption:r,noBackground:l})=>(0,a.jsx)("div",{className:(0,s.cn)(n.x.OptionDropdown,"inline-flex relative !mr-0"),children:(0,a.jsxs)("div",{className:(0,s.cn)("font-mono text-xs font-medium !inline-block !leading-4 items-center rounded-md space-x-1.5 text-gray-600 dark:text-gray-200 py-0.5",!l&&"bg-gray-100/50 dark:bg-white/5"),children:[(0,a.jsx)("select",{className:"flex bg-transparent focus:outline-0 cursor-pointer text-start pl-2 pr-6 hover:text-gray-950 dark:hover:text-white",onChange:e=>r?.(e.target.selectedIndex),value:t,children:e.map((e,t)=>(0,a.jsx)("option",{value:t,children:e},t))}),(0,a.jsx)(i.Ay,{icon:"angle-down",iconType:"solid",className:"absolute top-1/2 -translate-y-1/2 right-4 h-2.5 w-2.5 shrink-0 bg-gray-500 dark:bg-gray-400 pointer-events-none"})]})})},57812:(e,t,r)=>{r.d(t,{AccordionGroup:()=>s});var a=r(54568),n=r(31467),i=r(16600);function s({children:e}){return(0,a.jsx)("div",{className:(0,i.cn)(n.x.AccordionGroup,"[&>details]:border-0 [&>details]:rounded-none [&>details>summary]:rounded-none [&>details]:mb-0 overflow-hidden mt-0 mb-3 rounded-xl prose prose-gray dark:prose-invert divide-y divide-inherit border dark:border-gray-800/50"),children:e})}},63656:(e,t,r)=>{r.d(t,{H:()=>a});let a=e=>e.map(({title:e},t)=>e??`Option ${t+1}`)},68629:(e,t,r)=>{r.d(t,{l:()=>N});var a=r(54568),n=r(11334),i=r(7620),s=r(71197),l=r(28963),o=r(31467),d=r(89619),c=r(16600),p=r(68718),m=r(60502),u=r(5121),x=r(53330),h=r(88638),f=r(35720),g=r(49341),y=r(44902),b=r(95391);let v={bash:"bash",blade:"laravel",c:"c",css:"css","c#":"csharp","c++":"cplusplus",dart:"dart",go:"go",html:"html5",java:"java",javascript:"javascript",jsx:"javascript",json:"json",kotlin:"kotlin",lua:"lua",markdown:"markdown",mdx:"markdown",php:"php",powershell:"powershell",python:"python",ruby:"ruby",rust:"rust",solidity:"solidity",swift:"swift",typescript:"typescript",tsx:"typescript",yaml:"yaml","node.js":"javascript",".net":"dot-net"},j=({language:e,className:t})=>{let r=(e=>{let t=b.shikiLangMap[e.toLowerCase()]||e.toLowerCase();return v[t]?`https://mintlify.b-cdn.net/devicon/${v[t]}.svg`:null})(e);return r?(0,a.jsx)("svg",{className:(0,c.cn)("w-3.5 h-3.5 shrink-0",t),style:{WebkitMaskImage:`url(${r})`,WebkitMaskRepeat:"no-repeat",WebkitMaskPosition:"center",maskImage:`url(${r})`,maskRepeat:"no-repeat",maskPosition:"center",maskSize:"100%",backgroundColor:"currentColor"}}):null},k=({selectedLanguage:e,setSelectedLanguage:t,languages:r})=>{let n=r.length>1,{docsConfig:l}=(0,i.useContext)(s.H6),[o,d]=(0,i.useState)(!1),p=l?.styling?.codeblocks,m=e=>{let t=b.shikiLangMap[e.toLowerCase()];return t?(0,y.DL)(t):e};return(0,a.jsxs)(g.rI,{open:o,onOpenChange:d,children:[(0,a.jsx)(g.ty,{disabled:!n,className:"select-none bg-transparent dark:bg-transparent pb-1 text-xs font-medium cursor-default",children:(0,a.jsxs)("div",{className:(0,c.cn)("flex gap-1 items-center pl-2.5 pr-1.5 py-[5px] rounded-[10px] border text-gray-500 dark:text-gray-400",n?"cursor-pointer":"cursor-default",n&&"system"===p&&"hover:bg-gray-200/50 dark:hover:bg-gray-700/70 hover:text-primary dark:hover:text-primary-light",n&&("dark"===p||void 0==p)&&"hover:bg-gray-700/70 hover:text-primary-light",o?"ring-1 dark:ring-gray-800/50 "+("system"===p?"ring-gray-200/70 ":"ring-gray-800/50")+" border-gray-600/50 dark:border-gray-400/50":"border-transparent"),children:[(0,a.jsx)(j,{language:e}),(0,a.jsx)("p",{className:"truncate font-medium",children:m(e)}),n&&(0,a.jsx)(f.A,{className:"w-3.5 h-3.5 shrink-0"})]})}),(0,a.jsx)(g.SQ,{className:(0,c.cn)("p-1 overflow-y-auto min-w-[170px] border dark:border-white/10 dark:bg-codeblock","system"===p&&"border-gray-200/70",("dark"===p||void 0==p)&&"border-white/10 bg-codeblock"),align:"end",sideOffset:0,children:r.map((r,n)=>{let i=r===e;return(0,a.jsxs)(g._2,{isSelected:i,onSelect:()=>t(r),className:(0,c.cn)("flex items-center py-1.5 gap-1.5 text-xs","system"===p&&"hover:text-primary hover:bg-primary/10 dark:hover:text-primary-light dark:hover:bg-primary-light/10",("dark"===p||void 0==p)&&"text-primary-light dark:text-primary-light hover:text-primary-light hover:bg-primary-light/10",r===e&&"system"===p&&"text-primary dark:text-primary-light font-medium",r===e&&("dark"===p||void 0==p)&&"text-primary-light dark:text-primary-light font-medium",r!==e&&"system"===p&&"text-gray-500 dark:text-white/50",r!==e&&("dark"===p||void 0==p)&&"text-white/50"),children:[(0,a.jsx)(j,{language:r}),(0,a.jsx)("div",{className:"flex items-center min-w-0 flex-1 font-medium",children:m(r)})]},n)})})]})};var w=r(58397);let N=(0,i.forwardRef)(function({children:e,onCopied:t,onChange:r,isSmallText:f,className:g,noMargins:y,dropdown:b,...v},j){let{docsConfig:N}=(0,i.useContext)(s.H6),{preferredCodeLanguage:C,setPreferredCodeLanguage:$}=(0,i.useContext)(l.O),S=Array.isArray(e)?e:i.Children.toArray(e),I=(0,i.useCallback)(e=>b?e.props.language:e.props.filename,[b]),A=(0,i.useCallback)(e=>e?S.findIndex(t=>I(t)===e):-1,[S,I]),[M,T]=(0,i.useState)(()=>{let e=A(window.localStorage.getItem("code"));return -1===e?0:e}),E=(0,i.useMemo)(()=>S.map(e=>e.props.filename).join(","),[S]),L=(0,i.useMemo)(()=>S.map(e=>e.props.language).join(","),[S]);(0,i.useEffect)(()=>{if(!C){0!==M&&T(0);return}let e=A(C);-1!==e&&M!==e&&T(e)},[E,L,C,A]);let R=(0,i.useCallback)(e=>{let t=Number(e);T(t);let a=S[t],n=a?I(a):null;n&&$&&$(n),r&&r(t)},[S,I,$,r]);if(!e)return null;if(0===S.length)return console.warn("CodeGroup has no children, expected at least one CodeBlock child."),null;let _=N?.styling?.codeblocks,P=Number(M);return(0,a.jsxs)(n.bL,{ref:j,value:String(M),onValueChange:R,className:(0,c.cn)(o.x.CodeGroup,"p-0.5 mt-5 mb-8 flex flex-col not-prose relative overflow-hidden rounded-2xl border border-gray-950/10 dark:border-white/10",y&&"my-0","system"===_&&"bg-gray-50 dark:bg-white/5 dark:codeblock-dark text-gray-950 dark:text-gray-50 codeblock-light",("dark"===_||void 0==_)&&"border-transparent bg-codeblock dark:bg-white/5 text-gray-50 codeblock-dark",g),...v,asChild:!1,children:[(0,a.jsxs)("div",{className:"flex items-center justify-between gap-2 relative px-2.5","data-component-part":"code-group-tab-bar",children:[b?(0,a.jsx)(()=>(0,a.jsxs)("div",{className:"flex items-center gap-1.5 text-xs font-medium min-w-0",children:[S[P]?.props.icon&&"string"==typeof S[P]?.props.icon&&(0,a.jsx)(d.VE,{icon:S[P]?.props.icon,iconType:"regular",className:(0,c.cn)("h-3.5 w-3.5 bg-gray-500 dark:bg-gray-400",o.x.CodeBlockIcon),overrideColor:!0}),(0,a.jsx)("span",{className:(0,c.cn)("truncate","system"===_&&"text-gray-950 dark:text-gray-50",("dark"===_||void 0==_)&&"text-gray-50"),children:S[P]?.props.filename})]}),{}):(0,a.jsx)(()=>(0,a.jsx)(n.B8,{className:(0,c.cn)("flex-1 min-w-0 text-xs leading-6 rounded-tl-xl gap-1 flex overflow-x-auto overflow-y-hidden",(0,m.O)(_),g),children:S.map((e,t)=>(0,a.jsxs)(O,{value:String(t),isSelected:P===t,tabsLength:S.length,codeBlockTheme:_,children:[e.props.icon&&"string"==typeof e.props.icon&&(0,a.jsx)(d.VE,{icon:e.props.icon,iconType:"regular",className:(0,c.cn)("h-3.5 w-3.5 bg-gray-500 dark:bg-gray-400","system"===_?"group-hover:bg-primary dark:group-hover:bg-primary-light":"group-hover:bg-gray-700/70 group-hover:text-primary-light",o.x.CodeBlockIcon),color:P===t?"currentColor":void 0,overrideColor:!0}),e.props.filename]},e.props.filename+"TabItem"+t))}),{}),(0,a.jsxs)("div",{className:"flex items-center justify-end shrink-0 gap-1.5",children:[b&&(0,a.jsx)(k,{selectedLanguage:S[P]?.props.language||"",setSelectedLanguage:e=>{let t=S.findIndex(t=>t.props.language===e);-1!==t&&R(String(t))},languages:S.map(e=>e.props.language||"")}),(0,a.jsx)(h.TN,{textToCopy:(0,w.O)(S[P]?.props?.children),onCopied:t}),(0,a.jsx)(u.y,{code:(0,w.O)(S[P]?.props?.children),...(0,p.f)(S[P]?.props)})]})]}),(0,a.jsx)("div",{className:"flex flex-1 overflow-hidden",children:S.map((e,t)=>(0,a.jsx)(n.UC,{value:String(t),className:"w-full min-w-full max-w-full h-full max-h-full",children:(0,a.jsx)(x.S7,{...e.props,isParentCodeGroup:!0,isSmallText:f,shouldHighlight:t===P,expandable:e.props.expandable&&t===P})},e.props.filename))})]})}),O=(0,i.forwardRef)(function({children:e,value:t,isSelected:r,tabsLength:i,codeBlockTheme:s},l){return(0,a.jsxs)(n.l9,{ref:l,value:t,className:(0,c.cn)("group flex items-center relative gap-1.5 py-1 pb-1.5 outline-none whitespace-nowrap font-medium",r&&"system"===s&&"text-primary dark:text-primary-light",r&&("dark"===s||void 0==s)&&"text-primary-light",!r&&"system"===s&&"text-gray-500 dark:text-gray-400",!r&&("dark"===s||void 0==s)&&"text-gray-400"),children:[(0,a.jsx)("div",{className:(0,c.cn)("flex items-center gap-1.5 px-1.5 rounded-lg z-10",i>1&&"system"===s&&"group-hover:bg-gray-200/50 dark:group-hover:bg-gray-700/70 group-hover:text-primary dark:group-hover:text-primary-light",i>1&&("dark"===s||void 0==s)&&"group-hover:bg-gray-700/70 group-hover:text-primary-light"),children:e}),r&&(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 h-0.5 rounded-full bg-primary dark:bg-primary-light"})]})})},69394:(e,t,r)=>{r.d(t,{W:()=>a});function a(e,t){if(e){if("family"in e)return e;else if(t in e){let r=e[t];return"string"==typeof r?{family:r}:r}}}},73254:(e,t,r)=>{r.d(t,{Latex:()=>n});var a=r(54568);function n({children:e}){return(0,a.jsx)("span",{className:"latex-style inline text-inherit p-0",children:e})}},75448:(e,t,r)=>{r.d(t,{r:()=>n});var a=r(83501);function n(e,t,r){return Array.isArray(e.items)?e.items:(0,a.EW)(e.items,t,void 0,r)}},76949:(e,t,r)=>{r.d(t,{U:()=>l});var a=r(7620),n=r(79460),i=r(75448),s=r(95876);function l(e){let t=(0,s.j)(),{location:r}=(0,n.ad)();return(0,a.useMemo)(()=>(0,i.r)(e,t,r),[e,t,r])}},78161:(e,t,r)=>{r.d(t,{Y:()=>n});var a=r(95666);let n=e=>"object"===e.type?(0,a.Y)(e.properties):"array"===e.type?(0,a.Y)(e.items):[]},79222:(e,t,r)=>{r.d(t,{RequestExample:()=>s,ResponseExample:()=>l});var a=r(54568),n=r(40113),i=r(47457);function s({dropdown:e,children:t}){return(0,a.jsx)("div",{className:"block xl:hidden mt-8",id:n.V.RequestExample,children:(0,a.jsx)(i.CodeGroup,{isSmallText:!0,noMargins:!0,dropdown:e,children:t})})}function l({children:e,dropdown:t}){return(0,a.jsx)("div",{className:"block xl:hidden mt-8",id:n.V.ResponseExample,children:(0,a.jsx)(i.CodeGroup,{isSmallText:!0,noMargins:!0,dropdown:t,children:e})})}},79460:(e,t,r)=>{r.d(t,{m$:()=>i,oW:()=>l,ad:()=>s});var a=r(54568),n=r(7620);let i=(0,n.createContext)({}),s=()=>(0,n.useContext)(i);function l({location:e,children:t}){let r=(0,n.useRef)(!1),s=(()=>{let[e,t]=(0,n.useState)(void 0);return(0,n.useEffect)(()=>{let e=()=>{let e=window.location.hash;e&&t(e)};return e(),window.addEventListener("hashchange",e),()=>{window.removeEventListener("hashchange",e)}},[]),e})();return(0,a.jsx)(i.Provider,{value:{location:e,anchor:s,hasScrolledToAnchorRef:r},children:t})}i.displayName="EndpointLocationContext"},82026:(e,t,r)=>{r.d(t,{n:()=>g,t1:()=>v,ks:()=>h,ParamField:()=>y,ob:()=>b,rG:()=>f});var a=r(54568),n=r(7620),i=r(32498),s=r(56486),l=r(79460),o=r(31467),d=r(60710),c=r(47181);let p=(e,t)=>{if(void 0===t||!e)return t;let r=e.toLowerCase();if(r.endsWith("[]")){let e=r.slice(0,-2),a=t=>p(e,t);try{let e=JSON.parse(t);if(Array.isArray(e))return e.map(e=>a(String(e)))}catch{}return t.split(",").map(e=>a(e.trim()))}switch(r){case"number":return Number(t);case"bool":if("true"===t.toLowerCase())return!0;if("false"===t.toLowerCase())return!1;return t;case"object":try{return JSON.parse(t)}catch{return t}default:return t}};var m=r(16600),u=r(26997),x=r(43059);function h({children:e,prefix:t,className:r}){return(0,a.jsxs)("div",{className:(0,m.cn)("flex items-center px-2 py-0.5 rounded-md bg-gray-100/50 dark:bg-white/5 text-gray-600 dark:text-gray-200 font-medium break-all",r),"data-component-part":"field-info-pill",children:[t&&(0,a.jsx)("span",{className:"text-gray-400 dark:text-gray-500",children:t}),(0,a.jsx)("span",{children:e})]})}function f(){return(0,a.jsx)("div",{className:"px-2 py-0.5 rounded-md bg-red-100/50 dark:bg-red-400/10 text-red-600 dark:text-red-300 font-medium whitespace-nowrap","data-component-part":"field-required-pill",children:"required"})}function g(){return(0,a.jsx)("div",{className:"px-2 py-0.5 rounded-md bg-amber-100/50 dark:bg-amber-400/10 text-amber-600 dark:text-amber-300 font-medium whitespace-nowrap","data-component-part":"field-deprecated-pill",children:"deprecated"})}function y({query:e,path:t,body:r,header:i,children:s,default:l,type:o,location:d,required:m=!1,deprecated:u=!1,hidden:x=!1,id:h,pre:f,post:g}){let y=e||t||r||i;return((0,n.useEffect)(()=>{if(y&&void 0!==l){let a=e?"query":t?"path":i?"header":r?"body":void 0;if(a){let e=p(o,"string"==typeof l?l:JSON.stringify(l));(0,c.x)(a,y,e)}}},[y,l,e,t,i,d]),null==y)?null:(0,a.jsx)(v,{name:y,defaultValue:l,type:o,location:d,required:m,deprecated:u,hidden:x,id:h,pre:f,post:g,children:s})}function b({name:e,typeLabel:t,location:r,required:o,deprecated:c,fieldType:p,defaultValue:y,typeOptions:b,selectedTypeOptionIndex:v,onSelectTypeOption:j,parentName:k,id:w,pre:N,post:O,style:C,explode:$}){let{hasScrolledToAnchorRef:S}=(0,n.useContext)(l.m$),I=(0,n.useRef)(null),[A,M]=(0,n.useState)(!1),T=w??(0,i.Ls)(p,e,k),E=(0,n.useCallback)(()=>{T&&((0,u.l)(window.location.href.split("#")[0]+"#"+T),window.location.hash=T)},[T]);(0,n.useEffect)(()=>{let e=I.current;if(!e)return;function t(){M((e?.offsetHeight??0)>28)}t();let r=new ResizeObserver(t);return r.observe(e),()=>{r.unobserve(e)}},[]),(0,n.useEffect)(()=>{(0,x.h)({id:T,shouldReturnEarly:!!S?.current||!T||!!k,checkIfShouldScroll:(e,t)=>t===T,postScrollCallback(){S&&(S.current=!0)}})},[S,T,k]);let L=k?"deepObject"===C&&$?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:k}),e,(0,a.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:"]"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:k}),e]}):e;return null==e?null:(0,a.jsx)("div",{className:(0,m.cn)("flex font-mono text-sm group/param-head param-head break-all relative"),id:T,children:(0,a.jsx)("div",{className:"flex-1 flex content-start py-0.5 mr-5",children:(0,a.jsxs)("div",{className:"flex items-center flex-wrap gap-2",children:[T&&(0,a.jsx)("div",{className:"absolute -top-1.5",children:(0,a.jsxs)("a",{href:`#${T}`,className:(0,m.cn)(k?"-ml-[2.1rem]":"-ml-10","flex items-center opacity-0 border-0 group-hover/param-head:opacity-100 py-2 [.expandable-content_&]:-ml-[2.1rem]"),"aria-label":"Navigate to header",onClick:E,children:["​",(0,a.jsx)("div",{className:"w-6 h-6 rounded-md flex items-center justify-center shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 bg-white ring-1 ring-gray-400/30 dark:ring-gray-700/25 hover:ring-gray-400/60 dark:hover:ring-white/20",children:(0,a.jsx)(d.qY,{})})]})}),N?.map((e,t)=>(0,a.jsx)("div",{className:"px-2 py-0.5 rounded-md bg-gray-100/50 dark:bg-white/5 text-gray-600 dark:text-gray-200","data-component-part":"field-meta-pre",children:e},t)),(k||e)&&(0,a.jsx)("div",{className:"font-semibold text-primary dark:text-primary-light cursor-pointer overflow-wrap-anywhere","data-component-part":"field-name",onClick:E,children:L}),(0,a.jsxs)("div",{ref:I,className:(0,m.cn)("inline items-center gap-2 text-xs font-medium [&_div]:inline [&_div]:mr-2",A?"[&_div]:leading-6":"[&_div]:leading-5"),"data-component-part":"field-meta",children:[b&&b.length>1&&j?(0,a.jsx)(s.b,{options:b,selectedIndex:v,onSelectOption:j}):t&&(0,a.jsx)(h,{children:t}),r&&(0,a.jsx)(h,{children:r}),null!=y&&(0,a.jsx)(h,{prefix:"default:",children:"string"==typeof y?""===y?'""':y:JSON.stringify(y)}),o&&(0,a.jsx)(f,{}),c&&(0,a.jsx)(g,{}),O?.map((e,t)=>(0,a.jsx)("div",{className:"px-2 py-0.5 rounded-md bg-gray-100/50 dark:bg-white/5 text-gray-600 dark:text-gray-200","data-component-part":"field-meta-post",children:e},t))]})]})})},T)}function v({name:e,type:t,location:r,defaultValue:i,required:s=!1,deprecated:l=!1,hidden:d=!1,id:c,pre:p,post:u,children:x}){let h=(0,n.useMemo)(()=>{if("object"==typeof i&&Object.values(i).some(e=>"object"==typeof e))return null;let e=JSON.stringify(i);return e&&e.length>0&&e.length<50?e:null},[i]);return d?null:(0,a.jsxs)("div",{className:(0,m.cn)(o.x.Field,"pt-2.5 pb-5 my-2.5 border-gray-50 dark:border-gray-800/50 border-b"),children:[(0,a.jsx)(b,{name:e,typeLabel:t,location:r,required:s,deprecated:l,defaultValue:h,id:c,pre:p,post:u}),x&&(0,a.jsx)("div",{className:"mt-4 prose-sm prose-gray dark:prose-invert [&_.prose>p:first-child]:mt-0 [&_.prose>p:last-child]:mb-0","data-component-part":"field-content",children:x})]})}},83501:(e,t,r)=>{r.d(t,{EW:()=>x});var a=r(86113);r(31721);let n=["binary","base64"];function i(e,t,r,a=5){let n=`#/components/${e}/`;if(!t.startsWith(n))return;let s=t.slice(n.length),l=null==r?void 0:r[s];return l&&"$ref"in l?a>0&&"string"==typeof l.$ref?i(e,l.$ref,r,a-1):void 0:l}let s=(e,t,r)=>{void 0!==t&&(r[e]=t)},l=(e,t,r)=>{void 0!==t[e]&&(r[e]=t[e])},o=(e,t)=>{var r;let a=void 0!==e.example?e.example:null==(r=e.examples)?void 0:r[0];void 0!==a&&(t.example=a)};function d(e,t){var r;if("object"==typeof e&&void 0!==t){if(e.discriminator&&e.discriminator.mapping){let a=e.discriminator.mapping;if(t in a&&(null==(r=a[t])?void 0:r.description))return a[t].description}if(Array.isArray(e)){for(let r of e){let e=d(r,t);if(e)return e}return}if("type"in e&&e.type===t&&"description"in e&&"string"==typeof e.description)return e.description;for(let[r,a]of Object.entries(e)){if(r===t&&"object"==typeof a&&null!==a&&"description"in a&&"string"==typeof a.description)return a.description;let e=d(a,t);if(e)return e}}}let c=(e,t,r,a)=>{var n;return void 0!==e[r]&&void 0!==t[r]?a(e[r],t[r]):null!=(n=e[r])?n:t[r]},p=(e,t,r,a)=>{s(e,c(t,r,e,a),r)};function m(e){let t=e.discriminator;if(null==t||!t.mapping)return!1;{let r=Object.values(t.mapping).map(e=>e),a=[];"allOf"in e&&Array.isArray(e.allOf)&&e.allOf.forEach(e=>{"$ref"in e&&a.push(e.$ref)}),"oneOf"in e&&Array.isArray(e.oneOf)&&e.oneOf.forEach(e=>{"$ref"in e&&a.push(e.$ref)}),"anyOf"in e&&Array.isArray(e.anyOf)&&e.anyOf.forEach(e=>{"$ref"in e&&a.push(e.$ref)});let n=new Set(r),i=new Set(a);return n.size===i.size&&[...n].every(e=>i.has(e))}}function u(e){if(!e[0])return[[]];let t=u(e.slice(1));return e[0].flatMap(e=>t.map(t=>{let r=[...e,...t];return r.map(e=>{let t={};return r.forEach(e=>{e.isOneOf&&(t.isOneOf=e.isOneOf),e.isAnyOf&&(t.isAnyOf=e.isAnyOf),e.isAllOf&&(t.isAllOf=e.isAllOf)}),e.refIdentifier&&(t.isOneOf===e.refIdentifier&&delete t.isOneOf,t.isAnyOf===e.refIdentifier&&delete t.isAnyOf,t.isAllOf===e.refIdentifier&&delete t.isAllOf),Object.assign(Object.assign({},e),t)})}))}function x(e,t,r,x){let h=(function e(t,r,a){var n,s,l,o,d,c,p,x,h,f,g,y;let b,v=null!=(n=null==a?void 0:a._depth)?n:0;if("$ref"in t){let e=t.$ref,a=i("schemas",t.$ref,r);if(!a)return[[{}]];void 0===a.type&&(null==(s=a.allOf)?void 0:s.find(e=>"type"in e&&"object"===e.type))&&(a.type="object");let n=null!=(l=t.title)?l:a.title;b=Object.assign(Object.assign({},a),{title:n,refIdentifier:e,description:null!=(o=t.description)?o:a.description,isOneOf:t.isOneOf,isAnyOf:t.isAnyOf,isAllOf:t.isAllOf})}else b=t;if((null==a?void 0:a.isRoot)&&"array"===b.type&&(null==(d=b.discriminator)?void 0:d.mapping)&&!m(b)){let e=b.discriminator,t=b.items;delete b.discriminator;let a=Object.values(null!=(c=e.mapping)?c:{}).map(e=>({$ref:e})),n=[];"oneOf"in t&&(n=(n=null!=(x=null==(p=t.oneOf)?void 0:p.filter(t=>{var r;return!("$ref"in t)||!Object.values(null!=(r=e.mapping)?r:{}).includes(t.$ref)}))?x:[]).map(e=>Object.assign(Object.assign({},e),{_depth:v+1})),delete t.oneOf);let s=a.map(e=>{var a,n;let s=i("schemas",e.$ref,r);if(!s)return Object.assign(Object.assign({},t),{_depth:v+1});let l="$ref"in t?i("schemas",t.$ref,r):t,o=null!=(a=null==l?void 0:l.properties)?a:{},d=null!=(n=s.properties)?n:{};return Object.assign(Object.assign(Object.assign({},t),s),{properties:Object.assign(Object.assign({},o),d),_depth:v+1})});b=Object.assign(Object.assign({},b),{items:Object.assign(Object.assign({},t),{oneOf:[...s,...n]})})}else if((null==a?void 0:a.isRoot)&&(null==(h=b.discriminator)?void 0:h.mapping)&&!b.oneOf&&!b.allOf&&!m(b)){let e=Object.values(b.discriminator.mapping);e.length&&(delete b.discriminator,b=Object.assign(Object.assign({},b),{allOf:[{oneOf:[...e.map(e=>({$ref:e}))]}]}))}let j=b.nullable;if(!b.oneOf&&!b.allOf&&!b.anyOf&&!Array.isArray(b.type)&&!j)return[[Object.assign(Object.assign({},b),{_depth:v})]];let k=Object.assign(Object.assign({},b),{_depth:v});delete k.oneOf,delete k.anyOf,delete k.allOf,delete k.not;let w=Array.isArray(k.type)?[...k.type]:[k.type];j&&!w.includes("null")&&w.push("null");let N=Object.keys(k).filter(e=>!e.startsWith("_")).length>0?[w.map(e=>[Object.assign(Object.assign({},k),{type:e})])]:[],O=null==(f=b.oneOf)?void 0:f.map(t=>("refIdentifier"in b&&(t.isOneOf=b.refIdentifier),e(t,r,{_depth:v+1}))),C=null==(g=b.anyOf)?void 0:g.map(t=>("refIdentifier"in b&&(t.isAnyOf=b.refIdentifier),e(t,r,{_depth:v+1}))),$=null==(y=b.allOf)?void 0:y.map(t=>("refIdentifier"in b&&(t.isAllOf=b.refIdentifier),e(t,r,{_depth:v+1}))),S=O?[O.flat()]:[],I=C?[C.flat()]:[],A=$?[u($)]:[];return u([...S,...I,...A,...N]).map(e=>e.map(e=>Object.assign(Object.assign(Object.assign(Object.assign({},e),e.isOneOf&&{isOneOf:e.isOneOf}),e.isAnyOf&&{isAnyOf:e.isAnyOf}),e.isAllOf&&{isAllOf:e.isAllOf})))})(e,t,{isRoot:!0}).flatMap(e=>{try{var m,u,h;let f=(m=e,u=t,h=x,m.sort((e,t)=>{var r,a;let n=null!=(r=e._depth)?r:0,i=null!=(a=t._depth)?a:0;return n!==i?n-i:e.type&&!t.type?-1:!e.type&&t.type?1:0}),m.reduce((e,t)=>{var r,n,o,x;let f=t.type;if("integer"===e.type&&"number"===f?f="integer":"number"===e.type&&"integer"===f?e.type="integer":void 0===e.type&&void 0!==f?e.type=f:void 0!==e.type&&void 0===f&&(f=e.type),e.type!==f)throw Error(`${e.type} vs ${f}`);for(let r of[e,t])"number"==typeof r.exclusiveMaximum&&(void 0===r.maximum||r.maximum>=r.exclusiveMaximum?(r.maximum=r.exclusiveMaximum,r.exclusiveMaximum=!0):r.exclusiveMaximum=void 0),"number"==typeof r.exclusiveMinimum&&(void 0===r.minimum||r.minimum<=r.exclusiveMinimum?(r.minimum=r.exclusiveMinimum,r.exclusiveMinimum=!0):r.exclusiveMinimum=void 0);void 0!=t.discriminator||void 0==t.type&&void 0!=e.title||t.isAllOf&&t.refIdentifier&&t.refIdentifier!==t.isAllOf||l("title",t,e);l("refIdentifier",t,e),l("examples",t,e),l("format",t,e),l("default",t,e),l("x-default",t,e),l("const",t,e),p("multipleOf",t,e,a),p("maxLength",t,e,Math.min),p("minLength",t,e,Math.max),p("maxItems",t,e,Math.min),p("minItems",t,e,Math.max),p("maxProperties",t,e,Math.min),p("minProperties",t,e,Math.max),p("required",t,e,(e,t)=>t.concat(e.filter(e=>!t.includes(e)))),p("enum",t,e,(e,t)=>t.filter(t=>e.includes(t))),p("readOnly",t,e,(e,t)=>e&&t),p("writeOnly",t,e,(e,t)=>e&&t),p("deprecated",t,e,(e,t)=>e||t);let g=c(t,e,"maximum",Math.min),y=c(t,e,"minimum",Math.max);s("exclusiveMaximum",(e.maximum===g?e.exclusiveMaximum:void 0)||(t.maximum===g?t.exclusiveMaximum:void 0),e),s("exclusiveMinimum",(e.minimum===y?e.exclusiveMinimum:void 0)||(t.minimum===y?t.exclusiveMinimum:void 0),e),s("maximum",g,e),s("minimum",y,e);let b=(null==(n=e.examples)?void 0:n[0])!==void 0?e.examples[0]:e.example,v=(null==(o=t.examples)?void 0:o[0])!==void 0?t.examples[0]:t.example;if(b&&v&&"object"==typeof b&&"object"==typeof v?e.example=Object.assign(Object.assign({},b),v):s("example",void 0!==v?v:b,e),t.items){let a=null!=(r=e.items)?r:{allOf:[]};a.allOf.push(t.items),e.items=a}t.properties&&Object.entries(t.properties).filter(([e,t])=>{if("$ref"in t){let e=i("schemas",t.$ref,u);if(!e)return!0;t=e}return(!t.readOnly||"request"!==h)&&(!t.writeOnly||"response"!==h)}).forEach(([t,r])=>{var a;let n=null!=(a=e.properties)?a:{},i=n[t];i?i.allOf.push(r):n[t]={allOf:[r]},e.properties=n}),function(e,t,r){var a,n;if((null==(a=e.properties)?void 0:a.type)&&t.discriminator&&!e.description){let a,i=null!=(n=e.properties.type.allOf[0])?n:{};"const"in i&&"string"==typeof i.const&&(a=i.const);let s=d(t,a)||r.flatMap(e=>d(e,a)).filter(Boolean)[0];s&&(e.description=s)}else e.description&&t.description&&!t.discriminator&&!e.description.includes(t.description)?e.description=`${e.description}
${t.description}`:e.description||l("description",t,e)}(e,t,m);if(!1===t.additionalProperties)e.additionalProperties=!1;else if(!1!==e.additionalProperties&&t.additionalProperties&&"object"==typeof t.additionalProperties){let r=null!=(x=e.additionalProperties)?x:{allOf:[]};r.allOf.push(t.additionalProperties),e.additionalProperties=r}return e},{}));return[function(e,t){var r,a;let i={};if(s("required",t,i),l("title",e,i),l("description",e,i),l("readOnly",e,i),l("writeOnly",e,i),l("deprecated",e,i),l("refIdentifier",e,i),l("examples",e,i),void 0===e.type){let t=(e=>{var t,r;let a;if((void 0!==e.format||void 0!==e.pattern||void 0!==e.minLength||void 0!==e.maxLength||(null==(t=e.enum)?void 0:t.every(e=>"string"==typeof e)))&&(a="string"),void 0!==e.multipleOf||void 0!==e.minimum||void 0!==e.maximum||void 0!==e.exclusiveMinimum||void 0!==e.exclusiveMaximum||(null==(r=e.enum)?void 0:r.every(e=>"number"==typeof e))){if(void 0!==a)return;a="number"}if("items"in e&&void 0!==e.items||void 0!==e.minItems||void 0!==e.maxItems||void 0!==e.uniqueItems){if(void 0!==a)return;a="array"}if(void 0!==e.additionalProperties||void 0!==e.properties||void 0!==e.minProperties||void 0!==e.maxProperties){if(void 0!==a)return;a="object"}return a})(e);if(void 0===t)return Object.assign({type:"any"},i);e.type=t}switch(e.type){case"boolean":case"null":return l("default",e,i),l("x-default",e,i),o(e,i),Object.assign({type:e.type},i);case"number":case"integer":if(e.enum)return l("default",e,i),l("x-default",e,i),o(e,i),Object.assign({type:"number"===e.type?"enum<number>":"enum<integer>",enum:e.enum.filter(e=>"number"==typeof e)},i);return l("multipleOf",e,i),l("maximum",e,i),l("exclusiveMaximum",e,i),l("minimum",e,i),l("exclusiveMinimum",e,i),l("default",e,i),l("x-default",e,i),o(e,i),Object.assign({type:e.type},i);case"string":if(e.enum)return l("default",e,i),l("x-default",e,i),o(e,i),Object.assign({type:"enum<string>",enum:e.enum.filter(e=>"string"==typeof e)},i);if(e.format&&n.includes(e.format))return Object.assign({type:"file",contentEncoding:e.format},i);return l("format",e,i),l("pattern",e,i),l("maxLength",e,i),l("minLength",e,i),l("default",e,i),l("x-default",e,i),l("const",e,i),o(e,i),Object.assign({type:e.type},i);case"array":return l("maxItems",e,i),l("minItems",e,i),l("uniqueItems",e,i),l("default",e,i),l("x-default",e,i),o(e,i),Object.assign({type:e.type,items:null!=(r=e.items)?r:{}},i);case"object":return s("requiredProperties",e.required,i),l("additionalProperties",e,i),l("maxProperties",e,i),l("minProperties",e,i),l("default",e,i),l("x-default",e,i),o(e,i),Object.assign({type:e.type,properties:null!=(a=e.properties)?a:{}},i);default:throw Error()}}(f,r)]}catch(e){return[]}});return h[0]?[h[0],...h.slice(1)]:[{type:"any"}]}},83897:(e,t,r)=>{r.d(t,{Tab:()=>l,Tabs:()=>x});var a=r(54568),n=r(31467),i=r(89619),s=r(16600);function l({title:e,isActive:t=!0,icon:r,iconType:l,children:o}){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("button",{className:(0,s.cn)("flex text-sm items-center gap-1.5 leading-6 font-semibold whitespace-nowrap pt-3 pb-2.5 -mb-px max-w-max border-b",t?"text-primary dark:text-primary-light border-current":"text-gray-900 border-transparent hover:border-gray-300 dark:text-gray-200 dark:hover:border-gray-700"),"data-component-part":"tab-button","data-active":t,children:[r&&(0,a.jsx)(i.VE,{icon:r,iconType:l,className:(0,s.cn)("h-4 w-4 shrink-0",t?"bg-primary":"bg-gray-900 dark:bg-gray-200",n.x.TabIcon),overrideColor:!0})," ",e]}),o?(0,a.jsx)("div",{className:"prose dark:prose-dark","data-component-part":"tab-content",children:o}):null]})}var o=r(46346),d=r(7620),c=r(79460),p=r(32894),m=r(43059);let u="Tab Title";function x({children:e,defaultTabIndex:t=0,onClickTab:r,className:i}){let{hasScrolledToAnchorRef:x}=(0,d.useContext)(c.m$),h=(0,d.useRef)(null),f=(0,d.useRef)(null),g=(0,d.useRef)(!1),[y,b]=(0,d.useState)(t),v=d.Children.toArray(e),j=v[y]?.props?.children,k=v.map(e=>e.props?.id?e.props.id:function(e,t=(0,o.a)()){let r=encodeURIComponent(e.toLowerCase().trim().replace(/\s+/g,"-"));return/%[0-9A-F]{2}/.test(r)?t(r,{decamelize:!1,preserveCharacters:["%"],lowercase:!1}):t(r,{decamelize:!1})}(e.props?.title||u));return(0,p.H)(k,b),(0,d.useEffect)(()=>{if(g.current)return;g.current=!0;let e=window.location.hash.substring(1);if(!e||!h.current||-1===k.indexOf(e))return;let t=CSS.escape(e);if(!h.current.querySelector(`#${t}`))return;let r=h.current.children;for(let t=0;t<r.length;t++){let a=r[t];if(a&&a.id===e){b(t),setTimeout(()=>{(0,m.h)({shouldReturnEarly:!!x?.current,element:document.getElementById(e),postScrollCallback(){x&&(x.current=!0)}})},10);break}}},[k,x]),(0,a.jsxs)("div",{className:(0,s.cn)(n.x.Tabs,"tabs tab-container"),id:k[y],children:[(0,a.jsx)("ul",{ref:h,className:(0,s.cn)("not-prose mb-6 pb-[1px] flex-none min-w-full overflow-auto border-b border-gray-200 gap-x-6 flex dark:border-gray-200/10",i),"data-component-part":"tabs-list",children:v.map((e,t)=>(0,a.jsx)("li",{id:k[t],className:"cursor-pointer",onClick:()=>(e=>{b(e),r?.(e);let t=k[e];if(t){let e=new URL(window.location.href);e.hash=t,window.history.pushState({},"",e.toString())}})(t),"data-child-tab-ids":e.props?.["data-child-tab-ids"],children:(0,a.jsx)(l,{title:e.props?.title??u,icon:e.props?.icon,iconType:e.props?.iconType,isActive:t===y})},`${k[t]}-${t}`))}),(0,a.jsx)("div",{className:"prose dark:prose-dark overflow-x-auto",ref:f,"data-component-part":"tab-content",children:j})]},k[y])}},84620:(e,t,r)=>{r.d(t,{Heading:()=>x});var a=r(54568),n=r(7620),i=r(50139),s=r(71197),l=r(33227),o=r(19637),d=r(22968),c=r(60710),p=r(16600),m=r(43059),u=r(26997);function x({level:e,id:t,children:r,className:x="",hidden:h=!1,style:f={},isAtRootLevel:g,...y}){let{docsConfig:b}=(0,n.useContext)(s.H6),v=`h${e}`,[j,k]=(0,o.O)(),[w,N]=(0,n.useState)(null),[O]=(0,i.y)(N),C=(0,d.T)(w),$=(0,n.useCallback)(()=>{(0,u.l)(`https://${window.location.host}${window.location.pathname}#${t}`),window.location.hash=t,(0,m.h)({id:t})},[t]),S=!!j;return(0,n.useEffect)(()=>{if(S)return void 0!==C&&k({type:"register_heading",payload:{id:t,top:C,isAtRootLevel:"true"===g}}),()=>{k({type:"unregister_heading",payload:t})}},[C,t,S,k,g]),(0,a.jsxs)(v,{className:(0,p.cn)("flex whitespace-pre-wrap group font-semibold",(0,l.H)(b?.theme).headings,x,{"text-2xl sm:text-3xl mt-8":"h1"===v}),id:t,ref:O,style:{...h?{marginBottom:0}:{},...f},...y,children:[!h&&(0,a.jsx)("div",{className:"absolute",children:(0,a.jsxs)("a",{href:`#${t}`,className:"-ml-10 flex items-center opacity-0 border-0 group-hover:opacity-100","aria-label":"Navigate to header",onClick:$,children:["​",(0,a.jsx)("div",{className:"w-6 h-6 rounded-md flex items-center justify-center shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 bg-white ring-1 ring-gray-400/30 dark:ring-gray-700/25 hover:ring-gray-400/60 dark:hover:ring-white/20",children:(0,a.jsx)(c.qY,{})})]})}),(0,a.jsx)("span",{className:(0,p.cn)("cursor-pointer",h&&"sr-only"),onClick:$,children:r})]})}},87381:(e,t,r)=>{r.d(t,{Card:()=>m});var a=r(54568),n=r(21067),i=r(27261),s=r.n(i),l=r(7620),o=r(31467),d=r(89619),c=r(60710),p=r(16600);function m({title:e,icon:t,iconType:r,color:n,horizontal:i,href:s,img:o,children:c,disabled:m,cta:x,arrow:h}){let f="string"==typeof t?(0,a.jsx)(d.VE,{icon:t,iconType:r,color:n,className:"h-6 w-6 bg-primary dark:bg-primary-light !m-0 shrink-0",overrideColor:!0}):t;m&&(s=void 0);let g={className:(0,p.cn)(s&&"hover:!border-primary dark:hover:!border-primary-light"),title:e,icon:f,img:o,horizontal:i,href:s,children:c,cta:x,arrow:h},y=(0,l.forwardRef)((e,t)=>(0,a.jsx)(u,{...e,mRef:t}));return(y.displayName="RefCard",s&&(s.startsWith("/")||s.startsWith("#")))?(0,a.jsx)(y,{...g}):(0,a.jsx)(u,{...g})}function u({title:e,icon:t,img:r,className:i,children:l,horizontal:d,arrow:m,as:u,mRef:x,cta:h,...f}){let g=f.href&&(f.href.startsWith("/")||f.href.startsWith("#")),y=u||(g?s():void 0!=f.href?"a":"div"),b=function(e){if("string"!=typeof e)return!1;try{let t=new URL(e);return"https:"===t.protocol}catch{return!1}}(f.href??""),v="string"==typeof t,j=r?r.match(/\/([^\/]+)\.[^.]+$/)?.[1]??"":"",k=(0,a.jsx)(a.Fragment,{children:t?v?(0,a.jsx)("img",{src:t,alt:e,className:"h-6 w-6 object-cover object-center","data-component-part":"card-icon"}):(0,a.jsx)("div",{className:"h-6 w-6 fill-gray-800 dark:fill-gray-100 text-gray-800 dark:text-gray-100","data-component-part":"card-icon",children:t}):null});return(0,a.jsxs)(y,{className:(0,p.cn)(o.x.Card,"block font-normal group relative my-2 ring-2 ring-transparent rounded-2xl bg-white dark:bg-background-dark border border-gray-950/10 dark:border-white/10 overflow-hidden w-full",f.href&&"cursor-pointer",i),...b?{target:"_blank",rel:"noreferrer"}:{},...f,ref:x,children:[r&&(0,a.jsx)("img",{src:r,alt:j,className:"w-full object-cover object-center not-prose","data-component-part":"card-image"}),(0,a.jsxs)("div",{className:(0,p.cn)("px-6 py-5 relative",d&&"flex items-center gap-x-3"),"data-component-part":"card-content-container",children:[f.href&&(0,a.jsx)("div",{id:"card-link-arrow-icon",className:(0,p.cn)("absolute text-gray-400 dark:text-gray-500 group-hover:text-primary dark:group-hover:text-primary-light top-5 right-5",!(m??b)&&"hidden"),children:(0,a.jsx)(n.A,{className:"w-4 h-4"})}),k,(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:(0,p.cn)("not-prose font-semibold text-base text-gray-800 dark:text-white",null!=t&&!d&&"mt-4"),contentEditable:!1,"data-component-part":"card-title",children:e}),(0,a.jsx)("div",{className:(0,p.cn)("prose mt-1 font-normal text-sm leading-6",e?"text-gray-600 dark:text-gray-400":"text-gray-700 dark:text-gray-300",d&&"leading-6 mt-0"),"data-component-part":"card-content",children:l}),f.href&&h&&(0,a.jsx)("div",{className:"mt-8","data-component-part":"card-cta",children:(0,a.jsxs)("button",{className:"text-left text-gray-600 gap-2 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light text-sm font-medium flex flex-row items-center group-hover:text-primary group-hover:dark:text-primary-light",children:[h,(0,a.jsx)(c.fl,{className:"h-6"})]})})]})]})]})}},95666:(e,t,r)=>{r.d(t,{Y:()=>function e(t,r="",a={}){for(let n in t){let i=r?`${r}.${n}`:n,s=t[n];"object"==typeof s?e(s,i,a):a[i]=s}return a}})},95876:(e,t,r)=>{r.d(t,{j:()=>i});var a=r(7620),n=r(71197);let i=()=>{let{apiReferenceData:e}=(0,a.useContext)(n.fq);return e.componentSchemas}},99345:(e,t,r)=>{r.d(t,{CardGroup:()=>s,Columns:()=>l});var a=r(54568);r(7620);var n=r(31467),i=r(16600);function s({children:e,cols:t=2,className:r}){let s="";switch(t=Number(t)){case 1:s="sm:grid-cols-1";break;case 2:s="sm:grid-cols-2";break;case 3:s="sm:grid-cols-3";break;case 4:s="sm:grid-cols-4";break;default:s=""}return(0,a.jsx)("div",{className:(0,i.cn)(n.x.CardGroup,"not-prose grid gap-x-4",s,r),children:e})}function l({children:e,...t}){return(0,a.jsx)(s,{...t,children:e})}}}]);