!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="2eafae36-1b6d-4ff2-bb42-b0e34639e150",e._sentryDebugIdIdentifier="sentry-dbid-2eafae36-1b6d-4ff2-bb42-b0e34639e150")}catch(e){}}(),(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3818],{11:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(7620),s=r(21383);let n=()=>{let[e]=(0,s.A)();return(0,a.useMemo)(()=>e,[e])}},1514:(e,t,r)=>{"use strict";r.d(t,{default:()=>S});var a=r(54568),s=r(79460),n=r(3256),l=r(18073),i=r(7620),o=r(83897),d=r(95876),c=r(31467),u=r(44411),p=r(16600),m=r(32498),x=r(44664),h=r(56486);let g=({title:e,subtitle:t,rightElement:r,options:s,selectedIndex:n,onSelectOption:l})=>(0,a.jsxs)("div",{className:(0,p.cn)(c.x.APISectionHeading,"flex flex-col gap-y-4 w-full"),children:[(0,a.jsxs)("div",{className:"flex items-baseline border-b pb-2.5 border-gray-100 dark:border-gray-800 w-full",children:[(0,a.jsx)("h4",{className:(0,p.cn)(c.x.APISectionHeadingTitle,"flex-1 mb-0"),children:e}),s&&s.length>1&&(0,a.jsx)(h.b,{options:s,selectedIndex:n,onSelectOption:l,noBackground:!0}),(0,a.jsx)("div",{className:"flex items-center",children:r&&("string"==typeof r?(0,a.jsx)("div",{className:"font-mono px-2 py-0.5 text-xs font-medium text-gray-600 dark:text-gray-300",children:r}):r)})]}),t&&(0,a.jsx)("div",{className:(0,p.cn)(c.x.APISectionHeadingSubtitle,"text-sm prose prose-gray dark:prose-invert mb-2"),children:(0,a.jsx)(x.V,{markdown:t})})]}),y=({body:e})=>{let[t,r]=(0,i.useState)(0),s=(0,d.j)(),n=(0,i.useMemo)(()=>Object.entries(e).map(([e,{schemaArray:t,description:r}])=>{let{typeOptionLabels:a,originalIndices:n}=(0,u.e)(t,s),l=n[0],i=void 0!==l?t[l]:void 0;return{contentTypeLabel:1===a.length&&i&&!f(i)?`${e} \xb7 ${a[0]}`:e,typeOptionLabels:a,originalIndices:n,description:r,schemaArray:n.map(e=>t[e])}}),[e,s])[t];if(void 0===n)return null;let l=Object.keys(e);l[t]=n.contentTypeLabel;let x=n.description==n.schemaArray[0].description;return(0,a.jsxs)("div",{className:(0,p.cn)(c.x.APISection),children:[l.length>1?(0,a.jsx)(g,{title:"Body",options:l,selectedIndex:t,onSelectOption:r,subtitle:n.description}):(0,a.jsx)(g,{title:"Body",rightElement:l[0],subtitle:n.description}),n.schemaArray.length>1?(0,a.jsx)("div",{className:"mt-4 rounded-xl border border-gray-100 px-4 pt-2 dark:border-white/10",children:(0,a.jsx)(o.Tabs,{className:"!-mt-1 !border-gray-100 !mb-0 dark:!border-white/10",children:n.schemaArray.map((e,t)=>(0,a.jsx)(o.Tab,{title:n.typeOptionLabels[t]??"",children:(0,a.jsx)(m.ae,{fieldType:"body",schemaArray:[e],hideDescription:x})},t))},t)}):(0,a.jsx)(m.ae,{fieldType:"body",schemaArray:n.schemaArray,hideDescription:x})]})},f=e=>"object"===e.type&&Object.keys(e.properties).length>0,b=({parameters:e})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(j,{title:"Headers",parameters:e.header}),(0,a.jsx)(j,{title:"Path Parameters",parameters:e.path}),(0,a.jsx)(j,{title:"Query Parameters",parameters:e.query}),(0,a.jsx)(j,{title:"Cookies",parameters:e.cookie})]}),j=({parameters:e,title:t})=>{if(0===Object.keys(e).length)return null;let r=Object.entries(e).map(([e,t])=>(0,a.jsx)(m.ae,{fieldType:"parameter",name:e,style:t.style,explode:t.explode,schemaArray:t.schema},e));return(0,a.jsxs)("div",{className:(0,p.cn)(c.x.APISection),children:[(0,a.jsx)(g,{title:t}),r]})};var v=r(63656),k=r(88426);function w({response:e}){let[t,r]=(0,i.useState)(0),[s,n]=(0,i.useState)(0),l=(0,i.useMemo)(()=>Object.entries(e),[e]),d=(0,i.useMemo)(()=>Object.entries(l[s]?.[1]||{}),[l,s]),u=(0,i.useCallback)(e=>{n(e),t>=Object.entries(l[e]?.[1]||{}).length&&r(0)},[l,t]),x=(0,i.useCallback)(e=>{r(e)},[]),{contentType:h,content:y,description:f}=(0,i.useMemo)(()=>{let e=d[t];if(!e)return{contentType:void 0,content:void 0,description:void 0};let[r,a]=e,s=a.description!==a.schemaArray[0].description?a.description:void 0;return"_mintlify/placeholder"===r?{content:a,contentType:"",description:s}:{content:a,contentType:r,description:s}},[d,t]);if(!y)return null;let{schemaArray:b}=y,j=(0,v.H)(b),w=l.map(([e,t])=>({label:e,schema:t})),N=d.filter(([e])=>"_mintlify/placeholder"!==e).map(([e,t])=>({label:e,schema:t})),C=l[0]?`${l[0][0]}`:"",S=""===h?"":`${h}`,O=`${C}${C&&S?" - ":""}${S}`,A=l.length>1,E=d.length>1,q=A||E;return(0,a.jsxs)("div",{className:(0,p.cn)(c.x.APISection),children:[(0,a.jsx)(g,{title:"Response",subtitle:f,rightElement:q?(0,a.jsxs)("div",{className:"flex items-center gap-x-3 font-mono text-xs",children:[(0,a.jsx)(k.C,{onSelectOption:u,options:w,selectedIndex:s,className:(0,p.cn)("bg-transparent dark:bg-transparent",A&&"hover:text-zinc-950 dark:hover:text-white transition-all")}),(0,a.jsx)(k.C,{onSelectOption:x,options:N,selectedIndex:t,className:(0,p.cn)("bg-transparent dark:bg-transparent",E&&"hover:text-zinc-950 dark:hover:text-white transition-all")})]}):O}),b.length>1?(0,a.jsx)(o.Tabs,{children:b.map((e,t)=>(0,a.jsx)(o.Tab,{title:j[t]??"",children:(0,a.jsx)(m.ae,{fieldType:"response",schemaArray:[e]})},t))}):(0,a.jsx)(m.ae,{fieldType:"response",schemaArray:b})]})}var N=r(22467);let C=({securityOptions:e})=>{let[t,r]=(0,i.useState)(0),s=(0,i.useMemo)(()=>e.map(e=>e.title),[e]),n=e[t];if(void 0===n)return null;let l=["header","query","cookie"].flatMap(e=>Object.entries(n.parameters[e]).map(([t,r])=>(0,a.jsx)(m.ae,{fieldType:"authorization",schemaArray:[{type:"string",description:(0,N.Bd)(r),required:!0,default:r["x-default"]}],name:t,location:e},`${e}:${t}`)));return(0,a.jsxs)("div",{className:(0,p.cn)(c.x.APISection),children:[(0,a.jsx)(g,{title:"Authorizations",options:s,selectedIndex:t,onSelectOption:r}),l]})},S=()=>{let e=(0,n.Fe)();if(void 0===e)return null;let t=(0,l._)(e);return(0,a.jsxs)(a.Fragment,{children:[t.length>0&&(0,a.jsx)(C,{securityOptions:t}),(0,a.jsx)(b,{parameters:e.request.parameters}),(0,a.jsx)(s.oW,{location:"request",children:(0,a.jsx)(y,{body:e.request.body})}),(0,a.jsx)(s.oW,{location:"response",children:Object.entries(e.response)[0]&&(0,a.jsx)(w,{response:e.response})})]})}},3256:(e,t,r)=>{"use strict";r.d(t,{Fe:()=>l,Zk:()=>o,j1:()=>i,j5:()=>d});var a=r(7620),s=r(71197),n=r(19637);let l=()=>{let{apiReferenceData:{endpoint:e}}=(0,a.useContext)(s.fq);return e},i=()=>void 0!==l(),o=()=>{let e=l(),[{endpoint:t}]=(0,n.O)();return(0,a.useMemo)(()=>e??t,[e,t])},d=()=>void 0!==o()},7129:(e,t,r)=>{"use strict";r.d(t,{SchemaFields:()=>c});var a=r(54568),s=r(83897),n=r(79460),l=r(95876),i=r(59722),o=r(44411),d=r(32498);let c=()=>{let e=(0,i.p)(),t=(0,l.j)();if(void 0===e)return null;let{typeOptionLabels:r,originalIndices:c}=(0,o.e)(e,t),u=c.map(t=>e[t]).filter(e=>void 0!==e);return void 0===u[0]?null:(0,a.jsx)(n.oW,{children:(0,a.jsx)("div",{children:u.length>1?(0,a.jsx)(s.Tabs,{children:u.map((e,t)=>(0,a.jsx)(s.Tab,{title:r[t]??"",children:(0,a.jsx)(d.ae,{fieldType:"schema",schemaArray:[e]})},t))}):(0,a.jsx)(d.ae,{fieldType:"schema",schemaArray:[u[0]]})})})}},11375:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});var a=r(54568),s=r(32441);let n=({name:e,channel:t})=>{let r=t.sendMessages.map(e=>({type:"send",id:e.id,title:e.title??e.example??"",content:e.example})),n=t.receiveMessages.map(e=>({type:"receive",id:e.id,title:e.title??e.example??"",content:e.example}));return(0,a.jsx)(s.X,{name:e,messages:[...r,...n]})}},17552:(e,t,r)=>{"use strict";r.r(t),r.d(t,{PageHeader:()=>G});var a=r(54568),s=r(38974),n=r(14820),l=r(7620),i=r(47006),o=r(71197),d=r(33227),c=r(13592),u=r(3256),p=r(19637),m=r(465),x=r(40113),h=r(27911),g=r(27261),y=r.n(g),f=r(31467),b=r(16600),j=r(84873),v=r(38668),k=r(71737),w=r(37050);let N=e=>{let t=e[0];if(t)return"string"==typeof t?t:N("pages"in t?t.pages:[])};function C(e,t){if("pages"in e){let r={title:e.group,path:(0,j.M)(N(e.pages)??"")};for(let a of e.pages)if((0,k.a)(a)){let e=C(a,t);if(e.length>0)return[r,...e]}else if((0,w.N)(a,t))return[r]}return[]}var S=r(7751);function O(){let e=(0,c.G)(),{docsConfig:t}=(0,l.useContext)(o.H6),r=(0,S.a)(e,t?.navigation),s=function e(t,r){if(!r)return[];if("pages"in r){for(let e of r.pages)if((0,k.a)(e)){let r=C(e,t);if(r.length>0)return r}}if("groups"in r)for(let e of r.groups){let r=C(e,t);if(r.length>0)return r}for(let a of v.J)if(a in r)for(let s of r[a]){let r=e(t,s);if(r.length>0)return r}return[]}(e,t?.navigation);return(0,a.jsx)(a.Fragment,{children:t?.styling?.eyebrows==="breadcrumbs"?(0,a.jsx)(A,{breadcrumbs:s}):r&&(0,a.jsx)("div",{className:(0,b.cn)(f.x.Eyebrow,"h-5 text-primary dark:text-primary-light text-sm font-semibold"),children:r})})}let A=({breadcrumbs:e})=>0===e.length?null:(0,a.jsx)("div",{className:"flex flex-row items-center gap-1.5",children:e.map((t,r)=>(0,a.jsxs)("div",{className:"inline-flex items-center gap-1.5 text-sm text-gray-500 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300",children:[(0,a.jsx)(y(),{href:t.path??"#",children:t.title}),r<e.length-1&&(0,a.jsx)(h.A,{size:12,strokeWidth:2.5,className:"text-gray-400 dark:text-gray-500"})]},t.title))});var E=r(1521),q=r(48836),I=r(21067),T=r(49341),$=r(93351),P=r(41384),M=r(97056),B=r(26997);function V(e){return void 0!==e}var D=r(93696),L=r(50887).Buffer;let R=()=>{let e=P.cD.BASE_PATH||"",t=window.location.href;if(t){let r=new URL(t);return`${(0,D.f)(r.origin)}${e}/mcp`}return`${e}/mcp`};var H=r(89619),U=r(60710);let F="regular",_=({className:e})=>{let{docsConfig:t}=(0,l.useContext)(o.H6),{pageMetadata:r}=(0,l.useContext)(o.NQ),s=!0===r.rss,{copyPage:n,viewAsMarkdown:i,openInChatGPT:d,openInClaude:u,openInPerplexity:p,copyPageAsMarkdown:m,viewPageAsMarkdown:h,askQuestionsAboutPage:g,copyMCPServer:y,copyMCPServerDescription:f,connectToCursor:j,installMCPServerOnCursor:v,connectToVSCode:k,installMCPServerOnVSCode:w}=(0,$.n)(),N=(0,c.G)(),{actualSubdomain:C}=(0,l.useContext)(o.Em),[S,O]=(0,l.useState)(!1),[A,E]=(0,l.useState)(!1);(0,l.useEffect)(()=>{A&&setTimeout(()=>{E(!1)},2e3)},[A]);let D=async()=>{E(!0);try{await (0,M.wC)(C,N)}catch(e){console.error("Failed to copy markdown:",e)}},_=async e=>{let t=window.location.href,r=`Read from ${t}.md so I can ask questions about it.`;"chatgpt"===e?window.open(`https://chat.openai.com/?hints=search&q=${encodeURIComponent(r)}`,"_blank"):"claude"===e?window.open(`https://claude.ai/new?q=${encodeURIComponent(r)}`,"_blank"):window.open(`https://www.perplexity.ai/search?q=${encodeURIComponent(r)}`,"_blank")},G=e=>()=>{O(!1),e()},z=e=>{let r=R(),a={name:t?.name||"Documentation",url:r},s=null;(s="cursor"===e?(e=>{try{let t=JSON.stringify(e),r=L.from(t,"utf8").toString("base64").replace(/\+/g,"%2B");return`cursor://anysphere.cursor-deeplink/mcp/install?name=${encodeURIComponent(e.name)}&config=${encodeURIComponent(r)}`}catch(e){return console.error("Error generating Cursor deep link:",e),null}})(a):`vscode:mcp/install?${encodeURIComponent(JSON.stringify(a))}`)&&window.open(s,"_blank")},W=[{id:"copy",title:n,description:m,icon:U.j,action:G(D)},{id:"view",title:i,description:h,icon:U.Hb,action:G(()=>{let e=`${P.cD.BASE_PATH}${N}.md`;window.open(e,"_blank")}),externalLink:!0},{id:"chatgpt",title:d,description:g,icon:U.pc,action:G(()=>_("chatgpt")),externalLink:!0},{id:"claude",title:u,description:g,icon:U.w6,action:G(()=>_("claude")),externalLink:!0},{id:"perplexity",title:p,description:g,icon:U.Mm,action:G(()=>_("perplexity")),externalLink:!0},{id:"mcp",title:y,description:f,icon:U.hA,action:G(()=>(()=>{let e=R();(0,B.l)(e)})())},{id:"cursor",title:j,description:v,icon:U.oA,action:G(()=>z("cursor")),externalLink:!0},{id:"vscode",title:k,description:w,icon:U.WH,action:G(()=>z("vscode")),externalLink:!0}],J=(0,l.useMemo)(()=>Array.from(new Set(t?.contextual?.options.map(e=>"string"==typeof e?W.find(t=>t.id===e):{id:`custom-${e.title}`,...e,action:()=>K(e),externalLink:"string"==typeof e.href?(0,q.v)(e.href):(0,q.v)(e.href.base)}).filter(V))),[t?.contextual?.options]),K=async e=>{if("string"==typeof e.href)window.open(e.href,"_blank");else{let{base:t,query:r}=e.href,a=new URL(t);r&&r.forEach(async({key:e,value:t})=>{let r=t;if(t.includes("$page")){let e=await (0,M.V)(C,N);r=t.replace("$page",e.slice(0,200))}t.includes("$path")&&(r=t.replace("$path",N)),t.includes("$mcp")&&(r=t.replace("$mcp",R())),a.searchParams.set(e,r)}),window.open(a.toString(),"_blank")}},Z=({icon:e})=>"function"==typeof e?(0,a.jsx)(e,{className:"w-4 h-4 shrink-0"}):(0,a.jsx)(H.VE,{icon:"string"==typeof e?e:e?.name??"info",iconType:"string"==typeof e?F:e?.style||F,className:"h-4 w-4 bg-gray-700 dark:bg-gray-300",overrideColor:!0});if(!J.length&&!s)return null;let Q=J[0];return(0,a.jsxs)("div",{id:x.V.PageContextMenu,className:(0,b.cn)("flex items-center shrink-0",Q?.id==="copy"&&"min-w-[156px]",e),children:[Q&&(0,a.jsx)("button",{id:x.V.PageContextMenuButton,className:(0,b.cn)("rounded-l-xl px-3 text-gray-700 dark:text-gray-300 py-1.5 border border-gray-200 dark:border-white/[0.07] bg-background-light dark:bg-background-dark hover:bg-gray-600/5 dark:hover:bg-gray-200/5",1===J.length?"rounded-xl":"border-r-0",A&&"text-gray-600 dark:text-gray-400"),onClick:()=>{Q.action()},children:(0,a.jsx)("div",{className:"flex items-center gap-2 text-sm text-center font-medium",children:"copy"===Q.id?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(U.j,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:A?"Copied":n})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(Z,{icon:Q.icon}),(0,a.jsx)("span",{children:Q.title})]})})}),J.length>1&&(0,a.jsxs)(T.rI,{open:S,onOpenChange:O,children:[(0,a.jsx)(T.ty,{asChild:!0,className:"rounded-none",children:(0,a.jsx)("button",{className:"rounded-r-xl border px-3 border-gray-200 aspect-square dark:border-white/[0.07] bg-background-light dark:bg-background-dark hover:bg-gray-600/5 dark:hover:bg-gray-200/5",children:(0,a.jsx)(U.mu,{className:(0,b.cn)("rotate-90 ",S&&"rotate-[270deg]")})})}),(0,a.jsx)(T.SQ,{side:"bottom",align:"end",className:"inline-flex max-h-[420px] p-1 border border-gray-200 dark:border-white/[0.07] flex-col",children:J.map((e,t)=>(0,a.jsxs)("div",{onClick:e.action,className:"flex items-center p-1.5 gap-1 hover:bg-gray-600/5 dark:hover:bg-gray-200/5 cursor-pointer rounded-xl",children:[(0,a.jsx)("div",{className:"border border-gray-200 dark:border-white/[0.07] rounded-lg p-1.5",children:(0,a.jsx)(Z,{icon:e.icon})}),(0,a.jsxs)("div",{className:"flex flex-col px-1",children:[(0,a.jsxs)("div",{className:"text-sm font-medium text-gray-800 dark:text-gray-300 flex items-center gap-1",children:[e.title,e.externalLink&&(0,a.jsx)(I.A,{className:"w-3 h-3 text-gray-600 dark:text-gray-400 shrink-0"})]}),(0,a.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:e.description})]})]},e.id+t))})]}),s&&(0,a.jsx)("button",{className:"flex items-center text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-white/[0.07] rounded-xl p-2 ml-2 hover:bg-gray-600/5 dark:hover:bg-gray-200/5",onClick:()=>{let e=window.location.href;window.open(`${e}/rss.xml`,"_blank")},children:(0,a.jsx)(U.wi,{className:"w-4 h-4"})})]})};function G(){let{description:e,pageType:t}=(0,l.useContext)(o.NQ),{docsConfig:r}=(0,l.useContext)(o.H6),[{pageMetadata:h}]=(0,p.O)(),g=(0,c.G)(),y=(0,u.Zk)(),{isCustom:f,isFrame:j}=(0,m.c)(),v=h.title||(0,s.r)(g),k="pdf"===t;return!v&&!e||f||j?null:(0,a.jsxs)("header",{id:k?g.replace(/[\/\\:*?"<>|]/g,"-"):x.V.Header,className:"relative",children:[(0,a.jsxs)("div",{className:"mt-0.5 space-y-2.5",children:[(0,a.jsx)(O,{}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center relative gap-2",children:[(0,a.jsx)("h1",{id:x.V.PageTitle,className:(0,b.cn)("inline-block text-2xl sm:text-3xl font-bold text-gray-900 tracking-tight dark:text-gray-200",(0,d.H)(r?.theme).title),children:v}),y?.deprecated&&(0,a.jsx)(E.m,{}),!k&&(0,a.jsx)(_,{className:"justify-end ml-auto sm:flex hidden"})]})]}),(0,a.jsx)(()=>"string"==typeof e?(0,a.jsx)("div",{className:"mt-2 text-lg prose prose-gray dark:prose-invert",children:e}):e?(0,a.jsx)("div",{className:"mt-2 text-lg prose prose-gray dark:prose-invert",children:(0,a.jsx)(n.Lo,{components:i.A,...e})}):null,{}),!k&&(0,a.jsx)(_,{className:"mt-3 sm:hidden"})]})}},17759:(e,t,r)=>{"use strict";r.d(t,{CA:()=>d,rV:()=>c,s4:()=>p,sb:()=>u});var a=r(7620),s=r(71197),n=r(41384),l=r(88374),i=r(11),o=r(50887).Buffer;let d=({endpoint:e,baseUrl:t,contentType:o,setResult:d,setIsFetching:f})=>{let b=(0,l.p)("docs.api_playground.request"),j=(0,i.A)(),{docsConfig:v}=(0,a.useContext)(s.H6),k=v?.api?.mdx?.auth?.method==="cobo",w=v?.api?.playground?.proxy===!1;return(0,a.useCallback)(async()=>{let a;f(!0);let s=await r.e(8683).then(r.bind(r,48683)).then(e=>e.default),l=c(e.path,j.path),i=p(u(t,j.server),l),v=m(j.header,o);k&&(v=l.startsWith("/v1")?await x(e,j,v):await h(e.method,l,j.body,j.query,v));let N={method:e.method,url:i,header:v,body:j.body,cookie:j.cookie,query:j.query},C=!1;try{if(w){let e=await g(N);a=e.status,d({error:!1,response:{status:e.status,statusText:e.statusText,headers:e.headers,data:e.data}})}else if(o.includes("multipart/form-data")){let e=await y(N);"response"in e&&(a=e.response.status),d(e)}else{let{data:e}=await s.post(`${n.cD.BASE_PATH}/api/request`,N);"response"in e&&(a=e.response.status),d(e)}}catch(e){C=!0,d({error:!0,errorMessage:"unable to complete request"}),console.error(e)}f(!1),b({path:e.path,method:e.method,responseStatus:a,isError:C})},[f,e,j,t,o,k,b,w,d])},c=(e,t)=>Object.entries(t).reduce((e,[t,r])=>void 0==r?e:e.replace(`{${t}}`,encodeURIComponent(r.toString())),e),u=(e,t)=>{let r=e;return Object.entries(t).forEach(([e,t])=>{t&&(r=r.replace(`{${e}}`,t))}),r},p=(e,t)=>e.replace(/\/$/,"")+"/"+t.replace(/^\//,""),m=(e,t)=>Object.keys(e).some(e=>"content-type"===e.toLowerCase())?e:{...e,"content-type":t},x=async(e,t,a)=>{let s=a["API-SECRET"],{getCoboV1CustodyGeneratedKeys:n}=await Promise.all([r.e(79),r.e(338)]).then(r.bind(r,6488)),l={...a,...n(e.method,e.path,s,t.body||t.query)};return delete l["API-SECRET"],l},h=async(e,t,a,s,n)=>{let{getCoboV2CustodyGeneratedKeys:l}=await r.e(5416).then(r.bind(r,55416)),i=n["BIZ-API-KEY"];return"string"==typeof i?l(i,e,t,a,s):{}},g=async e=>{let t=await r.e(8683).then(r.bind(r,48683)).then(e=>e.default),a=Object.keys(e.header).find(e=>"content-type"===e.toLowerCase()),s=e.body,n=e.header;if(a?.includes("multipart/form-data")){let{data:t,headers:r}=f(e);s=t,n=r}return t({url:e.url,method:e.method,params:e.query,data:s,headers:n,paramsSerializer:{indexes:null},validateStatus:()=>!0,transformResponse:e=>o.from(e).toString("base64")})},y=async e=>{let t=await r.e(8683).then(r.bind(r,48683)).then(e=>e.default),a=new FormData;a.append("requestInfo",JSON.stringify({method:e.method,url:e.url,header:e.header,query:e.query,cookie:e.cookie})),"object"==typeof e.body&&null!==e.body&&Object.entries(e.body).forEach(([e,t])=>{t instanceof File?a.append(e,t):null!=t&&a.append(e,"object"==typeof t?JSON.stringify(t):t.toString())});let{data:s}=await t.post(`${n.cD.BASE_PATH}/api/file-request`,a);return s},f=e=>{let t=e.body,r=e.header;if(r["content-type"]?.includes("multipart/form-data")){let e=new FormData;"object"==typeof t&&null!==t&&(Object.entries(t).forEach(([t,r])=>{r instanceof File?e.append(t,r):null!=r&&e.append(t,"object"==typeof r?JSON.stringify(r):r.toString())}),t=e,delete r["content-type"])}return{data:t,headers:r}}},18073:(e,t,r)=>{"use strict";r.d(t,{_:()=>a});let a=e=>e.request.security.filter(e=>Object.keys(e.parameters.cookie).length>0||Object.keys(e.parameters.header).length>0||Object.keys(e.parameters.query).length>0)},21383:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(7620),s=r(38987);let n=()=>(0,a.useContext)(s.Qo)},22467:(e,t,r)=>{"use strict";r.d(t,{Bd:()=>m,sS:()=>c});var a=r(54568),s=r(7620),n=r(16600),l=r(31581),i=r(88426),o=r(63213),d=r(50887).Buffer;let c=({inputs:e,setInputs:t,securityOptions:r,selectedSecurityOptionIndex:d,setSelectedSecurityOptionIndex:c})=>{let p=r[d],m=(0,s.useMemo)(()=>p?["header","query","cookie"].flatMap(r=>Object.entries(p.parameters[r]).map(([s,n])=>{let{schemaArray:l,transform:i,value:d}=u(n,e[r][s]),c=void 0===d?void 0:()=>{let a={...e,[r]:{...e[r]}};delete a[r][s],t(a)};return(0,a.jsx)(o.t,{name:s,schemaArray:l,value:d,setValue:a=>{let n={...e,[r]:{...e[r]}};n[r][s]=i(a),t(n)},clearValue:c},`${r}:${s}`)})):null,[p,t,e]);return(0,a.jsx)(l.X,{name:"Authorization",rightElement:(0,a.jsx)("div",{onClick:e=>e.stopPropagation(),className:(0,n.cn)("text-xs font-mono",r.length<=1&&"hidden"),children:(0,a.jsx)(i.C,{options:r.map(e=>({label:e.title,schema:e})),selectedIndex:d,onSelectOption:e=>{c(e)}})}),children:m})},u=(e,t)=>({schemaArray:p(e),transform:h(e),value:x(e,t)}),p=e=>{let t=m(e);switch(e.type){case"apiKey":return[{type:"string",required:!0,description:t,default:e["x-default"]}];case"http":return"basic"===e.scheme?[{type:"object",required:!0,description:t,properties:{username:[{type:"string"}],password:[{type:"string"}]},additionalProperties:!1,default:e["x-default"]}]:[{type:"string",required:!0,format:"bearer",description:t,default:e["x-default"]}];case"oauth2":return[{type:"string",required:!0,format:"bearer",description:t}]}},m=e=>{switch(e.type){case"apiKey":return e.description;case"http":return e.description??("basic"===e.scheme?"Basic authentication header of the form `Basic <encoded-value>`, where `<encoded-value>` is the base64-encoded string `username:password`.":"Bearer authentication header of the form `Bearer <token>`, where `<token>` is your auth token.");case"oauth2":return e.description??"The access token received from the authorization server in the OAuth 2.0 flow."}},x=(e,t)=>{if("http"!==e.type||"basic"!==e.scheme||"string"!=typeof t||!t.startsWith("Basic "))return t;let r=t.slice(6),[a,s]=d.from(r,"base64").toString("utf-8").split(":");return{username:a,password:s}},h=e=>"http"!==e.type||"basic"!==e.scheme?e=>e:e=>{let t=e&&"object"==typeof e&&"username"in e?e.username:void 0,r=e&&"object"==typeof e&&"password"in e?e.password:void 0,a=`${t??""}:${r??""}`,s=d.from(a).toString("base64");return`Basic ${s}`}},26976:(e,t,r)=>{"use strict";r.d(t,{B:()=>n});var a=r(7620),s=r(71197);let n=()=>{let{apiReferenceData:{channel:e}}=(0,a.useContext)(s.fq);return(0,a.useMemo)(()=>e,[e])}},28847:(e,t,r)=>{"use strict";r.d(t,{A:()=>eu});var a=r(54568),s=r(7620),n=r(45744),l=r(71476),i=r(16600),o=r(50816),d=r(47457),c=r(71197),u=r(10122);let p=()=>{let e=(()=>{let{apiReferenceData:e}=(0,s.useContext)(c.fq);return(0,s.useMemo)(()=>{let t=e.schemaData?.example;if(void 0!=t)return JSON.stringify(t,null,2)},[e.schemaData?.example])})();return null==e?null:(0,a.jsx)(d.CodeGroup,{isSmallText:!0,noMargins:!0,children:(0,a.jsx)(o.CodeBlock,{filename:"Example",children:(0,a.jsx)(u.z,{language:"json",children:e})},"schema-example")})},m={curl:"bash",golang:"go",js:"javascript",sh:"bash",ts:"typescript"},x={bash:"cURL",c:"C","c#":"C#","c++":"C++",go:"Go",java:"Java",javascript:"JavaScript",php:"PHP",python:"Python",typescript:"TypeScript",ruby:"Ruby"},h={filename:"cURL",snippet:{target:"shell"},language:"bash"},g={filename:"Python",snippet:{target:"python",client:"requests"},language:"python"},y={filename:"JavaScript",snippet:{target:"javascript",client:"fetch"},language:"javascript"},f={filename:"PHP",snippet:{target:"php",client:"curl"},language:"php"},b={filename:"Go",snippet:{target:"go"},language:"go"},j={filename:"Java",snippet:{target:"java"},language:"java"},v=[h,g,y,f,b,j],k={bash:h,python:g,javascript:y,php:f,go:b,java:j},w=e=>{let t=e.toLowerCase();return m[t]??t};var N=r(74840),C=r(62837),S=r(3256),O=r(19637),A=r(11),E=r(51657),q=r(5121),I=r(88374),T=r(88638);let $=({code:e})=>{let t=(0,I.p)("docs.code_block.copy");return(0,a.jsx)("div",{"data-testid":"code-group-select-copy-button",children:(0,a.jsx)(T.TN,{textToCopy:e,onCopied:(e,r)=>t({code:r})})})};var P=r(74711),M=r(49341);let B=({selectedOption:e,setSelectedOption:t,options:r})=>{let n=r.length>1,{docsConfig:l}=(0,s.useContext)(c.H6),o=l?.styling?.codeblocks;return(0,a.jsxs)(M.rI,{children:[(0,a.jsx)(M.ty,{disabled:!n,className:(0,i.cn)("select-none bg-transparent px-2 py-[5px] text-xs font-medium min-w-16 flex-1 dark:bg-transparent","system"===o&&"text-primary dark:text-primary-light",("dark"===o||void 0==o)&&"text-primary-light"),children:(0,a.jsxs)("div",{className:"flex gap-1.5 items-center px-2 py-1 group-hover:hover:bg-primary-light/10 rounded-lg min-w-16",children:[(0,a.jsx)("p",{className:"truncate",children:e}),n&&(0,a.jsx)(P.A,{size:12,className:"min-w-3"})]})}),(0,a.jsx)(M.SQ,{className:(0,i.cn)("p-1 overflow-y-auto","system"===o&&"border border-gray-200/70 dark:border-gray-800/50 bg-gray-50 dark:bg-[#0F1117]",("dark"===o||void 0==o)&&"border-none bg-[#0F1117]"),children:r.map((r,s)=>(0,a.jsx)(M._2,{onSelect:()=>t(r),className:(0,i.cn)("py-1.5 text-xs","system"===o&&"hover:text-primary hover:bg-primary/10 dark:hover:text-primary-light dark:hover:bg-primary-light/10",("dark"===o||void 0==o)&&"text-primary-light dark:text-primary-light hover:text-primary-light hover:bg-primary-light/10",r===e&&"system"===o&&"text-primary dark:text-primary-light font-medium",r===e&&("dark"===o||void 0==o)&&"text-primary-light dark:text-primary-light font-medium",r!==e&&"system"===o&&"text-gray-500 dark:text-white/50",r!==e&&("dark"===o||void 0==o)&&"text-white/50"),children:r},s))})]})},V=({snippets:e})=>{let{docsConfig:t}=(0,s.useContext)(c.H6),r=Object.keys(e),[n,l]=(0,s.useState)(r[0]),o=void 0!==n?e[n]:void 0,d=o?Object.keys(o):void 0,[p,m]=(0,s.useState)(d&&d[0]),x=t?.styling?.codeblocks,h=p&&d?.includes(p)?p:d?.[0],g=void 0!==o&&void 0!==h?o[h]:void 0;return(0,a.jsxs)("div",{className:(0,i.cn)("text-xs p-0.5 leading-6 rounded-2xl not-prose overflow-hidden relative max-w-full min-w-full","system"===x&&"bg-gray-50 dark:bg-white/5 border border-gray-950/10 dark:border-white/10",("dark"===x||void 0==x)&&"bg-codeblock dark:bg-white/5 ring-1 ring-transparent dark:ring-white/[0.14]"),"data-testid":"code-group-select",children:[(0,a.jsxs)("div",{className:"flex justify-between text-xs leading-6 rounded-t-2xl w-full",children:[(0,a.jsx)(B,{selectedOption:n,setSelectedOption:l,options:r,"data-testid":"code-group-select-group"}),(0,a.jsxs)("div",{className:"flex overflow-hidden",children:[d&&(0,a.jsx)(B,{selectedOption:h,setSelectedOption:m,options:d,"data-testid":"code-group-select-option"}),(0,a.jsxs)("div",{className:"flex items-center gap-1.5 pr-2.5","data-testid":"code-group-select-copy-button",children:[(0,a.jsx)($,{code:g?.code??""}),(0,a.jsx)(q.y,{code:g?.code??"",filename:g?.filename,language:g?.language})]})]})]}),(0,a.jsx)("div",{className:(0,i.cn)("dark:bg-codeblock not-prose rounded-[14px] p-5 min-w-full max-w-0 overflow-x-auto max-h-[calc(100%-34px)] w-full scrollbar-track-transparent scrollbar-thin scrollbar-thumb-rounded text-xs leading-[1.35rem]","system"===x&&"bg-white text-gray-950 dark:text-gray-50 scrollbar-thumb-black/20 dark:scrollbar-thumb-white/10 codeblock-light",("dark"===x||void 0==x)&&"bg-codeblock text-gray-50 scrollbar-thumb-white/25 dark:scrollbar-thumb-white/10 codeblock-dark"),children:(0,a.jsx)(u.z,{language:g?.language,children:g?.code})})]})},D=({snippets:e,dropdown:t,...r})=>(0,a.jsx)(d.CodeGroup,{isSmallText:!0,...r,dropdown:t,children:e.map(({code:e,...t})=>(0,a.jsx)(o.CodeBlock,{language:t.language,filename:t.filename,children:(0,a.jsx)(u.z,{language:t.language,children:e})},t.filename))}),L={headers:[],method:"GET",url:"",httpVersion:"HTTP/1.1",queryString:[],headersSize:-1,cookies:[],bodySize:-1};var R=r(17759),H=r(66642),U=r(95666);let F=({exampleData:e,endpoint:t,baseUrl:r,inputs:a,selectedBodyContentType:s,requiredOnly:n})=>{let l=_(a,Object.entries(t.request.parameters.query).filter(([e,t])=>"deepObject"===t.style).map(([e])=>e)),i=(0,R.rV)(t.path,a.path),o=((e,t,r)=>{let a=[],s=new Set;return Object.entries(t).filter(e=>null!=e[1]).forEach(([e,t])=>{s.add(e),a.push({name:e,value:t.toString()})}),Object.entries(e.request.parameters.header).filter(([e,t])=>t.schema[0].required).forEach(([e])=>{s.has(e)||a.push({name:e,value:`<${e.toLowerCase()}>`})}),Object.entries(e.request.security[0]?.parameters.header??[]).forEach(([e,t])=>{s.has(e)||a.push({name:e,value:(e=>{switch(e.type){case"http":return"basic"===e.scheme?"Basic <encoded-value>":"Bearer <token>";case"oauth2":return"Bearer <token>";default:return"<api-key>"}})(t)})}),r&&((e,t)=>{e.some(({name:e})=>"content-type"===e.toLowerCase())||e.push({name:"Content-Type",value:t})})(a,r),a})(t,a.header,s),d=G(a.body)?e:a.body,c=n&&!a.body?((e,t)=>{let r=[];if(t){let a=e.request.body[t];a?.schemaArray&&a.schemaArray.forEach(e=>{"object"===e.type&&e.requiredProperties&&r.push(...e.requiredProperties)})}return r})(t,s):void 0,u=((e,t)=>{let r=[];if(t){let a=e.request.body[t];a?.schemaArray&&a.schemaArray.forEach(e=>{"object"===e.type&&Object.entries(e.properties).map(([e,t])=>{Object.entries((0,U.Y)(t)).some(([e,t])=>"format"===e.split(".").pop()&&("binary"===t||"base64"===t))&&r.push(e)})})}return r})(t,s),p=((e,t,r,a)=>{if(void 0!==e){if("object"==typeof e&&null!==e&&r&&r.length>0&&(e=Object.fromEntries(Object.entries(e).filter(([e])=>r.includes(e)))),"multipart/form-data"===t||"application/x-www-form-urlencoded"===t){if(!e||"object"!=typeof e)return;"object"==typeof e&&a&&a.length>0&&(0,H.Gv)(e)&&a.forEach(t=>{t in e||(e[t]={fileName:"example-file"})});let r=e=>(0,H.fo)(e)?e.name||"file":"object"==typeof e?JSON.stringify(e,void 0,2):e?.toString();return{mimeType:t,params:Object.entries(e).flatMap(([e,t])=>(0,H.fo)(t)&&"name"in t?[{name:e,value:r(t),fileName:t.name}]:a&&a.includes(e)?[{name:e,value:r(t),fileName:"example-file"}]:[{name:e,value:r(t)}])}}return"string"==typeof e&&t&&function(e){let t=e.toLowerCase();return t.startsWith("text/")||t.endsWith("+xml")||t.endsWith("/xml")}(t)?{mimeType:t,text:e}:{mimeType:"application/json",text:JSON.stringify(e,null,2)}}})(d,s,c,u);return{...L,method:t.method.toUpperCase(),url:(0,R.s4)((0,R.sb)(r,a.server),i),queryString:l,headers:o,postData:p}},_=(e,t)=>Object.entries(e.query).flatMap(([e,r])=>r?Array.isArray(r)?r.filter(Boolean).map(t=>({name:e,value:t.toString()})):"object"==typeof r?t?.includes(e)?Object.entries(r).filter(([e,t])=>t).map(([t,r])=>({name:`${e}[${t}]`,value:r.toString()})):[{name:e,value:JSON.stringify(r)}]:[{name:e,value:r.toString()}]:[]),G=e=>null==e||("object"==typeof e?Object.values(e).every(e=>void 0===e):void 0);var z=r(11855),W=r(73874),J=r.n(W);let K=({request:e,pathInputs:t,endpoint:r,snippetPresets:a=v,isMultipleRequest:s=!1})=>{let n={},l=e.url.replace(/:{[^{}]+}/g,e=>{let t=":62437";return n[t]=e,t}).replace(/{[^{}]+}/g,e=>{let r=t[e.slice(1,e.length-1)];if(r&&("string"!=typeof r||r.length>0))return`${encodeURIComponent(r.toString())}`;let a=J()();return n[a]=e,a}),i=new z.HTTPSnippet({...e,url:l,postData:e.postData||{mimeType:"application/json"}});return a.map(e=>{let{filename:t,snippet:a,language:l}=e,{target:o,client:d}=a,c=Z(n,i.convert(o,d).toString());return{filename:s?t:r.title||t,code:c,language:l}})},Z=(e,t)=>Object.entries(e).reduce((e,[t,r])=>e.replace(t,r),t),Q=({baseUrl:e,endpoint:t,inputs:r,requestExampleLanguages:s,requiredOnly:n})=>{let[l]=Object.entries(t.request.body);if(void 0===l)return(0,a.jsx)(X,{endpoint:t,baseUrl:e,inputs:r,requestExampleLanguages:s,requiredOnly:n});let[i,o]=l,d=Object.values(o.examples)[0];return void 0===d?null:Object.keys(o.examples).length>1?(0,a.jsx)(Y,{endpoint:t,selectedBodyContentType:i,baseUrl:e,inputs:r,examples:o.examples,requiredOnly:n}):(0,a.jsx)(X,{endpoint:t,selectedBodyContentType:i,baseUrl:e,inputs:r,exampleValue:d.value,requestExampleLanguages:s,requiredOnly:n})},X=({baseUrl:e,endpoint:t,inputs:r,selectedBodyContentType:s,exampleValue:n,requestExampleLanguages:l,requiredOnly:i})=>{let o,d=F({baseUrl:e,endpoint:t,inputs:r,selectedBodyContentType:s,exampleData:n,requiredOnly:i}),c=l?.reduce((e,t)=>{let r=k[t];return r&&e.push(r),e},[]);try{o=K({endpoint:t,pathInputs:r.path,request:d,snippetPresets:c})}catch(e){return console.error(e),null}return(0,a.jsx)(D,{snippets:o,noMargins:!0,dropdown:!0})},Y=({baseUrl:e,endpoint:t,inputs:r,selectedBodyContentType:s,examples:n,requiredOnly:l})=>{let i={},o=r.path;for(let[a,{value:d}]of Object.entries(n)){if(!d)continue;let n=F({baseUrl:e,endpoint:t,inputs:r,selectedBodyContentType:s,exampleData:d,requiredOnly:l});try{K({endpoint:t,pathInputs:o,request:n,isMultipleRequest:!0}).forEach(e=>{i[e.filename]={...i[e.filename],[a]:e}})}catch(e){return console.error(e),null}}return(0,a.jsx)(V,{snippets:i})},ee=["bash","python","javascript","php","go","java"],et=(e,t,r,a,s)=>{""in a&&(a["Example 1"]=a[""],delete a[""]);let n=Object.keys(a).length,l=e.label??(n>0?`Example ${n+1}`:"");a[l]={filename:s?r:l||r,language:t,code:e.source}},er=e=>{let t=(({codeSamples:e,endpoint:t,baseUrl:r,inputs:a,apiPlaygroundMode:s,requestExampleLanguages:n,requiredOnly:l})=>{let i=r&&"hide"!==s||r&&n,o=n??[];return e=e.map(e=>({...e,lang:w(e.lang)})),void 0===n&&(e.forEach(e=>{o.includes(e.lang)||o.push(e.lang)}),i&&ee.forEach(e=>{o.includes(e)||o.push(e)})),o.reduce((n,i)=>{let o=x[i]??i,d=e.filter(e=>e.lang===i),c=n[o]??{};if(0===d.length){let e=(({endpoint:e,baseUrl:t,inputs:r,apiPlaygroundMode:a,lang:s,requiredOnly:n})=>{if(t&&"hide"!==a)try{let a,l,[i]=Object.entries(e.request.body);if(i){let[e,t]=i,r=Object.values(t.examples)[0];a=r?.value,l=e}let o=F({baseUrl:t,endpoint:e,inputs:r,exampleData:a,selectedBodyContentType:l,requiredOnly:n}),d=k[s];if(void 0==d)return;let[c]=K({request:o,pathInputs:r.path,snippetPresets:[d],endpoint:e});if(c)return{label:"Interactive Example",lang:c.language,source:c.code}}catch(e){}})({baseUrl:r,endpoint:t,inputs:a,apiPlaygroundMode:s,lang:i,requiredOnly:l});e&&et(e,i,o,c,!0)}else d.forEach(e=>{o=x[i]??e.label??i,et(e,i,o,c,!1)});return Object.keys(c).length>0&&(n[o]=c),n},{})})(e);if(Object.values(t).every(e=>Object.keys(e).length<=1)){let e=Object.values(t).flatMap(e=>Object.values(e));return new Set(e.map(e=>e.filename)).size!==e.length&&(e=e.map(e=>({...e,filename:x[e.language]??e.language}))),(0,a.jsx)(D,{snippets:e,noMargins:!0,dropdown:!0})}return(0,a.jsx)(V,{snippets:t})};var ea=r(44715),es=r(44902);let en=({data:e})=>{let t=e.attributes?.some(e=>"dropdown"===e.name&&"false"!==e.value);return(0,a.jsx)(d.CodeGroup,{isSmallText:!0,noMargins:!0,dropdown:t,children:e.children?.map((e,t)=>{let r=(0,ea.Ay)(e.html??""),s="object"!=typeof r||Array.isArray(r)||"pre"!==r.type?void 0:r.props.language??(0,es.G4)(r.props.className,e.filename);return(0,a.jsx)(o.CodeBlock,{language:s,filename:e.filename,children:(0,a.jsx)("span",{children:r})},(e.filename??"")+t)})})};var el=r(68629);let ei=()=>{let e,t=(0,S.Zk)();if(!t)return null;let[r]=Object.entries(t.request.body);if(r){let[t,a]=r,s=Object.values(a.examples)[0];s&&(e=JSON.stringify(s.value,null,2))}return e?(0,a.jsx)(el.l,{isSmallText:!0,noMargins:!0,children:(0,a.jsx)(T.NG,{language:"json",filename:"Example Request Body",children:(0,a.jsx)(u.z,{language:"json",children:e})})}):null},eo=()=>{let e=(0,E.u)("request"),t=(0,S.Zk)(),r=(0,A.A)(),[{apiBaseIndex:n}]=(0,O.O)(),l=(0,N.D)(),i=(0,C.u)(),{docsConfig:o}=(0,s.useContext)(c.H6),d=i?.[n]??i?.[0],u=t?.request.codeSamples,p=o?.api?.examples?.languages?.map(w),m=o?.api?.examples?.defaults==="required";return void 0!==e?(0,a.jsx)(en,{data:e}):t?.type==="webhook"?(0,a.jsx)(ei,{}):u&&u.length>0?(0,a.jsx)(er,{endpoint:t,inputs:r,baseUrl:d,codeSamples:u,apiPlaygroundMode:l,requestExampleLanguages:p,requiredOnly:m}):void 0!==t&&void 0!==d?(0,a.jsx)(Q,{endpoint:t,inputs:r,baseUrl:d,requestExampleLanguages:p,requiredOnly:m}):null},ed=({endpoint:e,selectedResponseContentType:t})=>{if(0===Object.keys(e.response).length||Object.values(e.response).every(e=>0===Object.keys(e).length)||Object.values(e.response).every(e=>e["_mintlify/placeholder"]))return null;let r=((e,t)=>Object.entries(e).sort().reduce((e,[r,a])=>{let s=void 0!==t&&a[t]?a[t]:Object.values(a)[0];if(void 0===s)return e[r]={"":{filename:r,code:"This response has no body data.",language:""}},e;if(Object.values(s.examples)[0]?.value===void 0)return e[r]={"":{filename:r,code:"This response does not have an example.",language:""}},e;let n=Object.entries(s.examples).reduce((e,[t,a])=>{let s=JSON.stringify(a.value,null,2);return e[t]={filename:r,language:"json",code:s},e},{});return e[r]=n,e},{}))(e.response,t);if(Object.values(r).every(e=>Object.keys(e).length<=1)){let e=Object.values(r).flatMap(e=>Object.values(e));return(0,a.jsx)(D,{snippets:e,noMargins:!0})}return(0,a.jsx)(V,{snippets:r})},ec=()=>{let e=(0,E.u)("response"),t=(0,S.Zk)();return void 0!==e?(0,a.jsx)(en,{data:e}):void 0!==t?(0,a.jsx)(ed,{endpoint:t}):null},eu=({className:e})=>{let t=(()=>{let{isChatSheetOpen:e}=(0,s.useContext)(l.ChatAssistantContext);return e})(),r=(0,s.useMemo)(()=>(0,i.cn)("w-full xl:w-[28rem] gap-6 grid grid-rows-[repeat(auto-fit,minmax(0,min-content))] grid-rows relative max-h-[calc(100%-32px)] min-h-[18rem]",e,t&&"xl:invisible"),[e,t]);return(0,a.jsx)("div",{className:r,children:(0,a.jsxs)(n.A,{children:[(0,a.jsx)(eo,{}),(0,a.jsx)(ec,{}),(0,a.jsx)(p,{})]})})}},31581:(e,t,r)=>{"use strict";r.d(t,{X:()=>i});var a=r(54568),s=r(7620),n=r(89619),l=r(16600);let i=({name:e,validationState:t,children:r,rightElement:i})=>{let[c,u]=(0,s.useState)(!0);return(0,a.jsxs)("div",{children:[(0,a.jsxs)("button",{"aria-label":"expand input section",onClick:()=>u(e=>!e),className:(0,l.cn)("flex w-full px-4 py-2.5 items-center justify-between border-standard cursor-pointer hover:bg-gray-50 dark:hover:bg-white/5",c?"!border-b-0 border-t border-x rounded-t-2xl":"rounded-2xl"),children:[(0,a.jsxs)("div",{className:"flex items-center gap-x-1.5",children:[(0,a.jsx)(n.Ay,{icon:"angle-right",iconType:"solid",className:(0,l.cn)("h-3 w-3 bg-gray-400 dark:bg-gray-500 transition-transform",c&&"rotate-90")}),(0,a.jsxs)("div",{className:"flex-1 flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-800 dark:text-gray-200 leading-6",children:e}),t?"invalid"===t?(0,a.jsx)(o,{}):(0,a.jsx)(d,{}):null]})]}),i]}),(0,a.jsxs)("div",{className:(0,l.cn)("bg-background-light dark:bg-background-dark flex-1 px-4 rounded-b-xl border-standard !border-t-0 divide-y divide-gray-100 dark:divide-white/10",c&&r?"":"hidden"),children:[(0,a.jsx)("div",{}),r]})]})},o=()=>(0,a.jsx)(n.Ay,{className:"h-4 w-4 bg-[#2AB673]",icon:"circle-check"}),d=()=>(0,a.jsx)(n.Ay,{className:"h-4 w-4 bg-[#CB3A32]",icon:"circle-exclamation"})},31972:(e,t,r)=>{"use strict";r.d(t,{y:()=>n});var a=r(54568),s=r(35720);let n=({onClick:e,count:t,labeled:r})=>{let n="";return 0===t?null:(n=1===t?"show 1 property":`show ${t} properties`,r)?(0,a.jsxs)("button",{onClick:e,className:"flex justify-center items-center pt-0.5 pb-0.5 pl-2 pr-1.5 rounded-lg border border-gray-200 dark:border-gray-800 text-gray-600 dark:text-gray-400",children:[(0,a.jsx)("span",{className:"text-sm font-sans font-medium mr-1 whitespace-nowrap",children:n}),(0,a.jsx)(s.A,{className:"w-3.5 h-3.5",strokeWidth:2.5})]}):(0,a.jsx)("button",{onClick:e,className:"px-1 text-gray-600 dark:text-gray-500 ",children:(0,a.jsx)(s.A,{className:"w-3.5 h-3.5",strokeWidth:2.5})})}},32441:(e,t,r)=>{"use strict";r.d(t,{X:()=>c});var a=r(54568),s=r(7620),n=r(10122),l=r(89619),i=r(60710),o=r(16600);let d=({type:e,title:t,content:r})=>{let[d,c]=(0,s.useState)(!1);return(0,a.jsxs)("div",{className:"border-t w-full border-zinc-100 dark:border-white/5",children:[(0,a.jsxs)("button",{"aria-label":"expand message example section",onClick:()=>c(e=>!e),className:"flex justify-between w-full px-3 py-2.5 items-center cursor-pointer",children:[(0,a.jsxs)("div",{className:"flex items-center gap-x-2",children:[(0,a.jsx)("div",{className:"w-4 h-4",children:"send"===e?(0,a.jsx)(i.t9,{}):(0,a.jsx)(i.Cc,{})}),(0,a.jsx)("div",{className:"text-xs font-mono text-gray-600 dark:text-gray-400 whitespace-nowrap overflow-hidden text-ellipsis",children:t})]}),(0,a.jsx)(l.Ay,{icon:"angle-right",iconType:"solid",className:(0,o.cn)("h-3 w-3 bg-gray-400 dark:bg-gray-600 transition-transform rotate-90",d&&"rotate-[-90deg]")})]}),(0,a.jsx)("div",{className:(0,o.cn)("bg-zinc-100 dark:bg-zinc-900 flex-1 px-4 divide-y divide-gray-100 dark:divide-white/5 text-xs",d?"":"hidden"),children:(0,a.jsx)("div",{className:"overflow-scroll py-2",children:(0,a.jsx)(n.z,{language:"json",children:r})})})]})},c=({name:e,messages:t,className:r,icon:s,rightElement:n,children:l})=>(0,a.jsxs)("div",{className:(0,o.cn)("w-full xl:w-[28rem] gap-6 grid grid-rows-[repeat(auto-fit,minmax(0,min-content))] grid-rows relative",r),children:[(0,a.jsx)("div",{className:"flex w-full items-center justify-between border-standard border-t border-x rounded-t-2xl rounded-b-2xl overflow-scroll h-full max-h-[calc(100vh-11rem)]",children:(0,a.jsxs)("div",{className:"flex flex-col w-full h-full",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between px-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-x-2",children:[s&&s,(0,a.jsx)("div",{className:"text-sm font-medium text-gray-800 dark:text-gray-200 leading-6 py-2",children:e})]}),(0,a.jsx)("div",{className:"flex items-center",children:n&&n})]}),t.map(e=>(0,a.jsx)(d,{type:e.type,title:e.title,content:e.content},e.id))]})}),l]})},32907:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Api:()=>tN});var a=r(54568),s=r(7620),n=r(71197),l=r(79460),i=r(62837),o=r(45978),d=r(11),c=r(19637),u=r(21383),p=r(17759),m=r(45744);let x=(0,s.createContext)({isSending:!1,selectedBaseUrlIndex:0,isPlaygroundExpanded:!1});var h=r(38187);let g="mintlify-server-variables",y=()=>{let e=localStorage.getItem(g);if(e)try{let t=JSON.parse(e);if((e=>"object"==typeof e&&null!=e&&Object.values(e).every(e=>"object"==typeof e&&null!==e&&Object.values(e).every(e=>"string"==typeof e)))(t))return t}catch(e){console.log(`unable to parse stored server variables: ${e}`)}return{}};var f=r(50887).Buffer;let b=(e,t,r)=>{let a={query:{},header:{},cookie:{}};if(!e)return a;for(let s of["query","header","cookie"])Object.entries(e.parameters[s]).forEach(([e,n])=>{a[s][e]=t?.[s][e]??j(r?.[s]?.[e],n)});return a},j=(e,t)=>{if(!e)return e;if("http"===t.type&&"basic"===t.scheme){if("object"==typeof e){let t="username"in e&&"string"==typeof e.username?e.username:"",r="password"in e&&"string"==typeof e.password?e.password:"";return`Basic ${f.from(`${t}:${r}`).toString("base64")}`}return}return"http"===t.type&&"bearer"===t.scheme?"string"==typeof e?e.startsWith("Bearer ")?e:`Bearer ${e}`:void 0:e},v=(e,t,r)=>{let a=r?.server,s=t&&e?t[e]:void 0;return{...a,...s}};var k=r(3256),w=r(16600),N=r(35720),C=r(74913),S=r(46060),O=r(31467),A=r(89619),E=r(26997);let q=({endpoint:e,display:t,onClickInteractive:r,hasGrayBackground:s})=>(0,a.jsxs)("div",{className:"flex items-center space-x-1.5",children:[(0,a.jsx)(T,{endpoint:e,display:t,hasGrayBackground:s}),"simple"===t?null:(0,a.jsx)(V,{method:e.method,label:"Try it",onClick:r})]}),I=()=>{let{baseUrlOptions:e,selectedBaseUrlIndex:t,setSelectedBaseUrlIndex:r}=(0,s.useContext)(x),[n,l]=(0,s.useState)(0),i=e?.[t];return((0,s.useEffect)(()=>{let t=document.createElement("span");t.className="text-sm absolute font-mono invisible";let a=sessionStorage.getItem("selectedBaseUrl");a&&a!==i&&e&&r&&e.includes(a)?(r(e.indexOf(a)),t.textContent=a):t.textContent=i??"",document.body.appendChild(t),l(t.offsetWidth+16),document.body.removeChild(t)},[i,r,t,e]),e&&!(e.length<=1)&&r)?(0,a.jsxs)("div",{className:"flex items-center gap-1 relative -mr-1",children:[(0,a.jsx)("select",{className:"bg-transparent text-sm text-gray-600 dark:text-gray-400 outline-none font-mono cursor-pointer hover:text-gray-900 dark:hover:text-gray-300",value:t,onClick:e=>e.stopPropagation(),onChange:t=>{sessionStorage.setItem("selectedBaseUrl",e[Number(t.target.value)]??""),r(Number(t.target.value))},style:{width:`${n}px`},children:e.map((e,t)=>(0,a.jsx)("option",{value:t,children:e},e))}),(0,a.jsx)(N.A,{className:"absolute right-0 w-3 h-3 text-gray-400 dark:text-white/30 pointer-events-none",strokeWidth:2.5})]}):null},T=({endpoint:e,display:t,hasGrayBackground:r})=>{let[n,l]=(0,s.useState)(!1),{baseUrlOptions:i,selectedBaseUrlIndex:o}=(0,s.useContext)(x),c=(0,d.A)(),u=i?.[o]??i?.[0]??"",m=async()=>{let t=(0,p.rV)(e.path,c.path),r=(0,p.s4)((0,p.sb)(u,c.server),t);"success"===await (0,E.l)(r)&&(l(!0),setTimeout(()=>{l(!1)},2e3))};return(0,a.jsxs)("div",{className:(0,w.cn)("relative flex-1 flex gap-2 min-w-0 rounded-xl items-center cursor-pointer p-1.5","simple"!=t&&"border-standard",r&&"bg-gray-50 dark:bg-white/5"),onClick:m,children:[(0,a.jsx)(S.l,{method:"webhook"===e.type?"webhook":e.method,active:!1}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 overflow-x-auto flex-1 no-scrollbar",children:["webhook"!==e.type&&(0,a.jsx)(I,{}),(0,a.jsx)($,{endpoint:e,display:t,isCopiedActive:n,hasGrayBackground:r})]})]})},$=({endpoint:e,isCopiedActive:t,hasGrayBackground:r})=>{let n=(0,s.useMemo)(()=>e.path.split("/").flatMap((t,r)=>{let s=[];return r>0&&s.push((0,a.jsx)(B,{},r)),t.split("{").forEach((t,n)=>{if(t.includes("}")){let[l,i]=t.split("}");s.push((0,a.jsx)(M,{method:e.method,children:l},`${r}-${n}-param`)),i&&s.push((0,a.jsx)(P,{children:i},`${r}-${n}`))}else t&&s.push((0,a.jsx)(P,{children:t},`${r}-${n}`))}),s}),[e.path,e.method]);return(0,a.jsxs)("div",{className:"group flex items-center flex-1 gap-0.5 font-mono",children:[(0,a.jsx)("div",{className:(0,w.cn)("absolute right-0 p-2 bg-background-light dark:bg-background-dark rounded-lg",r&&"bg-transparent dark:bg-transparent",t?"block":"hidden group-hover:block"),children:t?(0,a.jsx)(A.Ay,{icon:"circle-check",iconType:"solid",className:"w-4 h-4 bg-primary dark:bg-primary-light"}):(0,a.jsx)(A.Ay,{icon:"clone",className:"w-4 h-4 bg-gray-400 dark:bg-white/30"})}),n]})},P=({children:e})=>(0,a.jsx)("div",{className:"text-sm font-medium text-gray-800 dark:text-white min-w-max",children:e}),M=({children:e,method:t})=>{let{paramStyle:r}=(0,C.H)(t);return(0,a.jsx)("div",{className:(0,w.cn)("text-sm font-mono font-medium rounded-md px-1 border-2 min-w-max",r),children:`{${e}}`})},B=()=>(0,a.jsx)("div",{className:"text-sm text-gray-400",children:"/"}),V=({label:e,method:t,onClick:r,isSending:s,className:n})=>{let{activeNavPillBg:l}=(0,C.H)(t);return(0,a.jsx)("button",{className:(0,w.cn)(O.x.TryitButton,"flex items-center justify-center px-3 h-9 text-white font-medium rounded-xl mouse-pointer disabled:opacity-70 hover:opacity-80 gap-1.5",l,n),onClick:r,disabled:s,"data-testid":"try-it-button",children:s?(0,a.jsx)(A.Ay,{icon:"spinner-third",iconType:"duotone",className:"w-4 h-4 bg-white animate-spin"}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{children:e}),(0,a.jsx)(A.Ay,{icon:"play",iconType:"solid",className:"w-3 h-3 bg-white"})]})})};var D=r(44664),L=r(28847);let R=e=>{let t=e.default;if(void 0!==t)return t};var H=r(47181),U=r(18073),F=r(12565);let _=({message:e,icon:t})=>{let{docsConfig:r}=(0,s.useContext)(n.H6),l=r?.styling?.codeblocks;return(0,a.jsx)("div",{className:(0,w.cn)("system"===l?"bg-response-body-auto":"bg-response-body-dark","rounded-b-xl overflow-auto"),children:(0,a.jsxs)("div",{className:"pb-8 pt-10 px-6 space-y-4 flex flex-col items-center",children:[(0,a.jsx)(A.Ay,{icon:t,className:"h-8 w-8 bg-gray-300 dark:bg-white/20"}),(0,a.jsx)("div",{className:"text-sm text-center",children:e})]})})},G=({data:e})=>{let{docsConfig:t}=(0,s.useContext)(n.H6),r=t?.styling?.codeblocks;return(0,a.jsx)("div",{className:(0,w.cn)("system"===r?"bg-response-body-auto":"bg-response-body-dark","flex p-12 items-center rounded-b-xl h-full"),children:(0,a.jsx)("audio",{controls:!0,className:"w-full",src:e.url})})};var z=r(90487),W=r(60502),J=r(44902),K=r(95391);let Z=({data:e})=>{let{docsConfig:t}=(0,s.useContext)(n.H6),r=t?.styling?.codeblocks,l=!1,i=e.content,o=e.content.length>5*z.S5;try{i=JSON.parse(e.content),l=!0}catch(e){console.error(e)}let d=(0,J.Sh)({codeString:l?JSON.stringify(i,null,2):e.content,codeBlockTheme:r,language:l?"json":"text",opts:{structure:"inline"}});return(0,a.jsx)("div",{className:(0,w.cn)("overflow-auto h-full",(0,W.O)(r)),children:(0,a.jsx)("div",{className:(0,w.cn)(K.SHIKI_CLASSNAME,"px-3 py-3.5 h-full whitespace-pre border-gray-200 dark:border-gray-700 dark:text-gray-300 font-mono text-xs leading-5",o&&"whitespace-pre-line"),children:l&&void 0!==d?(0,a.jsx)("span",{suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:d}}):(0,a.jsx)("span",{suppressHydrationWarning:!0,children:l?JSON.stringify(i,null,2):e.content})})})},Q=({data:e})=>{let{docsConfig:t}=(0,s.useContext)(n.H6),r=t?.styling?.codeblocks;return(0,a.jsx)("div",{className:(0,w.cn)("system"===r?"bg-response-body-auto":"bg-response-body-dark","h-60"),children:(0,a.jsx)("img",{alt:"image response from the server",className:"p-6 w-full h-full object-scale-down",src:e.url})})},X=({data:e})=>{if(null==e)return(0,a.jsx)(_,{message:"No body data received from the server",icon:"brackets-curly"});switch(e.type){case"audio":return(0,a.jsx)(G,{data:e});case"image":return(0,a.jsx)(Q,{data:e});case"other":return(0,a.jsx)(Z,{data:e})}},Y=({message:e})=>(0,a.jsx)(_,{message:`An error occurred while making the request: ${e}`,icon:"triangle-exclamation"}),ee=({data:e})=>{let{docsConfig:t}=(0,s.useContext)(n.H6),r=t?.styling?.codeblocks;return(0,a.jsx)("div",{className:"w-full rounded-b-xl overflow-y-auto",children:(0,a.jsx)("table",{className:"table-fixed pb-8 w-full pt-10",children:e.flatMap(([e,t])=>(0,a.jsxs)("tr",{className:(0,w.cn)("w-full flex divide-x border-b last:border-b-0 text-xs text-gray-600 dark:text-gray-400","system"===r?"divide-gray-100 dark:divide-white/10 border-gray-100 dark:border-white/10 text-gray-600 dark:text-gray-400":"divide-white/10 border-white/10 text-gray-400"),children:[(0,a.jsx)("td",{className:"flex items-center basis-1/2 py-2 px-4 overflow-x-autotext-gray-600",children:(0,a.jsx)("div",{className:"min-w-max",children:e})}),(0,a.jsx)("td",{className:"flex items-center basis-1/2 py-2 px-4 overflow-x-auto",children:(0,a.jsx)("div",{className:"min-w-max",children:`${t}`})})]},e))})})},et=({response:e})=>{let t=Object.entries(e.headers);return 0===t.length?(0,a.jsx)(_,{message:"No headers received from the server",icon:"table"}):(0,a.jsx)(ee,{data:t})};var er=r(88638);let ea=({data:e})=>{let[t,r]=(0,s.useState)(!1),n=en(e,()=>{r(!0),setTimeout(()=>{r(!1)},2e3)},e=>console.error(`unable to copy response to clipboard: ${e}`));return n?(0,a.jsx)(er.rZ,{onClick:n,isCopiedActive:t}):null},es=["image/png","text/plain"],en=(e,t,r)=>(0,s.useMemo)(()=>{if(e.contentType&&es.includes(e.contentType))try{let a=new ClipboardItem({[e.contentType]:e.blob});return()=>navigator.clipboard.write([a]).then(t,r)}catch(e){console.error(`unable to create clipboard item: ${e}`)}if("other"===e.type)return()=>navigator.clipboard.writeText(e.content).then(t,r)},[e,t,r]),el=({data:e})=>{let{docsConfig:t}=(0,s.useContext)(n.H6),[r,l]=(0,s.useState)(!1),i=t?.styling?.codeblocks;return e.extension?(0,a.jsxs)("a",{className:"group h-7 w-7 flex items-center justify-center cursor-pointer relative",onClick:()=>{l(!0),setTimeout(()=>{l(!1)},2e3)},href:e.url,download:`api-response.${e.extension}`,children:[r?(0,a.jsx)(A.Ay,{icon:"circle-check",iconType:"solid",className:"h-4 w-4 bg-primary dark:bg-primary-light"}):(0,a.jsx)(A.Ay,{icon:"arrow-down-to-line",className:(0,w.cn)("w-4 h-4","system"===i?"bg-gray-400 group-hover:bg-gray-500 dark:bg-white/40 dark:group-hover:bg-white/60":"bg-white/40 group-hover:bg-white/60")}),(0,a.jsx)(er.iG,{text:r?"Copied!":"Download"})]}):null},ei=({result:e,selectedSection:t,setSelectedSection:r,data:s})=>(0,a.jsxs)("div",{className:"flex w-full px-2 py-1 space-x-3 items-center rounded-t-2xl",children:[(0,a.jsx)("div",{className:"flex flex-1 space-x-4 items-center",children:(0,a.jsx)(eo,{status:e.error?void 0:e.response.status,statusText:e.error?void 0:e.response.statusText})}),s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ec,{selectedSection:t,setSelectedSection:r}),(0,a.jsx)(el,{data:s}),(0,a.jsx)(ea,{data:s})]}):null]}),eo=({status:e,statusText:t})=>{let{docsConfig:r}=(0,s.useContext)(n.H6),l=r?.styling?.codeblocks;return(0,a.jsxs)("div",{className:"flex space-x-2 items-center",children:[(0,a.jsx)(ed,{status:e}),(0,a.jsx)("div",{className:(0,w.cn)("text-xs","system"===l?"text-gray-800 dark:text-gray-200 font-medium":"text-gray-200 font-medium"),children:e?`${e} - ${t}`:"--"})]})},ed=({status:e})=>{let t="w-3.5 h-3.5";return void 0===e?(0,a.jsx)(A.Ay,{icon:"triangle-exclamation",className:(0,w.cn)(t,"bg-red-600 dark:bg-red-500")}):e>=400?(0,a.jsx)(A.Ay,{icon:"circle-x",className:(0,w.cn)(t,"bg-red-600 dark:bg-red-500")}):e>=200&&e<300?(0,a.jsx)(A.Ay,{icon:"circle-check",className:(0,w.cn)(t,"bg-green-600 dark:bg-green-500")}):(0,a.jsx)(A.Ay,{icon:"circle-exclamation",className:(0,w.cn)(t,"bg-orange-600 dark:bg-orange-500")})},ec=({selectedSection:e,setSelectedSection:t})=>{let{docsConfig:r}=(0,s.useContext)(n.H6),l=r?.styling?.codeblocks;return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("select",{className:(0,w.cn)("pl-2.5 pr-6 py-1 rounded-lg text-xs font-medium border cursor-pointer focus:outline-0","system"===l?"text-gray-600 dark:text-gray-400 border-standard hover:bg-gray-50 dark:hover:bg-white/[0.03]":"text-gray-200 border-gray-800/50 hover:bg-gray-800/50 bg-gray-800/50"),value:e,onChange:e=>t(e.target.value),children:ep.map(e=>(0,a.jsx)("option",{value:e,children:(e[0]?.toUpperCase()??"")+e.slice(1)},e))}),(0,a.jsx)("div",{className:"pointer-events-none absolute inset-0 flex items-center justify-end pr-2.5",children:(0,a.jsx)(A.Ay,{icon:"angle-down",iconType:"solid",className:(0,w.cn)("w-2.5 h-2.5","system"===l?"bg-gray-400 dark:bg-gray-500":"bg-gray-200")})})]})};var eu=r(50887).Buffer;let ep=["body","headers"],em=({result:e,className:t})=>{let{docsConfig:r}=(0,s.useContext)(n.H6),[l,i]=(0,s.useState)("body"),o=ex(e),d=r?.styling?.codeblocks;return(0,a.jsx)(m.A,{children:(0,a.jsxs)("div",{className:(0,w.cn)("w-full rounded-2xl border p-0.5","system"===d?"bg-gray-50 dark:bg-white/5 border-gray-950/10 dark:border-white/10":"bg-codeblock dark:bg-white/5 border-white/10",t),children:[(0,a.jsx)(ei,{selectedSection:l,setSelectedSection:i,result:e,data:o}),(0,a.jsx)(()=>(0,a.jsx)("div",{className:(0,w.cn)("rounded-[14px] dark:bg-codeblock","system"===d?"bg-white":"bg-codeblock"),children:e.error?(0,a.jsx)(Y,{message:e.errorMessage}):"body"===l?(0,a.jsx)(X,{data:o}):(0,a.jsx)(et,{response:e.response})}),{})]})})},ex=e=>(0,s.useMemo)(()=>{if(e.error||void 0==e.response.data)return;let t=eu.from(e.response.data,"base64"),r=eh(e.response.headers),a=eg(r),s=new Blob([t],{type:r}),n=URL.createObjectURL(s),l=r&&(0,F.extension)(r)||void 0;return r&&"other"!==a?{type:a,contentType:r,blob:s,url:n,extension:l}:{type:"other",contentType:r,content:t.toString("utf-8"),blob:s,url:n,extension:l}},[e]),eh=e=>{for(let t in e)if("content-type"===t.toLowerCase()){let r=e[t];if("string"==typeof r)return r.split(";")[0]?.toLowerCase()}},eg=e=>{let[t]=e?.split("/")??[];return"image"===t||"audio"===t?t:"other"};var ey=r(38974),ef=r(92451),eb=r(74711),ej=r(49341),ev=r(93351),ek=r(13592),ew=r(40113),eN=r(27261),eC=r.n(eN),eS=r(62942),eO=r(37050);let eA=({page:e})=>{let t=(0,eS.useRouter)(),r=(0,ek.G)(),s=(0,eO.N)(e.href,r),n="asyncapi"===e.type,l="none"===e.playground||"simple"===e.playground||"webhook"===e.method;return(0,a.jsx)(eC(),{href:e.href,title:e.title,children:(0,a.jsxs)("div",{onClick:r=>{r.preventDefault(),l?t.push(e.href):t.push(`${e.href}?playground=open`)},className:(0,w.cn)("flex items-center gap-x-2 hover:bg-gray-50 dark:hover:bg-white/5 rounded-xl p-1.5 group cursor-pointer",s?"bg-gray-50 dark:bg-white/5 text-primary dark:text-primary-light font-medium":"text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,a.jsx)(S.l,{method:e.method,shortMethod:n,className:"font-medium px-1 py-px text-xs rounded-md",active:s}),(0,a.jsx)("div",{className:"text-sm truncate",children:e.title})]})})};var eE=r(20591),eq=r.n(eE),eI=r(15173),eT=r(68309),e$=r(71476);let eP=({search:e,setSearch:t})=>{let{resolvedTheme:r}=(0,eT.D)(),s=eq()(async e=>{t(e)},e$.DEBOUNCE_TIME_IN_MS);return(0,a.jsx)("div",{className:(0,w.cn)("h-[52px] sticky z-[2] -mb-1 -mx-2 pb-0 p-2 left-0 top-0","dark"===r?"fade-down-dark":"fade-down-light"),children:(0,a.jsxs)("div",{className:"p-1.5 border border-gray-200/70 dark:border-white/10 rounded-[0.6rem] bg-white dark:bg-gray-950 border-solid flex items-center gap-x-2 mb-2",children:[(0,a.jsx)(eI.A,{size:16,className:"min-w-4 flex-none text-gray-400 dark:text-gray-600"}),(0,a.jsx)("input",{type:"text",value:e,onChange:e=>s(e.target.value),autoFocus:!0,placeholder:"Search for endpoint...",className:"w-full text-sm text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-600 font-light focus:outline-none dark:bg-gray-950"})]})})},eM=e=>e.reduce((e,t)=>"pages"in t?[...e,...eM(t.pages)]:"openapi"in t||"api"in t||"asyncapi"in t?[...e,t]:e,[]),eB=({endpoint:e,channel:t})=>{let{divisions:r}=(0,s.useContext)(ev._),[{pageMetadata:n}]=(0,c.O)(),l=(0,ek.G)(),i=n.title||(0,ey.r)(l),[o,d]=(0,s.useState)(""),u=eM(r.groupsOrPages),p=(0,s.useMemo)(()=>u.map(e=>{if(e.asyncapi){if(void 0!=(e=>{let t,r,a=e.trim().split(/\s+/);if(!(a.length>2)){if(a[0]&&a[1])[t,r]=a;else{if(!a[0])return;r=a[0]}return{filename:t,channelId:r}}})(e.asyncapi))return{method:"websocket",title:e.title||(0,ey.r)(e.href),href:e.href,type:"asyncapi",playground:e.playground}}else{let t=e.openapi||e.api;if(void 0==t)return;let r=(0,ef.s)(t);if(void 0==r)return;return{method:r.method,title:e.title||(0,ey.r)(e.href),href:e.href,type:"openapi",playground:e.playground}}}).filter(e=>void 0!=e),[u]),m=e?e.title||i:t?.title||i,x=e?e.method:"websocket",h=(0,s.useMemo)(()=>o?p.filter(e=>e.title.toLowerCase().includes(o.toLowerCase())):p,[o,p]);return(0,a.jsxs)(ej.rI,{children:[(0,a.jsx)(ej.ty,{className:"hidden lg:block",id:ew.V.EndpointsMenuTrigger,children:(0,a.jsxs)("div",{className:"flex items-center gap-x-2 border-standard rounded-xl p-1.5 pr-3 min-w-80 hover:bg-gray-50 dark:hover:bg-white/5",children:[(0,a.jsx)(S.l,{method:x,shortMethod:"websocket"===x,active:!0}),(0,a.jsx)("div",{className:"flex-1 text-left text-sm font-medium text-gray-900 dark:text-white truncate",children:m}),(0,a.jsx)("div",{children:(0,a.jsx)(eb.A,{className:"w-4 h-4 text-gray-500 dark:text-gray-400"})})]})}),(0,a.jsx)(ej.SQ,{className:"hidden lg:block w-80 border border-gray-100 dark:border-white/10 rounded-xl px-2 pb-2 pt-0",children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)(eP,{search:o,setSearch:d}),h.map(e=>(0,a.jsx)("div",{children:(0,a.jsx)(eA,{page:e})},`search-${e.title}-${e.href}`))]})})]})};var eV=r(85523),eD=r(12459);let eL=({isPlaygroundExpanded:e,setIsPlaygroundExpanded:t,children:r})=>(0,a.jsx)(eV.e,{show:e,as:s.Fragment,children:(0,a.jsxs)("div",{className:(0,w.cn)(eD.f.Popup,"fixed inset-0 overflow-y-auto p-4 sm:p-6 md:p-12"),children:[(0,a.jsx)(eV._,{as:s.Fragment,enter:"ease-out duration-100",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-50",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,a.jsx)("div",{className:"fixed z-0 inset-0 bg-background-dark/20 transition-opacity backdrop-blur-sm",onClick:()=>t?.(!1)})}),(0,a.jsx)(eV._,{as:s.Fragment,enter:"ease-out duration-100",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-50",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,a.jsx)("div",{"data-testid":"api-playground-modal",className:"flex flex-col mx-auto max-w-screen-xl transform bg-background-light dark:bg-background-dark shadow-search rounded-3xl border-standard",children:(0,a.jsx)("div",{className:"p-4",children:r})})})]})});var eR=r(83897),eH=r(31581),eU=r(88426),eF=r(63213);let e_=({body:e,value:t,setValue:r})=>{let[n,l]=(0,s.useState)(0),[i,o]=(0,s.useState)(0),d=(0,s.useMemo)(()=>Object.entries(e),[e]),c=d[i]??d[0];if(void 0===c)return null;let[u,{schemaArray:p}]=c;return(0,a.jsx)(eH.X,{name:"Body",rightElement:d.length>1&&(0,a.jsx)("div",{onClick:e=>e.stopPropagation(),className:"text-xs font-mono",children:(0,a.jsx)(eU.C,{options:d.map(([e,t])=>({label:e,schema:t})),selectedIndex:i,onSelectOption:e=>{o(e)}})}),children:p.length>1?(0,a.jsx)(eR.Tabs,{defaultTabIndex:n,onClickTab:e=>l(e),className:"!mb-0",children:p.map((e,s)=>{let n=e.title??`Option ${s+1}`;return(0,a.jsx)(eR.Tab,{title:n,children:(0,a.jsx)(eF.t,{schemaArray:[e],value:t,setValue:r,clearValue:void 0===t?void 0:()=>r(void 0),isBody:!0})},n)})}):(0,a.jsx)(eF.t,{schemaArray:p,value:t,setValue:r,clearValue:void 0===t?void 0:()=>r(void 0),isBody:!0})})},eG=({name:e,parameters:t,value:r,setValue:n})=>{let l=(0,s.useMemo)(()=>Object.entries(t).map(([e,t])=>{let s=r[e],l=void 0===s?void 0:()=>{let t={...r};delete t[e],n(t)};return(0,a.jsx)(eF.t,{name:e,schemaArray:t.schema,style:t.style,explode:t.explode,value:s,setValue:t=>{let a={...r};a[e]=t,n(a)},clearValue:l},e)}),[t,n,r]);return 0===l.length?null:(0,a.jsx)(eH.X,{name:e,children:l})};var ez=r(22467);let eW=({variables:e,value:t,setValue:r})=>{let n=(0,s.useMemo)(()=>Object.entries(e).map(([e,s])=>{let n=t[e],l=void 0===n?void 0:()=>{let a={...t};delete a[e],r(a)};return(0,a.jsx)(eF.t,{name:e,schemaArray:[s],value:n,setValue:a=>{let s={...t};s[e]=String(a),r(s)},clearValue:l},e)}),[e,r,t]);return(0,a.jsx)(eH.X,{name:"Server",children:n})};function eJ({endpoint:e,inputs:t,setInputs:r}){let[{pageMetadata:n}]=(0,c.O)(),[l,i]=(0,s.useState)(0),{isSending:o,selectedBaseUrlIndex:d,isPlaygroundExpanded:u,setIsPlaygroundExpanded:p,sendRequest:m,result:h}=(0,s.useContext)(x),g=e.servers?.[d],y=(0,U._)(e),f=y[l],b=g?.variables;return(0,s.useEffect)(()=>{if(!u)return;let a=JSON.parse(JSON.stringify(t));if(b){let e={};Object.entries(b).forEach(([t,r])=>{void 0===a.server[t]&&(e[t]=String(r.default))}),a.server={...a.server,...e}}[["header",e.request.parameters.header],["path",e.request.parameters.path],["query",e.request.parameters.query],["cookie",e.request.parameters.cookie]].forEach(([e,t])=>{let r=(e=>{let t={};for(let[r,a]of Object.entries(e)){let e=R(a.schema[0]);void 0!==e&&(t[r]=e)}return t})(t);a[e]={...r,...H.a[e],...a[e]}});let s=Object.values(e.request.body)[0];if(s?.schemaArray.length){let e=R(s.schemaArray[0]),t=H.a.body;if("object"==typeof e&&null!==e)a.body=Object.assign({},e,t,"object"!=typeof a.body||null===a.body||Array.isArray(a.body)?{}:a.body);else if(Object.keys(t).length){let e="object"!=typeof a.body||null===a.body||Array.isArray(a.body)?{}:a.body;a.body={...t,...e}}else void 0===a.body&&void 0!==e&&(a.body=e)}JSON.stringify(a)!==JSON.stringify(t)&&r(a)},[u,e]),(0,a.jsxs)(eL,{isPlaygroundExpanded:u,setIsPlaygroundExpanded:p,children:[(0,a.jsxs)("div",{className:"flex items-center justify-between gap-x-2",children:[(0,a.jsx)(eB,{endpoint:e}),(0,a.jsx)(T,{endpoint:e,hasGrayBackground:!0,display:"interactive"}),(0,a.jsx)(V,{label:"Send",method:e.method,isSending:o,onClick:m,className:"w-20"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-5 gap-x-8 gap-y-6 mt-5 relative",children:[(0,a.jsxs)("div",{className:"col-span-1 lg:col-span-3",children:[(0,a.jsxs)("div",{className:"space-y-1 min-w-full w-full max-w-full overflow-hidden",children:[(0,a.jsx)("div",{className:"text-base font-medium text-gray-900 dark:text-white max-w-full",children:e.title}),(0,a.jsx)("div",{className:"flex items-start gap-x-12 justify-between !max-w-full overflow-x-auto",children:n.description&&(0,a.jsx)(D.$,{markdown:n.description,className:"text-sm text-gray-500 dark:text-gray-400"})})]}),h&&(0,a.jsx)(em,{result:h,className:"mt-6 block lg:hidden"}),(0,a.jsxs)("div",{className:"space-y-2 mt-6",children:[b&&Object.keys(b).length>0&&(0,a.jsx)(eW,{variables:b,value:t.server,setValue:e=>r({...t,server:e})}),f&&(0,a.jsx)(ez.sS,{inputs:t,setInputs:r,securityOptions:y,selectedSecurityOptionIndex:l,setSelectedSecurityOptionIndex:i}),(0,a.jsx)(eG,{name:"Header",value:t.header,setValue:e=>r({...t,header:e}),parameters:e.request.parameters.header}),(0,a.jsx)(eG,{name:"Cookie",value:t.cookie,setValue:e=>r({...t,cookie:e}),parameters:e.request.parameters.cookie}),(0,a.jsx)(eG,{name:"Path",value:t.path,setValue:e=>r({...t,path:e}),parameters:e.request.parameters.path}),(0,a.jsx)(eG,{name:"Query",value:t.query,setValue:e=>r({...t,query:e}),parameters:e.request.parameters.query}),(0,a.jsx)(e_,{value:t.body,setValue:e=>r({...t,body:e}),body:e.request.body})]})]}),(0,a.jsxs)("div",{className:(0,w.cn)("p-px col-span-1 lg:col-span-2 lg:space-y-6 lg:flex lg:sticky lg:top-0 lg:self-start lg:h-[calc(100vh-5.5rem)]",h&&"lg:flex-col overflow-y-auto lg:-top-12 lg:h-screen"),children:[h&&(0,a.jsx)(em,{result:h,className:"hidden lg:block min-h-fit"}),(0,a.jsx)(L.A,{"data-testid":"api-playground-request-example",className:"xl:w-full"})]})]})]})}let eK=({endpoint:e,inputs:t,setInputs:r,display:l})=>{let{selectedBaseUrlIndex:i,setIsPlaygroundExpanded:o}=(0,s.useContext)(x),d=e.servers?.[i],c=function(e){let[t,r]=(0,s.useState)(0),a=(0,k.j1)(),l=(e=>{let{docsConfig:t}=(0,s.useContext)(n.H6);return(0,s.useMemo)(()=>e.length>0?e:((e,t)=>{switch(e){case"basic":return[{title:"Basic Auth",parameters:{header:{Authorization:{type:"http",scheme:"basic",description:"Basic authentication header of the form `Basic <encoded-value>`, where `<encoded-value>` is the base64-encoded string `username:password`."}},query:{},cookie:{}}}];case"bearer":return[{title:"Bearer Auth",parameters:{header:{Authorization:{type:"http",scheme:"bearer",description:"Bearer authentication header of the form `Bearer <token>`, where `<token>` is your auth token."}},query:{},cookie:{}}}];case"cobo":return[{title:"Cobo Auth",parameters:{header:{"API-SECRET":{type:"apiKey"}},query:{},cookie:{}}}];case"key":return[{title:"API Key Auth",parameters:{header:{Key:{type:"apiKey"}},query:{},cookie:{}}}]}return[]})(t?.api?.mdx?.auth?.method),[e,t?.api?.mdx?.auth])})(e),i=a?l:e;return(0,s.useMemo)(()=>i.filter(e=>Object.values(e.parameters).some(e=>Object.keys(e).length>0)),[i])[t]}(e.request.security),{userInfo:u}=(0,s.useContext)(n.cy);return!function({inputs:e,setInputs:t,baseUrl:r,securityOption:a,userInfoInputs:n}){let[l,i]=(0,s.useState)(),[o,d]=(0,s.useState)();(0,s.useEffect)(()=>{i(y()),d((0,h.pC)())},[]),(0,s.useEffect)(()=>{let s=b(a,o,n),i=v(r,l,n);t({...s,server:i,path:e.path,body:e.body})},[l,o,n]),(0,s.useEffect)(()=>{let r=b(a,o,n);t({...e,...r})},[a]),(0,s.useEffect)(()=>{a&&(0,h.Ep)(a,{query:e.query,header:e.header,cookie:e.cookie},n)},[e.cookie,e.header,e.query]),(0,s.useEffect)(()=>{let a=v(r,l,n);t({...e,server:a})},[r]),(0,s.useEffect)(()=>{if(r){let t=Object.fromEntries(Object.entries(e.server).filter(([e,t])=>t&&n?.server?.[e]!==t)),a=JSON.stringify({...y(),[r]:t});localStorage.setItem(g,a)}},[e.server])}({inputs:t,setInputs:r,baseUrl:d?.url,securityOption:c,userInfoInputs:u?.apiPlaygroundInputs}),(0,a.jsx)(m.A,{children:(0,a.jsxs)("div",{className:(0,w.cn)("flex w-full flex-col bg-background-light dark:bg-background-dark border-standard rounded-2xl","simple"===l?"p-1":"p-1.5"),children:[(0,a.jsx)(q,{endpoint:e,display:l,onClickInteractive:()=>o?.(!0)}),(0,a.jsx)(eJ,{endpoint:e,inputs:t,setInputs:r})]})})},eZ=({endpoint:e,display:t})=>{let{docsConfig:r}=(0,s.useContext)(n.H6),h=(0,d.A)(),g=(()=>{let[,e]=(0,u.A)();return(0,s.useCallback)(t=>e({type:"set_api_playground_inputs",payload:t}),[e])})(),y=(()=>{let[,e]=(0,c.O)();return(0,s.useCallback)(t=>e({type:"set_api_base_index",payload:t}),[e])})(),f=(0,i.u)(),[b,j]=(0,s.useState)(0),[v,k]=(0,s.useState)(!1),[w,N]=(0,s.useState)(!1),[C,S]=(0,s.useState)(),[O,A]=(0,s.useState)(0),E=Object.keys(e.request.body)[O],q=f?.[b]??f?.[0]??"";(0,s.useEffect)(()=>{new URLSearchParams(window.location.search).has("playground")&&requestAnimationFrame(()=>{k(!0)})},[]);let I=(0,p.CA)({endpoint:e,baseUrl:q,contentType:E??"application/json",setIsFetching:N,setResult:S}),T=e=>{if(k(e),e){let e=new URL(window.location.href);e.searchParams.set("playground","open"),window.history.replaceState({},"",e)}else{let e=new URL(window.location.href);e.searchParams.delete("playground"),window.history.replaceState({},"",e)}};return(0,o.f)({key:"Enter",callback:r?.api?.playground?.display==="interactive"?I:void 0}),(0,o.f)({key:"Escape",callback:v?()=>T(!1):void 0,isSingleKey:!0}),(0,a.jsx)(m.A,{children:(0,a.jsx)(x.Provider,{value:{isSending:w,sendRequest:I,result:C,setSelectedBaseUrlIndex:e=>{j(e),y(e)},selectedBaseUrlIndex:b,baseUrlOptions:f,isPlaygroundExpanded:v,setIsPlaygroundExpanded:T},children:(0,a.jsx)("div",{className:"mt-6 flex w-full flex-col space-y-4",children:(0,a.jsx)(l.oW,{location:"request",children:(0,a.jsx)(eK,{inputs:h,setInputs:g,endpoint:e,display:t})})})})})},eQ=(0,s.createContext)({result:[],setResult:()=>{},address:"",setAddress:()=>{},clearResult:()=>{},onMessage:()=>{},parameterValues:{},setParameterValues:()=>{},handleParameterChange:()=>{},handleSecuritySchemeChange:()=>{},securitySchemeValues:{}}),eX=({children:e})=>{let[t,r]=(0,s.useState)([]),[n,l]=(0,s.useState)(""),[i,o]=(0,s.useState)({}),[d,c]=(0,s.useState)({});return(0,a.jsx)(eQ.Provider,{value:{result:t,setResult:r,address:n,setAddress:l,clearResult:()=>{r([])},onMessage:e=>{r(t=>[...t,e])},parameterValues:i,setParameterValues:o,handleParameterChange:(e,t)=>{o({...i,[e]:t})},handleSecuritySchemeChange:(e,t,r)=>{c({...d,[e]:{name:r,value:t}})},securitySchemeValues:d},children:e})};var eY=r(74840);let e0=({label:e,method:t,onClick:r,isSending:s,className:n,icon:l,disabled:i})=>{let{activeNavPillBg:o,activeNavPillText:d}=(0,C.H)(t);return(0,a.jsx)("button",{className:(0,w.cn)("flex items-center justify-center px-3 h-9 font-medium rounded-xl mouse-pointer hover:opacity-80 gap-1.5",o,d,n,(i||s)&&"opacity-50 hover:opacity-50 cursor-not-allowed pointer-events-none"),onClick:r,disabled:s||i,children:s?(0,a.jsx)(A.Ay,{icon:"spinner-third",iconType:"duotone",className:"w-4 h-4 bg-white animate-spin"}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{children:e}),l||(0,a.jsx)(A.Ay,{icon:"play",iconType:"solid",className:"w-3 h-3 bg-white"})]})})},e1=({name:e,children:t,rightElement:r,className:n,defaultExpanded:l=!0})=>{let[i,o]=(0,s.useState)(l);return(0,a.jsxs)("div",{className:(0,w.cn)(n,"rounded-2xl border border-zinc-200 dark:border-zinc-800 border-solid",i&&"pb-1"),children:[(0,a.jsxs)("button",{"aria-label":"expand input section",onClick:()=>o(e=>!e),className:(0,w.cn)("flex w-full pt-3.5 pb-3 px-3.5 items-center justify-between cursor-pointer rounded-t-2xl hover:bg-gray-50 dark:hover:bg-white/5 ",i?"rounded-t-2xl":"rounded-2xl"),children:[(0,a.jsxs)("div",{className:"flex items-center gap-x-1.5",children:[(0,a.jsx)(A.Ay,{icon:"angle-right",iconType:"solid",className:(0,w.cn)("h-3 w-3 bg-gray-400 dark:bg-gray-500 transition-transform",i&&"rotate-90")}),(0,a.jsx)("div",{className:"flex-1 flex items-center space-x-2",children:(0,a.jsx)("div",{className:"text-sm font-medium text-gray-800 dark:text-gray-200 leading-6",children:e})})]}),r]}),(0,a.jsxs)("div",{className:(0,w.cn)("flex-1 rounded-b-xl w-full",i&&t?"":"hidden"),children:[(0,a.jsx)("div",{className:"h-px mx-3.5 bg-[#f4f4f5] dark:bg-[#18181b]"}),(0,a.jsx)("div",{className:"mx-3.5",children:t})]})]})};var e5=r(82026),e2=r(31972);let e6=({children:e,label:t,typeLabel:r,required:n,deprecated:l,propertiesCount:i,defaultShowChildren:o,description:d,className:c,topLevel:u})=>{let[p,m]=(0,s.useState)(0===i||(o??i<=2));return(0,a.jsx)("div",{className:(0,w.cn)("expand-schema-container flex space-x-3 items-start",u?"":"px-3.5",c),children:(0,a.jsxs)("div",{className:(0,w.cn)("flex flex-col flex-1 grid gap-x-12 gap-y-0 rounded-xl",p?"mt-3.5":"my-3.5",!d&&"my-3.5"),children:[(0,a.jsx)("div",{className:(0,w.cn)(!p&&"border-none"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between font-mono font-bold",children:[(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-2 text-xs truncate",title:t,children:[(0,a.jsx)("div",{className:"text-sm truncate",children:(0,a.jsx)("span",{className:"font-semibold text-gray-900 dark:text-gray-100",children:t})}),r&&(0,a.jsx)(e5.ks,{prefix:"type:",children:r}),n&&(0,a.jsx)(e5.rG,{}),l&&(0,a.jsx)(e5.n,{})]}),(0,a.jsx)(e2.y,{onClick:()=>m(!p),count:i,labeled:!0})]})}),d&&(0,a.jsx)("div",{className:"py-2",children:(0,a.jsx)(D.V,{markdown:d,className:"text-gray-500 dark:text-gray-400"})}),p&&(0,a.jsx)("div",{className:"grid grid-cols-1 w-full items-start divide-y divide-gray-100 dark:divide-white/5",children:e})]})})},e4=({schema:e,defaultShowChildren:t,className:r,topLevel:s=!0})=>(0,a.jsx)(a.Fragment,{children:e.map(({name:e,type:n,description:l,properties:i,required:o,deprecated:d})=>(0,a.jsx)(e6,{label:e,description:l,typeLabel:n,propertiesCount:i?.length??0,defaultShowChildren:t,required:o,deprecated:d,className:r,topLevel:s,children:(e=>!!e&&("object"===e||"oneOf"===e||"anyOf"===e||"allOf"===e))(n)&&i&&(0,a.jsx)(e4,{schema:i,defaultShowChildren:t,topLevel:!1})},e))}),e3=({bindings:e})=>(0,a.jsx)(e1,{name:"Bindings",className:"bindings-container",children:e.map(e=>(0,a.jsx)(e4,{schema:e.schemaProperties,className:"bindings-schema"},e.protocol))}),e7=({baseUrlOptions:e,selectedBaseUrlIndex:t=0,setSelectedBaseUrlIndex:r,onBaseUrlChange:n})=>{let[l,i]=(0,s.useState)(0),o=e?.[t];return((0,s.useEffect)(()=>{let e=document.createElement("span");e.className="text-sm absolute font-mono invisible",e.textContent=o??"",document.body.appendChild(e),i(e.offsetWidth+16),document.body.removeChild(e)},[o]),e&&0!==e.length&&r)?1===e.length?(0,a.jsx)("div",{className:"min-w-max bg-transparent text-sm text-gray-600 dark:text-gray-400 outline-none font-mono cursor-pointer hover:text-gray-900 dark:hover:text-gray-300",children:e[0]}):(0,a.jsxs)("div",{className:"flex items-center gap-1 relative -mr-1",children:[(0,a.jsx)("select",{className:"bg-transparent text-sm text-gray-600 dark:text-gray-400 outline-none font-mono cursor-pointer hover:text-gray-900 dark:hover:text-gray-300",value:t,onClick:e=>e.stopPropagation(),onChange:e=>{r(Number(e.target.value)),n?.()},style:{width:`${l}px`},children:e.map((e,t)=>(0,a.jsx)("option",{value:t,children:e},e))}),(0,a.jsx)(N.A,{className:"absolute right-0 w-3 h-3 text-gray-400 dark:text-white/30 pointer-events-none",strokeWidth:2.5})]}):null},e8=({path:e,method:t,isCopiedActive:r,hasGrayBackground:n})=>{let l=(0,s.useMemo)(()=>e.split("/").flatMap((e,r)=>{let s=[];return r>0&&e&&s.push((0,a.jsx)(tt,{},r)),e.split("{").forEach((e,n)=>{if(e.includes("}")){let[l,i]=e.split("}");s.push((0,a.jsx)(te,{method:t,children:l},`${r}-${n}-param`)),i&&s.push((0,a.jsx)(e9,{children:i},`${r}-${n}`))}else e&&s.push((0,a.jsx)(e9,{children:e},`${r}-${n}`))}),s}),[e,t]);return(0,a.jsxs)("div",{className:"group flex items-center flex-1 gap-0.5 font-mono",children:[(0,a.jsx)("div",{className:(0,w.cn)("absolute right-0 p-2 bg-background-light dark:bg-background-dark rounded-lg",n&&"bg-transparent dark:bg-transparent",r?"block":"hidden group-hover:block"),children:r?(0,a.jsx)(A.Ay,{icon:"circle-check",iconType:"solid",className:"w-4 h-4 bg-primary dark:bg-primary-light"}):(0,a.jsx)(A.Ay,{icon:"clone",className:"w-4 h-4 bg-gray-400 dark:bg-white/30"})}),l]})},e9=({children:e})=>(0,a.jsx)("div",{className:"text-sm font-medium text-gray-800 dark:text-white min-w-max",children:e}),te=({children:e,method:t})=>{let{paramStyle:r}=(0,C.H)(t);return(0,a.jsx)("div",{className:(0,w.cn)("text-sm font-mono font-medium rounded-md px-1 border-2 min-w-max",r),children:`{${e}}`})},tt=()=>(0,a.jsx)("div",{className:"text-sm text-gray-400",children:"/"}),tr=({onCopy:e,baseUrlOptions:t,selectedBaseUrlIndex:r,setSelectedBaseUrlIndex:s,isCopiedActive:n,hasGrayBackground:l,display:i,path:o,method:d,onBaseUrlChange:c})=>(0,a.jsxs)("div",{className:(0,w.cn)("relative flex-1 flex gap-2 min-w-0 rounded-xl items-center cursor-pointer p-1.5","simple"!=i&&"border-standard",l&&"bg-gray-50 dark:bg-white/5"),onClick:e,children:[(0,a.jsx)(S.l,{method:d,shortMethod:!0,active:!1}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 overflow-x-auto flex-1 no-scrollbar pr-8",children:[(0,a.jsx)(e7,{baseUrlOptions:t,selectedBaseUrlIndex:r,setSelectedBaseUrlIndex:s,onBaseUrlChange:c}),(0,a.jsx)(e8,{path:o,method:d,isCopiedActive:n,hasGrayBackground:l})]})]});var ta=r(11375),ts=r(60710);let tn=({operations:e,type:t})=>{let r="send"===t?(0,a.jsx)(ts.t9,{}):(0,a.jsx)(ts.Cc,{}),s=e.flatMap(e=>e.messages);return(0,a.jsx)(e1,{name:"send"===t?"Send":"Receive",rightElement:r,children:s.map(e=>e.payload?(0,a.jsx)(e4,{schema:e.payload,defaultShowChildren:!1},e.id):null)})},tl=({parameters:e})=>(0,a.jsx)(e1,{name:"Parameters",children:e.map(e=>(0,a.jsx)(e4,{schema:[{name:e.id,type:e.type,description:e.description,required:e.required,deprecated:e.deprecated}],className:"bindings-schema"},e.id))}),ti=({address:e,baseUrl:t,parameterValues:r,securitySchemeValues:a})=>{let s=Object.entries(r).reduce((e,[t,r])=>null==r?e:e.replace(`{${t}}`,encodeURIComponent(String(r))),e),n="",l=a.httpApiKey?.name,i=a.httpApiKey?.value;return l&&i&&(n=`?${l}=${i}`),n&&(s=`${s}${n}`),(0,p.s4)(t,s)},to=({connecting:e,isConnected:t,onClick:r})=>{let s=e?(0,a.jsx)(A.Ay,{icon:"spinner-third",iconType:"duotone",className:"w-4 h-4 bg-white animate-spin"}):t?(0,a.jsx)(ts.W1,{}):(0,a.jsx)(ts.oD,{});return(0,a.jsxs)("button",{className:"flex items-center justify-center px-3 h-9 text-white font-medium rounded-xl mouse-pointer disabled:opacity-70 hover:opacity-80 gap-1.5 bg-[#000000] dark:bg-[#52525c]",onClick:r,disabled:e,children:[(0,a.jsx)("span",{children:t?"Disconnect":"Connect"}),s]})},td=({title:e,description:t})=>(0,a.jsxs)("div",{className:"space-y-1 min-w-full w-full max-w-full overflow-hidden",children:[(0,a.jsx)("div",{className:"text-base font-medium text-gray-900 dark:text-white max-w-full",children:e}),(0,a.jsx)("div",{className:"flex items-start gap-x-12 justify-between !max-w-full overflow-x-auto",children:t&&(0,a.jsx)(D.$,{markdown:t,className:"text-sm text-gray-500 dark:text-gray-400"})})]}),tc=({name:e,parameters:t,parameterValues:r,onChange:s})=>t&&0!==t.length?(0,a.jsx)(e1,{name:e,children:t.map(e=>{let t={...e.jsonSchema,deprecated:e.deprecated,required:e.required};return(0,a.jsx)(eF.t,{name:e.id,schemaArray:[t],value:r[e.id],setValue:t=>s(e.id,t),clearValue:()=>s(e.id,void 0)},e.id)})}):null,tu=({name:e,securitySchemes:t,securitySchemeValues:r,handleSecuritySchemeChange:s})=>(0,a.jsx)(e1,{name:e,children:t.map(e=>{let t={id:e.id,type:"string",title:e.name,description:e.description,required:!0};return(0,a.jsx)(eF.t,{name:e.name,schemaArray:[t],value:r[e.type]?.value||"",setValue:t=>s(e.type,t,e.name??""),clearValue:()=>s(e.type,"",e.name??"")},e.id)})}),tp=({isConnected:e})=>(0,a.jsxs)("div",{className:(0,w.cn)("flex items-center justify-center px-2 h-6 text-white font-medium rounded-md mouse-pointer disabled:opacity-70 gap-1.5",e?"bg-green-500 dark:bg-[#084608]":"bg-red-100 dark:bg-[#460809]"),children:[(0,a.jsx)(A.Ay,{icon:e?"circle-check":"circle-xmark",iconType:"solid",className:(0,w.cn)("w-3 h-3",e?"bg-white dark:bg-[#2cfb2c]":"bg-red-600 dark:bg-[#fb2c36]")}),(0,a.jsx)("div",{className:(0,w.cn)("font-mono text-sm font-bold uppercase tracking-tighter",e?"text-white dark:text-[#2cfb2c]":"text-red-600 dark:text-[#fb2c36]"),children:e?"Connected":"Not Connected"})]});var tm=r(32441);let tx=({ariaLabel:e,onClick:t})=>(0,a.jsx)("button",{"aria-label":e,className:"flex items-center justify-center h-6 w-6 rounded-lg mouse-pointer hover:opacity-80 gap-1.5 border border-standard",onClick:t,children:(0,a.jsx)(A.Ay,{icon:"ban",iconType:"solid",className:"w-2.5 h-2.5 bg-gray-400 dark:bg-gray-500 rotate-90"})}),th=({name:e,className:t,messages:r,isConnected:s,clearResult:n})=>{if(!r)return null;let l=r.map((e,t)=>{let r=e.timestamp?`${e.timestamp}`:`message-${t}`,a=JSON.stringify(e.data,null,2);return{id:r,type:e.type,title:a,content:a}});return(0,a.jsx)(tm.X,{name:e,messages:l,className:t,icon:(0,a.jsx)(ts.bM,{}),rightElement:(0,a.jsxs)("div",{className:"flex items-center gap-x-2",children:[(0,a.jsx)(tp,{isConnected:s}),l.length>0&&(0,a.jsx)(tx,{ariaLabel:"Clear messages",onClick:n})]})})},tg=({message:e,sendMessage:t,isConnected:r})=>{let[n,l]=(0,s.useState)({}),i=`${e.title}`;if((0,o.f)({key:"Enter",callback:()=>t({...n}),isSingleKey:!0}),!e.jsonPayloadSchema||!e.jsonPayloadSchema.properties)return null;let d={...e.jsonPayloadSchema,requiredProperties:e.jsonPayloadSchema.required};return(0,a.jsx)("div",{className:"flex flex-col gap-y-2 items-end",children:(0,a.jsxs)(e1,{name:i,className:"w-full",defaultExpanded:!1,children:[(0,a.jsx)(eF.t,{schemaArray:[d],value:n,setValue:e=>l(e),clearValue:()=>l({}),defaultExpanded:!1},e.id),(0,a.jsx)("div",{className:"flex items-center justify-end gap-x-2 p-3",children:(0,a.jsx)(e0,{label:"Send message",method:"websocket",onClick:()=>r&&t({...n}),className:"bg-[#000000] dark:bg-[#52525c]",disabled:!r})})]},e.title)})},ty=({sendOperations:e,sendMessage:t,isConnected:r})=>{let s=e.flatMap(({messages:e})=>e);return(0,a.jsx)("div",{className:"space-y-2 ",children:s.map(e=>(0,a.jsx)(tg,{message:e,sendMessage:t,isConnected:r},e.id))})},tf=({channel:e,isPlaygroundExpanded:t,setIsPlaygroundExpanded:r,baseUrlOptions:n,initialSelectedBaseUrlIndex:l})=>{let[i,d]=(0,s.useState)(l),[c,u]=(0,s.useState)(!1),{address:p,setAddress:m,onMessage:x,parameterValues:h,handleParameterChange:g,result:y,clearResult:f,securitySchemeValues:b,handleSecuritySchemeChange:j}=(0,s.useContext)(eQ),{connect:v,disconnect:k,connecting:N,isConnected:C,sendMessage:S}=(({address:e,onMessage:t})=>{let r=(0,s.useRef)(null),[a,n]=(0,s.useState)(""),[l,i]=(0,s.useState)(!1),[o,d]=(0,s.useState)(!1);(0,s.useEffect)(()=>()=>{r.current&&(r.current.close(),r.current=null)},[]);let c=(0,s.useCallback)(()=>{try{r.current&&r.current.close();let a=new WebSocket(e);r.current=a,d(!0),a.onopen=()=>{i(!0),n(""),d(!1)},a.onclose=()=>{i(!1),n("")},a.onerror=e=>{n("WebSocket connection error"),i(!1),d(!1),console.error("WebSocket error:",e)},a.onmessage=e=>{if(t)try{let r=JSON.parse(e.data);t({type:"receive",timestamp:Date.now(),data:r})}catch(r){t({type:"receive",timestamp:Date.now(),data:e.data})}}}catch(e){n(e instanceof Error?e.message:"Failed to connect to WebSocket"),i(!1),d(!1)}},[e,t,i]);return{connect:c,disconnect:(0,s.useCallback)(()=>{d(!0),r.current&&(r.current.onclose=()=>{d(!1),i(!1)},r.current.close(),r.current=null)},[]),sendMessage:(0,s.useCallback)(e=>{if(!r.current||r.current.readyState!==WebSocket.OPEN)return void n("WebSocket is not connected");try{let a="string"==typeof e?e:JSON.stringify(e);r.current.send(a),t&&t({type:"send",timestamp:Date.now(),data:e})}catch(e){n(e instanceof Error?e.message:"Failed to send message")}},[t]),error:a,connecting:o,setIsConnecting:d,isConnected:l,setIsConnected:i}})({address:p,onMessage:x});(0,s.useEffect)(()=>{m(ti({address:e.address??"",baseUrl:n[i]??n[0]??"",parameterValues:h,securitySchemeValues:b}))},[i,n,e,m,h,b]);let O=async()=>{let t=ti({address:e.address??"",baseUrl:n[i]??n[0]??"",parameterValues:h,securitySchemeValues:b});"success"===await (0,E.l)(t)&&(u(!0),setTimeout(()=>{u(!1)},2e3))};return(0,o.f)({key:"Escape",callback:t?()=>void(C&&k(),r(!1)):void 0,isSingleKey:!0}),(0,a.jsxs)(eL,{isPlaygroundExpanded:t,setIsPlaygroundExpanded:r,children:[(0,a.jsxs)("div",{className:"flex items-center justify-between gap-x-2",children:[(0,a.jsx)(eB,{channel:e}),(0,a.jsx)(tr,{onCopy:O,baseUrlOptions:n,selectedBaseUrlIndex:i,setSelectedBaseUrlIndex:d,onBaseUrlChange:C?k:()=>{},isCopiedActive:c,hasGrayBackground:!0,display:"interactive",path:e.address??"",method:"websocket"}),(0,a.jsx)(to,{connecting:N,isConnected:C,onClick:C?k:v})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-5 gap-x-8 gap-y-6 mt-5 relative",children:[(0,a.jsxs)("div",{className:"col-span-1 lg:col-span-3",children:[(0,a.jsx)(td,{title:e.title,description:e.description}),(0,a.jsxs)("div",{className:"space-y-2 mt-6",children:[e.securitySchemes.length>0&&(0,a.jsx)(tu,{name:"Security Schemes",securitySchemes:e.securitySchemes,securitySchemeValues:b,handleSecuritySchemeChange:j}),e.parameters.length>0&&(0,a.jsx)(tc,{name:"Path Parameters",parameters:e.parameters,parameterValues:h,onChange:g}),e.sendOperations.length>0&&(0,a.jsx)(ty,{sendOperations:e.sendOperations,sendMessage:S,isConnected:C})]})]}),(0,a.jsx)("div",{className:(0,w.cn)("p-px col-span-1 lg:col-span-2 lg:space-y-6 lg:flex lg:sticky lg:top-0 lg:self-start lg:h-[calc(100vh-5.5rem)]",y.length>0&&"lg:flex-col overflow-y-auto lg:-top-12 lg:h-screen"),children:(0,a.jsx)(th,{name:"Messages",messages:y,isConnected:C,clearResult:f})})]})]})},tb=({securitySchemes:e})=>(0,a.jsx)(e1,{name:"Security Schemes",children:e.map(e=>(0,a.jsx)(e6,{label:e.name,typeLabel:e.type,propertiesCount:0,defaultShowChildren:!1,required:!1,deprecated:!1,description:e.description,topLevel:!0},e.id))}),tj=({channel:e})=>{let t=(0,eY.D)(),[r,n]=(0,s.useState)(0),[l,i]=(0,s.useState)(!1),[o,d]=(0,s.useState)(!1),c=["httpApiKey"],u=e.securitySchemes.every(e=>c.includes(e.type)),m="simple"===t||!u;(0,s.useEffect)(()=>{new URLSearchParams(window.location.search).has("playground")&&requestAnimationFrame(()=>{i(!0)})},[]);let h=e.servers.map(e=>{let t=e.protocol,r=e.host;return`${t}://${r}`}),g=async()=>{let t=h[r]??h[0]??"",a=e.address??"",s=(0,p.s4)(t,a);"success"===await (0,E.l)(s)&&(d(!0),setTimeout(()=>{d(!1)},2e3))};return(0,a.jsxs)(x.Provider,{value:{isSending:!1,setSelectedBaseUrlIndex:n,selectedBaseUrlIndex:r,baseUrlOptions:h,isPlaygroundExpanded:l,setIsPlaygroundExpanded:e=>{if(i(e),e){let e=new URL(window.location.href);e.searchParams.set("playground","open"),window.history.replaceState({},"",e)}else{let e=new URL(window.location.href);e.searchParams.delete("playground"),window.history.replaceState({},"",e)}}},children:[(0,a.jsx)("div",{className:"flex flex-col gap-8",children:(0,a.jsxs)("div",{className:"mt-6 flex w-full flex-col space-y-4",children:[(0,a.jsx)("div",{className:"flex w-full flex-col bg-background-light dark:bg-background-dark border-standard rounded-2xl p-1.5",children:(0,a.jsxs)("div",{className:"flex items-center space-x-1.5",children:[(0,a.jsx)(tr,{onCopy:g,baseUrlOptions:h,selectedBaseUrlIndex:r,setSelectedBaseUrlIndex:n,isCopiedActive:o,hasGrayBackground:!1,display:t,path:e.address??"",method:"websocket"}),m?null:(0,a.jsx)(e0,{label:"Connect",method:"websocket",onClick:()=>i(!0),className:"bg-[#000000] dark:bg-[#52525c]",isSending:!1})]})}),(0,a.jsx)("div",{className:"mt-8 lg:mt-0 flex flex-col gap-6 xl:hidden",children:(0,a.jsx)(ta.z,{name:"Messages",channel:e})}),(0,a.jsxs)("div",{className:"flex flex-col gap-6",children:[e.parameters.length>0&&(0,a.jsx)(tl,{parameters:e.parameters}),e.securitySchemes.length>0&&(0,a.jsx)(tb,{securitySchemes:e.securitySchemes}),e.bindings.length>0&&(0,a.jsx)(e3,{bindings:e.bindings}),e.sendOperations.length>0&&(0,a.jsx)(tn,{operations:e.sendOperations,type:"send"}),e.receiveOperations.length>0&&(0,a.jsx)(tn,{operations:e.receiveOperations,type:"receive"})]})]})}),!m&&(0,a.jsx)(eX,{children:(0,a.jsx)(tf,{channel:e,isPlaygroundExpanded:l,setIsPlaygroundExpanded:i,baseUrlOptions:h,initialSelectedBaseUrlIndex:r})})]})};var tv=r(38987),tk=r(26976),tw=r(51657);let tN=()=>{let e=(0,eY.D)(),t=(0,tw.u)("request"),r=(0,tw.u)("response"),s=(0,k.Zk)(),n=(0,tk.B)(),l="none"!==e&&s,i=(0,tv.qO)(tv.x4);return n?(0,a.jsx)(tj,{channel:n}):(0,a.jsx)(tv.Qo.Provider,{value:i,children:(0,a.jsxs)("div",{className:"flex flex-col gap-8",children:[l&&(0,a.jsx)(eZ,{endpoint:s,display:e}),(s||t||r)&&(0,a.jsx)("div",{className:"mt-8 lg:mt-0 flex flex-col gap-6 xl:hidden max-h-[30rem]",children:(0,a.jsx)(L.A,{})})]})})}},38987:(e,t,r)=>{"use strict";r.d(t,{Qo:()=>i,qO:()=>l,x4:()=>s});var a=r(7620);let s={server:{},path:{},query:{},header:{},cookie:{},body:void 0},n=(e,t)=>({...e,...t.payload,server:Object.keys(t.payload.server).length?{...e.server,...t.payload.server}:{},path:Object.keys(t.payload.path).length?{...e.path,...t.payload.path}:{},query:Object.keys(t.payload.query).length?{...e.query,...t.payload.query}:{},header:Object.keys(t.payload.header).length?{...e.header,...t.payload.header}:{},cookie:Object.keys(t.payload.cookie).length?{...e.cookie,...t.payload.cookie}:{},body:t.payload.body?"object"!=typeof t.payload.body||Array.isArray(t.payload.body)?t.payload.body:{...e.body||{},...t.payload.body}:""===t.payload.body?void 0:e.body}),l=e=>(0,a.useReducer)(n,{...s,...e}),i=(0,a.createContext)([s,()=>null]);i.displayName="ApiPlaygroundInputsContext"},45744:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(7620);class s extends a.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(){return{hasError:!0}}componentDidCatch(e,t){console.log({error:e,errorInfo:t})}render(){return this.state.hasError?null:this.props.children}}let n=s},47006:(e,t,r)=>{"use strict";r.d(t,{A:()=>I});var a=r(27261),s=r.n(a),n=r(55595),l=r(57812),i=r(7421),o=r(87381),d=r(99345),c=r(50816),u=r(47457),p=r(49580),m=r(17809),x=r(9506),h=r(7949),g=r(84620),y=r(55849),f=r(73254),b=r(6578),j=r(54568),v=r(40113),k=r(82026),w=r(36401),N=r(50443),C=r(79222),S=r(83897),O=r(25575),A=r(42649),E=r(28346),q=r(19043);let I={Accordion:n.Accordion,AccordionGroup:l.AccordionGroup,Heading:g.Heading,Card:o.Card,CardGroup:d.CardGroup,Columns:d.Columns,Check:i.Check,CodeGroup:u.CodeGroup,CodeBlock:c.CodeBlock,CustomCode:q.DynamicCustomCode,CustomComponent:p.default,DynamicCustomComponent:m.default,Expandable:x.Expandable,Frame:h.Frame,Info:i.Info,Icon:y.Icon,Link:s(),Note:i.Note,ParamField:k.ParamField,RequestExample:C.RequestExample,ResponseExample:C.ResponseExample,ResponseField:w.ResponseField,Step:N.Step,Steps:N.Steps,Tab:S.Tab,Tabs:S.Tabs,Tip:i.Tip,Tooltip:O.Tooltip,SnippetGroup:u.SnippetGroup,Warning:i.Warning,Latex:f.Latex,ZoomImage:E.ZoomImage,Mermaid:b.DynamicMermaid,Update:A.Update,Panel:function({children:e}){return(0,j.jsx)("div",{className:"block xl:hidden",id:v.V.Panel,children:e})},Danger:i.Danger}},51657:(e,t,r)=>{"use strict";r.d(t,{i:()=>n,u:()=>s});var a=r(19637);let s=e=>{let[{codeExamples:t}]=(0,a.O)();return t[e]},n=()=>{let[{codeExamples:e}]=(0,a.O)();return!!e.request||!!e.response}},56717:(e,t,r)=>{"use strict";r.d(t,{b:()=>h});var a=r(54568),s=r(89874),n=r(14820),l=r(7620),i=r.t(l,2),o=r(47006),d=r(29461),c=r(84620),u=r(28346),p=r(16600);let m="rounded",x={img:e=>(0,a.jsx)(u.ZoomImage,{...e,children:(0,a.jsx)("img",{...e,className:(0,p.cn)(m,e.className)})}),picture:e=>(0,a.jsx)(u.ZoomImage,{...e,children:(0,a.jsx)("picture",{className:(0,p.cn)(m,e.className)})}),figure:e=>(0,a.jsx)(u.ZoomImage,{...e,children:(0,a.jsx)("figure",{...e,className:(0,p.cn)(m,e.className)})}),p:e=>(0,a.jsx)("span",{"data-as":"p",...e})},h=({mdxSource:e,mdxSourceWithNoJs:t,pageMetadata:r,userInfo:u})=>{let[p,m]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{m(!0)},[]),(0,a.jsx)(s.x,{components:{a:d.O,Heading:c.Heading,...x},children:(0,a.jsx)(n.Lo,{components:o.A,compiledSource:p?e.compiledSource:t.compiledSource,frontmatter:e.frontmatter,scope:{pageMetadata:r,user:u?.content??{},...p?{React:i,useState:l.useState,useEffect:l.useEffect,useRef:l.useRef,useCallback:l.useCallback,useMemo:l.useMemo,useReducer:l.useReducer,useContext:l.useContext,useLayoutEffect:l.useLayoutEffect,useImperativeHandle:l.useImperativeHandle,useDebugValue:l.useDebugValue,useDeferredValue:l.useDeferredValue,useTransition:l.useTransition,useId:l.useId,useSyncExternalStore:l.useSyncExternalStore,useInsertionEffect:l.useInsertionEffect}:{}}})})}},59722:(e,t,r)=>{"use strict";r.d(t,{T:()=>l,p:()=>n});var a=r(7620),s=r(71197);let n=()=>{let{apiReferenceData:e}=(0,a.useContext)(s.fq);return e.schemaData?.schemaArray},l=()=>!!n()},62837:(e,t,r)=>{"use strict";r.d(t,{u:()=>l});var a=r(7620),s=r(71197),n=r(3256);let l=()=>{let e=(0,n.Zk)(),{docsConfig:t}=(0,a.useContext)(s.H6);return(0,a.useMemo)(()=>{let r=e?.servers?.map(({url:e})=>e);if(r&&r.length>0)return r;let a=t?.api?.mdx?.server;return"string"==typeof a?[a]:a},[e?.servers,t?.api?.mdx?.server])}},63213:(e,t,r)=>{"use strict";r.d(t,{t:()=>R});var a=r(54568),s=r(7620),n=r(29267),l=r(95876),i=r(45744),o=r(88426),d=r(66642),c=r(73874),u=r.n(c),p=r(76949),m=r(89619),x=r(16600),h=r(44664),g=r(82026);function y({children:e,className:t}){return(0,a.jsx)("div",{className:(0,x.cn)("rounded-full h-5 w-5 flex items-center justify-center bg-gray-50 dark:bg-white/10 text-xs font-sans font-medium text-gray-600 dark:text-gray-300",t),children:e})}var f=r(31972),b=r(60710);let j=({className:e,onClick:t})=>(0,a.jsx)("button",{className:(0,x.cn)("px-1 text-gray-400 dark:text-gray-600 hover:text-gray-600 dark:hover:text-gray-400",e),onClick:t,children:(0,a.jsx)(b.uc,{className:"w-4 h-4"})}),v=({name:e,children:t,schema:r,onClear:n,isObject:l,isArray:o,parentNames:d,arrayIndex:c,typeDropdown:u,defaultExpanded:p=!0,style:m,explode:b})=>{let[v,k]=(0,s.useState)(p),w="object"===r.type?Object.keys(r.properties).length:0,N="format"in r&&"string"==typeof r.format?`${r.type}<${r.format}>`:r.type;return(0,a.jsx)(i.A,{children:(0,a.jsxs)("div",{className:(0,x.cn)("flex space-x-3 items-start",void 0!=c&&!l&&"flex items-center"),children:[void 0!=c&&(0,a.jsx)(y,{className:(0,x.cn)(l&&"mt-8"),children:c+1}),(0,a.jsxs)("div",{className:(0,x.cn)("flex-1 grid lg:grid-cols-2 gap-x-12 gap-y-4 py-5 max-w-full",o&&"flex flex-col",l&&"gap-y-0 flex flex-col border border-gray-50 dark:border-white/5 rounded-xl px-4 py-0 my-5",void 0!==c&&"py-1 my-2"),children:[(0,a.jsxs)("div",{className:(0,x.cn)("space-y-2",l&&"pt-8 py-5 border-b border-gray-50 dark:border-white/5",!v&&"border-none",void 0!=c&&!l&&"flex items-center"),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between font-mono font-bold",children:[(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-2 text-xs truncate",title:d?`${d.join(".")}.${e}`:e,children:[(0,a.jsx)("div",{className:(0,x.cn)("text-sm truncate",void 0==e&&"hidden"),children:d&&d.length>0&&"deepObject"===m&&b?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:d.map((e,t)=>(0,a.jsxs)(a.Fragment,{children:[e,0===t?"[":"]["]}))}),(0,a.jsx)("span",{className:"font-semibold text-gray-900 dark:text-gray-100",children:e}),(0,a.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:"]"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:d?`${d.join(".")}.`:""}),(0,a.jsx)("span",{className:"font-semibold text-gray-900 dark:text-gray-100",children:e})]})}),void 0!=u?u:(0,a.jsx)(g.ks,{children:N}),r.required&&(0,a.jsx)(g.rG,{}),r.deprecated&&(0,a.jsx)(g.n,{})]}),l&&(0,a.jsx)(f.y,{onClick:()=>k(!v),count:w,labeled:!1==p})]}),r.description&&(0,a.jsx)(h.V,{markdown:r.description,className:"text-gray-500 dark:text-gray-400"})]}),v&&(0,a.jsx)("div",{className:(0,x.cn)("grid grid-cols-1 w-full items-start divide-y divide-gray-50 dark:divide-white/5",o&&"divide-y-0"),children:t})]}),(void 0!=c||d)&&n&&(0,a.jsx)(j,{className:(0,x.cn)(l&&"mt-8",d&&"mt-7"),onClick:()=>n()})]})})},k=({name:e,schema:t,typeDropdown:r,value:n,setValue:l,clearValue:i,depth:o,parentNames:d})=>{let[c,m]=(0,s.useState)(n?.map(u())??[]),h=(0,p.U)(t),g=(0,s.useMemo)(()=>{let e=n?.map((e,t)=>(0,a.jsx)(R,{schemaArray:h,value:e,setValue:e=>{let r=[...n];r[t]=e,l(r)},clearValue:()=>{let e=[...n];e.splice(t,1),m(e=>{let r=[...e];return r.splice(t,1),r}),l(e)},depth:o+1,arrayIndex:t},c[t]))??[];return e.push((0,a.jsx)(w,{label:"Add an item",className:(0,x.cn)(e.length>0&&"mt-4"),onClick:()=>{m(e=>[...e,u()()]),l([...n??[],void 0])}},"add-button")),e},[c,h,n,l,o]);return(0,a.jsx)(v,{name:e,typeDropdown:r,schema:t,onClear:i,parentNames:d,isArray:!0,children:g})};function w({className:e,onClick:t,label:r}){return(0,a.jsx)("div",{className:e,children:(0,a.jsxs)("button",{className:"flex text-playground-input w-fit items-center gap-x-1.5 !py-1 !px-2 hover:bg-gray-50 dark:hover:bg-white/[0.03]",onClick:t,children:[(0,a.jsx)(m.Ay,{icon:"plus",iconType:"solid",className:"bg-gray-500 dark:bg-white/40 h-3 w-3"}),(0,a.jsx)("span",{className:"text-gray-600 dark:text-gray-200",children:r})]})})}let N=({name:e,schema:t,typeDropdown:r,value:n,setValue:l,clearValue:i,parentNames:o,arrayIndex:d})=>{let c=(0,s.useRef)(null);return(0,a.jsxs)(v,{name:e,typeDropdown:r,schema:t,onClear:i,parentNames:o,arrayIndex:d,children:[(0,a.jsx)("button",{"aria-label":"file upload button",className:(0,x.cn)("text-left flex text-playground-input overflow-hidden",n?"!text-gray-900 dark:!text-gray-100":"!text-gray-400 dark:!text-white/30"),onClick:()=>c.current?.click(),children:n?.name??t.placeholder??"Drop a file here or click to upload"}),(0,a.jsx)("input",{type:"file","data-testid":"file-input",onChange:e=>l(e.target.files?.[0]),ref:c,className:"hidden"})]})},C=({name:e,schema:t,typeDropdown:r,clearValue:s,parentNames:n,arrayIndex:l})=>(0,a.jsx)(v,{name:e,typeDropdown:r,schema:t,onClear:s,parentNames:n,arrayIndex:l,children:(0,a.jsx)("input",{className:"flex-1 min-w-0 outline-none bg-transparent text-playground-input",type:"text",placeholder:"--",contentEditable:"false",disabled:!0})});var S=r(40113);let O=({name:e,typeDropdown:t,schema:r,value:n,setValue:l,clearValue:i,arrayIndex:o,...d})=>{let c=(0,s.useCallback)(e=>{if(0===e.target.value.length)return void l(void 0);if("integer"===r.type){let t=parseInt(e.target.value);if(isNaN(t))return;l(t)}else l(parseFloat(e.target.value))},[l,r.type]);return(0,a.jsx)(v,{name:e,typeDropdown:t,schema:r,onClear:i,parentNames:d.parentNames,arrayIndex:o,children:(0,a.jsx)("input",{id:S.V.APIPlaygroundInput,className:"flex-1 min-w-0 bg-transparent outline-none text-playground-input",placeholder:r.placeholder??`enter ${e??"value"}`,onKeyDown:e=>{("e"===e.key||"."===e.key&&"integer"===r.type)&&e.preventDefault()},type:"number",value:n??"",onChange:c})})};var A=r(79460),E=r(27592),q=r(34049);let I=({onAddKey:e,existingKeys:t})=>{let[r,n]=(0,s.useState)(!1),[l,i]=(0,s.useState)(""),o=t.includes(l),d=l.length>0&&!o;return r?(0,a.jsx)("input",{className:(0,x.cn)("py-6 flex-1 bg-transparent outline-none text-sm text-gray-900 dark:text-gray-100 placeholder-gray-300 dark:placeholder-white/30 font-mono font-medium",o?"underline decoration-wavy decoration-red-400":""),placeholder:"Enter key of new property",type:"text",autoFocus:!0,value:l,onBlur:()=>{i(""),n(!1)},onChange:e=>i(e.target.value),onKeyDown:t=>{("Enter"===t.key||"Tab"===t.key)&&d&&e(t.currentTarget.value)},spellCheck:!1}):(0,a.jsx)(w,{className:"py-5",onClick:()=>n(e=>!e),label:"Add new property"})};var T=r(78161);let $=({name:e,schema:t,typeDropdown:r,setValue:n,clearValue:i,value:o,depth:d,parentNames:c,arrayIndex:p,defaultExpanded:m,style:x,explode:h,renderedRefs:g})=>{let[y,f]=(0,s.useState)(u()()),b=!1!==t.additionalProperties,j=[...Object.keys(o??{}),...Object.keys(t.properties)],k=function(e){let t=(0,l.j)(),{location:r}=(0,A.ad)();return(0,s.useMemo)(()=>(0,q.c)(e,t,r),[e,t,r])}(t),w=function(e){let t=(0,l.j)(),{location:r}=(0,A.ad)();return(0,s.useMemo)(()=>(0,E.D)(e,t,r),[e,t,r])}(t),N=t.refIdentifier,C=(0,s.useMemo)(()=>(({properties:e,additionalProperties:t,value:r,setValue:s,name:n,parentNames:l,defaultExpanded:i,depth:o,refId:d,refs:c,style:u,explode:p})=>{let m=new Set(c);d&&m.add(d);let x=[],h=((e,t,r)=>{let a=Object.entries(e).map(([e,t])=>({key:e,schema:t,value:r?.[e],isAdditionalProperty:!1}));if(!1===t)return a;let s="object"==typeof t?t:[{type:"any"}];return[...a,...Object.keys(r??{}).filter(t=>!Object.keys(e).includes(t)).map(e=>({key:e,schema:s,value:r?.[e],isAdditionalProperty:!0}))]})(e,t,r);return m.size>0?h.map(e=>{let{key:t,schema:d,value:c,isAdditionalProperty:h}=e,g=void 0!==c||h?()=>{let e={...r};delete e[t],s(e)}:void 0,y=n?[...l??[],n]:void 0,f=d[0],b=Object.values((0,T.Y)(f)).some(e=>"string"==typeof e&&m.has(e));x.push((0,a.jsx)(R,{name:t,schemaArray:d,value:c,setValue:e=>{let a={...r};a[t]=e,s(a)},clearValue:g,depth:o+1,parentNames:y,defaultExpanded:!b&&i,renderedRefs:new Set(m),style:u,explode:p},t))}):h.map(e=>{let{key:t,schema:d,value:c,isAdditionalProperty:m}=e,h=void 0!==c||m?()=>{let e={...r};delete e[t],s(e)}:void 0,g=n?[...l??[],n]:void 0;x.push((0,a.jsx)(R,{name:t,schemaArray:d,value:c,setValue:e=>{let a={...r};a[t]=e,s(a)},clearValue:h,depth:o+1,parentNames:g,defaultExpanded:i,style:u,explode:p,renderedRefs:new Set},t))}),x})({properties:k,additionalProperties:w,value:o,setValue:n,name:e,parentNames:c,defaultExpanded:m,depth:d,refId:N,refs:new Set(g),style:x,explode:h}),[w,m,d,e,c,k,N,g,n,o,x,h]);return 0===d?C:(0,a.jsxs)(v,{name:e,typeDropdown:r,schema:t,onClear:i,isObject:!0,parentNames:c,arrayIndex:p,defaultExpanded:m,style:x,explode:h,children:[C,b?(0,a.jsx)(I,{onAddKey:e=>{let t={...o};t[e]=void 0,n(t),f(u()())},existingKeys:j},y):null]})};var P=r(74711);let M=({name:e,schema:t,typeDropdown:r,value:s,setValue:n,clearValue:l,parentNames:i,arrayIndex:o})=>{let d="boolean"===t.type?[!0,!1]:t.enum;return(0,a.jsx)(v,{name:e,typeDropdown:r,schema:t,onClear:l,parentNames:i,arrayIndex:o,children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("select",{className:(0,x.cn)("relative text-playground-input w-full text-left flex-1 outline-none cursor-pointer bg-transparent",void 0!==s?"!text-gray-900 dark:!text-gray-100":"!text-gray-400 dark:!text-white/30"),value:s?.toString()??"",onChange:e=>{"boolean"===t.type?n("true"===e.target.value):n(e.target.value)},children:[(0,a.jsx)("option",{value:"",disabled:!0,children:t.placeholder??`select ${e||"option"}`}),d.map(e=>(0,a.jsx)("option",{value:e.toString(),children:e.toString()},e.toString()))]}),(0,a.jsx)(P.A,{className:"absolute right-2 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-white/30 pointer-events-none"})]})})},B=({name:e,schema:t,typeDropdown:r,value:n,setValue:l,clearValue:i,parentNames:o,arrayIndex:d,style:c,explode:u})=>{let{prefix:p,placeholder:m}=V(t.format),x=p&&n?.startsWith(p)?n.substring(p.length):n,h=(0,s.useCallback)(e=>{let t=`${p??""}${e}`;t?l(t):l(void 0)},[l,p]);return(0,a.jsx)(v,{name:e,typeDropdown:r,schema:t,onClear:i,parentNames:o,arrayIndex:d,style:c,explode:u,children:(0,a.jsxs)("div",{className:"relative flex flex-1 items-center",children:[(0,a.jsx)("div",{className:"absolute left-2.5 top-0 bottom-0 flex items-center justify-center text-sm text-gray-800 dark:text-gray-100",children:p}),(0,a.jsx)("input",{id:S.V.APIPlaygroundInput,className:"flex-1 min-w-0 bg-transparent outline-none text-playground-input",style:{paddingLeft:p?.52*p.length+"rem":void 0},placeholder:t.placeholder??m??`enter ${e??"value"}`,type:"text",value:x??"",onChange:e=>h(e.target.value),spellCheck:!1})]})})},V=e=>e?.toLowerCase()==="bearer"?{prefix:"Bearer ",placeholder:"enter bearer token"}:{},D=({name:e,schema:t,typeDropdown:r,value:s,setValue:n,clearValue:l,parentNames:i,arrayIndex:o})=>(0,a.jsx)(v,{name:e,typeDropdown:r,schema:t,onClear:l,parentNames:i,arrayIndex:o,children:(0,a.jsx)("textarea",{className:"-mx-3.5 px-3.5 flex-1 min-h-[2.5rem] min-w-0 bg-transparent outline-none text-playground-input resize-y",rows:2,placeholder:t.placeholder??`Enter ${e??"value"}`,value:s,onChange:e=>n(e.target.value),spellCheck:!1,autoCapitalize:"off",autoComplete:"off",autoCorrect:"off"})}),L=({schema:e,value:t,depth:r,isBody:n,setValue:l,defaultExpanded:i,renderedRefs:o,...c})=>{switch(((e,t,r)=>{let a=(0,s.useRef)(!1);(0,s.useEffect)(()=>{if(!a.current){switch(void 0===e.default&&void 0!==e["x-default"]&&(e.default=e["x-default"]),e.type){case"string":case"enum<string>":(0,d.Kg)(e.default)&&!(0,d.Kg)(t)&&r(e.default);break;case"number":case"enum<number>":(0,d.Et)(e.default)&&!(0,d.Et)(t)&&r(e.default);break;case"boolean":(0,d.Lm)(e.default)&&!(0,d.Lm)(t)&&r(e.default);break;case"object":if(e.default&&(0,d.Gv)(e.default)&&!(0,d.Gv)(t)){let t={};for(let[r,a]of Object.entries(e.default))t[r]=a;r(t)}break;case"integer":case"enum<integer>":(0,d.Fq)(e.default)&&!(0,d.Fq)(t)&&r(e.default);break;case"array":void 0===e.default||(0,d.cy)(t)||r([...(0,d.cy)(e.default)?e.default:[e.default]]);break;case"null":(0,d.kZ)(e.default)&&null!==t&&r(e.default)}a.current=!0}},[e,t,r])})(e,t,l),e.type){case"string":{let s=(0,d.Kg)(t)?t:void 0;return(0,a.jsx)(n&&0===r?D:B,{schema:e,value:s,setValue:l,...c})}case"number":{let r=(0,d.Et)(t)?t:void 0;return(0,a.jsx)(O,{schema:e,value:r,setValue:l,...c})}case"boolean":{let r=(0,d.Lm)(t)?t:void 0;return(0,a.jsx)(M,{schema:e,value:r,setValue:l,...c})}case"object":{let s=(0,d.Gv)(t)?t:void 0;return(0,a.jsx)($,{schema:e,value:s,setValue:l,depth:r,defaultExpanded:i,renderedRefs:o,...c})}case"integer":{let r=(0,d.Fq)(t)?t:void 0;return(0,a.jsx)(O,{schema:e,value:r,setValue:l,...c})}case"array":{let s=(0,d.cy)(t)?t:void 0;return(0,a.jsx)(k,{schema:e,value:s,setValue:l,depth:r,...c})}case"enum<string>":{let r=(0,d.Kg)(t)?t:void 0;return(0,a.jsx)(M,{schema:e,value:r,setValue:l,...c})}case"enum<number>":{let r=(0,d.Et)(t)?t:void 0;return(0,a.jsx)(M,{schema:e,value:r,setValue:l,...c})}case"enum<integer>":{let r=(0,d.Fq)(t)?t:void 0;return(0,a.jsx)(M,{schema:e,value:r,setValue:l,...c})}case"file":{let r=t instanceof File?t:void 0;return(0,a.jsx)(N,{schema:e,value:r,setValue:l,...c})}case"null":{let r=null===t?null:void 0;return(0,a.jsx)(C,{schema:e,value:r,setValue:l,...c})}}},R=({name:e,value:t,schemaArray:r,setValue:d,clearValue:c,isBody:u,depth:p=0,parentNames:m,arrayIndex:x,defaultExpanded:h,style:g,explode:y,renderedRefs:f=new Set})=>{let[b,j]=(0,s.useState)(0),v=(e=>{let t=(0,l.j)();return(0,s.useMemo)(()=>((e,t)=>{let r,a=e.some(e=>"null"===e.type),s=e.every(e=>"object"===e.type),l=(0,n.w)(e,t,s,a,!0),i=[],o=[];for(let t of e)"any"===t.type?r=r??t:i.push(t);for(let e of l)"any"===e?o.push(null):o.push(e);if(void 0!==r){let e={description:r.description,required:r.required};i.push({...e,type:"string"},{...e,type:"number"},{...e,type:"boolean"},{...e,type:"object",properties:{}},{...e,type:"array",items:[{type:"any"}]},{...e,type:"null"})}return i.map((e,t)=>({schema:e,label:o[t]??e.type}))})(e,t),[e,t])})(r),k=v[b]?.schema,w=(0,s.useCallback)(e=>{v[e]?.schema.type==="null"?d(null):d(void 0),j(e)},[v,d]),N=(0,a.jsx)(o.C,{options:v,selectedIndex:b,onSelectOption:w});return void 0===k?null:(0,a.jsx)(i.A,{children:(0,a.jsx)(L,{name:e,schema:k,typeDropdown:N,value:t,setValue:d,clearValue:c,isBody:u,depth:p,parentNames:m,arrayIndex:x,defaultExpanded:h,style:g,explode:y,renderedRefs:f})})}},66642:(e,t,r)=>{"use strict";function a(e){return"string"==typeof e}function s(e){let t=Number(e);return"number"==typeof t&&!isNaN(t)}function n(e){let t=Number(e);return"number"==typeof t&&!isNaN(t)&&t%1==0}function l(e){return!!e&&"object"==typeof e&&!Array.isArray(e)}function i(e){return!!e&&"object"==typeof e&&Array.isArray(e)}function o(e){return"boolean"==typeof e}function d(e){return null===e}function c(e){return e instanceof File}r.d(t,{Et:()=>s,Fq:()=>n,Gv:()=>l,Kg:()=>a,Lm:()=>o,cy:()=>i,fo:()=>c,kZ:()=>d})},74840:(e,t,r)=>{"use strict";r.d(t,{D:()=>l});var a=r(7620),s=r(71197),n=r(3256);let l=()=>{let{docsConfig:e}=(0,a.useContext)(s.H6),{pageMetadata:t}=(0,a.useContext)(s.NQ),r=(0,n.Zk)(),l=r?.type;return(0,a.useMemo)(()=>("webhook"===l?"simple":t.playground||e?.api?.playground?.display)??"interactive",[e,t,l])}},79772:()=>{},88426:(e,t,r)=>{"use strict";r.d(t,{C:()=>i});var a=r(54568),s=r(89619),n=r(16600);function l(e){if(e)return e.schema.deprecated?`${e.label}, deprecated`:e.label}let i=({options:e,selectedIndex:t,onSelectOption:r,className:i})=>{let o=e.length>1;return(0,a.jsxs)("div",{className:(0,n.cn)("relative flex items-center px-2 py-0.5 text-gray-600 dark:text-gray-300 hover:text-gray-950 dark:hover:text-white font-medium",i),children:[(0,a.jsx)("div",{className:"",children:l(e[t])}),o&&(0,a.jsx)(s.Ay,{icon:"angle-down",iconType:"solid",className:"h-2.5 w-2.5 bg-gray-500 dark:bg-gray-400 shrink-0 ml-1.5"}),o&&(0,a.jsx)("select",{"aria-label":"parameter type select",className:"absolute opacity-0 inset-0 cursor-pointer",onChange:e=>r(e.target.selectedIndex),value:t,children:e.map((e,t)=>(0,a.jsx)("option",{value:t,children:l(e)},t))})]})}},98959:(e,t,r)=>{"use strict";r.r(t),r.d(t,{MdxPanel:()=>c});var a=r(54568),s=r(95371),n=r(7620),l=r(71197),i=r(31467),o=r(56717),d=r(16600);function c({mobile:e}){let{pageMetadata:t,panelMdxSource:r,panelMdxSourceWithNoJs:c}=(0,n.useContext)(l.NQ),{userInfo:u}=(0,n.useContext)(l.cy);return r&&c?(0,a.jsx)("div",{className:(0,d.cn)(i.x.Panel,"w-full xl:w-[28rem] mb-6 relative max-h-[calc(100%-32px)]",e?"mt-8 lg:mt-0 flex flex-col gap-6 xl:hidden":"overflow-y-auto"),children:(0,a.jsx)(s.tH,{children:(0,a.jsx)(o.b,{mdxSource:r,mdxSourceWithNoJs:c,pageMetadata:t,userInfo:u})})}):null}}}]);