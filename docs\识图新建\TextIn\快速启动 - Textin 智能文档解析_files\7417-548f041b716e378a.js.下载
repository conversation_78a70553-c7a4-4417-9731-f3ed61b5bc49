!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="39784661-156c-4a9b-92e6-4b20312b14e8",e._sentryDebugIdIdentifier="sentry-dbid-39784661-156c-4a9b-92e6-4b20312b14e8")}catch(e){}}();"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7417],{17142:(e,t,n)=>{function r(e){if(1!=[...e].length)throw Error(`Expected "${e}" to be a single code point`);return e.codePointAt(0)}n.d(t,{vr:()=>e8});let a=new Set(["alnum","alpha","ascii","blank","cntrl","digit","graph","lower","print","punct","space","upper","word","xdigit"]),o=String.raw;function s(e,t){if(null==e)throw Error(t??"Value expected");return e}let i=o`\[\^?`,l=`c.? | C(?:-.?)?|${o`[pP]\{(?:\^?[-\x20_]*[A-Za-z][-\x20\w]*\})?`}|${o`x[89A-Fa-f]\p{AHex}(?:\\x[89A-Fa-f]\p{AHex})*`}|${o`u(?:\p{AHex}{4})? | x\{[^\}]*\}? | x\p{AHex}{0,2}`}|${o`o\{[^\}]*\}?`}|${o`\d{1,3}`}`,u=/[?*+][?+]?|\{(?:\d+(?:,\d*)?|,\d+)\}\??/,c=RegExp(o`
  \\ (?:
    ${l}
    | [gk]<[^>]*>?
    | [gk]'[^']*'?
    | .
  )
  | \( (?:
    \? (?:
      [:=!>({]
      | <[=!]
      | <[^>]*>
      | '[^']*'
      | ~\|?
      | #(?:[^)\\]|\\.?)*
      | [^:)]*[:)]
    )?
    | \*[^\)]*\)?
  )?
  | (?:${u.source})+
  | ${i}
  | .
`.replace(/\s+/g,""),"gsu"),p=RegExp(o`
  \\ (?:
    ${l}
    | .
  )
  | \[:(?:\^?\p{Alpha}+|\^):\]
  | ${i}
  | &&
  | .
`.replace(/\s+/g,""),"gsu");function h(e,{inCharClass:t}){let n=e[1];if("c"===n||"C"===n){var a=e;let t="c"===a[1]?a[2]:a[3];if(!t||!/[A-Za-z]/.test(t))throw Error(`Unsupported control character "${a}"`);return f(r(t.toUpperCase())-64,a)}if("dDhHsSwW".includes(n)){var s=e;let t=s[1].toLowerCase();return y({d:"digit",h:"hex",s:"space",w:"word"}[t],s,{negate:s[1]!==t})}if(e.startsWith(o`\o{`))throw Error(`Incomplete, invalid, or unsupported octal code point "${e}"`);if(/^\\[pP]\{/.test(e)){if(3===e.length)throw Error(`Incomplete or invalid Unicode property "${e}"`);var i=e;let{p:t,neg:n,value:r}=/^\\(?<p>[pP])\{(?<neg>\^?)(?<value>[^}]+)/.exec(i).groups;return y("property",i,{value:r,negate:"P"===t&&!n||"p"===t&&!!n})}if(/^\\x[89A-Fa-f]\p{AHex}/u.test(e))try{let t=e.split(/\\x/).slice(1).map(e=>parseInt(e,16)),n=new TextDecoder("utf-8",{ignoreBOM:!0,fatal:!0}).decode(new Uint8Array(t)),a=new TextEncoder;return[...n].map(e=>{let t=[...a.encode(e)].map(e=>`\\x${e.toString(16)}`).join("");return f(r(e),t)})}catch{throw Error(`Multibyte code "${e}" incomplete or invalid in Oniguruma`)}if("u"===n||"x"===n)return f(function(e){if(/^(?:\\u(?!\p{AHex}{4})|\\x(?!\p{AHex}{1,2}|\{\p{AHex}{1,8}\}))/u.test(e))throw Error(`Incomplete or invalid escape "${e}"`);return parseInt("{"===e[2]?/^\\x\{\s*(?<hex>\p{AHex}+)/u.exec(e).groups.hex:e.slice(2),16)}(e),e);if(k.has(n))return f(k.get(n),e);if(/\d/.test(n))return{type:"EscapedNumber",inCharClass:t,raw:e};if("\\"===e)throw Error(o`Incomplete escape "\"`);if("M"===n)throw Error(`Unsupported meta "${e}"`);if(2==[...e].length)return f(e.codePointAt(1),e);throw Error(`Unexpected escape "${e}"`)}function d(e,t){return{type:"Assertion",kind:e,raw:t}}function g(e){return{type:"Backreference",raw:e}}function f(e,t){return{type:"Character",value:e,raw:t}}function m(e,t){return{type:"CharacterClassOpen",negate:e,raw:t}}function y(e,t,n={}){return{type:"CharacterSet",kind:e,...n,raw:t}}function b(e,t,n={}){return"keep"===e?{type:"Directive",kind:e,raw:t}:{type:"Directive",kind:e,flags:s(n.flags),raw:t}}function _(e,t,n={}){return{type:"GroupOpen",kind:e,...n,raw:t}}let C=new Set(["COUNT","CMP","ERROR","FAIL","MAX","MISMATCH","SKIP","TOTAL_COUNT"]),k=new Map([["a",7],["b",8],["e",27],["f",12],["n",10],["r",13],["t",9],["v",11]]);function w(e){let t=null,n,r;if("{"===e[0]){let{minStr:a,maxStr:o}=/^\{(?<minStr>\d*)(?:,(?<maxStr>\d*))?/.exec(e).groups;if(+a>1e5||o&&+o>1e5)throw Error("Quantifier value unsupported in Oniguruma");if((n=+a)>(r=void 0===o?+a:""===o?1/0:+o)&&(t="possessive",[n,r]=[r,n]),e.endsWith("?")){if("possessive"===t)throw Error('Unsupported possessive interval quantifier chain with "?"');t="lazy"}else t||(t="greedy")}else n=+("+"===e[0]),r="?"===e[0]?1:1/0,t="+"===e[1]?"possessive":"?"===e[1]?"lazy":"greedy";return{type:"Quantifier",kind:t,min:n,max:r,raw:e}}function S(e){let t={};return e.includes("i")&&(t.ignoreCase=!0),e.includes("m")&&(t.dotAll=!0),e.includes("x")&&(t.extended=!0),Object.keys(t).length?t:null}function x(e,t){if(!Array.isArray(e.body))throw Error("Expected node with body array");if(1!==e.body.length)return!1;let n=e.body[0];return!t||Object.keys(t).every(e=>t[e]===n[e])}let v=new Set(["AbsenceFunction","Backreference","CapturingGroup","Character","CharacterClass","CharacterSet","Group","Quantifier","Subroutine"]);function A(e,t={}){var n;let i={flags:"",normalizeUnknownPropertyNames:!1,skipBackrefValidation:!1,skipLookbehindValidation:!1,skipPropertyNameValidation:!1,unicodePropertyMap:null,...t,rules:{captureGroup:!1,singleline:!1,...t.rules}},l=function(e,t={}){let n={flags:"",...t,rules:{captureGroup:!1,singleline:!1,...t.rules}};if("string"!=typeof e)throw Error("String expected as pattern");let s=function(e){let t={ignoreCase:!1,dotAll:!1,extended:!1,digitIsAscii:!1,posixIsAscii:!1,spaceIsAscii:!1,wordIsAscii:!1,textSegmentMode:null};for(let n=0;n<e.length;n++){let r=e[n];if(!"imxDPSWy".includes(r))throw Error(`Invalid flag "${r}"`);if("y"===r){if(!/^y{[gw]}/.test(e.slice(n)))throw Error('Invalid or unspecified flag "y" mode');t.textSegmentMode="g"===e[n+2]?"grapheme":"word",n+=3;continue}t[({i:"ignoreCase",m:"dotAll",x:"extended",D:"digitIsAscii",P:"posixIsAscii",S:"spaceIsAscii",W:"wordIsAscii"})[r]]=!0}return t}(n.flags),i=[s.extended],l={captureGroup:n.rules.captureGroup,getCurrentModX:()=>i.at(-1),numOpenGroups:0,popModX(){i.pop()},pushModX(e){i.push(e)},replaceCurrentModX(e){i[i.length-1]=e},singleline:n.rules.singleline},k=[],x;for(c.lastIndex=0;x=c.exec(e);){let t=function(e,t,n,s){let[i,l]=n;if("["===n||"[^"===n){let e=function(e,t,n){let o=[m("^"===t[1],t)],s=1,i;for(p.lastIndex=n;i=p.exec(e);){let e=i[0];if("["===e[0]&&":"!==e[1])s++,o.push(m("^"===e[1],e));else if("]"===e){if("CharacterClassOpen"===o.at(-1).type)o.push(f(93,e));else if(s--,o.push({type:"CharacterClassClose",raw:e}),!s)break}else{let t=function(e){if("\\"===e[0])return h(e,{inCharClass:!0});if("["===e[0]){let t=/\[:(?<negate>\^?)(?<name>[a-z]+):\]/.exec(e);if(!t||!a.has(t.groups.name))throw Error(`Invalid POSIX class "${e}"`);return y("posix",e,{value:t.groups.name,negate:!!t.groups.negate})}return"-"===e?{type:"CharacterClassHyphen",raw:e}:"&&"===e?{type:"CharacterClassIntersector",raw:e}:f(r(e),e)}(e);Array.isArray(t)?o.push(...t):o.push(t)}}return{tokens:o,lastIndex:p.lastIndex||e.length}}(t,n,s);return{tokens:e.tokens,lastIndex:e.lastIndex}}if("\\"===i){if("AbBGyYzZ".includes(l))return{token:d(n,n)};if(/^\\g[<']/.test(n)){if(!/^\\g(?:<[^>]+>|'[^']+')$/.test(n))throw Error(`Invalid group name "${n}"`);return{token:{type:"Subroutine",raw:n}}}if(/^\\k[<']/.test(n)){if(!/^\\k(?:<[^>]+>|'[^']+')$/.test(n))throw Error(`Invalid group name "${n}"`);return{token:g(n)}}if("K"===l)return{token:b("keep",n)};if("N"===l||"R"===l)return{token:y("newline",n,{negate:"N"===l})};if("O"===l)return{token:y("any",n)};if("X"===l)return{token:y("text_segment",n)};let e=h(n,{inCharClass:!1});return Array.isArray(e)?{tokens:e}:{token:e}}if("("===i){if("*"===l)return{token:function(e){let t=/\(\*(?<name>[A-Za-z_]\w*)?(?:\[(?<tag>(?:[A-Za-z_]\w*)?)\])?(?:\{(?<args>[^}]*)\})?\)/.exec(e);if(!t)throw Error(`Incomplete or invalid named callout "${e}"`);let{name:n,tag:r,args:a}=t.groups;if(!n)throw Error(`Invalid named callout "${e}"`);if(""===r)throw Error(`Named callout tag with empty value not allowed "${e}"`);let o=a?a.split(",").filter(e=>""!==e).map(e=>/^[+-]?\d+$/.test(e)?+e:e):[],[s,i,l]=o,u=C.has(n)?n.toLowerCase():"custom";switch(u){case"fail":case"mismatch":case"skip":if(o.length>0)throw Error(`Named callout arguments not allowed "${o}"`);break;case"error":if(o.length>1)throw Error(`Named callout allows only one argument "${o}"`);if("string"==typeof s)throw Error(`Named callout argument must be a number "${s}"`);break;case"max":if(!o.length||o.length>2)throw Error(`Named callout must have one or two arguments "${o}"`);if("string"==typeof s&&!/^[A-Za-z_]\w*$/.test(s))throw Error(`Named callout argument one must be a tag or number "${s}"`);if(2===o.length&&("number"==typeof i||!/^[<>X]$/.test(i)))throw Error(`Named callout optional argument two must be '<', '>', or 'X' "${i}"`);break;case"count":case"total_count":if(o.length>1)throw Error(`Named callout allows only one argument "${o}"`);if(1===o.length&&("number"==typeof s||!/^[<>X]$/.test(s)))throw Error(`Named callout optional argument must be '<', '>', or 'X' "${s}"`);break;case"cmp":if(3!==o.length)throw Error(`Named callout must have three arguments "${o}"`);if("string"==typeof s&&!/^[A-Za-z_]\w*$/.test(s))throw Error(`Named callout argument one must be a tag or number "${s}"`);if("number"==typeof i||!/^(?:[<>!=]=|[<>])$/.test(i))throw Error(`Named callout argument two must be '==', '!=', '>', '<', '>=', or '<=' "${i}"`);if("string"==typeof l&&!/^[A-Za-z_]\w*$/.test(l))throw Error(`Named callout argument three must be a tag or number "${l}"`);break;case"custom":throw Error(`Undefined callout name "${n}"`);default:throw Error(`Unexpected named callout kind "${u}"`)}return{type:"NamedCallout",kind:u,tag:r??null,arguments:a?.split(",")??null,raw:e}}(n)};if("(?{"===n)throw Error(`Unsupported callout "${n}"`);if(n.startsWith("(?#")){if(")"!==t[s])throw Error('Unclosed comment group "(?#"');return{lastIndex:s+1}}if(/^\(\?[-imx]+[:)]$/.test(n))return{token:function(e,t){let{on:n,off:r}=/^\(\?(?<on>[imx]*)(?:-(?<off>[-imx]*))?/.exec(e).groups;r??="";let a=(t.getCurrentModX()||n.includes("x"))&&!r.includes("x"),o=S(n),s=S(r),i={};if(o&&(i.enable=o),s&&(i.disable=s),e.endsWith(")"))return t.replaceCurrentModX(a),b("flags",e,{flags:i});if(e.endsWith(":"))return t.pushModX(a),t.numOpenGroups++,_("group",e,{...(o||s)&&{flags:i}});throw Error(`Unexpected flag modifier "${e}"`)}(n,e)};if(e.pushModX(e.getCurrentModX()),e.numOpenGroups++,"("===n&&!e.captureGroup||"(?:"===n)return{token:_("group",n)};if("(?>"===n)return{token:_("atomic",n)};if("(?="===n||"(?!"===n||"(?<="===n||"(?<!"===n)return{token:_("<"===n[2]?"lookbehind":"lookahead",n,{negate:n.endsWith("!")})};if("("===n&&e.captureGroup||n.startsWith("(?<")&&n.endsWith(">")||n.startsWith("(?'")&&n.endsWith("'"))return{token:_("capturing",n,{..."("!==n&&{name:n.slice(3,-1)}})};if(n.startsWith("(?~")){if("(?~|"===n)throw Error(`Unsupported absence function kind "${n}"`);return{token:_("absence_repeater",n)}}throw"(?("===n?Error(`Unsupported conditional "${n}"`):Error(`Invalid or unsupported group option "${n}"`)}if(")"===n){if(e.popModX(),e.numOpenGroups--,e.numOpenGroups<0)throw Error('Unmatched ")"');return{token:{type:"GroupClose",raw:n}}}if(e.getCurrentModX()){if("#"===n){let e=t.indexOf(`
`,s);return{lastIndex:-1===e?t.length:e}}if(/^\s$/.test(n)){let e=/\s+/y;return e.lastIndex=s,{lastIndex:e.exec(t)?e.lastIndex:s}}}return"."===n?{token:y("dot",n)}:"^"===n||"$"===n?{token:d(e.singleline?({"^":o`\A`,$:o`\Z`})[n]:n,n)}:"|"===n?{token:{type:"Alternator",raw:n}}:u.test(n)?{tokens:function(e){let t,n=[],r=RegExp(u,"gy");for(;t=r.exec(e);){let e=t[0];if("{"===e[0]){let t=/^\{(?<min>\d+),(?<max>\d+)\}\??$/.exec(e);if(t){let{min:a,max:o}=t.groups;if(+a>+o&&e.endsWith("?")){r.lastIndex--,n.push(w(e.slice(0,-1)));continue}}}n.push(w(e))}return n}(n)}:{token:f(r(n),n)}}(l,e,x[0],c.lastIndex);t.tokens?k.push(...t.tokens):t.token&&k.push(t.token),void 0!==t.lastIndex&&(c.lastIndex=t.lastIndex)}let v=[],A=0;k.filter(e=>"GroupOpen"===e.type).forEach(e=>{"capturing"===e.kind?e.number=++A:"("===e.raw&&v.push(e)}),A||v.forEach((e,t)=>{e.kind="capturing",e.number=t+1});let E=A||v.length;return{tokens:k.map(e=>"EscapedNumber"===e.type?function(e,t){let{raw:n,inCharClass:a}=e,s=n.slice(1);if(!a&&("0"!==s&&1===s.length||"0"!==s[0]&&+s<=t))return[g(n)];let i=[],l=s.match(/^[0-7]+|\d/g);for(let e=0;e<l.length;e++){let t,a=l[e];if(0===e&&"8"!==a&&"9"!==a){if((t=parseInt(a,8))>127)throw Error(o`Octal encoded byte above 177 unsupported "${n}"`)}else t=r(a);i.push(f(t,(0===e?"\\":"")+a))}return i}(e,E):e).flat(),flags:s}}(e,{flags:i.flags,rules:{captureGroup:i.rules.captureGroup,singleline:i.rules.singleline}}),k=(e,t)=>{let n=l.tokens[x.nextIndex];switch(x.parent=e,x.nextIndex++,n.type){case"Alternator":return E();case"Assertion":return function({kind:e}){return I(s({"^":"line_start",$:"line_end","\\A":"string_start","\\b":"word_boundary","\\B":"word_boundary","\\G":"search_start","\\y":"text_segment_boundary","\\Y":"text_segment_boundary","\\z":"string_end","\\Z":"string_end_newline"}[e],`Unexpected assertion kind "${e}"`),{negate:e===o`\B`||e===o`\Y`})}(n);case"Backreference":return function({raw:e},t){let n=/^\\k[<']/.test(e),r=n?e.slice(3,-1):e.slice(1),a=(n,r=!1)=>{let a=t.capturingGroups.length,o=!1;if(n>a)if(t.skipBackrefValidation)o=!0;else throw Error(`Not enough capturing groups defined to the left "${e}"`);return t.hasNumberedRef=!0,N(r?a+1-n:n,{orphan:o})};if(n){let n=/^(?<sign>-?)0*(?<num>[1-9]\d*)$/.exec(r);if(n)return a(+n.groups.num,!!n.groups.sign);if(/[-+]/.test(r))throw Error(`Invalid backref name "${e}"`);if(!t.namedGroupsByName.has(r))throw Error(`Group name not defined to the left "${e}"`);return N(r)}return a(+r)}(n,x);case"Character":return R(n.value,{useLastValid:!!t.isCheckingRangeEnd});case"CharacterClassHyphen":return function(e,t,n){let{tokens:a,walk:o}=t,s=t.parent,i=s.body.at(-1),l=a[t.nextIndex];if(!n.isCheckingRangeEnd&&i&&"CharacterClass"!==i.type&&"CharacterClassRange"!==i.type&&l&&"CharacterClassOpen"!==l.type&&"CharacterClassClose"!==l.type&&"CharacterClassIntersector"!==l.type){let e=o(s,{...n,isCheckingRangeEnd:!0});if("Character"===i.type&&"Character"===e.type)return s.body.pop(),function(e,t){if(t.value<e.value)throw Error("Character class range out of order");return{type:"CharacterClassRange",min:e,max:t}}(i,e);throw Error("Invalid character class range")}return R(r("-"))}(0,x,t);case"CharacterClassOpen":return function({negate:e},t,n){let{tokens:r,walk:a}=t,o=r[t.nextIndex],s=[$()],i=W(o);for(;"CharacterClassClose"!==i.type;){if("CharacterClassIntersector"===i.type)s.push($()),t.nextIndex++;else{let e=s.at(-1);e.body.push(a(e,n))}i=W(r[t.nextIndex],o)}let l=$({negate:e});return 1===s.length?l.body=s[0].body:(l.kind="intersection",l.body=s.map(e=>1===e.body.length?e.body[0]:e)),t.nextIndex++,l}(n,x,t);case"CharacterSet":return function({kind:e,negate:t,value:n},r){let{normalizeUnknownPropertyNames:o,skipPropertyNameValidation:s,unicodePropertyMap:i}=r;if("property"===e){let r=z(n);if(!a.has(r)||i?.has(r))return G(n,{negate:t,normalizeUnknownPropertyNames:o,skipPropertyNameValidation:s,unicodePropertyMap:i});e="posix",n=r}return"posix"===e?function(e,t){let n=!!t?.negate;if(!a.has(e))throw Error(`Invalid POSIX class "${e}"`);return{type:"CharacterSet",kind:"posix",value:e,negate:n}}(n,{negate:t}):L(e,{negate:t})}(n,x);case"Directive":return function(e,t={}){if("keep"===e)return{type:"Directive",kind:e};if("flags"===e)return{type:"Directive",kind:e,flags:s(t.flags)};throw Error(`Unexpected directive kind "${e}"`)}(n.kind,{flags:n.flags});case"GroupOpen":return function(e,t,n){var r,a;let{tokens:o,capturingGroups:s,namedGroupsByName:i,skipLookbehindValidation:l,walk:u}=t,c=function({flags:e,kind:t,name:n,negate:r,number:a}){switch(t){case"absence_repeater":var o="repeater";!1;return{type:"AbsenceFunction",kind:o,body:B(void 0)};case"atomic":return M({atomic:!0});case"capturing":return P(a,{name:n});case"group":return M({flags:e});case"lookahead":case"lookbehind":return O({behind:"lookbehind"===t,negate:r});default:throw Error(`Unexpected group kind "${t}"`)}}(e),p="AbsenceFunction"===c.type,h=j(c),d=h&&c.negate;if("CapturingGroup"===c.type&&(s.push(c),c.name&&(r=c.name,a=[],i.has(r)||i.set(r,a),i.get(r)).push(c)),p&&n.isInAbsenceFunction)throw Error("Nested absence function not supported by Oniguruma");let g=q(o[t.nextIndex]);for(;"GroupClose"!==g.type;){if("Alternator"===g.type)c.body.push(E()),t.nextIndex++;else{let e=c.body.at(-1),t=u(e,{...n,isInAbsenceFunction:n.isInAbsenceFunction||p,isInLookbehind:n.isInLookbehind||h,isInNegLookbehind:n.isInNegLookbehind||d});if(e.body.push(t),(h||n.isInLookbehind)&&!l){let e="Lookbehind includes a pattern not allowed by Oniguruma";if(d||n.isInNegLookbehind){if(U(t)||"CapturingGroup"===t.type)throw Error(e)}else if(U(t)||j(t)&&t.negate)throw Error(e)}}g=q(o[t.nextIndex])}return t.nextIndex++,c}(n,x,t);case"NamedCallout":var i,u;return i=n.kind,u=n.tag,{type:"NamedCallout",kind:i,tag:u,arguments:n.arguments};case"Quantifier":return function({kind:e,min:t,max:n},r){let a=r.parent,o=a.body.at(-1);if(!o||!v.has(o.type))throw Error("Quantifier requires a repeatable token");let s=T(e,t,n,o);return a.body.pop(),s}(n,x);case"Subroutine":return function({raw:e},t){let{capturingGroups:n,subroutines:r}=t,a=e.slice(3,-1),o=/^(?<sign>[-+]?)0*(?<num>[1-9]\d*)$/.exec(a);if(o){let e=+o.groups.num,r=n.length;if(t.hasNumberedRef=!0,(a=({"":e,"+":r+e,"-":r+1-e})[o.groups.sign])<1)throw Error("Invalid subroutine number")}else"0"===a&&(a=0);let s=D(a);return r.push(s),s}(n,x);default:throw Error(`Unexpected token type "${n.type}"`)}},x={capturingGroups:[],hasNumberedRef:!1,namedGroupsByName:new Map,nextIndex:0,normalizeUnknownPropertyNames:i.normalizeUnknownPropertyNames,parent:null,skipBackrefValidation:i.skipBackrefValidation,skipLookbehindValidation:i.skipLookbehindValidation,skipPropertyNameValidation:i.skipPropertyNameValidation,subroutines:[],tokens:l.tokens,unicodePropertyMap:i.unicodePropertyMap,walk:k},F=(n={type:"Flags",...l.flags},{type:"Regex",body:B(void 0),flags:n}),H=F.body[0];for(;x.nextIndex<l.tokens.length;){let e=k(H,{});"Alternative"===e.type?(F.body.push(e),H=e):H.body.push(e)}let{capturingGroups:V,hasNumberedRef:X,namedGroupsByName:K,subroutines:Z}=x;if(X&&K.size&&!i.rules.captureGroup)throw Error("Numbered backref/subroutine not allowed when using named capture");for(let{ref:e}of Z)if("number"==typeof e){if(e>V.length)throw Error("Subroutine uses a group number that's not defined");e&&(V[e-1].isSubroutined=!0)}else if(K.has(e)){if(K.get(e).length>1)throw Error(o`Subroutine uses a duplicate group name "\g<${e}>"`);K.get(e)[0].isSubroutined=!0}else throw Error(o`Subroutine uses a group name that's not defined "\g<${e}>"`);return F}function E(e){return{type:"Alternative",body:F(e?.body)}}function I(e,t){let n={type:"Assertion",kind:e};return("word_boundary"===e||"text_segment_boundary"===e)&&(n.negate=!!t?.negate),n}function N(e,t){let n=!!t?.orphan;return{type:"Backreference",ref:e,...n&&{orphan:n}}}function P(e,t){var n;let r={name:void 0,isSubroutined:!1,...t};if(void 0!==r.name&&(n=r.name,!/^[\p{Alpha}\p{Pc}][^)]*$/u.test(n)))throw Error(`Group name "${r.name}" invalid in Oniguruma`);return{type:"CapturingGroup",number:e,...r.name&&{name:r.name},...r.isSubroutined&&{isSubroutined:r.isSubroutined},body:B(t?.body)}}function R(e,t){let n={useLastValid:!1,...t};if(e>1114111){let t=e.toString(16);if(n.useLastValid)e=1114111;else throw e>1310719?Error(`Invalid code point out of range "\\x{${t}}"`):Error(`Invalid code point out of range in JS "\\x{${t}}"`)}return{type:"Character",value:e}}function $(e){let t={kind:"union",negate:!1,...e};return{type:"CharacterClass",kind:t.kind,negate:t.negate,body:F(e?.body)}}function L(e,t){let n=!!t?.negate,r={type:"CharacterSet",kind:e};return("digit"===e||"hex"===e||"newline"===e||"space"===e||"word"===e)&&(r.negate=n),"text_segment"!==e&&("newline"!==e||n)||(r.variableLength=!0),r}function M(e){let t=e?.atomic,n=e?.flags;if(t&&n)throw Error("Atomic group cannot have flags");return{type:"Group",...t&&{atomic:t},...n&&{flags:n},body:B(e?.body)}}function O(e){let t={behind:!1,negate:!1,...e};return{type:"LookaroundAssertion",kind:t.behind?"lookbehind":"lookahead",negate:t.negate,body:B(e?.body)}}function T(e,t,n,r){if(t>n)throw Error("Invalid reversed quantifier range");return{type:"Quantifier",kind:e,min:t,max:n,body:r}}function D(e){return{type:"Subroutine",ref:e}}function G(e,t){let n={negate:!1,normalizeUnknownPropertyNames:!1,skipPropertyNameValidation:!1,unicodePropertyMap:null,...t},r=n.unicodePropertyMap?.get(z(e));if(!r){if(n.normalizeUnknownPropertyNames)r=e.trim().replace(/[- _]+/g,"_").replace(/[A-Z][a-z]+(?=[A-Z])/g,"$&_").replace(/[A-Za-z]+/g,e=>e[0].toUpperCase()+e.slice(1).toLowerCase());else if(n.unicodePropertyMap&&!n.skipPropertyNameValidation)throw Error(o`Invalid Unicode property "\p{${e}}"`)}return{type:"CharacterSet",kind:"property",value:r??e,negate:n.negate}}function B(e){if(void 0===e)e=[E()];else if(!Array.isArray(e)||!e.length||!e.every(e=>"Alternative"===e.type))throw Error("Invalid body; expected array of one or more Alternative nodes");return e}function F(e){if(void 0===e)e=[];else if(!Array.isArray(e)||!e.every(e=>!!e.type))throw Error("Invalid body; expected array of nodes");return e}function U(e){return"LookaroundAssertion"===e.type&&"lookahead"===e.kind}function j(e){return"LookaroundAssertion"===e.type&&"lookbehind"===e.kind}function z(e){return e.replace(/[- _]+/g,"").toLowerCase()}function W(e,t){return s(e,`${t?.type==="Character"&&93===t.value?"Empty":"Unclosed"} character class`)}function q(e){return s(e,"Unclosed group")}function H(e,t,n=null){function r(e,t){for(let n=0;n<e.length;n++){let r=a(e[n],t,n,e);n=Math.max(-1,n+r)}}function a(o,i=null,l=null,u=null){let c=0,p=!1,h={node:o,parent:i,key:l,container:u,root:e,remove(){V(u).splice(Math.max(0,X(l)+c),1),c--,p=!0},removeAllNextSiblings:()=>V(u).splice(X(l)+1),removeAllPrevSiblings(){let e=X(l)+c;return c-=e,V(u).splice(0,Math.max(0,e))},replaceWith(e,t={}){let n=!!t.traverse;u?u[Math.max(0,X(l)+c)]=e:s(i,"Can't replace root node")[l]=e,n&&a(e,i,l,u),p=!0},replaceWithMultiple(e,t={}){let n=!!t.traverse;if(V(u).splice(Math.max(0,X(l)+c),1,...e),c+=e.length-1,n){let t=0;for(let n=0;n<e.length;n++)t+=a(e[n],i,X(l)+n+t,u)}p=!0},skip(){p=!0}},{type:d}=o,g=t["*"],f=t[d],m="function"==typeof g?g:g?.enter,y="function"==typeof f?f:f?.enter;if(m?.(h,n),y?.(h,n),!p)switch(d){case"AbsenceFunction":case"CapturingGroup":case"Group":case"Alternative":case"CharacterClass":case"LookaroundAssertion":r(o.body,o);break;case"Assertion":case"Backreference":case"Character":case"CharacterSet":case"Directive":case"Flags":case"NamedCallout":case"Subroutine":break;case"CharacterClassRange":a(o.min,o,"min"),a(o.max,o,"max");break;case"Quantifier":a(o.body,o,"body");break;case"Regex":r(o.body,o),a(o.flags,o,"flags");break;default:throw Error(`Unexpected node type "${d}"`)}return f?.exit?.(h,n),g?.exit?.(h,n),c}return a(e),e}function V(e){if(!Array.isArray(e))throw Error("Container expected");return e}function X(e){if("number"!=typeof e)throw Error("Numeric key expected");return e}let K=String.raw`\(\?(?:[:=!>A-Za-z\-]|<[=!]|\(DEFINE\))`,Z=Object.freeze({DEFAULT:"DEFAULT",CHAR_CLASS:"CHAR_CLASS"});function Q(e,t,n,r){let a=RegExp(String.raw`${t}|(?<$skip>\[\^?|\\?.)`,"gsu"),o=[!1],s=0,i="";for(let t of e.matchAll(a)){let{0:e,groups:{$skip:a}}=t;if(!a&&(!r||r===Z.DEFAULT==!s)){n instanceof Function?i+=n(t,{context:s?Z.CHAR_CLASS:Z.DEFAULT,negated:o[o.length-1]}):i+=n;continue}"["===e[0]?(s++,o.push("^"===e[1])):"]"===e&&s&&(s--,o.pop()),i+=e}return i}function Y(e,t,n){return!!function(e,t,n=0,r){let a;if(!RegExp(t,"su").test(e))return null;let o=RegExp(`${t}|(?<$skip>\\\\?.)`,"gsu");o.lastIndex=n;let s=0;for(;a=o.exec(e);){let{0:e,groups:{$skip:t}}=a;if(!t&&(!r||r===Z.DEFAULT==!s))return a;"["===e?s++:"]"===e&&s&&s--,o.lastIndex==a.index&&o.lastIndex++}return null}(e,t,0,n)}let J=RegExp(String.raw`(?<noncapturingStart>${K})|(?<capturingStart>\((?:\?<[^>]+>)?)|\\?.`,"gsu"),ee=String.raw`(?:[?*+]|\{\d+(?:,\d*)?\})`,et=RegExp(String.raw`
\\(?: \d+
  | c[A-Za-z]
  | [gk]<[^>]+>
  | [pPu]\{[^\}]+\}
  | u[A-Fa-f\d]{4}
  | x[A-Fa-f\d]{2}
  )
| \((?: \? (?: [:=!>]
  | <(?:[=!]|[^>]+>)
  | [A-Za-z\-]+:
  | \(DEFINE\)
  ))?
| (?<qBase>${ee})(?<qMod>[?+]?)(?<invalidQ>[?*+\{]?)
| \\?.
`.replace(/\s+/g,""),"gsu"),en=String.raw,er=en`\\g<(?<gRNameOrNum>[^>&]+)&R=(?<gRDepth>[^>]+)>`,ea=en`\(\?R=(?<rDepth>[^\)]+)\)|${er}`,eo=en`\(\?<(?![=!])(?<captureName>[^>]+)>`,es=en`${eo}|(?<unnamed>\()(?!\?)`,ei=RegExp(en`${eo}|${ea}|\(\?|\\?.`,"gsu"),el="Cannot use multiple overlapping recursions";function eu(e){let t=`Max depth must be integer between 2 and 100; used ${e}`;if(!/^[1-9]\d*$/.test(e)||(e*=1)<2||e>100)throw Error(t)}function ec(e,t,n,r,a,o,s){let i=new Set;r&&Q(e+t,eo,({groups:{captureName:e}})=>{i.add(e)},Z.DEFAULT);let l=[n,r?i:null,a,o,s];return`${e}${ep(`(?:${e}`,"forward",...l)}(?:)${ep(`${t})`,"backward",...l)}${t}`}function ep(e,t,n,r,a,o,s){let i=e=>"forward"===t?e+2:n-e+2-1,l="";for(let t=0;t<n;t++){let n=i(t);l+=Q(e,en`${es}|\\k<(?<backref>[^>]+)>`,({0:e,groups:{captureName:t,unnamed:i,backref:l}})=>{if(l&&r&&!r.has(l))return e;let u=`_$${n}`;if(i||t){let n=s+o.length+1;return o.push(n),function(e,t){for(let n=0;n<e.length;n++)e[n]>=t&&e[n]++}(a,n),i?e:`(?<${t}${u}>`}return en`\k<${l}${u}>`},Z.DEFAULT)}return l}function eh(e,t,n,r,a,o){if(e.size&&r){let s=0;Q(t,es,()=>s++,Z.DEFAULT);let i=o-s+a,l=new Map;return e.forEach((e,t)=>{let a=(r-s*n)/n,o=s*n,u=t>i+s?t+r:t,c=[];for(let t of e)if(t<=i)c.push(t);else if(t>i+s+a)c.push(t+r);else if(t<=i+s)for(let e=0;e<=n;e++)c.push(t+s*e);else for(let e=0;e<=n;e++)c.push(t+o+a*e);l.set(u,c)}),l}return e}var ed=String.fromCodePoint,eg=String.raw,ef={flagGroups:(()=>{try{RegExp("(?i:)")}catch{return!1}return!0})(),unicodeSets:(()=>{try{RegExp("","v")}catch{return!1}return!0})()};function em(e,{enable:t,disable:n}){return{dotAll:!n?.dotAll&&!!(t?.dotAll||e.dotAll),ignoreCase:!n?.ignoreCase&&!!(t?.ignoreCase||e.ignoreCase)}}function ey(e,t,n){return e.has(t)||e.set(t,n),e.get(t)}ef.bugFlagVLiteralHyphenIsRange=!!ef.unicodeSets&&(()=>{try{RegExp(eg`[\d\-a]`,"v")}catch{return!0}return!1})(),ef.bugNestedClassIgnoresNegation=ef.unicodeSets&&RegExp("[[^a]]","v").test("a");var eb={ES2025:2025,ES2024:2024,ES2018:2018},e_={auto:"auto",ES2025:"ES2025",ES2024:"ES2024",ES2018:"ES2018"};function eC(e={}){if("[object Object]"!==({}).toString.call(e))throw Error("Unexpected options");if(void 0!==e.target&&!e_[e.target])throw Error(`Unexpected target "${e.target}"`);let t={accuracy:"default",avoidSubclass:!1,flags:"",global:!1,hasIndices:!1,lazyCompileLength:1/0,target:"auto",verbose:!1,...e,rules:{allowOrphanBackrefs:!1,asciiWordBoundaries:!1,captureGroup:!1,recursionLimit:20,singleline:!1,...e.rules}};return"auto"===t.target&&(t.target=ef.flagGroups?"ES2025":ef.unicodeSets?"ES2024":"ES2018"),t}var ek=new Set([ed(304),ed(305)]),ew=eg`[\p{L}\p{M}\p{N}\p{Pc}]`;function eS(e){if(ek.has(e))return[e];let t=new Set,n=e.toLowerCase(),r=n.toUpperCase(),a=eE.get(n),o=ev.get(n),s=eA.get(n);return 1==[...r].length&&t.add(r),s&&t.add(s),a&&t.add(a),t.add(n),o&&t.add(o),[...t]}var ex=new Map(`C Other
Cc Control cntrl
Cf Format
Cn Unassigned
Co Private_Use
Cs Surrogate
L Letter
LC Cased_Letter
Ll Lowercase_Letter
Lm Modifier_Letter
Lo Other_Letter
Lt Titlecase_Letter
Lu Uppercase_Letter
M Mark Combining_Mark
Mc Spacing_Mark
Me Enclosing_Mark
Mn Nonspacing_Mark
N Number
Nd Decimal_Number digit
Nl Letter_Number
No Other_Number
P Punctuation punct
Pc Connector_Punctuation
Pd Dash_Punctuation
Pe Close_Punctuation
Pf Final_Punctuation
Pi Initial_Punctuation
Po Other_Punctuation
Ps Open_Punctuation
S Symbol
Sc Currency_Symbol
Sk Modifier_Symbol
Sm Math_Symbol
So Other_Symbol
Z Separator
Zl Line_Separator
Zp Paragraph_Separator
Zs Space_Separator
ASCII
ASCII_Hex_Digit AHex
Alphabetic Alpha
Any
Assigned
Bidi_Control Bidi_C
Bidi_Mirrored Bidi_M
Case_Ignorable CI
Cased
Changes_When_Casefolded CWCF
Changes_When_Casemapped CWCM
Changes_When_Lowercased CWL
Changes_When_NFKC_Casefolded CWKCF
Changes_When_Titlecased CWT
Changes_When_Uppercased CWU
Dash
Default_Ignorable_Code_Point DI
Deprecated Dep
Diacritic Dia
Emoji
Emoji_Component EComp
Emoji_Modifier EMod
Emoji_Modifier_Base EBase
Emoji_Presentation EPres
Extended_Pictographic ExtPict
Extender Ext
Grapheme_Base Gr_Base
Grapheme_Extend Gr_Ext
Hex_Digit Hex
IDS_Binary_Operator IDSB
IDS_Trinary_Operator IDST
ID_Continue IDC
ID_Start IDS
Ideographic Ideo
Join_Control Join_C
Logical_Order_Exception LOE
Lowercase Lower
Math
Noncharacter_Code_Point NChar
Pattern_Syntax Pat_Syn
Pattern_White_Space Pat_WS
Quotation_Mark QMark
Radical
Regional_Indicator RI
Sentence_Terminal STerm
Soft_Dotted SD
Terminal_Punctuation Term
Unified_Ideograph UIdeo
Uppercase Upper
Variation_Selector VS
White_Space space
XID_Continue XIDC
XID_Start XIDS`.split(/\s/).map(e=>[z(e),e])),ev=new Map([["s",ed(383)],[ed(383),"s"]]),eA=new Map([[ed(223),ed(7838)],[ed(107),ed(8490)],[ed(229),ed(8491)],[ed(969),ed(8486)]]),eE=new Map([eN(453),eN(456),eN(459),eN(498),...eP(8072,8079),...eP(8088,8095),...eP(8104,8111),eN(8124),eN(8140),eN(8188)]),eI=new Map([["alnum",eg`[\p{Alpha}\p{Nd}]`],["alpha",eg`\p{Alpha}`],["ascii",eg`\p{ASCII}`],["blank",eg`[\p{Zs}\t]`],["cntrl",eg`\p{Cc}`],["digit",eg`\p{Nd}`],["graph",eg`[\P{space}&&\P{Cc}&&\P{Cn}&&\P{Cs}]`],["lower",eg`\p{Lower}`],["print",eg`[[\P{space}&&\P{Cc}&&\P{Cn}&&\P{Cs}]\p{Zs}]`],["punct",eg`[\p{P}\p{S}]`],["space",eg`\p{space}`],["upper",eg`\p{Upper}`],["word",eg`[\p{Alpha}\p{M}\p{Nd}\p{Pc}]`],["xdigit",eg`\p{AHex}`]]);function eN(e){let t=ed(e);return[t.toLowerCase(),t]}function eP(e,t){return(function(e,t){let n=[];for(let r=e;r<=t;r++)n.push(r);return n})(e,t).map(e=>eN(e))}var eR=new Set(["Lower","Lowercase","Upper","Uppercase","Ll","Lowercase_Letter","Lt","Titlecase_Letter","Lu","Uppercase_Letter"]),e$={AbsenceFunction({node:e,parent:t,replaceWith:n}){let{body:r,kind:a}=e;if("repeater"===a){let e=M();e.body[0].body.push(O({negate:!0,body:r}),G("Any"));let a=M();a.body[0].body.push(T("greedy",0,1/0,e)),n(eW(a,t),{traverse:!0})}else throw Error('Unsupported absence function "(?~|"')},Alternative:{enter({node:e,parent:t,key:n},{flagDirectivesByAlt:r}){let a=e.body.filter(e=>"flags"===e.kind);for(let e=n+1;e<t.body.length;e++)ey(r,t.body[e],[]).push(...a)},exit({node:e},{flagDirectivesByAlt:t}){if(t.get(e)?.length){let n=eG(t.get(e));if(n){let t=M({flags:n});t.body[0].body=e.body,e.body=[eW(t,e)]}}}},Assertion({node:e,parent:t,key:n,container:r,root:a,remove:o,replaceWith:s},i){let{kind:l,negate:u}=e,{asciiWordBoundaries:c,avoidSubclass:p,supportedGNodes:h,wordIsAscii:d}=i;if("text_segment_boundary"===l)throw Error(`Unsupported text segment boundary "\\${u?"Y":"y"}"`);if("line_end"===l)s(eW(O({body:[E({body:[I("string_end")]}),E({body:[R(10)]})]}),t));else if("line_start"===l)s(eW(eU(eg`(?<=\A|\n(?!\z))`,{skipLookbehindValidation:!0}),t));else if("search_start"===l)if(h.has(e))a.flags.sticky=!0,o();else{let e=r[n-1];if(e&&function(e){let t=["Character","CharacterClass","CharacterSet"];return t.includes(e.type)||"Quantifier"===e.type&&e.min&&t.includes(e.body.type)}(e))s(eW(O({negate:!0}),t));else if(p)throw Error(eg`Uses "\G" in a way that requires a subclass`);else s(ez(I("string_start"),t)),i.strategy="clip_search"}else if("string_end"===l||"string_start"===l);else if("string_end_newline"===l)s(eW(eU(eg`(?=\n?\z)`),t));else if("word_boundary"===l){if(!d&&!c){let e=`(?:(?<=${ew})(?!${ew})|(?<!${ew})(?=${ew}))`,n=`(?:(?<=${ew})(?=${ew})|(?<!${ew})(?!${ew}))`;s(eW(eU(u?n:e),t))}}else throw Error(`Unexpected assertion kind "${l}"`)},Backreference({node:e},{jsGroupNameMap:t}){let{ref:n}=e;"string"!=typeof n||eF(n)||(e.ref=n=eD(n,t))},CapturingGroup({node:e},{jsGroupNameMap:t,subroutineRefMap:n}){let{name:r}=e;r&&!eF(r)&&(e.name=r=eD(r,t)),n.set(e.number,e),r&&n.set(r,e)},CharacterClassRange({node:e,parent:t,replaceWith:n}){"intersection"===t.kind&&n(eW($({body:[e]}),t),{traverse:!0})},CharacterSet({node:e,parent:t,replaceWith:n},{accuracy:r,minTargetEs2024:a,digitIsAscii:o,spaceIsAscii:s,wordIsAscii:i}){let{kind:l,negate:u,value:c}=e;if(o&&("digit"===l||"digit"===c))return void n(ez(L("digit",{negate:u}),t));if(s&&("space"===l||"space"===c))return void n(eW(ej(eU("[	-\r ]"),u),t));if(i&&("word"===l||"word"===c))return void n(ez(L("word",{negate:u}),t));if("any"===l)n(ez(G("Any"),t));else if("digit"===l)n(ez(G("Nd",{negate:u}),t));else if("dot"===l);else if("text_segment"===l){if("strict"===r)throw Error(eg`Use of "\X" requires non-strict accuracy`);let e="\\p{Emoji}(?:\\p{EMod}|\\uFE0F\\u20E3?|[\\x{E0020}-\\x{E007E}]+\\x{E007F})?",o=eg`\p{RI}{2}|${e}(?:\u200D${e})*`;n(eW(eU(eg`(?>\r\n|${a?eg`\p{RGI_Emoji}`:o}|\P{M}\p{M}*)`,{skipPropertyNameValidation:!0}),t))}else if("hex"===l)n(ez(G("AHex",{negate:u}),t));else if("newline"===l)n(eW(eU(u?"[^\n]":"(?>\r\n?|[\n\v\f\x85\u2028\u2029])"),t));else if("posix"===l)if(a||"graph"!==c&&"print"!==c)n(eW(ej(eU(eI.get(c)),u),t));else{if("strict"===r)throw Error(`POSIX class "${c}" requires min target ES2024 or non-strict accuracy`);let e={graph:"!-~",print:" -~"}[c];u&&(e=`\0-${ed(e.codePointAt(0)-1)}${ed(e.codePointAt(2)+1)}-\u{10FFFF}`),n(eW(eU(`[${e}]`),t))}else if("property"===l)ex.has(z(c))||(e.key="sc");else if("space"===l)n(ez(G("space",{negate:u}),t));else if("word"===l)n(eW(ej(eU(ew),u),t));else throw Error(`Unexpected character set kind "${l}"`)},Directive({node:e,parent:t,root:n,remove:r,replaceWith:a,removeAllPrevSiblings:o,removeAllNextSiblings:s}){let{kind:i,flags:l}=e;if("flags"===i)if(l.enable||l.disable){let e=M({flags:l});e.body[0].body=s(),a(eW(e,t),{traverse:!0})}else r();else if("keep"===i){let e=n.body[0],r=1===n.body.length&&x(e,{type:"Group"})&&1===e.body[0].body.length?e.body[0]:n;if(t.parent!==r||r.body.length>1)throw Error(eg`Uses "\K" in a way that's unsupported`);let s=O({behind:!0});s.body[0].body=o(),a(eW(s,t))}else throw Error(`Unexpected directive kind "${i}"`)},Flags({node:e,parent:t}){if(e.posixIsAscii)throw Error('Unsupported flag "P"');if("word"===e.textSegmentMode)throw Error('Unsupported flag "y{w}"');["digitIsAscii","extended","posixIsAscii","spaceIsAscii","wordIsAscii","textSegmentMode"].forEach(t=>delete e[t]),Object.assign(e,{global:!1,hasIndices:!1,multiline:!1,sticky:e.sticky??!1}),t.options={disable:{x:!0,n:!0},force:{v:!0}}},Group({node:e}){if(!e.flags)return;let{enable:t,disable:n}=e.flags;t?.extended&&delete t.extended,n?.extended&&delete n.extended,t?.dotAll&&n?.dotAll&&delete t.dotAll,t?.ignoreCase&&n?.ignoreCase&&delete t.ignoreCase,t&&!Object.keys(t).length&&delete e.flags.enable,n&&!Object.keys(n).length&&delete e.flags.disable,e.flags.enable||e.flags.disable||delete e.flags},LookaroundAssertion({node:e},t){let{kind:n}=e;"lookbehind"===n&&(t.passedLookbehind=!0)},NamedCallout({node:e,parent:t,replaceWith:n}){let{kind:r}=e;if("fail"===r)n(eW(O({negate:!0}),t));else throw Error(`Unsupported named callout "(*${r.toUpperCase()}"`)},Quantifier({node:e}){if("Quantifier"===e.body.type){let t=M();t.body[0].body.push(e.body),e.body=eW(t,e)}},Regex:{enter({node:e},{supportedGNodes:t}){let n=[],r=!1,a=!1;for(let t of e.body)if(1===t.body.length&&"search_start"===t.body[0].kind)t.body.pop();else{let e=function e(t){let n=t.find(e=>"search_start"===e.kind||function(e,t){let n={negate:null,...t};return"LookaroundAssertion"===e.type&&(null===n.negate||e.negate===n.negate)&&1===e.body.length&&x(e.body[0],{type:"Assertion",kind:"search_start"})}(e,{negate:!1})||!function({type:e}){return"Assertion"===e||"Directive"===e||"LookaroundAssertion"===e}(e));if(!n)return null;if("search_start"===n.kind)return n;if("LookaroundAssertion"===n.type)return n.body[0].body[0];if("CapturingGroup"===n.type||"Group"===n.type){let t=[];for(let r of n.body){let n=e(r.body);if(!n)return null;Array.isArray(n)?t.push(...n):t.push(n)}return t}return null}(t.body);e?(r=!0,Array.isArray(e)?n.push(...e):n.push(e)):a=!0}r&&!a&&n.forEach(e=>t.add(e))},exit(e,{accuracy:t,passedLookbehind:n,strategy:r}){if("strict"===t&&n&&r)throw Error(eg`Uses "\G" in a way that requires non-strict accuracy`)}},Subroutine({node:e},{jsGroupNameMap:t}){let{ref:n}=e;"string"!=typeof n||eF(n)||(e.ref=n=eD(n,t))}},eL={Backreference({node:e},{multiplexCapturesToLeftByRef:t,reffedNodesByReferencer:n}){let{orphan:r,ref:a}=e;r||n.set(e,[...t.get(a).map(({node:e})=>e)])},CapturingGroup:{enter({node:e,parent:t,replaceWith:n,skip:r},{groupOriginByCopy:a,groupsByName:o,multiplexCapturesToLeftByRef:s,openRefs:i,reffedNodesByReferencer:l}){let u=a.get(e);if(u&&i.has(e.number)){let r=ez(eT(e.number),t);l.set(r,i.get(e.number)),n(r);return}i.set(e.number,e),s.set(e.number,[]),e.name&&ey(s,e.name,[]);let c=s.get(e.name??e.number);for(let t=0;t<c.length;t++){let n=c[t];if(u===n.node||u&&u===n.origin||e===n.origin){c.splice(t,1);break}}if(s.get(e.number).push({node:e,origin:u}),e.name&&s.get(e.name).push({node:e,origin:u}),e.name){let t=ey(o,e.name,new Map),n=!1;if(u)n=!0;else for(let e of t.values())if(!e.hasDuplicateNameToRemove){n=!0;break}o.get(e.name).set(e,{node:e,hasDuplicateNameToRemove:n})}},exit({node:e},{openRefs:t}){t.delete(e.number)}},Group:{enter({node:e},t){t.prevFlags=t.currentFlags,e.flags&&(t.currentFlags=em(t.currentFlags,e.flags))},exit(e,t){t.currentFlags=t.prevFlags}},Subroutine({node:e,parent:t,replaceWith:n},r){let{isRecursive:a,ref:o}=e;if(a){let n=t;for(;(n=n.parent)&&("CapturingGroup"!==n.type||n.name!==o&&n.number!==o););r.reffedNodesByReferencer.set(e,n);return}let s=r.subroutineRefMap.get(o),i=0===o,l=i?eT(0):function e(t,n,r,a){let o=Array.isArray(t)?[]:{};for(let[s,i]of Object.entries(t))"parent"===s?o.parent=Array.isArray(r)?a:r:i&&"object"==typeof i?o[s]=e(i,n,o,r):("type"===s&&"CapturingGroup"===i&&n.set(o,n.get(t)??t),o[s]=i);return o}(s,r.groupOriginByCopy,null),u=l;if(!i){var c,p;let e=eG(function(e,t){let n=[];for(;e=e.parent;)(!t||t(e))&&n.push(e);return n}(s,e=>"Group"===e.type&&!!e.flags)),t=e?em(r.globalFlags,e):r.globalFlags;c=t,p=r.currentFlags,(c.dotAll!==p.dotAll||c.ignoreCase!==p.ignoreCase)&&(u=M({flags:function({dotAll:e,ignoreCase:t}){let n={};return(e||t)&&(n.enable={},e&&(n.enable.dotAll=!0),t&&(n.enable.ignoreCase=!0)),e&&t||(n.disable={},e||(n.disable.dotAll=!0),t||(n.disable.ignoreCase=!0)),n}(t)})).body[0].body.push(l)}n(eW(u,t),{traverse:!i})}},eM={Backreference({node:e,parent:t,replaceWith:n},r){if(e.orphan){r.highestOrphanBackref=Math.max(r.highestOrphanBackref,e.ref);return}let a=r.reffedNodesByReferencer.get(e).filter(t=>(function(e,t){let n=t;do{if("Regex"===n.type)return!1;if("Alternative"===n.type)continue;if(n===e)return!1;for(let t of eB(n.parent)){if(t===n)break;if(t===e||function e(t,n){for(let r of eB(t)??[])if(r===n||e(r,n))return!0;return!1}(t,e))return!0}}while(n=n.parent);throw Error("Unexpected path")})(t,e));a.length?a.length>1?n(eW(M({atomic:!0,body:a.reverse().map(e=>E({body:[N(e.number)]}))}),t)):e.ref=a[0].number:n(eW(O({negate:!0}),t))},CapturingGroup({node:e},t){e.number=++t.numCapturesToLeft,e.name&&t.groupsByName.get(e.name).get(e).hasDuplicateNameToRemove&&delete e.name},Regex:{exit({node:e},t){let n=Math.max(t.highestOrphanBackref-t.numCapturesToLeft,0);for(let t=0;t<n;t++){let t=P();e.body.at(-1).body.push(t)}}},Subroutine({node:e},t){e.isRecursive&&0!==e.ref&&(e.ref=t.reffedNodesByReferencer.get(e).number)}};function eO(e){H(e,{"*"({node:e,parent:t}){e.parent=t}})}function eT(e){let t=D(e);return t.isRecursive=!0,t}function eD(e,t){if(t.has(e))return t.get(e);let n=`$${t.size}_${e.replace(/^[^$_\p{IDS}]|[^$\u200C\u200D\p{IDC}]/ug,"_")}`;return t.set(e,n),n}function eG(e){let t=["dotAll","ignoreCase"],n={enable:{},disable:{}};return(e.forEach(({flags:e})=>{t.forEach(t=>{e.enable?.[t]&&(delete n.disable[t],n.enable[t]=!0),e.disable?.[t]&&(n.disable[t]=!0)})}),Object.keys(n.enable).length||delete n.enable,Object.keys(n.disable).length||delete n.disable,n.enable||n.disable)?n:null}function eB(e){if(!e)throw Error("Node expected");let{body:t}=e;return Array.isArray(t)?t:t?[t]:null}function eF(e){return/^[$_\p{IDS}][$\u200C\u200D\p{IDC}]*$/u.test(e)}function eU(e,t){let n=A(e,{...t,unicodePropertyMap:ex}).body;return n.length>1||n[0].body.length>1?M({body:n}):n[0].body[0]}function ej(e,t){return e.negate=t,e}function ez(e,t){return e.parent=t,e}function eW(e,t){return eO(e),e.parent=t,e}var eq={"*":{enter({node:e},t){if(e1(e)){let n=t.getCurrentModI();t.pushModI(e.flags?em({ignoreCase:n},e.flags).ignoreCase:n)}},exit({node:e},t){e1(e)&&t.popModI()}},Backreference(e,t){t.setHasCasedChar()},Character({node:e},t){eY(ed(e.value))&&t.setHasCasedChar()},CharacterClassRange({node:e,skip:t},n){t(),eJ(e,{firstOnly:!0}).length&&n.setHasCasedChar()},CharacterSet({node:e},t){"property"===e.kind&&eR.has(e.value)&&t.setHasCasedChar()}},eH={Alternative:({body:e},t,n)=>e.map(n).join(""),Assertion({kind:e,negate:t}){if("string_end"===e)return"$";if("string_start"===e)return"^";if("word_boundary"===e)return t?eg`\B`:eg`\b`;throw Error(`Unexpected assertion kind "${e}"`)},Backreference({ref:e},t){if("number"!=typeof e)throw Error("Unexpected named backref in transformed AST");if(!t.useFlagMods&&"strict"===t.accuracy&&t.currentFlags.ignoreCase&&!t.captureMap.get(e).ignoreCase)throw Error("Use of case-insensitive backref to case-sensitive group requires target ES2025 or non-strict accuracy");return"\\"+e},CapturingGroup(e,t,n){let{body:r,name:a,number:o}=e,s={ignoreCase:t.currentFlags.ignoreCase},i=t.originMap.get(e);return i&&(s.hidden=!0,o>i.number&&(s.transferTo=i.number)),t.captureMap.set(o,s),`(${a?`?<${a}>`:""}${r.map(n).join("|")})`},Character({value:e},t){let n=ed(e),r=e0(e,{escDigit:"Backreference"===t.lastNode.type,inCharClass:t.inCharClass,useFlagV:t.useFlagV});if(r!==n)return r;if(t.useAppliedIgnoreCase&&t.currentFlags.ignoreCase&&eY(n)){let e=eS(n);return t.inCharClass?e.join(""):e.length>1?`[${e.join("")}]`:e[0]}return n},CharacterClass(e,t,n){let{kind:r,negate:a,parent:o}=e,{body:s}=e;if("intersection"===r&&!t.useFlagV)throw Error("Use of class intersection requires min target ES2024");ef.bugFlagVLiteralHyphenIsRange&&t.useFlagV&&s.some(e2)&&(s=[R(45),...s.filter(e=>!e2(e))]);let i=()=>`[${a?"^":""}${s.map(n).join("intersection"===r?"&&":"")}]`;if(!t.inCharClass){if((!t.useFlagV||ef.bugNestedClassIgnoresNegation)&&!a){let t=s.filter(e=>"CharacterClass"===e.type&&"union"===e.kind&&e.negate);if(t.length){let r=M(),a=r.body[0];return r.parent=o,a.parent=r,e.body=s=s.filter(e=>!t.includes(e)),s.length?(e.parent=a,a.body.push(e)):r.body.pop(),t.forEach(e=>{let t=E({body:[e]});e.parent=t,t.parent=r,r.body.push(t)}),n(r)}}t.inCharClass=!0;let r=i();return t.inCharClass=!1,r}let l=s[0];if("union"===r&&!a&&l&&((!t.useFlagV||!t.verbose)&&"union"===o.kind&&!(ef.bugFlagVLiteralHyphenIsRange&&t.useFlagV)||!t.verbose&&"intersection"===o.kind&&1===s.length&&"CharacterClassRange"!==l.type))return s.map(n).join("");if(!t.useFlagV&&"CharacterClass"===o.type)throw Error("Use of nested character class requires min target ES2024");return i()},CharacterClassRange(e,t){let n=e.min.value,r=e.max.value,a={escDigit:!1,inCharClass:!0,useFlagV:t.useFlagV},o=e0(n,a),s=e0(r,a),i=new Set;return t.useAppliedIgnoreCase&&t.currentFlags.ignoreCase&&(function(e){let t=e.map(e=>e.codePointAt(0)).sort((e,t)=>e-t),n=[],r=null;for(let e=0;e<t.length;e++)t[e+1]===t[e]+1?r??=t[e]:null===r?n.push(t[e]):(n.push([r,t[e]]),r=null);return n})(eJ(e)).forEach(e=>{i.add(Array.isArray(e)?`${e0(e[0],a)}-${e0(e[1],a)}`:e0(e,a))}),`${o}-${s}${[...i].join("")}`},CharacterSet({kind:e,negate:t,value:n,key:r},a){if("dot"===e)return a.currentFlags.dotAll?a.appliedGlobalFlags.dotAll||a.useFlagMods?".":"[^]":eg`[^\n]`;if("digit"===e)return t?eg`\D`:eg`\d`;if("property"===e){if(a.useAppliedIgnoreCase&&a.currentFlags.ignoreCase&&eR.has(n))throw Error(`Unicode property "${n}" can't be case-insensitive when other chars have specific case`);return`${t?eg`\P`:eg`\p`}{${r?`${r}=`:""}${n}}`}if("word"===e)return t?eg`\W`:eg`\w`;throw Error(`Unexpected character set kind "${e}"`)},Flags:(e,t)=>(t.appliedGlobalFlags.ignoreCase?"i":"")+(e.dotAll?"s":"")+(e.sticky?"y":""),Group({atomic:e,body:t,flags:n,parent:r},a,o){let s=a.currentFlags;n&&(a.currentFlags=em(s,n));let i=t.map(o).join("|"),l=a.verbose||1!==t.length||"Quantifier"===r.type||e||a.useFlagMods&&n?`(?${function(e,t,n){if(e)return">";let r="";if(t&&n){let{enable:e,disable:n}=t;r=(e?.ignoreCase?"i":"")+(e?.dotAll?"s":"")+(n?"-":"")+(n?.ignoreCase?"i":"")+(n?.dotAll?"s":"")}return`${r}:`}(e,n,a.useFlagMods)}${i})`:i;return a.currentFlags=s,l},LookaroundAssertion({body:e,kind:t,negate:n},r,a){let o=`${"lookahead"===t?"":"<"}${n?"!":"="}`;return`(?${o}${e.map(a).join("|")})`},Quantifier:(e,t,n)=>n(e.body)+function({kind:e,max:t,min:n}){return(n||1!==t?n||t!==1/0?1===n&&t===1/0?"+":n===t?`{${n}}`:`{${n},${t===1/0?"":t}}`:"*":"?")+({greedy:"",lazy:"?",possessive:"+"})[e]}(e),Subroutine({isRecursive:e,ref:t},n){if(!e)throw Error("Unexpected non-recursive subroutine in transformed AST");let r=n.recursionLimit;return 0===t?`(?R=${r})`:eg`\g<${t}&R=${r}>`}},eV=new Set(["$","(",")","*","+",".","?","[","\\","]","^","{","|","}"]),eX=new Set(["-","\\","]","^","["]),eK=new Set(["(",")","-","/","[","\\","]","^","{","|","}","!","#","$","%","&","*","+",",",".",":",";","<","=",">","?","@","`","~"]),eZ=new Map([[9,eg`\t`],[10,eg`\n`],[11,eg`\v`],[12,eg`\f`],[13,eg`\r`],[8232,eg`\u2028`],[8233,eg`\u2029`],[65279,eg`\uFEFF`]]),eQ=/^\p{Cased}$/u;function eY(e){return eQ.test(e)}function eJ(e,t){let n=!!t?.firstOnly,r=e.min.value,a=e.max.value,o=[];if(r<65&&(65535===a||a>=131071)||65536===r&&a>=131071)return o;for(let e=r;e<=a;e++){let t=ed(e);if(!eY(t))continue;let s=eS(t).filter(e=>{let t=e.codePointAt(0);return t<r||t>a});if(s.length&&(o.push(...s),n))break}return o}function e0(e,{escDigit:t,inCharClass:n,useFlagV:r}){var a;if(eZ.has(e))return eZ.get(e);if(e<32||e>126&&e<160||e>262143||t&&(a=e)>47&&a<58)return e>255?`\\u{${e.toString(16).toUpperCase()}}`:`\\x${e.toString(16).toUpperCase().padStart(2,"0")}`;let o=ed(e);return((n?r?eK:eX:eV).has(o)?"\\":"")+o}function e1({type:e}){return"CapturingGroup"===e||"Group"===e||"LookaroundAssertion"===e}function e2({type:e,value:t}){return"Character"===e&&45===t}var e4=class e extends RegExp{#e=new Map;#t=null;#n;#r=null;#a=null;rawOptions={};get source(){return this.#n||"(?:)"}constructor(t,n,r){let a=!!r?.lazyCompile;if(t instanceof RegExp){if(r)throw Error("Cannot provide options when copying a regexp");super(t,n),this.#n=t.source,t instanceof e&&(this.#e=t.#e,this.#r=t.#r,this.#a=t.#a,this.rawOptions=t.rawOptions)}else{let e={hiddenCaptures:[],strategy:null,transfers:[],...r};super(a?"":t,n),this.#n=t,this.#e=function(e,t){let n=new Map;for(let t of e)n.set(t,{hidden:!0});for(let[e,r]of t)for(let t of r)ey(n,t,{}).transferTo=e;return n}(e.hiddenCaptures,e.transfers),this.#a=e.strategy,this.rawOptions=r??{}}a||(this.#t=this)}exec(t){if(!this.#t){let{lazyCompile:t,...n}=this.rawOptions;this.#t=new e(this.#n,this.flags,n)}let n=this.global||this.sticky,r=this.lastIndex;if("clip_search"===this.#a&&n&&r){this.lastIndex=0;let e=this.#o(t.slice(r));return e&&(function(e,t,n,r){if(e.index+=t,e.input=n,r){let n=e.indices;for(let e=0;e<n.length;e++){let r=n[e];r&&(n[e]=[r[0]+t,r[1]+t])}let r=n.groups;r&&Object.keys(r).forEach(e=>{let n=r[e];n&&(r[e]=[n[0]+t,n[1]+t])})}}(e,r,t,this.hasIndices),this.lastIndex+=r),e}return this.#o(t)}#o(e){let t;this.#t.lastIndex=this.lastIndex;let n=super.exec.call(this.#t,e);if(this.lastIndex=this.#t.lastIndex,!n||!this.#e.size)return n;let r=[...n];n.length=1,this.hasIndices&&(t=[...n.indices],n.indices.length=1);let a=[0];for(let e=1;e<r.length;e++){let{hidden:o,transferTo:s}=this.#e.get(e)??{};if(o?a.push(null):(a.push(n.length),n.push(r[e]),this.hasIndices&&n.indices.push(t[e])),s&&void 0!==r[e]){let o=a[s];if(!o)throw Error(`Invalid capture transfer to "${o}"`);if(n[o]=r[e],this.hasIndices&&(n.indices[o]=t[e]),n.groups){this.#r||(this.#r=function(e){let t,n=/(?<capture>\((?:\?<(?![=!])(?<name>[^>]+)>|(?!\?)))|\\?./gsu,r=new Map,a=0,o=0;for(;t=n.exec(e);){let{0:e,groups:{capture:n,name:s}}=t;"["===e?a++:a?"]"===e&&a--:n&&(o++,s&&r.set(o,s))}return r}(this.source));let a=this.#r.get(s);a&&(n.groups[a]=r[e],this.hasIndices&&(n.indices.groups[a]=t[e]))}}}return n}};function e8(e,t){let n=function(e,t){let n=eC(t),r=function(e,t){let n={accuracy:"default",asciiWordBoundaries:!1,avoidSubclass:!1,bestEffortTarget:"ES2025",...t};eO(e);let r={accuracy:n.accuracy,asciiWordBoundaries:n.asciiWordBoundaries,avoidSubclass:n.avoidSubclass,flagDirectivesByAlt:new Map,jsGroupNameMap:new Map,minTargetEs2024:eb[n.bestEffortTarget]>=eb.ES2024,passedLookbehind:!1,strategy:null,subroutineRefMap:new Map,supportedGNodes:new Set,digitIsAscii:e.flags.digitIsAscii,spaceIsAscii:e.flags.spaceIsAscii,wordIsAscii:e.flags.wordIsAscii};H(e,e$,r);let a={dotAll:e.flags.dotAll,ignoreCase:e.flags.ignoreCase},o={currentFlags:a,prevFlags:null,globalFlags:a,groupOriginByCopy:new Map,groupsByName:new Map,multiplexCapturesToLeftByRef:new Map,openRefs:new Map,reffedNodesByReferencer:new Map,subroutineRefMap:r.subroutineRefMap};return H(e,eL,o),H(e,eM,{groupsByName:o.groupsByName,highestOrphanBackref:0,numCapturesToLeft:0,reffedNodesByReferencer:o.reffedNodesByReferencer}),e._originMap=o.groupOriginByCopy,e._strategy=r.strategy,e}(A(e,{flags:n.flags,normalizeUnknownPropertyNames:!0,rules:{captureGroup:n.rules.captureGroup,singleline:n.rules.singleline},skipBackrefValidation:n.rules.allowOrphanBackrefs,unicodePropertyMap:ex}),{accuracy:n.accuracy,asciiWordBoundaries:n.rules.asciiWordBoundaries,avoidSubclass:n.avoidSubclass,bestEffortTarget:n.target}),a=function(e,t){let n=eC(t),r=eb[n.target]>=eb.ES2024,a=eb[n.target]>=eb.ES2025,o=n.rules.recursionLimit;if(!Number.isInteger(o)||o<2||o>20)throw Error("Invalid recursionLimit; use 2-20");let s=null,i=null;if(!a){let t=[e.flags.ignoreCase];H(e,eq,{getCurrentModI:()=>t.at(-1),popModI(){t.pop()},pushModI(e){t.push(e)},setHasCasedChar(){t.at(-1)?s=!0:i=!0}})}let l={dotAll:e.flags.dotAll,ignoreCase:!!((e.flags.ignoreCase||s)&&!i)},u=e,c={accuracy:n.accuracy,appliedGlobalFlags:l,captureMap:new Map,currentFlags:{dotAll:e.flags.dotAll,ignoreCase:e.flags.ignoreCase},inCharClass:!1,lastNode:u,originMap:e._originMap,recursionLimit:o,useAppliedIgnoreCase:!!(!a&&s&&i),useFlagMods:a,useFlagV:r,verbose:n.verbose};function p(e){return c.lastNode=u,u=e,(function(e,t){if(null==e)throw Error(t??"Value expected");return e})(eH[e.type],`Unexpected node type "${e.type}"`)(e,c,p)}let h={pattern:e.body.map(p).join("|"),flags:p(e.flags),options:{...e.options}};return r||(delete h.options.force.v,h.options.disable.v=!0,h.options.unicodeSetsPlugin=null),h._captureTransfers=new Map,h._hiddenCaptures=[],c.captureMap.forEach((e,t)=>{e.hidden&&h._hiddenCaptures.push(t),e.transferTo&&ey(h._captureTransfers,e.transferTo,[]).push(t)}),h}(r,n),o=function(e,t){let n,{hiddenCaptures:r,mode:a}={hiddenCaptures:[],mode:"plugin",...t},o=t?.captureTransfers??new Map;if(!RegExp(ea,"su").test(e))return{pattern:e,captureTransfers:o,hiddenCaptures:r};if("plugin"===a&&Y(e,en`\(\?\(DEFINE\)`,Z.DEFAULT))throw Error("DEFINE groups cannot be used with recursion");let s=[],i=Y(e,en`\\[1-9]`,Z.DEFAULT),l=new Map,u=[],c=!1,p=0,h=0;for(ei.lastIndex=0;n=ei.exec(e);){let{0:t,groups:{captureName:d,rDepth:g,gRNameOrNum:f,gRDepth:m}}=n;if("["===t)p++;else if(p)"]"===t&&p--;else if(g){if(eu(g),c)throw Error(el);if(i)throw Error(`${"external"===a?"Backrefs":"Numbered backrefs"} cannot be used with global recursion`);let t=e.slice(0,n.index),l=e.slice(ei.lastIndex);if(Y(l,ea,Z.DEFAULT))throw Error(el);let u=g-1;e=ec(t,l,u,!1,r,s,h),o=eh(o,t,u,s.length,0,h);break}else if(f){eu(m);let p=!1;for(let e of u)if(e.name===f||e.num===+f){if(p=!0,e.hasRecursedWithin)throw Error(el);break}if(!p)throw Error(en`Recursive \g cannot be used outside the referenced group "${"external"===a?f:en`\g<${f}&R=${m}>`}"`);let d=l.get(f),g=function(e,t){let n,r=/\\?./gsu;r.lastIndex=t;let a=e.length,o=0,s=1;for(;n=r.exec(e);){let[e]=n;if("["===e)o++;else if(o)"]"===e&&o--;else if("("===e)s++;else if(")"===e&&!--s){a=n.index;break}}return e.slice(t,a)}(e,d);if(i&&Y(g,en`${eo}|\((?!\?)`,Z.DEFAULT))throw Error(`${"external"===a?"Backrefs":"Numbered backrefs"} cannot be used with recursion of capturing groups`);let y=e.slice(d,n.index),b=g.slice(y.length+t.length),_=s.length,C=m-1,k=ec(y,b,C,!0,r,s,h);o=eh(o,y,C,s.length-_,_,h);let w=e.slice(0,d),S=e.slice(d+g.length);e=`${w}${k}${S}`,ei.lastIndex+=k.length-t.length-y.length-b.length,u.forEach(e=>e.hasRecursedWithin=!0),c=!0}else if(d)h++,l.set(String(h),ei.lastIndex),l.set(d,ei.lastIndex),u.push({num:h,name:d});else if("("===t[0]){let e="("===t;e&&(h++,l.set(String(h),ei.lastIndex)),u.push(e?{num:h}:{})}else")"===t&&u.pop()}return r.push(...s),{pattern:e,captureTransfers:o,hiddenCaptures:r}}(a.pattern,{captureTransfers:a._captureTransfers,hiddenCaptures:a._hiddenCaptures,mode:"external"}),s=function(e,t){let n,r=t?.hiddenCaptures??[],a=t?.captureTransfers??new Map;if(!/\(\?>/.test(e))return{pattern:e,captureTransfers:a,hiddenCaptures:r};let o="(?:(?=(",s=[0],i=[],l=0,u=0,c=NaN;do{let t;n=!1;let p=0,h=0,d=!1;for(J.lastIndex=Number.isNaN(c)?0:c+o.length;t=J.exec(e);){let{0:g,index:f,groups:{capturingStart:m,noncapturingStart:y}}=t;if("["===g)p++;else if(p)"]"===g&&p--;else if("(?>"!==g||d){if(d&&y)h++;else if(m)d?h++:(l++,s.push(l+u));else if(")"===g&&d){if(!h){let t=l+ ++u;e=`${e.slice(0,c)}${o}${e.slice(c+3,f)}))<$$${t}>)${e.slice(f+1)}`,n=!0,i.push(t);for(let e=0;e<r.length;e++)r[e]>=t&&r[e]++;if(a.size){let e=new Map;a.forEach((n,r)=>{e.set(r>=t?r+1:r,n.map(e=>e>=t?e+1:e))}),a=e}break}h--}}else c=f,d=!0}}while(n);return r.push(...i),{pattern:e=Q(e,String.raw`\\(?<backrefNum>[1-9]\d*)|<\$\$(?<wrappedBackrefNum>\d+)>`,({0:e,groups:{backrefNum:t,wrappedBackrefNum:n}})=>{if(t){let n=+t;if(n>s.length-1)throw Error(`Backref "${e}" greater than number of captures`);return`\\${s[n]}`}return`\\${n}`},Z.DEFAULT),captureTransfers:a,hiddenCaptures:r}}(function(e){let t;if(!RegExp(`${ee}\\+`).test(e))return{pattern:e};let n=[],r=null,a=null,o="",s=0;for(et.lastIndex=0;t=et.exec(e);){let{0:u,index:c,groups:{qBase:p,qMod:h,invalidQ:d}}=t;if("["===u)s||(a=c),s++;else if("]"===u)s?s--:a=null;else if(!s)if("+"===h&&o&&!o.startsWith("(")){if(d)throw Error(`Invalid quantifier "${u}"`);let t=-1;if(/^\{\d+\}$/.test(p)){var i,l;i=e,l=c+p.length,e=i.slice(0,l)+""+i.slice(l+h.length)}else{if(")"===o||"]"===o){let t=")"===o?r:a;if(null===t)throw Error(`Invalid unmatched "${o}"`);e=`${e.slice(0,t)}(?>${e.slice(t,c)}${p})${e.slice(c+u.length)}`}else e=`${e.slice(0,c-o.length)}(?>${o}${p})${e.slice(c+u.length)}`;t+=4}et.lastIndex+=t}else"("===u[0]?n.push(c):")"===u&&(r=n.length?n.pop():null);o=u}return{pattern:e}}(o.pattern).pattern,{captureTransfers:o.captureTransfers,hiddenCaptures:o.hiddenCaptures}),i={pattern:s.pattern,flags:`${n.hasIndices?"d":""}${n.global?"g":""}${a.flags}${a.options.disable.v?"u":"v"}`};if(n.avoidSubclass){if(n.lazyCompileLength!==1/0)throw Error("Lazy compilation requires subclass")}else{let e=s.hiddenCaptures.sort((e,t)=>e-t),t=Array.from(s.captureTransfers),a=r._strategy,o=i.pattern.length>=n.lazyCompileLength;(e.length||t.length||a||o)&&(i.options={...e.length&&{hiddenCaptures:e},...t.length&&{transfers:t},...a&&{strategy:a},...o&&{lazyCompile:o}})}return i}(e,t);return n.options?new e4(n.pattern,n.flags,n.options):new RegExp(n.pattern,n.flags)}},32264:(e,t,n)=>{n.d(t,{m:()=>a});let r=/[ \t\n\f\r]/g;function a(e){return"object"==typeof e?"text"===e.type&&o(e.value):o(e)}function o(e){return""===e.replace(r,"")}},45675:(e,t,n)=>{let r;n.d(t,{V:()=>em});var a={};n.r(a),n.d(a,{boolean:()=>p,booleanish:()=>h,commaOrSpaceSeparated:()=>y,commaSeparated:()=>m,number:()=>g,overloadedBoolean:()=>d,spaceSeparated:()=>f});let o=["area","base","basefont","bgsound","br","col","command","embed","frame","hr","image","img","input","keygen","link","meta","param","source","track","wbr"];class s{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}}function i(e,t){let n={},r={};for(let t of e)Object.assign(n,t.property),Object.assign(r,t.normal);return new s(n,r,t)}function l(e){return e.toLowerCase()}s.prototype.normal={},s.prototype.property={},s.prototype.space=void 0;class u{constructor(e,t){this.attribute=t,this.property=e}}u.prototype.attribute="",u.prototype.booleanish=!1,u.prototype.boolean=!1,u.prototype.commaOrSpaceSeparated=!1,u.prototype.commaSeparated=!1,u.prototype.defined=!1,u.prototype.mustUseProperty=!1,u.prototype.number=!1,u.prototype.overloadedBoolean=!1,u.prototype.property="",u.prototype.spaceSeparated=!1,u.prototype.space=void 0;let c=0,p=b(),h=b(),d=b(),g=b(),f=b(),m=b(),y=b();function b(){return 2**++c}let _=Object.keys(a);class C extends u{constructor(e,t,n,r){let o=-1;if(super(e,t),function(e,t,n){n&&(e[t]=n)}(this,"space",r),"number"==typeof n)for(;++o<_.length;){let e=_[o];!function(e,t,n){n&&(e[t]=n)}(this,_[o],(n&a[e])===a[e])}}}function k(e){let t={},n={};for(let[r,a]of Object.entries(e.properties)){let o=new C(r,e.transform(e.attributes||{},r),a,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(o.mustUseProperty=!0),t[r]=o,n[l(r)]=r,n[l(o.attribute)]=r}return new s(t,n,e.space)}C.prototype.defined=!0;let w=k({properties:{ariaActiveDescendant:null,ariaAtomic:h,ariaAutoComplete:null,ariaBusy:h,ariaChecked:h,ariaColCount:g,ariaColIndex:g,ariaColSpan:g,ariaControls:f,ariaCurrent:null,ariaDescribedBy:f,ariaDetails:null,ariaDisabled:h,ariaDropEffect:f,ariaErrorMessage:null,ariaExpanded:h,ariaFlowTo:f,ariaGrabbed:h,ariaHasPopup:null,ariaHidden:h,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:f,ariaLevel:g,ariaLive:null,ariaModal:h,ariaMultiLine:h,ariaMultiSelectable:h,ariaOrientation:null,ariaOwns:f,ariaPlaceholder:null,ariaPosInSet:g,ariaPressed:h,ariaReadOnly:h,ariaRelevant:null,ariaRequired:h,ariaRoleDescription:f,ariaRowCount:g,ariaRowIndex:g,ariaRowSpan:g,ariaSelected:h,ariaSetSize:g,ariaSort:null,ariaValueMax:g,ariaValueMin:g,ariaValueNow:g,ariaValueText:null,role:null},transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase()});function S(e,t){return t in e?e[t]:t}function x(e,t){return S(e,t.toLowerCase())}let v=k({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:m,acceptCharset:f,accessKey:f,action:null,allow:null,allowFullScreen:p,allowPaymentRequest:p,allowUserMedia:p,alt:null,as:null,async:p,autoCapitalize:null,autoComplete:f,autoFocus:p,autoPlay:p,blocking:f,capture:null,charSet:null,checked:p,cite:null,className:f,cols:g,colSpan:null,content:null,contentEditable:h,controls:p,controlsList:f,coords:g|m,crossOrigin:null,data:null,dateTime:null,decoding:null,default:p,defer:p,dir:null,dirName:null,disabled:p,download:d,draggable:h,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:p,formTarget:null,headers:f,height:g,hidden:d,high:g,href:null,hrefLang:null,htmlFor:f,httpEquiv:f,id:null,imageSizes:null,imageSrcSet:null,inert:p,inputMode:null,integrity:null,is:null,isMap:p,itemId:null,itemProp:f,itemRef:f,itemScope:p,itemType:f,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:p,low:g,manifest:null,max:null,maxLength:g,media:null,method:null,min:null,minLength:g,multiple:p,muted:p,name:null,nonce:null,noModule:p,noValidate:p,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:p,optimum:g,pattern:null,ping:f,placeholder:null,playsInline:p,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:p,referrerPolicy:null,rel:f,required:p,reversed:p,rows:g,rowSpan:g,sandbox:f,scope:null,scoped:p,seamless:p,selected:p,shadowRootClonable:p,shadowRootDelegatesFocus:p,shadowRootMode:null,shape:null,size:g,sizes:null,slot:null,span:g,spellCheck:h,src:null,srcDoc:null,srcLang:null,srcSet:null,start:g,step:null,style:null,tabIndex:g,target:null,title:null,translate:null,type:null,typeMustMatch:p,useMap:null,value:h,width:g,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:f,axis:null,background:null,bgColor:null,border:g,borderColor:null,bottomMargin:g,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:p,declare:p,event:null,face:null,frame:null,frameBorder:null,hSpace:g,leftMargin:g,link:null,longDesc:null,lowSrc:null,marginHeight:g,marginWidth:g,noResize:p,noHref:p,noShade:p,noWrap:p,object:null,profile:null,prompt:null,rev:null,rightMargin:g,rules:null,scheme:null,scrolling:h,standby:null,summary:null,text:null,topMargin:g,valueType:null,version:null,vAlign:null,vLink:null,vSpace:g,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:p,disableRemotePlayback:p,prefix:null,property:null,results:g,security:null,unselectable:null},space:"html",transform:x}),A=k({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:y,accentHeight:g,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:g,amplitude:g,arabicForm:null,ascent:g,attributeName:null,attributeType:null,azimuth:g,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:g,by:null,calcMode:null,capHeight:g,className:f,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:g,diffuseConstant:g,direction:null,display:null,dur:null,divisor:g,dominantBaseline:null,download:p,dx:null,dy:null,edgeMode:null,editable:null,elevation:g,enableBackground:null,end:null,event:null,exponent:g,externalResourcesRequired:null,fill:null,fillOpacity:g,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:m,g2:m,glyphName:m,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:g,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:g,horizOriginX:g,horizOriginY:g,id:null,ideographic:g,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:g,k:g,k1:g,k2:g,k3:g,k4:g,kernelMatrix:y,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:g,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:g,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:g,overlineThickness:g,paintOrder:null,panose1:null,path:null,pathLength:g,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:f,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:g,pointsAtY:g,pointsAtZ:g,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:y,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:y,rev:y,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:y,requiredFeatures:y,requiredFonts:y,requiredFormats:y,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:g,specularExponent:g,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:g,strikethroughThickness:g,string:null,stroke:null,strokeDashArray:y,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:g,strokeOpacity:g,strokeWidth:null,style:null,surfaceScale:g,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:y,tabIndex:g,tableValues:null,target:null,targetX:g,targetY:g,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:y,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:g,underlineThickness:g,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:g,values:null,vAlphabetic:g,vMathematical:g,vectorEffect:null,vHanging:g,vIdeographic:g,version:null,vertAdvY:g,vertOriginX:g,vertOriginY:g,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:g,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:S}),E=k({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase()}),I=k({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:x}),N=k({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase()}),P=i([w,v,E,I,N],"html"),R=i([w,A,E,I,N],"svg"),$={}.hasOwnProperty,L=["AElig","AMP","Aacute","Acirc","Agrave","Aring","Atilde","Auml","COPY","Ccedil","ETH","Eacute","Ecirc","Egrave","Euml","GT","Iacute","Icirc","Igrave","Iuml","LT","Ntilde","Oacute","Ocirc","Ograve","Oslash","Otilde","Ouml","QUOT","REG","THORN","Uacute","Ucirc","Ugrave","Uuml","Yacute","aacute","acirc","acute","aelig","agrave","amp","aring","atilde","auml","brvbar","ccedil","cedil","cent","copy","curren","deg","divide","eacute","ecirc","egrave","eth","euml","frac12","frac14","frac34","gt","iacute","icirc","iexcl","igrave","iquest","iuml","laquo","lt","macr","micro","middot","nbsp","not","ntilde","oacute","ocirc","ograve","ordf","ordm","oslash","otilde","ouml","para","plusmn","pound","quot","raquo","reg","sect","shy","sup1","sup2","sup3","szlig","thorn","times","uacute","ucirc","ugrave","uml","uuml","yacute","yen","yuml"],M={nbsp:"\xa0",iexcl:"\xa1",cent:"\xa2",pound:"\xa3",curren:"\xa4",yen:"\xa5",brvbar:"\xa6",sect:"\xa7",uml:"\xa8",copy:"\xa9",ordf:"\xaa",laquo:"\xab",not:"\xac",shy:"\xad",reg:"\xae",macr:"\xaf",deg:"\xb0",plusmn:"\xb1",sup2:"\xb2",sup3:"\xb3",acute:"\xb4",micro:"\xb5",para:"\xb6",middot:"\xb7",cedil:"\xb8",sup1:"\xb9",ordm:"\xba",raquo:"\xbb",frac14:"\xbc",frac12:"\xbd",frac34:"\xbe",iquest:"\xbf",Agrave:"\xc0",Aacute:"\xc1",Acirc:"\xc2",Atilde:"\xc3",Auml:"\xc4",Aring:"\xc5",AElig:"\xc6",Ccedil:"\xc7",Egrave:"\xc8",Eacute:"\xc9",Ecirc:"\xca",Euml:"\xcb",Igrave:"\xcc",Iacute:"\xcd",Icirc:"\xce",Iuml:"\xcf",ETH:"\xd0",Ntilde:"\xd1",Ograve:"\xd2",Oacute:"\xd3",Ocirc:"\xd4",Otilde:"\xd5",Ouml:"\xd6",times:"\xd7",Oslash:"\xd8",Ugrave:"\xd9",Uacute:"\xda",Ucirc:"\xdb",Uuml:"\xdc",Yacute:"\xdd",THORN:"\xde",szlig:"\xdf",agrave:"\xe0",aacute:"\xe1",acirc:"\xe2",atilde:"\xe3",auml:"\xe4",aring:"\xe5",aelig:"\xe6",ccedil:"\xe7",egrave:"\xe8",eacute:"\xe9",ecirc:"\xea",euml:"\xeb",igrave:"\xec",iacute:"\xed",icirc:"\xee",iuml:"\xef",eth:"\xf0",ntilde:"\xf1",ograve:"\xf2",oacute:"\xf3",ocirc:"\xf4",otilde:"\xf5",ouml:"\xf6",divide:"\xf7",oslash:"\xf8",ugrave:"\xf9",uacute:"\xfa",ucirc:"\xfb",uuml:"\xfc",yacute:"\xfd",thorn:"\xfe",yuml:"\xff",fnof:"ƒ",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",bull:"•",hellip:"…",prime:"′",Prime:"″",oline:"‾",frasl:"⁄",weierp:"℘",image:"ℑ",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦",quot:'"',amp:"&",lt:"<",gt:">",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",circ:"ˆ",tilde:"˜",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",permil:"‰",lsaquo:"‹",rsaquo:"›",euro:"€"},O=["cent","copy","divide","gt","lt","not","para","times"],T={}.hasOwnProperty,D={};for(r in M)T.call(M,r)&&(D[M[r]]=r);function G(e,t,n){let r,a=function(e,t,n){let r="&#x"+e.toString(16).toUpperCase();return n&&t&&!/[\dA-Fa-f]/.test(String.fromCharCode(t))?r:r+";"}(e,t,n.omitOptionalSemicolons);if((n.useNamedReferences||n.useShortestReferences)&&(r=function(e,t,n,r){let a=String.fromCharCode(e);if(T.call(D,a)){let e=D[a],o="&"+e;return n&&L.includes(e)&&!O.includes(e)&&(!r||t&&61!==t&&/[^\da-z]/i.test(String.fromCharCode(t)))?o:o+";"}return""}(e,t,n.omitOptionalSemicolons,n.attribute)),(n.useShortestReferences||!r)&&n.useShortestReferences){let r=function(e,t,n){let r="&#"+String(e);return n&&t&&!/\d/.test(String.fromCharCode(t))?r:r+";"}(e,t,n.omitOptionalSemicolons);r.length<a.length&&(a=r)}return r&&(!n.useShortestReferences||r.length<a.length)?r:a}function B(e,t){var n=e,r=Object.assign({format:G},t);if(n=n.replace(r.subset?function(e){let t=[],n=-1;for(;++n<e.length;)t.push(e[n].replace(/[|\\{}()[\]^$+*?.]/g,"\\$&"));return RegExp("(?:"+t.join("|")+")","g")}(r.subset):/["&'<>`]/g,a),r.subset||r.escapeOnly)return n;return n.replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,function(e,t,n){return r.format((e.charCodeAt(0)-55296)*1024+e.charCodeAt(1)-56320+65536,n.charCodeAt(t+2),r)}).replace(/[\x01-\t\v\f\x0E-\x1F\x7F\x81\x8D\x8F\x90\x9D\xA0-\uFFFF]/g,a);function a(e,t,n){return r.format(e.charCodeAt(0),n.charCodeAt(t+1),r)}}let F=/^>|^->|<!--|-->|--!>|<!-$/g,U=[">"],j=["<",">"];var z=n(77263),W=n(48249);let q=/[A-Z]/g,H=/-[a-z]/g,V=/^data[-\w.:]+$/i;function X(e){return"-"+e.toLowerCase()}function K(e){return e.charAt(1).toUpperCase()}var Z=n(46450),Q=n(32264);let Y=et(1),J=et(-1),ee=[];function et(e){return function(t,n,r){let a=t?t.children:ee,o=(n||0)+e,s=a[o];if(!r)for(;s&&(0,Q.m)(s);)o+=e,s=a[o];return s}}let en={}.hasOwnProperty;function er(e){return function(t,n,r){return en.call(e,t.tagName)&&e[t.tagName](t,n,r)}}let ea=er({body:function(e,t,n){let r=Y(n,t);return!r||"comment"!==r.type},caption:eo,colgroup:eo,dd:function(e,t,n){let r=Y(n,t);return!r||"element"===r.type&&("dt"===r.tagName||"dd"===r.tagName)},dt:function(e,t,n){let r=Y(n,t);return!!(r&&"element"===r.type&&("dt"===r.tagName||"dd"===r.tagName))},head:eo,html:function(e,t,n){let r=Y(n,t);return!r||"comment"!==r.type},li:function(e,t,n){let r=Y(n,t);return!r||"element"===r.type&&"li"===r.tagName},optgroup:function(e,t,n){let r=Y(n,t);return!r||"element"===r.type&&"optgroup"===r.tagName},option:function(e,t,n){let r=Y(n,t);return!r||"element"===r.type&&("option"===r.tagName||"optgroup"===r.tagName)},p:function(e,t,n){let r=Y(n,t);return r?"element"===r.type&&("address"===r.tagName||"article"===r.tagName||"aside"===r.tagName||"blockquote"===r.tagName||"details"===r.tagName||"div"===r.tagName||"dl"===r.tagName||"fieldset"===r.tagName||"figcaption"===r.tagName||"figure"===r.tagName||"footer"===r.tagName||"form"===r.tagName||"h1"===r.tagName||"h2"===r.tagName||"h3"===r.tagName||"h4"===r.tagName||"h5"===r.tagName||"h6"===r.tagName||"header"===r.tagName||"hgroup"===r.tagName||"hr"===r.tagName||"main"===r.tagName||"menu"===r.tagName||"nav"===r.tagName||"ol"===r.tagName||"p"===r.tagName||"pre"===r.tagName||"section"===r.tagName||"table"===r.tagName||"ul"===r.tagName):!n||"element"!==n.type||"a"!==n.tagName&&"audio"!==n.tagName&&"del"!==n.tagName&&"ins"!==n.tagName&&"map"!==n.tagName&&"noscript"!==n.tagName&&"video"!==n.tagName},rp:es,rt:es,tbody:function(e,t,n){let r=Y(n,t);return!r||"element"===r.type&&("tbody"===r.tagName||"tfoot"===r.tagName)},td:ei,tfoot:function(e,t,n){return!Y(n,t)},th:ei,thead:function(e,t,n){let r=Y(n,t);return!!(r&&"element"===r.type&&("tbody"===r.tagName||"tfoot"===r.tagName))},tr:function(e,t,n){let r=Y(n,t);return!r||"element"===r.type&&"tr"===r.tagName}});function eo(e,t,n){let r=Y(n,t,!0);return!r||"comment"!==r.type&&!("text"===r.type&&(0,Q.m)(r.value.charAt(0)))}function es(e,t,n){let r=Y(n,t);return!r||"element"===r.type&&("rp"===r.tagName||"rt"===r.tagName)}function ei(e,t,n){let r=Y(n,t);return!r||"element"===r.type&&("td"===r.tagName||"th"===r.tagName)}let el=er({body:function(e){let t=Y(e,-1,!0);return!t||"comment"!==t.type&&!("text"===t.type&&(0,Q.m)(t.value.charAt(0)))&&("element"!==t.type||"meta"!==t.tagName&&"link"!==t.tagName&&"script"!==t.tagName&&"style"!==t.tagName&&"template"!==t.tagName)},colgroup:function(e,t,n){let r=J(n,t),a=Y(e,-1,!0);return!(n&&r&&"element"===r.type&&"colgroup"===r.tagName&&ea(r,n.children.indexOf(r),n))&&!!(a&&"element"===a.type&&"col"===a.tagName)},head:function(e){let t=new Set;for(let n of e.children)if("element"===n.type&&("base"===n.tagName||"title"===n.tagName)){if(t.has(n.tagName))return!1;t.add(n.tagName)}let n=e.children[0];return!n||"element"===n.type},html:function(e){let t=Y(e,-1);return!t||"comment"!==t.type},tbody:function(e,t,n){let r=J(n,t),a=Y(e,-1);return!(n&&r&&"element"===r.type&&("thead"===r.tagName||"tbody"===r.tagName)&&ea(r,n.children.indexOf(r),n))&&!!(a&&"element"===a.type&&"tr"===a.tagName)}}),eu={name:[["	\n\f\r &/=>".split(""),"	\n\f\r \"&'/=>`".split("")],["\0	\n\f\r \"&'/<=>".split(""),"\0	\n\f\r \"&'/<=>`".split("")]],unquoted:[["	\n\f\r &>".split(""),"\0	\n\f\r \"&'<=>`".split("")],["\0	\n\f\r \"&'<=>`".split(""),"\0	\n\f\r \"&'<=>`".split("")]],single:[["&'".split(""),"\"&'`".split("")],["\0&'".split(""),"\0\"&'`".split("")]],double:[['"&'.split(""),"\"&'`".split("")],['\0"&'.split(""),"\0\"&'`".split("")]]},ec=["<","&"];function ep(e,t,n,r){return n&&"element"===n.type&&("script"===n.tagName||"style"===n.tagName)?e.value:B(e.value,Object.assign({},r.settings.characterReferences,{subset:ec}))}let eh=function(e,t){let n=t||{};function r(t,...n){let a=r.invalid,o=r.handlers;if(t&&$.call(t,e)){let n=String(t[e]);a=$.call(o,n)?o[n]:r.unknown}if(a)return a.call(this,t,...n)}return r.handlers=n.handlers||{},r.invalid=n.invalid,r.unknown=n.unknown,r}("type",{invalid:function(e){throw Error("Expected node, not `"+e+"`")},unknown:function(e){throw Error("Cannot compile unknown node `"+e.type+"`")},handlers:{comment:function(e,t,n,r){return r.settings.bogusComments?"<?"+B(e.value,Object.assign({},r.settings.characterReferences,{subset:U}))+">":"\x3c!--"+e.value.replace(F,function(e){return B(e,Object.assign({},r.settings.characterReferences,{subset:j}))})+"--\x3e"},doctype:function(e,t,n,r){return"<!"+(r.settings.upperDoctype?"DOCTYPE":"doctype")+(r.settings.tightDoctype?"":" ")+"html>"},element:function(e,t,n,r){let a,o=r.schema,s="svg"!==o.space&&r.settings.omitOptionalTags,i="svg"===o.space?r.settings.closeEmptyElements:r.settings.voids.includes(e.tagName.toLowerCase()),c=[];"html"===o.space&&"svg"===e.tagName&&(r.schema=R);let p=function(e,t){let n,r=[],a=-1;if(t){for(n in t)if(null!==t[n]&&void 0!==t[n]){let a=function(e,t,n){let r,a=function(e,t){let n=l(t),r=t,a=u;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&V.test(t)){if("-"===t.charAt(4)){let e=t.slice(5).replace(H,K);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{let e=t.slice(4);if(!H.test(e)){let n=e.replace(q,X);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}a=C}return new a(r,t)}(e.schema,t),o=e.settings.allowParseErrors&&"html"===e.schema.space?0:1,s=+!e.settings.allowDangerousCharacters,i=e.quote;if(a.overloadedBoolean&&(n===a.attribute||""===n)?n=!0:(a.boolean||a.overloadedBoolean)&&("string"!=typeof n||n===a.attribute||""===n)&&(n=!!n),null==n||!1===n||"number"==typeof n&&Number.isNaN(n))return"";let c=B(a.attribute,Object.assign({},e.settings.characterReferences,{subset:eu.name[o][s]}));return!0===n||(n=Array.isArray(n)?(a.commaSeparated?W.A:Z.A)(n,{padLeft:!e.settings.tightCommaSeparatedLists}):String(n),e.settings.collapseEmptyAttributes&&!n)?c:(e.settings.preferUnquoted&&(r=B(n,Object.assign({},e.settings.characterReferences,{attribute:!0,subset:eu.unquoted[o][s]}))),r!==n&&(e.settings.quoteSmart&&(0,z.D)(n,i)>(0,z.D)(n,e.alternative)&&(i=e.alternative),r=i+B(n,Object.assign({},e.settings.characterReferences,{subset:("'"===i?eu.single:eu.double)[o][s],attribute:!0}))+i),c+(r?"="+r:r))}(e,n,t[n]);a&&r.push(a)}}for(;++a<r.length;){let t=e.settings.tightAttributes?r[a].charAt(r[a].length-1):void 0;a!==r.length-1&&'"'!==t&&"'"!==t&&(r[a]+=" ")}return r.join("")}(r,e.properties),h=r.all("html"===o.space&&"template"===e.tagName?e.content:e);return r.schema=o,h&&(i=!1),!p&&s&&el(e,t,n)||(c.push("<",e.tagName,p?" "+p:""),i&&("svg"===o.space||r.settings.closeSelfClosing)&&(a=p.charAt(p.length-1),(!r.settings.tightSelfClosing||"/"===a||a&&'"'!==a&&"'"!==a)&&c.push(" "),c.push("/")),c.push(">")),c.push(h),i||s&&ea(e,t,n)||c.push("</"+e.tagName+">"),c.join("")},raw:function(e,t,n,r){return r.settings.allowDangerousHtml?e.value:ep(e,t,n,r)},root:function(e,t,n,r){return r.all(e)},text:ep}}),ed={},eg={},ef=[];function em(e,t){let n=t||ed,r=n.quote||'"';if('"'!==r&&"'"!==r)throw Error("Invalid quote `"+r+"`, expected `'` or `\"`");return({one:ey,all:eb,settings:{omitOptionalTags:n.omitOptionalTags||!1,allowParseErrors:n.allowParseErrors||!1,allowDangerousCharacters:n.allowDangerousCharacters||!1,quoteSmart:n.quoteSmart||!1,preferUnquoted:n.preferUnquoted||!1,tightAttributes:n.tightAttributes||!1,upperDoctype:n.upperDoctype||!1,tightDoctype:n.tightDoctype||!1,bogusComments:n.bogusComments||!1,tightCommaSeparatedLists:n.tightCommaSeparatedLists||!1,tightSelfClosing:n.tightSelfClosing||!1,collapseEmptyAttributes:n.collapseEmptyAttributes||!1,allowDangerousHtml:n.allowDangerousHtml||!1,voids:n.voids||o,characterReferences:n.characterReferences||eg,closeSelfClosing:n.closeSelfClosing||!1,closeEmptyElements:n.closeEmptyElements||!1},schema:"svg"===n.space?R:P,quote:r,alternative:'"'===r?"'":'"'}).one(Array.isArray(e)?{type:"root",children:e}:e,void 0,void 0)}function ey(e,t,n){return eh(e,t,n,this)}function eb(e){let t=[],n=e&&e.children||ef,r=-1;for(;++r<n.length;)t[r]=this.one(n[r],r,e);return t.join("")}},46450:(e,t,n)=>{n.d(t,{A:()=>r});function r(e){return e.join(" ").trim()}},48249:(e,t,n)=>{n.d(t,{A:()=>r});function r(e,t){let n=t||{};return(""===e[e.length-1]?[...e,""]:e).join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}},65905:(e,t,n)=>{n.d(t,{DI:()=>eg,OR:()=>ed,Sx:()=>p,j8:()=>C,zz:()=>f});var r=n(40459);function a(e,...t){return t.forEach(t=>{for(let n in t)e[n]=t[n]}),e}var o=/\$(\d+)|\${(\d+):\/(downcase|upcase)}/g,s=class{static hasCaptures(e){return null!==e&&(o.lastIndex=0,o.test(e))}static replaceCaptures(e,t,n){return e.replace(o,(e,r,a,o)=>{let s=n[parseInt(r||a,10)];if(!s)return e;{let e=t.substring(s.start,s.end);for(;"."===e[0];)e=e.substring(1);switch(o){case"downcase":return e.toLowerCase();case"upcase":return e.toUpperCase();default:return e}}})}};function i(e,t){if(null===e&&null===t)return 0;if(!e)return -1;if(!t)return 1;let n=e.length,r=t.length;if(n===r){for(let r=0;r<n;r++){var a,o;let n=(a=e[r],a<(o=t[r])?-1:+(a>o));if(0!==n)return n}return 0}return n-r}function l(e){return!!(/^#[0-9a-f]{6}$/i.test(e)||/^#[0-9a-f]{8}$/i.test(e)||/^#[0-9a-f]{3}$/i.test(e)||/^#[0-9a-f]{4}$/i.test(e))}function u(e){return e.replace(/[\-\\\{\}\*\+\?\|\^\$\.\,\[\]\(\)\#\s]/g,"\\$&")}var c=class{constructor(e){this.fn=e}cache=new Map;get(e){if(this.cache.has(e))return this.cache.get(e);let t=this.fn(e);return this.cache.set(e,t),t}},p=class{constructor(e,t,n){this._colorMap=e,this._defaults=t,this._root=n}static createFromRawTheme(e,t){return this.createFromParsedTheme(function(e){if(!e||!e.settings||!Array.isArray(e.settings))return[];let t=e.settings,n=[],r=0;for(let e=0,a=t.length;e<a;e++){let a,o=t[e];if(!o.settings)continue;if("string"==typeof o.scope){let e=o.scope;a=(e=(e=e.replace(/^[,]+/,"")).replace(/[,]+$/,"")).split(",")}else a=Array.isArray(o.scope)?o.scope:[""];let s=-1;if("string"==typeof o.settings.fontStyle){s=0;let e=o.settings.fontStyle.split(" ");for(let t=0,n=e.length;t<n;t++)switch(e[t]){case"italic":s|=1;break;case"bold":s|=2;break;case"underline":s|=4;break;case"strikethrough":s|=8}}let i=null;"string"==typeof o.settings.foreground&&l(o.settings.foreground)&&(i=o.settings.foreground);let u=null;"string"==typeof o.settings.background&&l(o.settings.background)&&(u=o.settings.background);for(let t=0,o=a.length;t<o;t++){let o=a[t].trim().split(" "),l=o[o.length-1],c=null;o.length>1&&(c=o.slice(0,o.length-1)).reverse(),n[r++]=new g(l,c,e,s,i,u)}}return n}(e),t)}static createFromParsedTheme(e,t){return function(e,t){e.sort((e,t)=>{var n,r;let a=(n=e.scope,n<(r=t.scope)?-1:+(n>r));return 0!==a||0!==(a=i(e.parentScopes,t.parentScopes))?a:e.index-t.index});let n=0,r="#000000",a="#ffffff";for(;e.length>=1&&""===e[0].scope;){let t=e.shift();-1!==t.fontStyle&&(n=t.fontStyle),null!==t.foreground&&(r=t.foreground),null!==t.background&&(a=t.background)}let o=new m(t),s=new d(n,o.getId(r),o.getId(a)),l=new _(new b(0,null,-1,0,0),[]);for(let t=0,n=e.length;t<n;t++){let n=e[t];l.insert(0,n.scope,n.parentScopes,n.fontStyle,o.getId(n.foreground),o.getId(n.background))}return new p(o,s,l)}(e,t)}_cachedMatchRoot=new c(e=>this._root.match(e));getColorMap(){return this._colorMap.getColorMap()}getDefaults(){return this._defaults}match(e){if(null===e)return this._defaults;let t=e.scopeName,n=this._cachedMatchRoot.get(t).find(t=>(function(e,t){if(0===t.length)return!0;for(let a=0;a<t.length;a++){var n,r;let o=t[a],s=!1;if(">"===o){if(a===t.length-1)return!1;o=t[++a],s=!0}for(;e&&(n=e.scopeName,!((r=o)===n||n.startsWith(r)&&"."===n[r.length]));){if(s)return!1;e=e.parent}if(!e)return!1;e=e.parent}return!0})(e.parent,t.parentScopes));return n?new d(n.fontStyle,n.foreground,n.background):null}},h=class e{constructor(e,t){this.parent=e,this.scopeName=t}static push(t,n){for(let r of n)t=new e(t,r);return t}static from(...t){let n=null;for(let r=0;r<t.length;r++)n=new e(n,t[r]);return n}push(t){return new e(this,t)}getSegments(){let e=this,t=[];for(;e;)t.push(e.scopeName),e=e.parent;return t.reverse(),t}toString(){return this.getSegments().join(" ")}extends(e){return this===e||null!==this.parent&&this.parent.extends(e)}getExtensionIfDefined(e){let t=[],n=this;for(;n&&n!==e;)t.push(n.scopeName),n=n.parent;return n===e?t.reverse():void 0}},d=class{constructor(e,t,n){this.fontStyle=e,this.foregroundId=t,this.backgroundId=n}},g=class{constructor(e,t,n,r,a,o){this.scope=e,this.parentScopes=t,this.index=n,this.fontStyle=r,this.foreground=a,this.background=o}},f=(e=>(e[e.NotSet=-1]="NotSet",e[e.None=0]="None",e[e.Italic=1]="Italic",e[e.Bold=2]="Bold",e[e.Underline=4]="Underline",e[e.Strikethrough=8]="Strikethrough",e))(f||{}),m=class{_isFrozen;_lastColorId;_id2color;_color2id;constructor(e){if(this._lastColorId=0,this._id2color=[],this._color2id=Object.create(null),Array.isArray(e)){this._isFrozen=!0;for(let t=0,n=e.length;t<n;t++)this._color2id[e[t]]=t,this._id2color[t]=e[t]}else this._isFrozen=!1}getId(e){if(null===e)return 0;e=e.toUpperCase();let t=this._color2id[e];if(t)return t;if(this._isFrozen)throw Error(`Missing color in color map - ${e}`);return t=++this._lastColorId,this._color2id[e]=t,this._id2color[t]=e,t}getColorMap(){return this._id2color.slice(0)}},y=Object.freeze([]),b=class e{scopeDepth;parentScopes;fontStyle;foreground;background;constructor(e,t,n,r,a){this.scopeDepth=e,this.parentScopes=t||y,this.fontStyle=n,this.foreground=r,this.background=a}clone(){return new e(this.scopeDepth,this.parentScopes,this.fontStyle,this.foreground,this.background)}static cloneArr(e){let t=[];for(let n=0,r=e.length;n<r;n++)t[n]=e[n].clone();return t}acceptOverwrite(e,t,n,r){this.scopeDepth>e?console.log("how did this happen?"):this.scopeDepth=e,-1!==t&&(this.fontStyle=t),0!==n&&(this.foreground=n),0!==r&&(this.background=r)}},_=class e{constructor(e,t=[],n={}){this._mainRule=e,this._children=n,this._rulesWithParentScopes=t}_rulesWithParentScopes;static _cmpBySpecificity(e,t){if(e.scopeDepth!==t.scopeDepth)return t.scopeDepth-e.scopeDepth;let n=0,r=0;for(;">"===e.parentScopes[n]&&n++,">"===t.parentScopes[r]&&r++,!(n>=e.parentScopes.length)&&!(r>=t.parentScopes.length);){let a=t.parentScopes[r].length-e.parentScopes[n].length;if(0!==a)return a;n++,r++}return t.parentScopes.length-e.parentScopes.length}match(t){if(""!==t){let e,n,r=t.indexOf(".");if(-1===r?(e=t,n=""):(e=t.substring(0,r),n=t.substring(r+1)),this._children.hasOwnProperty(e))return this._children[e].match(n)}let n=this._rulesWithParentScopes.concat(this._mainRule);return n.sort(e._cmpBySpecificity),n}insert(t,n,r,a,o,s){let i,l,u;if(""===n)return void this._doInsertHere(t,r,a,o,s);let c=n.indexOf(".");-1===c?(i=n,l=""):(i=n.substring(0,c),l=n.substring(c+1)),this._children.hasOwnProperty(i)?u=this._children[i]:(u=new e(this._mainRule.clone(),b.cloneArr(this._rulesWithParentScopes)),this._children[i]=u),u.insert(t+1,l,r,a,o,s)}_doInsertHere(e,t,n,r,a){if(null===t)return void this._mainRule.acceptOverwrite(e,n,r,a);for(let o=0,s=this._rulesWithParentScopes.length;o<s;o++){let s=this._rulesWithParentScopes[o];if(0===i(s.parentScopes,t))return void s.acceptOverwrite(e,n,r,a)}-1===n&&(n=this._mainRule.fontStyle),0===r&&(r=this._mainRule.foreground),0===a&&(a=this._mainRule.background),this._rulesWithParentScopes.push(new b(e,t,n,r,a))}},C=class e{static toBinaryStr(e){return e.toString(2).padStart(32,"0")}static print(t){let n=e.getLanguageId(t),r=e.getTokenType(t),a=e.getFontStyle(t);console.log({languageId:n,tokenType:r,fontStyle:a,foreground:e.getForeground(t),background:e.getBackground(t)})}static getLanguageId(e){return(255&e)>>>0}static getTokenType(e){return(768&e)>>>8}static containsBalancedBrackets(e){return(1024&e)!=0}static getFontStyle(e){return(30720&e)>>>11}static getForeground(e){return(0xff8000&e)>>>15}static getBackground(e){return(0xff000000&e)>>>24}static set(t,n,r,a,o,s,i){let l=e.getLanguageId(t),u=e.getTokenType(t),c=+!!e.containsBalancedBrackets(t),p=e.getFontStyle(t),h=e.getForeground(t),d=e.getBackground(t);return 0!==n&&(l=n),8!==r&&(u=r),null!==a&&(c=+!!a),-1!==o&&(p=o),0!==s&&(h=s),0!==i&&(d=i),(0|l|u<<8|c<<10|p<<11|h<<15|d<<24)>>>0}};function k(e,t){var n;let r,a,o=[],s=(n=e,a=(r=/([LR]:|[\w\.:][\w\.:\-]*|[\,\|\-\(\)])/g).exec(n),{next:()=>{if(!a)return null;let e=a[0];return a=r.exec(n),e}}),i=s.next();for(;null!==i;){let e=0;if(2===i.length&&":"===i.charAt(1)){switch(i.charAt(0)){case"R":e=1;break;case"L":e=-1;break;default:console.log(`Unknown priority ${i} in scope selector`)}i=s.next()}let t=u();if(o.push({matcher:t,priority:e}),","!==i)break;i=s.next()}return o;function l(){if("-"===i){i=s.next();let e=l();return t=>!!e&&!e(t)}if("("===i){i=s.next();let e=function(){let e=[],t=u();for(;t&&(e.push(t),"|"===i||","===i);){do i=s.next();while("|"===i||","===i);t=u()}return t=>e.some(e=>e(t))}();return")"===i&&(i=s.next()),e}if(w(i)){let e=[];do e.push(i),i=s.next();while(w(i));return n=>t(e,n)}return null}function u(){let e=[],t=l();for(;t;)e.push(t),t=l();return t=>e.every(e=>e(t))}}function w(e){return!!e&&!!e.match(/[\w\.:]+/)}var S=(e=>(e[e.None=0]="None",e[e.NotBeginString=1]="NotBeginString",e[e.NotEndString=2]="NotEndString",e[e.NotBeginPosition=4]="NotBeginPosition",e[e.DebugCall=8]="DebugCall",e))(S||{});function x(e){"function"==typeof e.dispose&&e.dispose()}var v=class{constructor(e){this.scopeName=e}toKey(){return this.scopeName}},A=class{constructor(e,t){this.scopeName=e,this.ruleName=t}toKey(){return`${this.scopeName}#${this.ruleName}`}},E=class{_references=[];_seenReferenceKeys=new Set;get references(){return this._references}visitedRule=new Set;add(e){let t=e.toKey();this._seenReferenceKeys.has(t)||(this._seenReferenceKeys.add(t),this._references.push(e))}},I=class{constructor(e,t){this.repo=e,this.initialScopeName=t,this.seenFullScopeRequests.add(this.initialScopeName),this.Q=[new v(this.initialScopeName)]}seenFullScopeRequests=new Set;seenPartialScopeRequests=new Set;Q;processQueue(){let e=this.Q;this.Q=[];let t=new E;for(let n of e)!function(e,t,n,r){let a=n.lookup(e.scopeName);if(!a){if(e.scopeName===t)throw Error(`No grammar provided for <${t}>`);return}let o=n.lookup(t);e instanceof v?P({baseGrammar:o,selfGrammar:a},r):N(e.ruleName,{baseGrammar:o,selfGrammar:a,repository:a.repository},r);let s=n.injections(e.scopeName);if(s)for(let e of s)r.add(new v(e))}(n,this.initialScopeName,this.repo,t);for(let e of t.references)if(e instanceof v){if(this.seenFullScopeRequests.has(e.scopeName))continue;this.seenFullScopeRequests.add(e.scopeName),this.Q.push(e)}else{if(this.seenFullScopeRequests.has(e.scopeName)||this.seenPartialScopeRequests.has(e.toKey()))continue;this.seenPartialScopeRequests.add(e.toKey()),this.Q.push(e)}}};function N(e,t,n){t.repository&&t.repository[e]&&R([t.repository[e]],t,n)}function P(e,t){e.selfGrammar.patterns&&Array.isArray(e.selfGrammar.patterns)&&R(e.selfGrammar.patterns,{...e,repository:e.selfGrammar.repository},t),e.selfGrammar.injections&&R(Object.values(e.selfGrammar.injections),{...e,repository:e.selfGrammar.repository},t)}function R(e,t,n){for(let r of e){if(n.visitedRule.has(r))continue;n.visitedRule.add(r);let e=r.repository?a({},t.repository,r.repository):t.repository;Array.isArray(r.patterns)&&R(r.patterns,{...t,repository:e},n);let o=r.include;if(!o)continue;let s=D(o);switch(s.kind){case 0:P({...t,selfGrammar:t.baseGrammar},n);break;case 1:P(t,n);break;case 2:N(s.ruleName,{...t,repository:e},n);break;case 3:case 4:let i=s.scopeName===t.selfGrammar.scopeName?t.selfGrammar:s.scopeName===t.baseGrammar.scopeName?t.baseGrammar:void 0;if(i){let r={baseGrammar:t.baseGrammar,selfGrammar:i,repository:e};4===s.kind?N(s.ruleName,r,n):P(r,n)}else 4===s.kind?n.add(new A(s.scopeName,s.ruleName)):n.add(new v(s.scopeName))}}}var $=class{kind=0},L=class{kind=1},M=class{constructor(e){this.ruleName=e}kind=2},O=class{constructor(e){this.scopeName=e}kind=3},T=class{constructor(e,t){this.scopeName=e,this.ruleName=t}kind=4};function D(e){if("$base"===e)return new $;if("$self"===e)return new L;let t=e.indexOf("#");return -1===t?new O(e):0===t?new M(e.substring(1)):new T(e.substring(0,t),e.substring(t+1))}var G=/\\(\d+)/,B=/\\(\d+)/g;Symbol("RuleId");var F=class{$location;id;_nameIsCapturing;_name;_contentNameIsCapturing;_contentName;constructor(e,t,n,r){this.$location=e,this.id=t,this._name=n||null,this._nameIsCapturing=s.hasCaptures(this._name),this._contentName=r||null,this._contentNameIsCapturing=s.hasCaptures(this._contentName)}get debugName(){let e=this.$location?`${function e(t){let n=~t.lastIndexOf("/")||~t.lastIndexOf("\\");return 0===n?t:~n==t.length-1?e(t.substring(0,t.length-1)):t.substr(~n+1)}(this.$location.filename)}:${this.$location.line}`:"unknown";return`${this.constructor.name}#${this.id} @ ${e}`}getName(e,t){return this._nameIsCapturing&&null!==this._name&&null!==e&&null!==t?s.replaceCaptures(this._name,e,t):this._name}getContentName(e,t){return this._contentNameIsCapturing&&null!==this._contentName?s.replaceCaptures(this._contentName,e,t):this._contentName}},U=class extends F{retokenizeCapturedWithRuleId;constructor(e,t,n,r,a){super(e,t,n,r),this.retokenizeCapturedWithRuleId=a}dispose(){}collectPatterns(e,t){throw Error("Not supported!")}compile(e,t){throw Error("Not supported!")}compileAG(e,t,n,r){throw Error("Not supported!")}},j=class extends F{_match;captures;_cachedCompiledPatterns;constructor(e,t,n,r,a){super(e,t,n,null),this._match=new V(r,this.id),this.captures=a,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugMatchRegExp(){return`${this._match.source}`}collectPatterns(e,t){t.push(this._match)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,n,r){return this._getCachedCompiledPatterns(e).compileAG(e,n,r)}_getCachedCompiledPatterns(e){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new X,this.collectPatterns(e,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}},z=class extends F{hasMissingPatterns;patterns;_cachedCompiledPatterns;constructor(e,t,n,r,a){super(e,t,n,r),this.patterns=a.patterns,this.hasMissingPatterns=a.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}collectPatterns(e,t){for(let n of this.patterns)e.getRule(n).collectPatterns(e,t)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,n,r){return this._getCachedCompiledPatterns(e).compileAG(e,n,r)}_getCachedCompiledPatterns(e){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new X,this.collectPatterns(e,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}},W=class extends F{_begin;beginCaptures;_end;endHasBackReferences;endCaptures;applyEndPatternLast;hasMissingPatterns;patterns;_cachedCompiledPatterns;constructor(e,t,n,r,a,o,s,i,l,u){super(e,t,n,r),this._begin=new V(a,this.id),this.beginCaptures=o,this._end=new V(s||"￿",-1),this.endHasBackReferences=this._end.hasBackReferences,this.endCaptures=i,this.applyEndPatternLast=l||!1,this.patterns=u.patterns,this.hasMissingPatterns=u.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugEndRegExp(){return`${this._end.source}`}getEndWithResolvedBackReferences(e,t){return this._end.resolveBackReferences(e,t)}collectPatterns(e,t){t.push(this._begin)}compile(e,t){return this._getCachedCompiledPatterns(e,t).compile(e)}compileAG(e,t,n,r){return this._getCachedCompiledPatterns(e,t).compileAG(e,n,r)}_getCachedCompiledPatterns(e,t){if(!this._cachedCompiledPatterns){for(let t of(this._cachedCompiledPatterns=new X,this.patterns))e.getRule(t).collectPatterns(e,this._cachedCompiledPatterns);this.applyEndPatternLast?this._cachedCompiledPatterns.push(this._end.hasBackReferences?this._end.clone():this._end):this._cachedCompiledPatterns.unshift(this._end.hasBackReferences?this._end.clone():this._end)}return this._end.hasBackReferences&&(this.applyEndPatternLast?this._cachedCompiledPatterns.setSource(this._cachedCompiledPatterns.length()-1,t):this._cachedCompiledPatterns.setSource(0,t)),this._cachedCompiledPatterns}},q=class extends F{_begin;beginCaptures;whileCaptures;_while;whileHasBackReferences;hasMissingPatterns;patterns;_cachedCompiledPatterns;_cachedCompiledWhilePatterns;constructor(e,t,n,r,a,o,s,i,l){super(e,t,n,r),this._begin=new V(a,this.id),this.beginCaptures=o,this.whileCaptures=i,this._while=new V(s,-2),this.whileHasBackReferences=this._while.hasBackReferences,this.patterns=l.patterns,this.hasMissingPatterns=l.hasMissingPatterns,this._cachedCompiledPatterns=null,this._cachedCompiledWhilePatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null),this._cachedCompiledWhilePatterns&&(this._cachedCompiledWhilePatterns.dispose(),this._cachedCompiledWhilePatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugWhileRegExp(){return`${this._while.source}`}getWhileWithResolvedBackReferences(e,t){return this._while.resolveBackReferences(e,t)}collectPatterns(e,t){t.push(this._begin)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,n,r){return this._getCachedCompiledPatterns(e).compileAG(e,n,r)}_getCachedCompiledPatterns(e){if(!this._cachedCompiledPatterns)for(let t of(this._cachedCompiledPatterns=new X,this.patterns))e.getRule(t).collectPatterns(e,this._cachedCompiledPatterns);return this._cachedCompiledPatterns}compileWhile(e,t){return this._getCachedCompiledWhilePatterns(e,t).compile(e)}compileWhileAG(e,t,n,r){return this._getCachedCompiledWhilePatterns(e,t).compileAG(e,n,r)}_getCachedCompiledWhilePatterns(e,t){return this._cachedCompiledWhilePatterns||(this._cachedCompiledWhilePatterns=new X,this._cachedCompiledWhilePatterns.push(this._while.hasBackReferences?this._while.clone():this._while)),this._while.hasBackReferences&&this._cachedCompiledWhilePatterns.setSource(0,t||"￿"),this._cachedCompiledWhilePatterns}},H=class e{static createCaptureRule(e,t,n,r,a){return e.registerRule(e=>new U(t,e,n,r,a))}static getCompiledRuleId(t,n,r){return t.id||n.registerRule(o=>{if(t.id=o,t.match)return new j(t.$vscodeTextmateLocation,t.id,t.name,t.match,e._compileCaptures(t.captures,n,r));if(void 0===t.begin){t.repository&&(r=a({},r,t.repository));let o=t.patterns;return void 0===o&&t.include&&(o=[{include:t.include}]),new z(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,e._compilePatterns(o,n,r))}return t.while?new q(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,t.begin,e._compileCaptures(t.beginCaptures||t.captures,n,r),t.while,e._compileCaptures(t.whileCaptures||t.captures,n,r),e._compilePatterns(t.patterns,n,r)):new W(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,t.begin,e._compileCaptures(t.beginCaptures||t.captures,n,r),t.end,e._compileCaptures(t.endCaptures||t.captures,n,r),t.applyEndPatternLast,e._compilePatterns(t.patterns,n,r))}),t.id}static _compileCaptures(t,n,r){let a=[];if(t){let o=0;for(let e in t){if("$vscodeTextmateLocation"===e)continue;let t=parseInt(e,10);t>o&&(o=t)}for(let e=0;e<=o;e++)a[e]=null;for(let o in t){if("$vscodeTextmateLocation"===o)continue;let s=parseInt(o,10),i=0;t[o].patterns&&(i=e.getCompiledRuleId(t[o],n,r)),a[s]=e.createCaptureRule(n,t[o].$vscodeTextmateLocation,t[o].name,t[o].contentName,i)}}return a}static _compilePatterns(t,n,r){let a=[];if(t)for(let o=0,s=t.length;o<s;o++){let s=t[o],i=-1;if(s.include){let t=D(s.include);switch(t.kind){case 0:case 1:i=e.getCompiledRuleId(r[s.include],n,r);break;case 2:let a=r[t.ruleName];a&&(i=e.getCompiledRuleId(a,n,r));break;case 3:case 4:let o=t.scopeName,l=4===t.kind?t.ruleName:null,u=n.getExternalGrammar(o,r);if(u)if(l){let t=u.repository[l];t&&(i=e.getCompiledRuleId(t,n,u.repository))}else i=e.getCompiledRuleId(u.repository.$self,n,u.repository)}}else i=e.getCompiledRuleId(s,n,r);if(-1!==i){let e=n.getRule(i),t=!1;if((e instanceof z||e instanceof W||e instanceof q)&&e.hasMissingPatterns&&0===e.patterns.length&&(t=!0),t)continue;a.push(i)}}return{patterns:a,hasMissingPatterns:(t?t.length:0)!==a.length}}},V=class e{source;ruleId;hasAnchor;hasBackReferences;_anchorCache;constructor(e,t){if(e&&"string"==typeof e){let t=e.length,n=0,r=[],a=!1;for(let o=0;o<t;o++)if("\\"===e.charAt(o)&&o+1<t){let t=e.charAt(o+1);"z"===t?(r.push(e.substring(n,o)),r.push("$(?!\\n)(?<!\\n)"),n=o+2):("A"===t||"G"===t)&&(a=!0),o++}this.hasAnchor=a,0===n?this.source=e:(r.push(e.substring(n,t)),this.source=r.join(""))}else this.hasAnchor=!1,this.source=e;this.hasAnchor?this._anchorCache=this._buildAnchorCache():this._anchorCache=null,this.ruleId=t,"string"==typeof this.source?this.hasBackReferences=G.test(this.source):this.hasBackReferences=!1}clone(){return new e(this.source,this.ruleId)}setSource(e){this.source!==e&&(this.source=e,this.hasAnchor&&(this._anchorCache=this._buildAnchorCache()))}resolveBackReferences(e,t){if("string"!=typeof this.source)throw Error("This method should only be called if the source is a string");let n=t.map(t=>e.substring(t.start,t.end));return B.lastIndex=0,this.source.replace(B,(e,t)=>u(n[parseInt(t,10)]||""))}_buildAnchorCache(){let e,t,n,r;if("string"!=typeof this.source)throw Error("This method should only be called if the source is a string");let a=[],o=[],s=[],i=[];for(e=0,t=this.source.length;e<t;e++)n=this.source.charAt(e),a[e]=n,o[e]=n,s[e]=n,i[e]=n,"\\"===n&&e+1<t&&("A"===(r=this.source.charAt(e+1))?(a[e+1]="￿",o[e+1]="￿",s[e+1]="A",i[e+1]="A"):"G"===r?(a[e+1]="￿",o[e+1]="G",s[e+1]="￿",i[e+1]="G"):(a[e+1]=r,o[e+1]=r,s[e+1]=r,i[e+1]=r),e++);return{A0_G0:a.join(""),A0_G1:o.join(""),A1_G0:s.join(""),A1_G1:i.join("")}}resolveAnchors(e,t){if(!this.hasAnchor||!this._anchorCache||"string"!=typeof this.source)return this.source;if(e)if(t)return this._anchorCache.A1_G1;else return this._anchorCache.A1_G0;return t?this._anchorCache.A0_G1:this._anchorCache.A0_G0}},X=class{_items;_hasAnchors;_cached;_anchorCache;constructor(){this._items=[],this._hasAnchors=!1,this._cached=null,this._anchorCache={A0_G0:null,A0_G1:null,A1_G0:null,A1_G1:null}}dispose(){this._disposeCaches()}_disposeCaches(){this._cached&&(this._cached.dispose(),this._cached=null),this._anchorCache.A0_G0&&(this._anchorCache.A0_G0.dispose(),this._anchorCache.A0_G0=null),this._anchorCache.A0_G1&&(this._anchorCache.A0_G1.dispose(),this._anchorCache.A0_G1=null),this._anchorCache.A1_G0&&(this._anchorCache.A1_G0.dispose(),this._anchorCache.A1_G0=null),this._anchorCache.A1_G1&&(this._anchorCache.A1_G1.dispose(),this._anchorCache.A1_G1=null)}push(e){this._items.push(e),this._hasAnchors=this._hasAnchors||e.hasAnchor}unshift(e){this._items.unshift(e),this._hasAnchors=this._hasAnchors||e.hasAnchor}length(){return this._items.length}setSource(e,t){this._items[e].source!==t&&(this._disposeCaches(),this._items[e].setSource(t))}compile(e){if(!this._cached){let t=this._items.map(e=>e.source);this._cached=new K(e,t,this._items.map(e=>e.ruleId))}return this._cached}compileAG(e,t,n){if(!this._hasAnchors)return this.compile(e);if(t)if(n)return this._anchorCache.A1_G1||(this._anchorCache.A1_G1=this._resolveAnchors(e,t,n)),this._anchorCache.A1_G1;else return this._anchorCache.A1_G0||(this._anchorCache.A1_G0=this._resolveAnchors(e,t,n)),this._anchorCache.A1_G0;return n?(this._anchorCache.A0_G1||(this._anchorCache.A0_G1=this._resolveAnchors(e,t,n)),this._anchorCache.A0_G1):(this._anchorCache.A0_G0||(this._anchorCache.A0_G0=this._resolveAnchors(e,t,n)),this._anchorCache.A0_G0)}_resolveAnchors(e,t,n){return new K(e,this._items.map(e=>e.resolveAnchors(t,n)),this._items.map(e=>e.ruleId))}},K=class{constructor(e,t,n){this.regExps=t,this.rules=n,this.scanner=e.createOnigScanner(t)}scanner;dispose(){"function"==typeof this.scanner.dispose&&this.scanner.dispose()}toString(){let e=[];for(let t=0,n=this.rules.length;t<n;t++)e.push("   - "+this.rules[t]+": "+this.regExps[t]);return e.join("\n")}findNextMatchSync(e,t,n){let r=this.scanner.findNextMatchSync(e,t,n);return r?{ruleId:this.rules[r.index],captureIndices:r.captureIndices}:null}},Z=class{constructor(e,t){this.languageId=e,this.tokenType=t}},Q=class e{_defaultAttributes;_embeddedLanguagesMatcher;constructor(e,t){this._defaultAttributes=new Z(e,8),this._embeddedLanguagesMatcher=new Y(Object.entries(t||{}))}getDefaultAttributes(){return this._defaultAttributes}getBasicScopeAttributes(t){return null===t?e._NULL_SCOPE_METADATA:this._getBasicScopeAttributes.get(t)}static _NULL_SCOPE_METADATA=new Z(0,0);_getBasicScopeAttributes=new c(e=>new Z(this._scopeToLanguage(e),this._toStandardTokenType(e)));_scopeToLanguage(e){return this._embeddedLanguagesMatcher.match(e)||0}_toStandardTokenType(t){let n=t.match(e.STANDARD_TOKEN_TYPE_REGEXP);if(!n)return 8;switch(n[1]){case"comment":return 1;case"string":return 2;case"regex":return 3;case"meta.embedded":return 0}throw Error("Unexpected match for standard token type!")}static STANDARD_TOKEN_TYPE_REGEXP=/\b(comment|string|regex|meta\.embedded)\b/},Y=class{values;scopesRegExp;constructor(e){if(0===e.length)this.values=null,this.scopesRegExp=null;else{this.values=new Map(e);let t=e.map(([e,t])=>u(e));t.sort(),t.reverse(),this.scopesRegExp=RegExp(`^((${t.join(")|(")}))($|\\.)`,"")}}match(e){if(!this.scopesRegExp)return;let t=e.match(this.scopesRegExp);if(t)return this.values.get(t[1])}};void 0!==r&&r.env.VSCODE_TEXTMATE_DEBUG;var J=class{constructor(e,t){this.stack=e,this.stoppedEarly=t}};function ee(e,t,n,r,a,o,s,i){let l=t.content.length,u=!1,c=-1;if(s){let s=function(e,t,n,r,a,o){let s=a.beginRuleCapturedEOL?0:-1,i=[];for(let t=a;t;t=t.pop()){let n=t.getRule(e);n instanceof q&&i.push({rule:n,stack:t})}for(let d=i.pop();d;d=i.pop()){var l,u,c,p,h;let{ruleScanner:i,findOptions:g}=(l=d.rule,u=e,c=d.stack.endRule,p=n,h=r===s,{ruleScanner:l.compileWhileAG(u,c,p,h),findOptions:0}),f=i.findNextMatchSync(t,r,g);if(f){if(-2!==f.ruleId){a=d.stack.pop();break}f.captureIndices&&f.captureIndices.length&&(o.produce(d.stack,f.captureIndices[0].start),en(e,t,n,d.stack,o,d.rule.whileCaptures,f.captureIndices),o.produce(d.stack,f.captureIndices[0].end),s=f.captureIndices[0].end,f.captureIndices[0].end>r&&(r=f.captureIndices[0].end,n=!1))}else{a=d.stack.pop();break}}return{stack:a,linePos:r,anchorPosition:s,isFirstLine:n}}(e,t,n,r,a,o);a=s.stack,r=s.linePos,n=s.isFirstLine,c=s.anchorPosition}let p=Date.now();for(;!u;){if(0!==i&&Date.now()-p>i)return new J(a,!0);!function(){let s=function(e,t,n,r,a,o){let s=function(e,t,n,r,a,o){let{ruleScanner:s,findOptions:i}=et(a.getRule(e),e,a.endRule,n,r===o),l=s.findNextMatchSync(t,r,i);return l?{captureIndices:l.captureIndices,matchedRuleId:l.ruleId}:null}(e,t,n,r,a,o),i=e.getInjections();if(0===i.length)return s;let l=function(e,t,n,r,a,o,s){let i,l=Number.MAX_VALUE,u=null,c=0,p=o.contentNameScopesList.getScopeNames();for(let o=0,h=e.length;o<h;o++){let h=e[o];if(!h.matcher(p))continue;let{ruleScanner:d,findOptions:g}=et(t.getRule(h.ruleId),t,null,r,a===s),f=d.findNextMatchSync(n,a,g);if(!f)continue;let m=f.captureIndices[0].start;if(!(m>=l)&&(l=m,u=f.captureIndices,i=f.ruleId,c=h.priority,l===a))break}return u?{priorityMatch:-1===c,captureIndices:u,matchedRuleId:i}:null}(i,e,t,n,r,a,o);if(!l)return s;if(!s)return l;let u=s.captureIndices[0].start,c=l.captureIndices[0].start;return c<u||l.priorityMatch&&c===u?l:s}(e,t,n,r,a,c);if(!s){o.produce(a,l),u=!0;return}let i=s.captureIndices,p=s.matchedRuleId,h=!!i&&i.length>0&&i[0].end>r;if(-1===p){let s=a.getRule(e);o.produce(a,i[0].start),a=a.withContentNameScopesList(a.nameScopesList),en(e,t,n,a,o,s.endCaptures,i),o.produce(a,i[0].end);let p=a;if(a=a.parent,c=p.getAnchorPos(),!h&&p.getEnterPos()===r){a=p,o.produce(a,l),u=!0;return}}else{let s=e.getRule(p);o.produce(a,i[0].start);let d=a,g=s.getName(t.content,i),f=a.contentNameScopesList.pushAttributed(g,e);if(a=a.push(p,r,c,i[0].end===l,null,f,f),s instanceof W){en(e,t,n,a,o,s.beginCaptures,i),o.produce(a,i[0].end),c=i[0].end;let r=s.getContentName(t.content,i),p=f.pushAttributed(r,e);if(a=a.withContentNameScopesList(p),s.endHasBackReferences&&(a=a.withEndRule(s.getEndWithResolvedBackReferences(t.content,i))),!h&&d.hasSameRuleAs(a)){a=a.pop(),o.produce(a,l),u=!0;return}}else if(s instanceof q){en(e,t,n,a,o,s.beginCaptures,i),o.produce(a,i[0].end),c=i[0].end;let r=s.getContentName(t.content,i),p=f.pushAttributed(r,e);if(a=a.withContentNameScopesList(p),s.whileHasBackReferences&&(a=a.withEndRule(s.getWhileWithResolvedBackReferences(t.content,i))),!h&&d.hasSameRuleAs(a)){a=a.pop(),o.produce(a,l),u=!0;return}}else if(en(e,t,n,a,o,s.captures,i),o.produce(a,i[0].end),a=a.pop(),!h){a=a.safePop(),o.produce(a,l),u=!0;return}}i[0].end>r&&(r=i[0].end,n=!1)}()}return new J(a,!1)}function et(e,t,n,r,a){return{ruleScanner:e.compileAG(t,n,r,a),findOptions:0}}function en(e,t,n,r,a,o,s){if(0===o.length)return;let i=t.content,l=Math.min(o.length,s.length),u=[],c=s[0].end;for(let t=0;t<l;t++){let l=o[t];if(null===l)continue;let p=s[t];if(0===p.length)continue;if(p.start>c)break;for(;u.length>0&&u[u.length-1].endPos<=p.start;)a.produceFromScopes(u[u.length-1].scopes,u[u.length-1].endPos),u.pop();if(u.length>0?a.produceFromScopes(u[u.length-1].scopes,p.start):a.produce(r,p.start),l.retokenizeCapturedWithRuleId){let t=l.getName(i,s),o=r.contentNameScopesList.pushAttributed(t,e),u=l.getContentName(i,s),c=o.pushAttributed(u,e),h=r.push(l.retokenizeCapturedWithRuleId,p.start,-1,!1,null,o,c),d=e.createOnigString(i.substring(0,p.end));ee(e,d,n&&0===p.start,p.start,h,a,!1,0),x(d);continue}let h=l.getName(i,s);if(null!==h){let t=(u.length>0?u[u.length-1].scopes:r.contentNameScopesList).pushAttributed(h,e);u.push(new er(t,p.end))}}for(;u.length>0;)a.produceFromScopes(u[u.length-1].scopes,u[u.length-1].endPos),u.pop()}var er=class{scopes;endPos;constructor(e,t){this.scopes=e,this.endPos=t}};function ea(e,t,n,r,a){let o=k(t,eo),s=H.getCompiledRuleId(n,r,a.repository);for(let n of o)e.push({debugSelector:t,matcher:n.matcher,ruleId:s,grammar:a,priority:n.priority})}function eo(e,t){if(t.length<e.length)return!1;let n=0;return e.every(e=>{for(let r=n;r<t.length;r++)if(function(e,t){if(!e)return!1;if(e===t)return!0;let n=t.length;return e.length>n&&e.substr(0,n)===t&&"."===e[n]}(t[r],e))return n=r+1,!0;return!1})}var es=class{constructor(e,t,n,r,a,o,s,i){if(this._rootScopeName=e,this.balancedBracketSelectors=o,this._onigLib=i,this._basicScopeAttributesProvider=new Q(n,r),this._rootId=-1,this._lastRuleId=0,this._ruleId2desc=[null],this._includedGrammars={},this._grammarRepository=s,this._grammar=ei(t,null),this._injections=null,this._tokenTypeMatchers=[],a)for(let e of Object.keys(a))for(let t of k(e,eo))this._tokenTypeMatchers.push({matcher:t.matcher,type:a[e]})}_rootId;_lastRuleId;_ruleId2desc;_includedGrammars;_grammarRepository;_grammar;_injections;_basicScopeAttributesProvider;_tokenTypeMatchers;get themeProvider(){return this._grammarRepository}dispose(){for(let e of this._ruleId2desc)e&&e.dispose()}createOnigScanner(e){return this._onigLib.createOnigScanner(e)}createOnigString(e){return this._onigLib.createOnigString(e)}getMetadataForScope(e){return this._basicScopeAttributesProvider.getBasicScopeAttributes(e)}_collectInjections(){let e=[],t=this._rootScopeName,n=({lookup:e=>e===this._rootScopeName?this._grammar:this.getExternalGrammar(e),injections:e=>this._grammarRepository.injections(e)}).lookup(t);if(n){let r=n.injections;if(r)for(let t in r)ea(e,t,r[t],this,n);let a=this._grammarRepository.injections(t);a&&a.forEach(t=>{let n=this.getExternalGrammar(t);if(n){let t=n.injectionSelector;t&&ea(e,t,n,this,n)}})}return e.sort((e,t)=>e.priority-t.priority),e}getInjections(){return null===this._injections&&(this._injections=this._collectInjections()),this._injections}registerRule(e){let t=++this._lastRuleId,n=e(t);return this._ruleId2desc[t]=n,n}getRule(e){return this._ruleId2desc[e]}getExternalGrammar(e,t){if(this._includedGrammars[e])return this._includedGrammars[e];if(this._grammarRepository){let n=this._grammarRepository.lookup(e);if(n)return this._includedGrammars[e]=ei(n,t&&t.$base),this._includedGrammars[e]}}tokenizeLine(e,t,n=0){let r=this._tokenize(e,t,!1,n);return{tokens:r.lineTokens.getResult(r.ruleStack,r.lineLength),ruleStack:r.ruleStack,stoppedEarly:r.stoppedEarly}}tokenizeLine2(e,t,n=0){let r=this._tokenize(e,t,!0,n);return{tokens:r.lineTokens.getBinaryResult(r.ruleStack,r.lineLength),ruleStack:r.ruleStack,stoppedEarly:r.stoppedEarly}}_tokenize(e,t,n,r){let a;if(-1===this._rootId&&(this._rootId=H.getCompiledRuleId(this._grammar.repository.$self,this,this._grammar.repository),this.getInjections()),t&&t!==eu.NULL)a=!1,t.reset();else{let e;a=!0;let n=this._basicScopeAttributesProvider.getDefaultAttributes(),r=this.themeProvider.getDefaults(),o=C.set(0,n.languageId,n.tokenType,null,r.fontStyle,r.foregroundId,r.backgroundId),s=this.getRule(this._rootId).getName(null,null);e=s?el.createRootAndLookUpScopeName(s,o,this):el.createRoot("unknown",o),t=new eu(null,this._rootId,-1,-1,!1,null,e,e)}e+="\n";let o=this.createOnigString(e),s=o.content.length,i=new ep(n,e,this._tokenTypeMatchers,this.balancedBracketSelectors),l=ee(this,o,a,0,t,i,!0,r);return x(o),{lineLength:s,lineTokens:i,ruleStack:l.stack,stoppedEarly:l.stoppedEarly}}};function ei(e,t){return(e=function e(t){return Array.isArray(t)?function(t){let n=[];for(let r=0,a=t.length;r<a;r++)n[r]=e(t[r]);return n}(t):t instanceof RegExp?t:"object"==typeof t?function(t){let n={};for(let r in t)n[r]=e(t[r]);return n}(t):t}(e)).repository=e.repository||{},e.repository.$self={$vscodeTextmateLocation:e.$vscodeTextmateLocation,patterns:e.patterns,name:e.scopeName},e.repository.$base=t||e.repository.$self,e}var el=class e{constructor(e,t,n){this.parent=e,this.scopePath=t,this.tokenAttributes=n}static fromExtension(t,n){let r=t,a=t?.scopePath??null;for(let t of n)r=new e(r,a=h.push(a,t.scopeNames),t.encodedTokenAttributes);return r}static createRoot(t,n){return new e(null,new h(null,t),n)}static createRootAndLookUpScopeName(t,n,r){let a=r.getMetadataForScope(t),o=new h(null,t),s=r.themeProvider.themeMatch(o),i=e.mergeAttributes(n,a,s);return new e(null,o,i)}get scopeName(){return this.scopePath.scopeName}toString(){return this.getScopeNames().join(" ")}equals(t){return e.equals(this,t)}static equals(e,t){for(;;){if(e===t||!e&&!t)return!0;if(!e||!t||e.scopeName!==t.scopeName||e.tokenAttributes!==t.tokenAttributes)return!1;e=e.parent,t=t.parent}}static mergeAttributes(e,t,n){let r=-1,a=0,o=0;return null!==n&&(r=n.fontStyle,a=n.foregroundId,o=n.backgroundId),C.set(e,t.languageId,t.tokenType,null,r,a,o)}pushAttributed(t,n){if(null===t)return this;if(-1===t.indexOf(" "))return e._pushAttributed(this,t,n);let r=t.split(/ /g),a=this;for(let t of r)a=e._pushAttributed(a,t,n);return a}static _pushAttributed(t,n,r){let a=r.getMetadataForScope(n),o=t.scopePath.push(n),s=r.themeProvider.themeMatch(o),i=e.mergeAttributes(t.tokenAttributes,a,s);return new e(t,o,i)}getScopeNames(){return this.scopePath.getSegments()}getExtensionIfDefined(e){let t=[],n=this;for(;n&&n!==e;)t.push({encodedTokenAttributes:n.tokenAttributes,scopeNames:n.scopePath.getExtensionIfDefined(n.parent?.scopePath??null)}),n=n.parent;return n===e?t.reverse():void 0}},eu=class e{constructor(e,t,n,r,a,o,s,i){this.parent=e,this.ruleId=t,this.beginRuleCapturedEOL=a,this.endRule=o,this.nameScopesList=s,this.contentNameScopesList=i,this.depth=this.parent?this.parent.depth+1:1,this._enterPos=n,this._anchorPos=r}_stackElementBrand=void 0;static NULL=new e(null,0,0,0,!1,null,null,null);_enterPos;_anchorPos;depth;equals(t){return null!==t&&e._equals(this,t)}static _equals(e,t){return e===t||!!this._structuralEquals(e,t)&&el.equals(e.contentNameScopesList,t.contentNameScopesList)}static _structuralEquals(e,t){for(;;){if(e===t||!e&&!t)return!0;if(!e||!t||e.depth!==t.depth||e.ruleId!==t.ruleId||e.endRule!==t.endRule)return!1;e=e.parent,t=t.parent}}clone(){return this}static _reset(e){for(;e;)e._enterPos=-1,e._anchorPos=-1,e=e.parent}reset(){e._reset(this)}pop(){return this.parent}safePop(){return this.parent?this.parent:this}push(t,n,r,a,o,s,i){return new e(this,t,n,r,a,o,s,i)}getEnterPos(){return this._enterPos}getAnchorPos(){return this._anchorPos}getRule(e){return e.getRule(this.ruleId)}toString(){let e=[];return this._writeString(e,0),"["+e.join(",")+"]"}_writeString(e,t){return this.parent&&(t=this.parent._writeString(e,t)),e[t++]=`(${this.ruleId}, ${this.nameScopesList?.toString()}, ${this.contentNameScopesList?.toString()})`,t}withContentNameScopesList(e){return this.contentNameScopesList===e?this:this.parent.push(this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,this.endRule,this.nameScopesList,e)}withEndRule(t){return this.endRule===t?this:new e(this.parent,this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,t,this.nameScopesList,this.contentNameScopesList)}hasSameRuleAs(e){let t=this;for(;t&&t._enterPos===e._enterPos;){if(t.ruleId===e.ruleId)return!0;t=t.parent}return!1}toStateStackFrame(){return{ruleId:this.ruleId,beginRuleCapturedEOL:this.beginRuleCapturedEOL,endRule:this.endRule,nameScopesList:this.nameScopesList?.getExtensionIfDefined(this.parent?.nameScopesList??null)??[],contentNameScopesList:this.contentNameScopesList?.getExtensionIfDefined(this.nameScopesList)??[]}}static pushFrame(t,n){let r=el.fromExtension(t?.nameScopesList??null,n.nameScopesList);return new e(t,n.ruleId,n.enterPos??-1,n.anchorPos??-1,n.beginRuleCapturedEOL,n.endRule,r,el.fromExtension(r,n.contentNameScopesList))}},ec=class{balancedBracketScopes;unbalancedBracketScopes;allowAny=!1;constructor(e,t){this.balancedBracketScopes=e.flatMap(e=>"*"===e?(this.allowAny=!0,[]):k(e,eo).map(e=>e.matcher)),this.unbalancedBracketScopes=t.flatMap(e=>k(e,eo).map(e=>e.matcher))}get matchesAlways(){return this.allowAny&&0===this.unbalancedBracketScopes.length}get matchesNever(){return 0===this.balancedBracketScopes.length&&!this.allowAny}match(e){for(let t of this.unbalancedBracketScopes)if(t(e))return!1;for(let t of this.balancedBracketScopes)if(t(e))return!0;return this.allowAny}},ep=class{constructor(e,t,n,r){this.balancedBracketSelectors=r,this._emitBinaryTokens=e,this._tokenTypeOverrides=n,this._lineText=null,this._tokens=[],this._binaryTokens=[],this._lastTokenEndIndex=0}_emitBinaryTokens;_lineText;_tokens;_binaryTokens;_lastTokenEndIndex;_tokenTypeOverrides;produce(e,t){this.produceFromScopes(e.contentNameScopesList,t)}produceFromScopes(e,t){if(this._lastTokenEndIndex>=t)return;if(this._emitBinaryTokens){let n=e?.tokenAttributes??0,r=!1;if(this.balancedBracketSelectors?.matchesAlways&&(r=!0),this._tokenTypeOverrides.length>0||this.balancedBracketSelectors&&!this.balancedBracketSelectors.matchesAlways&&!this.balancedBracketSelectors.matchesNever){let t=e?.getScopeNames()??[];for(let e of this._tokenTypeOverrides)e.matcher(t)&&(n=C.set(n,0,e.type,null,-1,0,0));this.balancedBracketSelectors&&(r=this.balancedBracketSelectors.match(t))}if(r&&(n=C.set(n,0,8,r,-1,0,0)),this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-1]===n){this._lastTokenEndIndex=t;return}this._binaryTokens.push(this._lastTokenEndIndex),this._binaryTokens.push(n),this._lastTokenEndIndex=t;return}let n=e?.getScopeNames()??[];this._tokens.push({startIndex:this._lastTokenEndIndex,endIndex:t,scopes:n}),this._lastTokenEndIndex=t}getResult(e,t){return this._tokens.length>0&&this._tokens[this._tokens.length-1].startIndex===t-1&&this._tokens.pop(),0===this._tokens.length&&(this._lastTokenEndIndex=-1,this.produce(e,t),this._tokens[this._tokens.length-1].startIndex=0),this._tokens}getBinaryResult(e,t){this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-2]===t-1&&(this._binaryTokens.pop(),this._binaryTokens.pop()),0===this._binaryTokens.length&&(this._lastTokenEndIndex=-1,this.produce(e,t),this._binaryTokens[this._binaryTokens.length-2]=0);let n=new Uint32Array(this._binaryTokens.length);for(let e=0,t=this._binaryTokens.length;e<t;e++)n[e]=this._binaryTokens[e];return n}},eh=class{constructor(e,t){this._onigLib=t,this._theme=e}_grammars=new Map;_rawGrammars=new Map;_injectionGrammars=new Map;_theme;dispose(){for(let e of this._grammars.values())e.dispose()}setTheme(e){this._theme=e}getColorMap(){return this._theme.getColorMap()}addGrammar(e,t){this._rawGrammars.set(e.scopeName,e),t&&this._injectionGrammars.set(e.scopeName,t)}lookup(e){return this._rawGrammars.get(e)}injections(e){return this._injectionGrammars.get(e)}getDefaults(){return this._theme.getDefaults()}themeMatch(e){return this._theme.match(e)}grammarForScopeName(e,t,n,r,a){if(!this._grammars.has(e)){let o=this._rawGrammars.get(e);if(!o)return null;this._grammars.set(e,new es(e,o,t,n,r,a,this,this._onigLib))}return this._grammars.get(e)}},ed=class{_options;_syncRegistry;_ensureGrammarCache;constructor(e){this._options=e,this._syncRegistry=new eh(p.createFromRawTheme(e.theme,e.colorMap),e.onigLib),this._ensureGrammarCache=new Map}dispose(){this._syncRegistry.dispose()}setTheme(e,t){this._syncRegistry.setTheme(p.createFromRawTheme(e,t))}getColorMap(){return this._syncRegistry.getColorMap()}loadGrammarWithEmbeddedLanguages(e,t,n){return this.loadGrammarWithConfiguration(e,t,{embeddedLanguages:n})}loadGrammarWithConfiguration(e,t,n){return this._loadGrammar(e,t,n.embeddedLanguages,n.tokenTypes,new ec(n.balancedBracketSelectors||[],n.unbalancedBracketSelectors||[]))}loadGrammar(e){return this._loadGrammar(e,0,null,null,null)}_loadGrammar(e,t,n,r,a){let o=new I(this._syncRegistry,e);for(;o.Q.length>0;)o.Q.map(e=>this._loadSingleGrammar(e.scopeName)),o.processQueue();return this._grammarForScopeName(e,t,n,r,a)}_loadSingleGrammar(e){this._ensureGrammarCache.has(e)||(this._doLoadSingleGrammar(e),this._ensureGrammarCache.set(e,!0))}_doLoadSingleGrammar(e){let t=this._options.loadGrammar(e);if(t){let n="function"==typeof this._options.getInjections?this._options.getInjections(e):void 0;this._syncRegistry.addGrammar(t,n)}}addGrammar(e,t=[],n=0,r=null){return this._syncRegistry.addGrammar(e,t),this._grammarForScopeName(e.scopeName,n,r)}_grammarForScopeName(e,t=0,n=null,r=null,a=null){return this._syncRegistry.grammarForScopeName(e,t,n,r,a)}},eg=eu.NULL},77263:(e,t,n)=>{n.d(t,{D:()=>r});function r(e,t){let n=String(e);if("string"!=typeof t)throw TypeError("Expected character");let r=0,a=n.indexOf(t);for(;-1!==a;)r++,a=n.indexOf(t,a+t.length);return r}},81796:(e,t,n)=>{n.d(t,{H:()=>r});class r extends Error{constructor(e){super(e),this.name="ShikiError"}}},84957:(e,t,n)=>{n.d(t,{J:()=>r});class r{constructor(e,t={}){this.patterns=e,this.options=t;let{forgiving:n=!1,cache:r,regexConstructor:a}=t;if(!a)throw Error("Option `regexConstructor` is not provided");this.regexps=e.map(e=>{if("string"!=typeof e)return e;let t=r?.get(e);if(t){if(t instanceof RegExp)return t;if(n)return null;throw t}try{let t=a(e);return r?.set(e,t),t}catch(t){if(r?.set(e,t),n)return null;throw t}})}regexps;findNextMatchSync(e,t,n){let r="string"==typeof e?e:e.content,a=[];function o(e,t,n=0){return{index:e,captureIndices:t.indices.map(e=>null==e?{start:0xffffffff,end:0xffffffff,length:0}:{start:e[0]+n,end:e[1]+n,length:e[1]-e[0]})}}for(let e=0;e<this.regexps.length;e++){let n=this.regexps[e];if(n)try{n.lastIndex=t;let s=n.exec(r);if(!s)continue;if(s.index===t)return o(e,s,0);a.push([e,s,0])}catch(e){if(this.options.forgiving)continue;throw e}}if(a.length){let e=Math.min(...a.map(e=>e[1].index));for(let[t,n,r]of a)if(n.index===e)return o(t,n,r)}return null}}}}]);