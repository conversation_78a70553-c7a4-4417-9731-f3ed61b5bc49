!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="d918f257-b14a-4a0a-9d9d-36588fd972d8",e._sentryDebugIdIdentifier="sentry-dbid-d918f257-b14a-4a0a-9d9d-36588fd972d8")}catch(e){}}();"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9058],{15869:(e,r,t)=>{t.d(r,{Eo:()=>c,JT:()=>g,N9:()=>o,Ob:()=>i,_x:()=>d,ab:()=>s});var a=t(29224),n=t(53044);let o=e=>{try{let r=(0,n.A)(e);return`${r.red} ${r.green} ${r.blue}`}catch{return"0 0 0"}},l=e=>`${e.r} ${e.g} ${e.b}`,i=e=>`#${e.r.toString(16).padStart(2,"0")}${e.g.toString(16).padStart(2,"0")}${e.b.toString(16).padStart(2,"0")}`,s=(e,r,t)=>{if(r)return t?r:o(r);let a=d("#09090b",1,e,.02);return t?i(a):l(a)},d=(e,r,t,o)=>{let l=(0,n.A)(e),i=(0,n.A)(t);return(0,a.qb)({r:l.red,g:l.green,b:l.blue,a:r},{r:i.red,g:i.green,b:i.blue,a:o})},u=(e,r)=>{let t=(0,n.A)(r);return l((0,a.qb)({r:e.red,g:e.green,b:e.blue,a:.6},{r:t.red,g:t.green,b:t.blue,a:.95}))},c=e=>{let r={50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717",950:"#0a0a0a"},t=(0,n.A)(e);return{50:u(t,r[50]),100:u(t,r[100]),200:u(t,r[200]),300:u(t,r[300]),400:u(t,r[400]),500:u(t,r[500]),600:u(t,r[600]),700:u(t,r[700]),800:u(t,r[800]),900:u(t,r[900]),950:u(t,r[950])}};function g(e){let r=e?.colors.primary||"#16A34A",t=e?.colors.light??"#4ADE80";e?.theme==="linden"&&(e.background={...e.background,color:{light:e.background?.color?.light||i(d("#FFFFFF",1,r,.03)),dark:e.background?.color?.dark||i(d("#09090B",1,t,.03))}});let a=e?.background?.color?.light??"#ffffff",n=o(a),l=s(t,e?.background?.color?.dark);return{light:n,dark:l,lightHex:a,darkHex:s(t,e?.background?.color?.dark,!0),background:e?.thumbnails?.background}}},29058:(e,r,t)=>{t.d(r,{z:()=>c});var a=t(54568),n=t(27261),o=t.n(n),l=t(85506),i=t(33227),s=t(52992),d=t(39046),u=t(15869);function c({statusCode:e,title:r,description:t}){let n="#4ADE80",o=`:root {
  --primary: ${(0,u.N9)("#117866")};
  --primary-light: ${(0,u.N9)(n)};
  --primary-dark: ${(0,u.N9)("#166534")};
  --background-light: ${(0,u.N9)("#FFFFFF")};
  --background-dark: ${(0,u.ab)(n,"#0f1117")};
}`;return(0,a.jsxs)(l.ThemeProvider,{children:[(0,a.jsx)(s.n,{}),(0,a.jsx)(i.D,{}),(0,a.jsx)(d.x,{}),(0,a.jsx)("style",{children:o}),(0,a.jsx)("main",{className:"h-screen bg-background-light dark:bg-background-dark",children:(0,a.jsx)("article",{className:"bg-custom bg-fixed bg-center bg-cover relative flex flex-col items-center justify-center h-full",children:(0,a.jsxs)("div",{className:"w-full max-w-xl px-10",children:[(0,a.jsxs)("span",{className:"inline-flex mb-6 rounded-full px-3 py-1 text-sm font-semibold mr-4 text-white p-1 bg-primary",children:["Error ",e]}),(0,a.jsx)("h1",{className:"font-semibold mb-3 text-3xl",children:r??"Page not found!"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-400 mb-6",children:t??(()=>{switch(e){case 404:return"We're sorry, we couldn't find the page you were looking for.";case 500:return(0,a.jsxs)(a.Fragment,{children:["An unexpected error occurred. Please ",(0,a.jsx)(g,{})," to get help."]});default:return"An unexpected error occurred. Please contact support."}})()})]})})})]})}function g(){return(0,a.jsx)(o(),{href:"mailto:<EMAIL>",className:"font-medium text-gray-700 dark:text-gray-100 border-b hover:border-b-[2px] border-primary-dark dark:border-primary-light",children:"contact support"})}},29224:(e,r,t)=>{function a(e,r,t){return{r:255*t(e.r/255,r.r/255),g:255*t(e.g/255,r.g/255),b:255*t(e.b/255,r.b/255)}}function n(e,r){return r}function o(e,r,t){return Math.min(Math.max(e||0,r),t)}function l(e){return{r:o(e.r,0,255),g:o(e.g,0,255),b:o(e.b,0,255),a:o(e.a,0,1)}}function i(e){return{r:255*e.r,g:255*e.g,b:255*e.b,a:e.a}}function s(e,r){void 0===r&&(r=0);var t=Math.pow(10,r);return{r:Math.round(e.r*t)/t,g:Math.round(e.g*t)/t,b:Math.round(e.b*t)/t,a:e.a}}function d(e,r,t,a,n,o){return(1-r/t)*a+r/t*Math.round((1-e)*n+e*o)}function u(e,r){return function(e,r,t,a,n){void 0===n&&(n={unitInput:!1,unitOutput:!1,roundOutput:!0}),n.unitInput&&(e=i(e),r=i(r)),e=l(e);var o=(r=l(r)).a+e.a-r.a*e.a,u=t(e,r,a),c=l({r:d(e.a,r.a,o,e.r,r.r,u.r),g:d(e.a,r.a,o,e.g,r.g,u.g),b:d(e.a,r.a,o,e.b,r.b,u.b),a:o});return n.unitOutput?{r:c.r/255,g:c.g/255,b:c.b/255,a:c.a}:n.roundOutput?s(c):s(c,9)}(e,r,a,n)}t.d(r,{qb:()=>u})},33227:(e,r,t)=>{t.d(r,{D:()=>c,H:()=>u});var a=t(54568);let n={fonts:{body:{family:"Google Sans"},heading:{family:"Google Sans"}}},o={fonts:{body:{family:"Geist Mono"},heading:{family:"Geist Mono"}}},l={title:"font-semibold",headings:"font-semibold"};function i({font:e}){return e?e.source?(0,a.jsx)("style",{children:`@font-face {
  font-family: '${e.family}';
  src: url('${e.source}') format('${e.format}');
  font-weight: ${e.weight};
}`}):(0,a.jsx)("link",{href:`https://fonts.googleapis.com/css2?family=${e.family.replace(/\s+/g,"+")}:wght@${e.weight??"400;500;600;700;800"}&display=block`,rel:"stylesheet"}):null}function s({heading:e,body:r}){return(0,a.jsx)("style",{children:`:root {
  ${e?.family?`--font-family-headings-custom: "${e.family}";`:""}
  ${e?.weight?`--font-weight-headings-custom: ${e.weight};`:""}
  ${r?.family?`--font-family-body-custom: "${r.family}";`:""}
  ${r?.weight?`--font-weight-body-custom: ${r.weight};`:""}
}`})}var d=t(69394);function u(e){return"maple"===e?l:"linden"===e?o:"almond"===e?n:{}}function c({theme:e,fonts:r}){let t=u(e).fonts;if(!r&&!t)return null;let n=r??t,o=(0,d.W)(n,"heading"),l=(0,d.W)(n,"body");return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i,{font:o}),(0,a.jsx)(i,{font:l}),(0,a.jsx)(s,{heading:o,body:l})]})}},39046:(e,r,t)=>{t.d(r,{x:()=>n});var a=t(54568);function n({theme:e}){if("linden"!==e)return null;{let e=`:root {
      --rounded-sm: 4px;
      --rounded: 4px;
      --rounded-md: 4px;
      --rounded-lg: 4px;
      --rounded-xl: 4px;
      --rounded-2xl: 4px;
      --rounded-search: 4px;
      --rounded-3xl: 4px;
      --rounded-full: 4px;
  }`;return(0,a.jsx)("style",{children:e})}}},52992:(e,r,t)=>{t.d(r,{n:()=>o});var a=t(54568),n=t(15869);function o({docsConfig:e}){let r=function(e){let r=e?.colors.primary??"#16A34A",t=e?.colors.light??"#4ADE80",a=e?.colors.dark??"#166534",o=e?.colors.primary,l=e?.colors.primary;return e?.theme==="linden"&&(e.background={...e.background,color:{light:e.background?.color?.light||(0,n.Ob)((0,n._x)("#FFFFFF",1,r,.03)),dark:e.background?.color?.dark||(0,n.Ob)((0,n._x)("#09090B",1,t,.03))}}),{primary:(0,n.N9)(r),primaryLight:(0,n.N9)(t),primaryDark:(0,n.N9)(a),backgroundLight:(0,n.N9)(e?.background?.color?.light??"#ffffff"),backgroundDark:(0,n.ab)(t,e?.background?.color?.dark),anchorDefault:o,dropdownDefault:l,gray:(0,n.Eo)(r)}}(e),t=`:root {
    --primary: ${r.primary};
    --primary-light: ${r.primaryLight};
    --primary-dark: ${r.primaryDark};
    --background-light: ${r.backgroundLight};
    --background-dark: ${r.backgroundDark};
    --gray-50: ${r.gray[50]};
    --gray-100: ${r.gray[100]};
    --gray-200: ${r.gray[200]};
    --gray-300: ${r.gray[300]};
    --gray-400: ${r.gray[400]};
    --gray-500: ${r.gray[500]};
    --gray-600: ${r.gray[600]};
    --gray-700: ${r.gray[700]};
    --gray-800: ${r.gray[800]};
    --gray-900: ${r.gray[900]};
    --gray-950: ${r.gray[950]};
  }`;return(0,a.jsx)("style",{children:t})}},53044:(e,r,t)=>{t.d(r,{A:()=>s});let a="a-f\\d",n=`#?[${a}]{3}[${a}]?`,o=`#?[${a}]{6}([${a}]{2})?`,l=RegExp(`[^#${a}]`,"gi"),i=RegExp(`^${n}$|^${o}$`,"i");function s(e,r={}){if("string"!=typeof e||l.test(e)||!i.test(e))throw TypeError("Expected a valid hex string");e=e.replace(/^#/,"");let t=1;8===e.length&&(t=Number.parseInt(e.slice(6,8),16)/255,e=e.slice(0,6)),4===e.length&&(t=Number.parseInt(e.slice(3,4).repeat(2),16)/255,e=e.slice(0,3)),3===e.length&&(e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]);let a=Number.parseInt(e,16),n=a>>16,o=a>>8&255,d=255&a,u="number"==typeof r.alpha?r.alpha:t;if("array"===r.format)return[n,o,d,u];if("css"===r.format){let e=1===u?"":` / ${Number((100*u).toFixed(2))}%`;return`rgb(${n} ${o} ${d}${e})`}return{red:n,green:o,blue:d,alpha:u}}},68309:(e,r,t)=>{t.d(r,{D:()=>d,N:()=>u});var a=t(7620),n=(e,r,t,a,n,o,l,i)=>{let s=document.documentElement,d=["light","dark"];function u(r){var t;(Array.isArray(e)?e:[e]).forEach(e=>{let t="class"===e,a=t&&o?n.map(e=>o[e]||e):n;t?(s.classList.remove(...a),s.classList.add(o&&o[r]?o[r]:r)):s.setAttribute(e,r)}),t=r,i&&d.includes(t)&&(s.style.colorScheme=t)}if(a)u(a);else try{let e=localStorage.getItem(r)||t,a=l&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;u(a)}catch(e){}},o=["light","dark"],l="(prefers-color-scheme: dark)",i=a.createContext(void 0),s={setTheme:e=>{},themes:[]},d=()=>{var e;return null!=(e=a.useContext(i))?e:s},u=e=>a.useContext(i)?a.createElement(a.Fragment,null,e.children):a.createElement(g,{...e}),c=["light","dark"],g=({forcedTheme:e,disableTransitionOnChange:r=!1,enableSystem:t=!0,enableColorScheme:n=!0,storageKey:s="theme",themes:d=c,defaultTheme:u=t?"system":"light",attribute:g="data-theme",value:y,children:p,nonce:k,scriptProps:x})=>{let[$,v]=a.useState(()=>f(s,u)),[w,E]=a.useState(()=>"system"===$?b():$),j=y?Object.values(y):d,N=a.useCallback(e=>{let a=e;if(!a)return;"system"===e&&t&&(a=b());let l=y?y[a]:a,i=r?h(k):null,s=document.documentElement,d=e=>{"class"===e?(s.classList.remove(...j),l&&s.classList.add(l)):e.startsWith("data-")&&(l?s.setAttribute(e,l):s.removeAttribute(e))};if(Array.isArray(g)?g.forEach(d):d(g),n){let e=o.includes(u)?u:null,r=o.includes(a)?a:e;s.style.colorScheme=r}null==i||i()},[k]),S=a.useCallback(e=>{let r="function"==typeof e?e($):e;v(r);try{localStorage.setItem(s,r)}catch(e){}},[$]),A=a.useCallback(r=>{E(b(r)),"system"===$&&t&&!e&&N("system")},[$,e]);a.useEffect(()=>{let e=window.matchMedia(l);return e.addListener(A),A(e),()=>e.removeListener(A)},[A]),a.useEffect(()=>{let e=e=>{e.key===s&&(e.newValue?v(e.newValue):S(u))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[S]),a.useEffect(()=>{N(null!=e?e:$)},[e,$]);let F=a.useMemo(()=>({theme:$,setTheme:S,forcedTheme:e,resolvedTheme:"system"===$?w:$,themes:t?[...d,"system"]:d,systemTheme:t?w:void 0}),[$,S,e,w,t,d]);return a.createElement(i.Provider,{value:F},a.createElement(m,{forcedTheme:e,storageKey:s,attribute:g,enableSystem:t,enableColorScheme:n,defaultTheme:u,value:y,themes:d,nonce:k,scriptProps:x}),p)},m=a.memo(({forcedTheme:e,storageKey:r,attribute:t,enableSystem:o,enableColorScheme:l,defaultTheme:i,value:s,themes:d,nonce:u,scriptProps:c})=>{let g=JSON.stringify([t,r,i,e,d,s,o,l]).slice(1,-1);return a.createElement("script",{...c,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:`(${n.toString()})(${g})`}})}),f=(e,r)=>{let t;try{t=localStorage.getItem(e)||void 0}catch(e){}return t||r},h=e=>{let r=document.createElement("style");return e&&r.setAttribute("nonce",e),r.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(r),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(r)},1)}},b=e=>(e||(e=window.matchMedia(l)),e.matches?"dark":"light")},69394:(e,r,t)=>{t.d(r,{W:()=>a});function a(e,r){if(e){if("family"in e)return e;else if(r in e){let t=e[r];return"string"==typeof t?{family:t}:t}}}},85506:(e,r,t)=>{t.d(r,{ThemeProvider:()=>o});var a=t(54568),n=t(68309);function o({children:e,appearance:r,...t}){return(0,a.jsx)(n.N,{attribute:"class",disableTransitionOnChange:!0,defaultTheme:r?.default,forcedTheme:r?.strict?r.default:void 0,storageKey:"isDarkMode",themes:["dark","light","true","false","system"],value:{true:"dark",false:"light",dark:"dark",light:"light"},enableSystem:!0,...t,children:e})}t(7620)}}]);