!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="72352904-a325-41f9-b9cb-c560d7843821",e._sentryDebugIdIdentifier="sentry-dbid-72352904-a325-41f9-b9cb-c560d7843821")}catch(e){}}();"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[803],{73920:(e,t,n)=>{n.d(t,{Ay:()=>ro});var i="undefined"!=typeof window?window:void 0,r="undefined"!=typeof globalThis?globalThis:i,s=Array.prototype,o=s.forEach,a=s.indexOf,u=null==r?void 0:r.navigator,l=null==r?void 0:r.document,c=null==r?void 0:r.location,d=null==r?void 0:r.fetch,h=null!=r&&r.XMLHttpRequest&&"withCredentials"in new r.XMLHttpRequest?r.XMLHttpRequest:void 0,f=null==r?void 0:r.AbortController,p=null==u?void 0:u.userAgent,v=null!=i?i:{},g={DEBUG:!1,LIB_VERSION:"1.164.3"},_=Array.isArray,m=Object.prototype,y=m.hasOwnProperty,b=m.toString,k=_||function(e){return"[object Array]"===b.call(e)},w=function(e){return"function"==typeof e},S=function(e){return e===Object(e)&&!k(e)},E=function(e){if(S(e)){for(var t in e)if(y.call(e,t))return!1;return!0}return!1},x=function(e){return void 0===e},I=function(e){return"[object String]"==b.call(e)},F=function(e){return I(e)&&0===e.trim().length},P=function(e){return null===e},R=function(e){return x(e)||P(e)},T=function(e){return"[object Number]"==b.call(e)},C=function(e){return"[object Boolean]"===b.call(e)},O="[PostHog.js]",$={_log:function(e){if(i&&(g.DEBUG||v.POSTHOG_DEBUG)&&!x(i.console)&&i.console){for(var t=("__rrweb_original__"in i.console[e])?i.console[e].__rrweb_original__:i.console[e],n=arguments.length,r=Array(n>1?n-1:0),s=1;s<n;s++)r[s-1]=arguments[s];t.apply(void 0,[O].concat(r))}},info:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];$._log.apply($,["log"].concat(t))},warn:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];$._log.apply($,["warn"].concat(t))},error:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];$._log.apply($,["error"].concat(t))},critical:function(){for(var e,t=arguments.length,n=Array(t),i=0;i<t;i++)n[i]=arguments[i];(e=console).error.apply(e,[O].concat(n))},uninitializedWarning:function(e){$.error("You must initialize PostHog before calling ".concat(e))}},A=function(e,t,n){if(e.config.disable_external_dependency_loading)return $.warn("".concat(t," was requested but loading of external scripts is disabled.")),n("Loading of external scripts is disabled");var i=function(){if(!l)return n("document not found");var e=l.createElement("script");e.type="text/javascript",e.src=t,e.onload=function(e){return n(void 0,e)},e.onerror=function(e){return n(e)};var i,r=l.querySelectorAll("body > script");r.length>0?null==(i=r[0].parentNode)||i.insertBefore(e,r[0]):l.body.appendChild(e)};null!=l&&l.body?i():null==l||l.addEventListener("DOMContentLoaded",i)};function M(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,i)}return n}function L(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?M(Object(n),!0).forEach(function(t){H(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):M(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function D(e){return(D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function N(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function q(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function B(e,t,n){return t&&q(e.prototype,t),n&&q(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function H(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function U(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var i,r,s=[],o=!0,a=!1;try{for(n=n.call(e);!(o=(i=n.next()).done)&&(s.push(i.value),!t||s.length!==t);o=!0);}catch(e){a=!0,r=e}finally{try{o||null==n.return||n.return()}finally{if(a)throw r}}return s}}(e,t)||W(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function j(e){return function(e){if(Array.isArray(e))return z(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||W(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function W(e,t){if(e){if("string"==typeof e)return z(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?z(e,t):void 0}}function z(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=Array(t);n<t;n++)i[n]=e[n];return i}function V(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=W(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var i=0,r=function(){};return{s:r,n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:r}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,o=!0,a=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){a=!0,s=e},f:function(){try{o||null==n.return||n.return()}finally{if(a)throw s}}}}v.__PosthogExtensions__=v.__PosthogExtensions__||{},v.__PosthogExtensions__.loadExternalDependency=function(e,t,n){var i="/static/".concat(t,".js")+"?v=".concat(e.version);if("toolbar"===t){var r=3e5*Math.floor(Date.now()/3e5);i="".concat(i,"?&=").concat(r)}var s=e.requestRouter.endpointFor("assets",i);A(e,s,n)},v.__PosthogExtensions__.loadSiteApp=function(e,t,n){var i=e.requestRouter.endpointFor("api",t);A(e,i,n)};var G={},Q=function(e){return e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")};function J(e,t,n){if(k(e)){if(o&&e.forEach===o)e.forEach(t,n);else if("length"in e&&e.length===+e.length){for(var i=0,r=e.length;i<r;i++)if(i in e&&t.call(n,e[i],i)===G)return}}}function Y(e,t,n){if(!R(e)){if(k(e))return J(e,t,n);if(e instanceof FormData){var i,r=V(e.entries());try{for(r.s();!(i=r.n()).done;){var s=i.value;if(t.call(n,s[1],s[0])===G)return}}catch(e){r.e(e)}finally{r.f()}}else for(var o in e)if(y.call(e,o)&&t.call(n,e[o],o)===G)return}}var X=function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];return J(n,function(t){for(var n in t)void 0!==t[n]&&(e[n]=t[n])}),e};function K(e,t){return -1!==e.indexOf(t)}function Z(e){for(var t=Object.keys(e),n=t.length,i=Array(n);n--;)i[n]=[t[n],e[t[n]]];return i}var ee,et=function(e){try{return e()}catch(e){return}},en=function(e){return function(){try{for(var t=arguments.length,n=Array(t),i=0;i<t;i++)n[i]=arguments[i];return e.apply(this,n)}catch(e){$.critical("Implementation error. Please turn on debug mode and open a ticket on https://app.posthog.com/home#panel=support%3Asupport%3A."),$.critical(e)}}},ei=function(e){var t={};return Y(e,function(e,n){I(e)&&e.length>0&&(t[n]=e)}),t},er=function(e){return e.replace(/^\$/,"")},es=function(e){var t,n,i,r,s="";for(t=n=0,i=(e=(e+"").replace(/\r\n/g,"\n").replace(/\r/g,"\n")).length,r=0;r<i;r++){var o=e.charCodeAt(r),a=null;o<128?n++:a=o>127&&o<2048?String.fromCharCode(o>>6|192,63&o|128):String.fromCharCode(o>>12|224,o>>6&63|128,63&o|128),P(a)||(n>t&&(s+=e.substring(t,n)),s+=a,t=n=r+1)}return n>t&&(s+=e.substring(t,e.length)),s},eo=function(){function e(t){return t&&(t.preventDefault=e.preventDefault,t.stopPropagation=e.stopPropagation),t}return e.preventDefault=function(){this.returnValue=!1},e.stopPropagation=function(){this.cancelBubble=!0},function(t,n,r,s,o){if(t)if(t.addEventListener&&!s)t.addEventListener(n,r,!!o);else{var a="on"+n,u=t[a];t[a]=function(n){if(n=n||e(null==i?void 0:i.event)){var s,o=!0;w(u)&&(s=u(n));var a=r.call(t,n);return!1!==s&&!1!==a||(o=!1),o}}}else $.error("No valid element provided to register_event")}}();function ea(e,t){for(var n=0;n<e.length;n++)if(t(e[n]))return e[n]}!function(e){e.GZipJS="gzip-js",e.Base64="base64"}(ee||(ee={}));var eu="$people_distinct_id",el="__alias",ec="__timers",ed="$autocapture_disabled_server_side",eh="$heatmaps_enabled_server_side",ef="$exception_capture_enabled_server_side",ep="$exception_capture_endpoint_suffix",ev="$web_vitals_enabled_server_side",eg="$web_vitals_allowed_metrics",e_="$session_recording_enabled_server_side",em="$console_log_recording_enabled_server_side",ey="$session_recording_network_payload_capture",eb="$session_recording_canvas_recording",ek="$replay_sample_rate",ew="$replay_minimum_duration",eS="$sesid",eE="$session_is_sampled",ex="$enabled_feature_flags",eI="$early_access_features",eF="$stored_person_properties",eP="$stored_group_properties",eR="$surveys",eT="$surveys_activated",eC="$flag_call_reported",eO="$user_state",e$="$client_session_props",eA="$capture_rate_limit",eM="$initial_campaign_params",eL="$initial_referrer_info",eD="$initial_person_info",eN="$epp",eq="__POSTHOG_TOOLBAR__",eB=[eu,el,"__cmpns",ec,e_,eh,eS,ex,eO,eI,eP,eF,eR,eC,e$,eA,eM,eL,eN],eH="$active_feature_flags",eU="$override_feature_flags",ej="$feature_flag_payloads",eW=function(e){var t,n={},i=V(Z(e||{}));try{for(i.s();!(t=i.n()).done;){var r=U(t.value,2),s=r[0],o=r[1];o&&(n[s]=o)}}catch(e){i.e(e)}finally{i.f()}return n},ez=function(){function e(t){N(this,e),this.instance=t,this._override_warning=!1,this.featureFlagEventHandlers=[],this.reloadFeatureFlagsQueued=!1,this.reloadFeatureFlagsInAction=!1}return B(e,[{key:"getFlags",value:function(){return Object.keys(this.getFlagVariants())}},{key:"getFlagVariants",value:function(){var e=this.instance.get_property(ex),t=this.instance.get_property(eU);if(!t)return e||{};for(var n=X({},e),i=Object.keys(t),r=0;r<i.length;r++)n[i[r]]=t[i[r]];return this._override_warning||($.warn(" Overriding feature flags!",{enabledFlags:e,overriddenFlags:t,finalFlags:n}),this._override_warning=!0),n}},{key:"getFlagPayloads",value:function(){return this.instance.get_property(ej)||{}}},{key:"reloadFeatureFlags",value:function(){this.reloadFeatureFlagsQueued||(this.reloadFeatureFlagsQueued=!0,this._startReloadTimer())}},{key:"setAnonymousDistinctId",value:function(e){this.$anon_distinct_id=e}},{key:"setReloadingPaused",value:function(e){this.reloadFeatureFlagsInAction=e}},{key:"resetRequestQueue",value:function(){this.reloadFeatureFlagsQueued=!1}},{key:"_startReloadTimer",value:function(){var e=this;this.reloadFeatureFlagsQueued&&!this.reloadFeatureFlagsInAction&&setTimeout(function(){!e.reloadFeatureFlagsInAction&&e.reloadFeatureFlagsQueued&&(e.reloadFeatureFlagsQueued=!1,e._reloadFeatureFlagsRequest())},5)}},{key:"_reloadFeatureFlagsRequest",value:function(){var e=this;if(!this.instance.config.advanced_disable_feature_flags){this.setReloadingPaused(!0);var t=this.instance.config.token,n=this.instance.get_property(eF),i=this.instance.get_property(eP),r={token:t,distinct_id:this.instance.get_distinct_id(),groups:this.instance.getGroups(),$anon_distinct_id:this.$anon_distinct_id,person_properties:n,group_properties:i,disable_flags:this.instance.config.advanced_disable_feature_flags||void 0};this.instance._send_request({method:"POST",url:this.instance.requestRouter.endpointFor("api","/decide/?v=3"),data:r,compression:this.instance.config.disable_compression?void 0:ee.Base64,timeout:this.instance.config.feature_flag_request_timeout_ms,callback:function(t){e.setReloadingPaused(!1);var n,i=!0;200===t.statusCode&&(e.$anon_distinct_id=void 0,i=!1),e.receivedFeatureFlags(null!=(n=t.json)?n:{},i),e._startReloadTimer()}})}}},{key:"getFeatureFlag",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.instance.decideEndpointWasHit||this.getFlags()&&this.getFlags().length>0){var n,i=this.getFlagVariants()[e],r="".concat(i),s=this.instance.get_property(eC)||{};return!t.send_event&&"send_event"in t||e in s&&s[e].includes(r)||(k(s[e])?s[e].push(r):s[e]=[r],null==(n=this.instance.persistence)||n.register(H({},eC,s)),this.instance.capture("$feature_flag_called",{$feature_flag:e,$feature_flag_response:i})),i}$.warn('getFeatureFlag for key "'+e+"\" failed. Feature flags didn't load in time.")}},{key:"getFeatureFlagPayload",value:function(e){return this.getFlagPayloads()[e]}},{key:"isFeatureEnabled",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.instance.decideEndpointWasHit||this.getFlags()&&this.getFlags().length>0)return!!this.getFeatureFlag(e,t);$.warn('isFeatureEnabled for key "'+e+"\" failed. Feature flags didn't load in time.")}},{key:"addFeatureFlagsHandler",value:function(e){this.featureFlagEventHandlers.push(e)}},{key:"removeFeatureFlagsHandler",value:function(e){this.featureFlagEventHandlers=this.featureFlagEventHandlers.filter(function(t){return t!==e})}},{key:"receivedFeatureFlags",value:function(e,t){if(this.instance.persistence){this.instance.decideEndpointWasHit=!0;var n=this.getFlagVariants(),i=this.getFlagPayloads();!function(e,t){var n,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},s=e.featureFlags,o=e.featureFlagPayloads;if(s)if(k(s)){var a,u={};if(s)for(var l=0;l<s.length;l++)u[s[l]]=!0;t&&t.register((H(a={},eH,s),H(a,ex,u),a))}else{var c=s,d=o;e.errorsWhileComputingFlags&&(c=L(L({},i),c),d=L(L({},r),d)),t&&t.register((H(n={},eH,Object.keys(eW(c))),H(n,ex,c||{}),H(n,ej,d||{}),n))}}(e,this.instance.persistence,n,i),this._fireFeatureFlagsCallbacks(t)}}},{key:"override",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.instance.__loaded||!this.instance.persistence)return $.uninitializedWarning("posthog.feature_flags.override");if(this._override_warning=t,!1===e)this.instance.persistence.unregister(eU);else if(k(e)){for(var n={},i=0;i<e.length;i++)n[e[i]]=!0;this.instance.persistence.register(H({},eU,n))}else this.instance.persistence.register(H({},eU,e))}},{key:"onFeatureFlags",value:function(e){var t=this;if(this.addFeatureFlagsHandler(e),this.instance.decideEndpointWasHit){var n=this._prepareFeatureFlagsForCallbacks();e(n.flags,n.flagVariants)}return function(){return t.removeFeatureFlagsHandler(e)}}},{key:"updateEarlyAccessFeatureEnrollment",value:function(e,t){var n,i,r=H({},"$feature_enrollment/".concat(e),t);this.instance.capture("$feature_enrollment_update",{$feature_flag:e,$feature_enrollment:t,$set:r}),this.setPersonPropertiesForFlags(r,!1);var s=L(L({},this.getFlagVariants()),{},H({},e,t));null==(n=this.instance.persistence)||n.register((H(i={},eH,Object.keys(eW(s))),H(i,ex,s),i)),this._fireFeatureFlagsCallbacks()}},{key:"getEarlyAccessFeatures",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=this.instance.get_property(eI);if(i&&!n)return e(i);this.instance._send_request({transport:"XHR",url:this.instance.requestRouter.endpointFor("api","/api/early_access_features/?token=".concat(this.instance.config.token)),method:"GET",callback:function(n){var i;if(n.json){var r=n.json.earlyAccessFeatures;return null==(i=t.instance.persistence)||i.register(H({},eI,r)),e(r)}}})}},{key:"_prepareFeatureFlagsForCallbacks",value:function(){var e=this.getFlags(),t=this.getFlagVariants();return{flags:e.filter(function(e){return t[e]}),flagVariants:Object.keys(t).filter(function(e){return t[e]}).reduce(function(e,n){return e[n]=t[n],e},{})}}},{key:"_fireFeatureFlagsCallbacks",value:function(e){var t=this._prepareFeatureFlagsForCallbacks(),n=t.flags,i=t.flagVariants;this.featureFlagEventHandlers.forEach(function(t){return t(n,i,{errorsLoading:e})})}},{key:"setPersonPropertiesForFlags",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=this.instance.get_property(eF)||{};this.instance.register(H({},eF,L(L({},n),e))),t&&this.instance.reloadFeatureFlags()}},{key:"resetPersonPropertiesForFlags",value:function(){this.instance.unregister(eF)}},{key:"setGroupPropertiesForFlags",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=this.instance.get_property(eP)||{};0!==Object.keys(n).length&&Object.keys(n).forEach(function(t){n[t]=L(L({},n[t]),e[t]),delete e[t]}),this.instance.register(H({},eP,L(L({},n),e))),t&&this.instance.reloadFeatureFlags()}},{key:"resetGroupPropertiesForFlags",value:function(e){if(e){var t=this.instance.get_property(eP)||{};this.instance.register(H({},eP,L(L({},t),{},H({},e,{}))))}else this.instance.unregister(eP)}}]),e}();Math.trunc||(Math.trunc=function(e){return e<0?Math.ceil(e):Math.floor(e)}),Number.isInteger||(Number.isInteger=function(e){return T(e)&&isFinite(e)&&Math.floor(e)===e});var eV="0123456789abcdef",eG=function(){function e(t){if(N(this,e),this.bytes=t,16!==t.length)throw TypeError("not 128-bit length")}return B(e,[{key:"toString",value:function(){for(var e="",t=0;t<this.bytes.length;t++)e=e+eV.charAt(this.bytes[t]>>>4)+eV.charAt(15&this.bytes[t]),3!==t&&5!==t&&7!==t&&9!==t||(e+="-");if(36!==e.length)throw Error("Invalid UUIDv7 was generated");return e}},{key:"clone",value:function(){return new e(this.bytes.slice(0))}},{key:"equals",value:function(e){return 0===this.compareTo(e)}},{key:"compareTo",value:function(e){for(var t=0;t<16;t++){var n=this.bytes[t]-e.bytes[t];if(0!==n)return Math.sign(n)}return 0}}],[{key:"fromFieldsV7",value:function(t,n,i,r){if(!Number.isInteger(t)||!Number.isInteger(n)||!Number.isInteger(i)||!Number.isInteger(r)||t<0||n<0||i<0||r<0||t>0xffffffffffff||n>4095||i>0x3fffffff||r>0xffffffff)throw RangeError("invalid field value");var s=new Uint8Array(16);return s[0]=t/0x10000000000,s[1]=t/0x100000000,s[2]=t/0x1000000,s[3]=t/65536,s[4]=t/256,s[5]=t,s[6]=112|n>>>8,s[7]=n,s[8]=128|i>>>24,s[9]=i>>>16,s[10]=i>>>8,s[11]=i,s[12]=r>>>24,s[13]=r>>>16,s[14]=r>>>8,s[15]=r,new e(s)}}]),e}(),eQ=function(){function e(){N(this,e),H(this,"timestamp",0),H(this,"counter",0),H(this,"random",new eK)}return B(e,[{key:"generate",value:function(){var e=this.generateOrAbort();if(x(e)){this.timestamp=0;var t=this.generateOrAbort();if(x(t))throw Error("Could not generate UUID after timestamp reset");return t}return e}},{key:"generateOrAbort",value:function(){var e=Date.now();if(e>this.timestamp)this.timestamp=e,this.resetCounter();else{if(!(e+1e4>this.timestamp))return;this.counter++,this.counter>0x3ffffffffff&&(this.timestamp++,this.resetCounter())}return eG.fromFieldsV7(this.timestamp,Math.trunc(this.counter/0x40000000),0x3fffffff&this.counter,this.random.nextUint32())}},{key:"resetCounter",value:function(){this.counter=1024*this.random.nextUint32()+(1023&this.random.nextUint32())}}]),e}(),eJ=function(e){if("undefined"!=typeof UUIDV7_DENY_WEAK_RNG&&UUIDV7_DENY_WEAK_RNG)throw Error("no cryptographically strong RNG available");for(var t=0;t<e.length;t++)e[t]=65536*Math.trunc(65536*Math.random())+Math.trunc(65536*Math.random());return e};i&&!x(i.crypto)&&crypto.getRandomValues&&(eJ=function(e){return crypto.getRandomValues(e)});var eY,eX,eK=function(){function e(){N(this,e),H(this,"buffer",new Uint32Array(8)),H(this,"cursor",1/0)}return B(e,[{key:"nextUint32",value:function(){return this.cursor>=this.buffer.length&&(eJ(this.buffer),this.cursor=0),this.buffer[this.cursor++]}}]),e}(),eZ=function(){return e0().toString()},e0=function(){return(eY||(eY=new eQ)).generate()},e1="",e2=/[a-z0-9][a-z0-9-]+\.[a-z]{2,}$/i,e3={is_supported:function(){return!!l},error:function(e){$.error("cookieStore error: "+e)},get:function(e){if(l){try{for(var t=e+"=",n=l.cookie.split(";").filter(function(e){return e.length}),i=0;i<n.length;i++){for(var r=n[i];" "==r.charAt(0);)r=r.substring(1,r.length);if(0===r.indexOf(t))return decodeURIComponent(r.substring(t.length,r.length))}}catch(e){}return null}},parse:function(e){var t;try{t=JSON.parse(e3.get(e))||{}}catch(e){}return t},set:function(e,t,n,i,r){if(l)try{var s="",o="",a=function(e,t){if(t){var n=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l;if(e1)return e1;if(!t||["localhost","127.0.0.1"].includes(e))return"";for(var n=e.split("."),i=Math.min(n.length,8),r="dmn_chk_"+eZ(),s=RegExp("(^|;)\\s*"+r+"=1");!e1&&i--;){var o=n.slice(i).join("."),a=r+"=1;domain=."+o;t.cookie=a,s.test(t.cookie)&&(t.cookie=a+";expires=Thu, 01 Jan 1970 00:00:00 GMT",e1=o)}return e1}(e);if(!n){var i,r=(i=e.match(e2))?i[0]:"";r!==n&&$.info("Warning: cookie subdomain discovery mismatch",r,n),n=r}return n?"; domain=."+n:""}return""}(l.location.hostname,i);if(n){var u=new Date;u.setTime(u.getTime()+24*n*36e5),s="; expires="+u.toUTCString()}r&&(o="; secure");var c=e+"="+encodeURIComponent(JSON.stringify(t))+s+"; SameSite=Lax; path=/"+a+o;return c.length>3686.4&&$.warn("cookieStore warning: large cookie, len="+c.length),l.cookie=c,c}catch(e){return}},remove:function(e,t){try{e3.set(e,"",-1,t)}catch(e){return}}},e5=null,e6={is_supported:function(){if(!P(e5))return e5;var e=!0;if(x(i))e=!1;else try{var t="__mplssupport__";e6.set(t,"xyz"),'"xyz"'!==e6.get(t)&&(e=!1),e6.remove(t)}catch(t){e=!1}return e||$.error("localStorage unsupported; falling back to cookie store"),e5=e,e},error:function(e){$.error("localStorage error: "+e)},get:function(e){try{return null==i?void 0:i.localStorage.getItem(e)}catch(e){e6.error(e)}return null},parse:function(e){try{return JSON.parse(e6.get(e))||{}}catch(e){}return null},set:function(e,t){try{null==i||i.localStorage.setItem(e,JSON.stringify(t))}catch(e){e6.error(e)}},remove:function(e){try{null==i||i.localStorage.removeItem(e)}catch(e){e6.error(e)}}},e8=["distinct_id",eS,eE,eN],e4=L(L({},e6),{},{parse:function(e){try{var t={};try{t=e3.parse(e)||{}}catch(e){}var n=X(t,JSON.parse(e6.get(e)||"{}"));return e6.set(e,n),n}catch(e){}return null},set:function(e,t,n,i,r,s){try{e6.set(e,t,void 0,void 0,s);var o={};e8.forEach(function(e){t[e]&&(o[e]=t[e])}),Object.keys(o).length&&e3.set(e,o,n,i,r,s)}catch(e){e6.error(e)}},remove:function(e,t){try{null==i||i.localStorage.removeItem(e),e3.remove(e,t)}catch(e){e6.error(e)}}}),e7={},e9={is_supported:function(){return!0},error:function(e){$.error("memoryStorage error: "+e)},get:function(e){return e7[e]||null},parse:function(e){return e7[e]||null},set:function(e,t){e7[e]=t},remove:function(e){delete e7[e]}},te=null,tt={is_supported:function(){if(!P(te))return te;if(te=!0,x(i))te=!1;else try{var e="__support__";tt.set(e,"xyz"),'"xyz"'!==tt.get(e)&&(te=!1),tt.remove(e)}catch(e){te=!1}return te},error:function(e){$.error("sessionStorage error: ",e)},get:function(e){try{return null==i?void 0:i.sessionStorage.getItem(e)}catch(e){tt.error(e)}return null},parse:function(e){try{return JSON.parse(tt.get(e))||null}catch(e){}return null},set:function(e,t){try{null==i||i.sessionStorage.setItem(e,JSON.stringify(t))}catch(e){tt.error(e)}},remove:function(e){try{null==i||i.sessionStorage.removeItem(e)}catch(e){tt.error(e)}}},tn=["localhost","127.0.0.1"],ti=function(e){var t=null==l?void 0:l.createElement("a");return x(t)?null:(t.href=e,t)},tr=function(e,t){return!!function(e){try{new RegExp(e)}catch(e){return!1}return!0}(t)&&new RegExp(t).test(e)},ts=function(e){var t,n,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"&",r=[];return Y(e,function(e,i){x(e)||x(i)||"undefined"===i||(t=encodeURIComponent(e instanceof File?e.name:e.toString()),n=encodeURIComponent(i),r[r.length]=n+"="+t)}),r.join(i)},to=function(e,t){for(var n,i=((e.split("#")[0]||"").split("?")[1]||"").split("&"),r=0;r<i.length;r++){var s=i[r].split("=");if(s[0]===t){n=s;break}}if(!k(n)||n.length<2)return"";var o=n[1];try{o=decodeURIComponent(o)}catch(e){$.error("Skipping decoding for malformed query param: "+o)}return o.replace(/\+/g," ")},ta=function(e,t){var n=e.match(RegExp(t+"=([^&]*)"));return n?n[1]:null},tu="Mobile",tl="Android",tc="Tablet",td=tl+" "+tc,th="iPad",tf="Apple",tp=tf+" Watch",tv="Safari",tg="BlackBerry",t_="Samsung",tm=t_+"Browser",ty=t_+" Internet",tb="Chrome",tk=tb+" OS",tw=tb+" iOS",tS="Internet Explorer",tE=tS+" "+tu,tx="Opera",tI=tx+" Mini",tF="Edge",tP="Microsoft "+tF,tR="Firefox",tT=tR+" iOS",tC="Nintendo",tO="PlayStation",t$="Xbox",tA=tl+" "+tu,tM=tu+" "+tv,tL="Windows",tD=tL+" Phone",tN="Nokia",tq="Ouya",tB="Generic",tH=tB+" "+tu.toLowerCase(),tU=tB+" "+tc.toLowerCase(),tj="Konqueror",tW="(\\d+(\\.\\d+)?)",tz=RegExp("Version/"+tW),tV=RegExp(t$,"i"),tG=RegExp(tO+" \\w+","i"),tQ=RegExp(tC+" \\w+","i"),tJ=RegExp(tg+"|PlayBook|BB10","i"),tY={"NT3.51":"NT 3.11","NT4.0":"NT 4.0","5.0":"2000",5.1:"XP",5.2:"XP","6.0":"Vista",6.1:"7",6.2:"8",6.3:"8.1",6.4:"10","10.0":"10"},tX=function(e,t){return t&&K(t,tf)||K(e,tv)&&!K(e,tb)&&!K(e,tl)},tK=function(e,t){return t=t||"",K(e," OPR/")&&K(e,"Mini")?tI:K(e," OPR/")?tx:tJ.test(e)?tg:K(e,"IE"+tu)||K(e,"WPDesktop")?tE:K(e,tm)?ty:K(e,tF)||K(e,"Edg/")?tP:K(e,"FBIOS")?"Facebook "+tu:K(e,"UCWEB")||K(e,"UCBrowser")?"UC Browser":K(e,"CriOS")?tw:K(e,"CrMo")?tb:K(e,tl)&&K(e,tv)?tA:K(e,tb)?tb:K(e,"FxiOS")?tT:K(e.toLowerCase(),tj.toLowerCase())?tj:tX(e,t)?K(e,tu)?tM:tv:K(e,tR)?tR:K(e,"MSIE")||K(e,"Trident/")?tS:K(e,"Gecko")?tR:""},tZ=(H(eX={},tE,[RegExp("rv:"+tW)]),H(eX,tP,[RegExp(tF+"?\\/"+tW)]),H(eX,tb,[RegExp("("+tb+"|CrMo)\\/"+tW)]),H(eX,tw,[RegExp("CriOS\\/"+tW)]),H(eX,"UC Browser",[RegExp("(UCBrowser|UCWEB)\\/"+tW)]),H(eX,tv,[tz]),H(eX,tM,[tz]),H(eX,tx,[RegExp("(Opera|OPR)\\/"+tW)]),H(eX,tR,[RegExp(tR+"\\/"+tW)]),H(eX,tT,[RegExp("FxiOS\\/"+tW)]),H(eX,tj,[RegExp("Konqueror[:/]?"+tW,"i")]),H(eX,tg,[RegExp(tg+" "+tW),tz]),H(eX,tA,[RegExp("android\\s"+tW,"i")]),H(eX,ty,[RegExp(tm+"\\/"+tW)]),H(eX,tS,[RegExp("(rv:|MSIE )"+tW)]),H(eX,"Mozilla",[RegExp("rv:"+tW)]),eX),t0=[[RegExp(t$+"; "+t$+" (.*?)[);]","i"),function(e){return[t$,e&&e[1]||""]}],[RegExp(tC,"i"),[tC,""]],[RegExp(tO,"i"),[tO,""]],[tJ,[tg,""]],[RegExp(tL,"i"),function(e,t){if(/Phone/.test(t)||/WPDesktop/.test(t))return[tD,""];if(new RegExp(tu).test(t)&&!/IEMobile\b/.test(t))return[tL+" "+tu,""];var n=/Windows NT ([0-9.]+)/i.exec(t);if(n&&n[1]){var i=tY[n[1]]||"";return/arm/i.test(t)&&(i="RT"),[tL,i]}return[tL,""]}],[/((iPhone|iPad|iPod).*?OS (\d+)_(\d+)_?(\d+)?|iPhone)/,function(e){return e&&e[3]?["iOS",[e[3],e[4],e[5]||"0"].join(".")]:["iOS",""]}],[/(watch.*\/(\d+\.\d+\.\d+)|watch os,(\d+\.\d+),)/i,function(e){var t="";return e&&e.length>=3&&(t=x(e[2])?e[3]:e[2]),["watchOS",t]}],[RegExp("("+tl+" (\\d+)\\.(\\d+)\\.?(\\d+)?|"+tl+")","i"),function(e){return e&&e[2]?[tl,[e[2],e[3],e[4]||"0"].join(".")]:[tl,""]}],[/Mac OS X (\d+)[_.](\d+)[_.]?(\d+)?/i,function(e){var t=["Mac OS X",""];if(e&&e[1]){var n=[e[1],e[2],e[3]||"0"];t[1]=n.join(".")}return t}],[/Mac/i,["Mac OS X",""]],[/CrOS/,[tk,""]],[/Linux|debian/i,["Linux",""]]],t1=function(e){return tQ.test(e)?tC:tG.test(e)?tO:tV.test(e)?t$:RegExp(tq,"i").test(e)?tq:RegExp("("+tD+"|WPDesktop)","i").test(e)?tD:/iPad/.test(e)?th:/iPod/.test(e)?"iPod Touch":/iPhone/.test(e)?"iPhone":/(watch)(?: ?os[,/]|\d,\d\/)[\d.]+/i.test(e)?tp:tJ.test(e)?tg:/(kobo)\s(ereader|touch)/i.test(e)?"Kobo":RegExp(tN,"i").test(e)?tN:/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i.test(e)||/(kf[a-z]+)( bui|\)).+silk\//i.test(e)?"Kindle Fire":/(Android|ZTE)/i.test(e)?!new RegExp(tu).test(e)||/(9138B|TB782B|Nexus [97]|pixel c|HUAWEISHT|BTV|noble nook|smart ultra 6)/i.test(e)?/pixel[\daxl ]{1,6}/i.test(e)&&!/pixel c/i.test(e)||/(huaweimed-al00|tah-|APA|SM-G92|i980|zte|U304AA)/i.test(e)||/lmy47v/i.test(e)&&!/QTAQZ3/i.test(e)?tl:td:tl:RegExp("(pda|"+tu+")","i").test(e)?tH:RegExp(tc,"i").test(e)&&!RegExp(tc+" pc","i").test(e)?tU:""},t2="https?://(.*)",t3=["utm_source","utm_medium","utm_campaign","utm_content","utm_term","gclid","gad_source","gclsrc","dclid","gbraid","wbraid","fbclid","msclkid","twclid","li_fat_id","mc_cid","igshid","ttclid","rdt_cid"],t5={campaignParams:function(e){return l?this._campaignParamsFromUrl(l.URL,e):{}},_campaignParamsFromUrl:function(e,t){var n=t3.concat(t||[]),i={};return Y(n,function(t){var n=to(e,t);n&&(i[t]=n)}),i},_searchEngine:function(e){return e?0===e.search(t2+"google.([^/?]*)")?"google":0===e.search(t2+"bing.com")?"bing":0===e.search(t2+"yahoo.com")?"yahoo":0===e.search(t2+"duckduckgo.com")?"duckduckgo":null:null},_searchInfoFromReferrer:function(e){var t=t5._searchEngine(e),n={};if(!P(t)){n.$search_engine=t;var i=l?to(l.referrer,"yahoo"!=t?"q":"p"):"";i.length&&(n.ph_keyword=i)}return n},searchInfo:function(){var e=null==l?void 0:l.referrer;return e?this._searchInfoFromReferrer(e):{}},browser:tK,browserVersion:function(e,t){var n=tZ[tK(e,t)];if(x(n))return null;for(var i=0;i<n.length;i++){var r=n[i],s=e.match(r);if(s)return parseFloat(s[s.length-2])}return null},browserLanguage:function(){return navigator.language||navigator.userLanguage},os:function(e){for(var t=0;t<t0.length;t++){var n=U(t0[t],2),i=n[0],r=n[1],s=i.exec(e),o=s&&(w(r)?r(s,e):r);if(o)return o}return["",""]},device:t1,deviceType:function(e){var t=t1(e);return t===th||t===td||"Kobo"===t||"Kindle Fire"===t||t===tU?tc:t===tC||t===t$||t===tO||t===tq?"Console":t===tp?"Wearable":t?tu:"Desktop"},referrer:function(){return(null==l?void 0:l.referrer)||"$direct"},referringDomain:function(){var e;return null!=l&&l.referrer&&(null==(e=ti(l.referrer))?void 0:e.host)||"$direct"},referrerInfo:function(){return{$referrer:this.referrer(),$referring_domain:this.referringDomain()}},initialPersonInfo:function(){return{r:this.referrer(),u:null==c?void 0:c.href}},initialPersonPropsFromInfo:function(e){var t,n=e.r,i=e.u,r={$initial_referrer:n,$initial_referring_domain:null==n?void 0:"$direct"==n?"$direct":null==(t=ti(n))?void 0:t.host};if(i){r.$initial_current_url=i;var s=ti(i);r.$initial_host=null==s?void 0:s.host,r.$initial_pathname=null==s?void 0:s.pathname,Y(this._campaignParamsFromUrl(i),function(e,t){r["$initial_"+er(t)]=e})}return n&&Y(this._searchInfoFromReferrer(n),function(e,t){r["$initial_"+er(t)]=e}),r},properties:function(){if(!p)return{};var e=U(t5.os(p),2);return X(ei({$os:e[0],$os_version:e[1],$browser:t5.browser(p,navigator.vendor),$device:t5.device(p),$device_type:t5.deviceType(p)}),{$current_url:null==c?void 0:c.href,$host:null==c?void 0:c.host,$pathname:null==c?void 0:c.pathname,$raw_user_agent:p.length>1e3?p.substring(0,997)+"...":p,$browser_version:t5.browserVersion(p,navigator.vendor),$browser_language:t5.browserLanguage(),$screen_height:null==i?void 0:i.screen.height,$screen_width:null==i?void 0:i.screen.width,$viewport_height:null==i?void 0:i.innerHeight,$viewport_width:null==i?void 0:i.innerWidth,$lib:"web",$lib_version:g.LIB_VERSION,$insert_id:Math.random().toString(36).substring(2,10)+Math.random().toString(36).substring(2,10),$time:Date.now()/1e3})},people_properties:function(){if(!p)return{};var e=U(t5.os(p),2);return X(ei({$os:e[0],$os_version:e[1],$browser:t5.browser(p,navigator.vendor)}),{$browser_version:t5.browserVersion(p,navigator.vendor)})}},t6=["cookie","localstorage","localstorage+cookie","sessionstorage","memory"],t8=function(){function e(t){var n;N(this,e),this.config=t,this.props={},this.campaign_params_saved=!1,this.name=(n="",t.token&&(n=t.token.replace(/\+/g,"PL").replace(/\//g,"SL").replace(/=/g,"EQ")),t.persistence_name?"ph_"+t.persistence_name:"ph_"+n+"_posthog"),this.storage=this.buildStorage(t),this.load(),t.debug&&$.info("Persistence loaded",t.persistence,L({},this.props)),this.update_config(t,t),this.save()}return B(e,[{key:"buildStorage",value:function(e){-1===t6.indexOf(e.persistence.toLowerCase())&&($.critical("Unknown persistence type "+e.persistence+"; falling back to localStorage+cookie"),e.persistence="localStorage+cookie");var t=e.persistence.toLowerCase();return"localstorage"===t&&e6.is_supported()?e6:"localstorage+cookie"===t&&e4.is_supported()?e4:"sessionstorage"===t&&tt.is_supported()?tt:"memory"===t?e9:"cookie"===t?e3:e4.is_supported()?e4:e3}},{key:"properties",value:function(){var e={};return Y(this.props,function(t,n){if(n===ex&&S(t))for(var i,r=Object.keys(t),s=0;s<r.length;s++)e["$feature/".concat(r[s])]=t[r[s]];else i=!1,(P(eB)?i:a&&eB.indexOf===a?-1!=eB.indexOf(n):(Y(eB,function(e){if(i||(i=e===n))return G}),i))||(e[n]=t)}),e}},{key:"load",value:function(){if(!this.disabled){var e=this.storage.parse(this.name);e&&(this.props=X({},e))}}},{key:"save",value:function(){this.disabled||this.storage.set(this.name,this.props,this.expire_days,this.cross_subdomain,this.secure,this.config.debug)}},{key:"remove",value:function(){this.storage.remove(this.name,!1),this.storage.remove(this.name,!0)}},{key:"clear",value:function(){this.remove(),this.props={}}},{key:"register_once",value:function(e,t,n){var i=this;if(S(e)){x(t)&&(t="None"),this.expire_days=x(n)?this.default_expiry:n;var r=!1;if(Y(e,function(e,n){i.props.hasOwnProperty(n)&&i.props[n]!==t||(i.props[n]=e,r=!0)}),r)return this.save(),!0}return!1}},{key:"register",value:function(e,t){var n=this;if(S(e)){this.expire_days=x(t)?this.default_expiry:t;var i=!1;if(Y(e,function(t,r){e.hasOwnProperty(r)&&n.props[r]!==t&&(n.props[r]=t,i=!0)}),i)return this.save(),!0}return!1}},{key:"unregister",value:function(e){e in this.props&&(delete this.props[e],this.save())}},{key:"update_campaign_params",value:function(){this.campaign_params_saved||(this.register(t5.campaignParams(this.config.custom_campaign_params)),this.campaign_params_saved=!0)}},{key:"update_search_keyword",value:function(){this.register(t5.searchInfo())}},{key:"update_referrer_info",value:function(){this.register_once(t5.referrerInfo(),void 0)}},{key:"set_initial_person_info",value:function(){this.props[eM]||this.props[eL]||this.register_once(H({},eD,t5.initialPersonInfo()),void 0)}},{key:"get_referrer_info",value:function(){return ei({$referrer:this.props.$referrer,$referring_domain:this.props.$referring_domain})}},{key:"get_initial_props",value:function(){var e=this,t={};Y([eL,eM],function(n){var i=e.props[n];i&&Y(i,function(e,n){t["$initial_"+er(n)]=e})});var n=this.props[eD];return n&&X(t,t5.initialPersonPropsFromInfo(n)),t}},{key:"safe_merge",value:function(e){return Y(this.props,function(t,n){n in e||(e[n]=t)}),e}},{key:"update_config",value:function(e,t){if(this.default_expiry=this.expire_days=e.cookie_expiration,this.set_disabled(e.disable_persistence),this.set_cross_subdomain(e.cross_subdomain_cookie),this.set_secure(e.secure_cookie),e.persistence!==t.persistence){var n=this.buildStorage(e),i=this.props;this.clear(),this.storage=n,this.props=i,this.save()}}},{key:"set_disabled",value:function(e){this.disabled=e,this.disabled?this.remove():this.save()}},{key:"set_cross_subdomain",value:function(e){e!==this.cross_subdomain&&(this.cross_subdomain=e,this.remove(),this.save())}},{key:"get_cross_subdomain",value:function(){return!!this.cross_subdomain}},{key:"set_secure",value:function(e){e!==this.secure&&(this.secure=e,this.remove(),this.save())}},{key:"set_event_timer",value:function(e,t){var n=this.props[ec]||{};n[e]=t,this.props[ec]=n,this.save()}},{key:"remove_event_timer",value:function(e){var t=(this.props[ec]||{})[e];return x(t)||(delete this.props[ec][e],this.save()),t}},{key:"get_property",value:function(e){return this.props[e]}},{key:"set_property",value:function(e,t){this.props[e]=t,this.save()}}]),e}();function t4(e){var t;return JSON.stringify(e,(t=[],function(e,n){if(S(n)){for(;t.length>0&&t.at(-1)!==this;)t.pop();return t.includes(n)?"[Circular]":(t.push(n),n)}return n})).length}var t7=function(e){return e[e.DomContentLoaded=0]="DomContentLoaded",e[e.Load=1]="Load",e[e.FullSnapshot=2]="FullSnapshot",e[e.IncrementalSnapshot=3]="IncrementalSnapshot",e[e.Meta=4]="Meta",e[e.Custom=5]="Custom",e[e.Plugin=6]="Plugin",e}(t7||{}),t9=function(e){return e[e.Mutation=0]="Mutation",e[e.MouseMove=1]="MouseMove",e[e.MouseInteraction=2]="MouseInteraction",e[e.Scroll=3]="Scroll",e[e.ViewportResize=4]="ViewportResize",e[e.Input=5]="Input",e[e.TouchMove=6]="TouchMove",e[e.MediaInteraction=7]="MediaInteraction",e[e.StyleSheetRule=8]="StyleSheetRule",e[e.CanvasMutation=9]="CanvasMutation",e[e.Font=10]="Font",e[e.Log=11]="Log",e[e.Drag=12]="Drag",e[e.StyleDeclaration=13]="StyleDeclaration",e[e.Selection=14]="Selection",e[e.AdoptedStyleSheet=15]="AdoptedStyleSheet",e[e.CustomElement=16]="CustomElement",e}(t9||{});function ne(e){return e?Q(e).split(/\s+/):[]}function nt(e){var t=null==i?void 0:i.location.href;return!!(t&&e&&e.some(function(e){return t.match(e)}))}function nn(e){var t="";switch(D(e.className)){case"string":t=e.className;break;case"object":t=(e.className&&"baseVal"in e.className?e.className.baseVal:null)||e.getAttribute("class")||"";break;default:t=""}return ne(t)}function ni(e){return R(e)?null:Q(e).split(/(\s+)/).filter(function(e){return nb(e)}).join("").replace(/[\r\n]/g," ").replace(/[ ]+/g," ").substring(0,255)}function nr(e){var t="";return nh(e)&&!nf(e)&&e.childNodes&&e.childNodes.length&&Y(e.childNodes,function(e){var n;nu(e)&&e.textContent&&(t+=null!=(n=ni(e.textContent))?n:"")}),Q(t)}function ns(e){var t;return x(e.target)?e.srcElement||null:null!=(t=e.target)&&t.shadowRoot?e.composedPath()[0]||null:e.target||null}function no(e){return!!e&&1===e.nodeType}function na(e,t){return!!e&&!!e.tagName&&e.tagName.toLowerCase()===t.toLowerCase()}function nu(e){return!!e&&3===e.nodeType}function nl(e){return!!e&&11===e.nodeType}var nc=["a","button","form","input","select","textarea","label"];function nd(e){var t=e.parentNode;return!(!t||!no(t))&&t}function nh(e){for(var t=e;t.parentNode&&!na(t,"body");t=t.parentNode){var n=nn(t);if(K(n,"ph-sensitive")||K(n,"ph-no-capture"))return!1}if(K(nn(e),"ph-include"))return!0;var i=e.type||"";if(I(i))switch(i.toLowerCase()){case"hidden":case"password":return!1}var r=e.name||e.id||"";return!(I(r)&&/^cc|cardnum|ccnum|creditcard|csc|cvc|cvv|exp|pass|pwd|routing|seccode|securitycode|securitynum|socialsec|socsec|ssn/i.test(r.replace(/[^a-zA-Z0-9]/g,"")))}function nf(e){return!!(na(e,"input")&&!["button","checkbox","submit","reset"].includes(e.type)||na(e,"select")||na(e,"textarea")||"true"===e.getAttribute("contenteditable"))}var np="(4[0-9]{12}(?:[0-9]{3})?)|(5[1-5][0-9]{14})|(6(?:011|5[0-9]{2})[0-9]{12})|(3[47][0-9]{13})|(3(?:0[0-5]|[68][0-9])[0-9]{11})|((?:2131|1800|35[0-9]{3})[0-9]{11})",nv=new RegExp("^(?:".concat(np,")$")),ng=new RegExp(np),n_="\\d{3}-?\\d{2}-?\\d{4}",nm=new RegExp("^(".concat(n_,")$")),ny=new RegExp("(".concat(n_,")"));function nb(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return!(R(e)||I(e)&&(e=Q(e),(t?nv:ng).test((e||"").replace(/[- ]/g,""))||(t?nm:ny).test(e)))&&!0}function nk(e){var t=nr(e);return nb(t="".concat(t," ").concat(function e(t){var n="";return t&&t.childNodes&&t.childNodes.length&&Y(t.childNodes,function(t){var i;if(t&&"span"===(null==(i=t.tagName)?void 0:i.toLowerCase()))try{var r=nr(t);n="".concat(n," ").concat(r).trim(),t.childNodes&&t.childNodes.length&&(n="".concat(n," ").concat(e(t)).trim())}catch(e){$.error(e)}}),n}(e)).trim())?t:""}function nw(e){return e.replace(/"|\\"/g,'\\"')}var nS="[SessionRecording]",nE="redacted",nx={initiatorTypes:["audio","beacon","body","css","early-hint","embed","fetch","frame","iframe","icon","image","img","input","link","navigation","object","ping","script","track","video","xmlhttprequest"],maskRequestFn:function(e){return e},recordHeaders:!1,recordBody:!1,recordInitialRequests:!1,recordPerformance:!1,performanceEntryTypeToObserve:["first-input","navigation","paint","resource"],payloadSizeLimitBytes:1e6,payloadHostDenyList:[".lr-ingest.io",".ingest.sentry.io"]},nI=["authorization","x-forwarded-for","authorization","cookie","set-cookie","x-api-key","x-real-ip","remote-addr","forwarded","proxy-authorization","x-csrf-token","x-csrftoken","x-xsrf-token"],nF=["password","secret","passwd","api_key","apikey","auth","credentials","mysql_pwd","privatekey","private_key","token"],nP=["/s/","/e/","/i/"];function nR(e,t,n,i){if(R(e))return e;var r=(null==t?void 0:t["content-length"])||new Blob([e]).size;return I(r)&&(r=parseInt(r)),r>n?nS+" ".concat(i," body too large to record (").concat(r," bytes)"):e}function nT(e,t){if(R(e))return e;var n=e;return nb(n,!1)||(n=nS+" "+t+" body "+nE),Y(nF,function(e){var i,r;null!=(i=n)&&i.length&&-1!==(null==(r=n)?void 0:r.indexOf(e))&&(n=nS+" "+t+" body "+nE+" as might contain: "+e)}),n}var nC,nO=function(e,t){var n,i,r={payloadSizeLimitBytes:nx.payloadSizeLimitBytes,performanceEntryTypeToObserve:j(nx.performanceEntryTypeToObserve),payloadHostDenyList:[].concat(j(t.payloadHostDenyList||[]),j(nx.payloadHostDenyList))},s=!1!==e.session_recording.recordHeaders&&t.recordHeaders,o=!1!==e.session_recording.recordBody&&t.recordBody,a=!1!==e.capture_performance&&t.recordPerformance,u=(i=Math.min(1e6,null!=(n=r.payloadSizeLimitBytes)?n:1e6),function(e){return null!=e&&e.requestBody&&(e.requestBody=nR(e.requestBody,e.requestHeaders,i,"Request")),null!=e&&e.responseBody&&(e.responseBody=nR(e.responseBody,e.responseHeaders,i,"Response")),e}),l=function(e){var t;return u(function(e){var t=ti(e.name);if(!(t&&t.pathname&&nP.some(function(e){return 0===t.pathname.indexOf(e)})))return e}((R(t=e.requestHeaders)||Y(Object.keys(null!=t?t:{}),function(e){nI.includes(e.toLowerCase())&&(t[e]=nE)}),e)))},c=w(e.session_recording.maskNetworkRequestFn);return c&&w(e.session_recording.maskCapturedNetworkRequestFn)&&$.warn("Both `maskNetworkRequestFn` and `maskCapturedNetworkRequestFn` are defined. `maskNetworkRequestFn` will be ignored."),c&&(e.session_recording.maskCapturedNetworkRequestFn=function(t){var n=e.session_recording.maskNetworkRequestFn({url:t.name});return L(L({},t),{},{name:null==n?void 0:n.url})}),r.maskRequestFn=w(e.session_recording.maskCapturedNetworkRequestFn)?function(t){var n,i,r,s=l(t);return s&&null!=(n=null==(i=(r=e.session_recording).maskCapturedNetworkRequestFn)?void 0:i.call(r,s))?n:void 0}:function(e){var t=l(e);return x(t)?void 0:(t.requestBody=nT(t.requestBody,"Request"),t.responseBody=nT(t.responseBody,"Response"),t)},L(L(L({},nx),r),{},{recordHeaders:s,recordBody:o,recordPerformance:a,recordInitialRequests:a})},n$=B(function e(t){var n,i,r=this,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};N(this,e),H(this,"bucketSize",100),H(this,"refillRate",10),H(this,"mutationBuckets",{}),H(this,"loggedTracker",{}),H(this,"refillBuckets",function(){Object.keys(r.mutationBuckets).forEach(function(e){r.mutationBuckets[e]=r.mutationBuckets[e]+r.refillRate,r.mutationBuckets[e]>=r.bucketSize&&delete r.mutationBuckets[e]})}),H(this,"getNodeOrRelevantParent",function(e){var t=r.rrweb.mirror.getNode(e);if("svg"!==(null==t?void 0:t.nodeName)&&t instanceof Element){var n=t.closest("svg");if(n)return[r.rrweb.mirror.getId(n),n]}return[e,t]}),H(this,"numberOfChanges",function(e){var t,n,i,r,s,o,a,u;return(null!=(t=null==(n=e.removes)?void 0:n.length)?t:0)+(null!=(i=null==(r=e.attributes)?void 0:r.length)?i:0)+(null!=(s=null==(o=e.texts)?void 0:o.length)?s:0)+(null!=(a=null==(u=e.adds)?void 0:u.length)?a:0)}),H(this,"throttleMutations",function(e){if(3!==e.type||0!==e.data.source)return e;var t=e.data,n=r.numberOfChanges(t);t.attributes&&(t.attributes=t.attributes.filter(function(e){var t,n,i,s=U(r.getNodeOrRelevantParent(e.id),2),o=s[0],a=s[1];return 0!==r.mutationBuckets[o]&&(r.mutationBuckets[o]=null!=(t=r.mutationBuckets[o])?t:r.bucketSize,r.mutationBuckets[o]=Math.max(r.mutationBuckets[o]-1,0),0===r.mutationBuckets[o]&&(r.loggedTracker[o]||(r.loggedTracker[o]=!0,null==(n=(i=r.options).onBlockedNode)||n.call(i,o,a))),e)}));var i=r.numberOfChanges(t);return 0!==i||n===i?e:void 0}),this.rrweb=t,this.options=s,this.refillRate=null!=(n=this.options.refillRate)?n:this.refillRate,this.bucketSize=null!=(i=this.options.bucketSize)?i:this.bucketSize,setInterval(function(){r.refillBuckets()},1e3)}),nA=[t9.MouseMove,t9.MouseInteraction,t9.Scroll,t9.ViewportResize,t9.Input,t9.TouchMove,t9.MediaInteraction,t9.Drag],nM=function(e){return{rrwebMethod:e,enqueuedAt:Date.now(),attempt:1}},nL="[SessionRecording]",nD=function(){function e(t){var n=this;if(N(this,e),H(this,"queuedRRWebEvents",[]),H(this,"isIdle",!1),H(this,"_linkedFlagSeen",!1),H(this,"_lastActivityTimestamp",Date.now()),H(this,"_linkedFlag",null),H(this,"_removePageViewCaptureHook",void 0),H(this,"_onSessionIdListener",void 0),H(this,"_persistDecideOnSessionListener",void 0),H(this,"_samplingSessionListener",void 0),H(this,"_forceAllowLocalhostNetworkCapture",!1),H(this,"_onBeforeUnload",function(){n._flushBuffer()}),H(this,"_onOffline",function(){n._tryAddCustomEvent("browser offline",{})}),H(this,"_onOnline",function(){n._tryAddCustomEvent("browser online",{})}),H(this,"_onVisibilityChange",function(){if(null!=l&&l.visibilityState){var e="window "+l.visibilityState;n._tryAddCustomEvent(e,{})}}),this.instance=t,this._captureStarted=!1,this._endpoint="/s/",this.stopRrweb=void 0,this.receivedDecide=!1,!this.instance.sessionManager)throw $.error(nL+" started without valid sessionManager"),Error(nL+" started without valid sessionManager. This is a bug.");var i=this.sessionManager.checkAndGetSessionAndWindowId(),r=i.sessionId,s=i.windowId;this.sessionId=r,this.windowId=s,this.buffer=this.clearBuffer()}return B(e,[{key:"rrwebRecord",get:function(){var e,t;return null==v||null==(e=v.__PosthogExtensions__)||null==(t=e.rrweb)?void 0:t.record}},{key:"started",get:function(){return this._captureStarted}},{key:"sessionManager",get:function(){if(!this.instance.sessionManager)throw Error(nL+" must be started with a valid sessionManager.");return this.instance.sessionManager}},{key:"fullSnapshotIntervalMillis",get:function(){var e;return(null==(e=this.instance.config.session_recording)?void 0:e.full_snapshot_interval_millis)||3e5}},{key:"isSampled",get:function(){var e=this.instance.get_property(eE);return C(e)?e:null}},{key:"sessionDuration",get:function(){var e,t,n=null==(e=this.buffer)?void 0:e.data[(null==(t=this.buffer)?void 0:t.data.length)-1],i=this.sessionManager.checkAndGetSessionAndWindowId(!0).sessionStartTimestamp;return n?n.timestamp-i:null}},{key:"isRecordingEnabled",get:function(){var e=!!this.instance.get_property(e_),t=!this.instance.config.disable_session_recording;return i&&e&&t}},{key:"isConsoleLogCaptureEnabled",get:function(){var e=!!this.instance.get_property(em),t=this.instance.config.enable_recording_console_log;return null!=t?t:e}},{key:"canvasRecording",get:function(){var e=this.instance.get_property(eb);return e&&e.fps&&e.quality?{enabled:e.enabled,fps:e.fps,quality:e.quality}:void 0}},{key:"networkPayloadCapture",get:function(){var e,t,n=this.instance.get_property(ey),i={recordHeaders:null==(e=this.instance.config.session_recording)?void 0:e.recordHeaders,recordBody:null==(t=this.instance.config.session_recording)?void 0:t.recordBody},r=(null==i?void 0:i.recordHeaders)||(null==n?void 0:n.recordHeaders),s=(null==i?void 0:i.recordBody)||(null==n?void 0:n.recordBody),o=S(this.instance.config.capture_performance)?this.instance.config.capture_performance.network_timing:this.instance.config.capture_performance,a=!!(C(o)?o:null==n?void 0:n.capturePerformance);return r||s||a?{recordHeaders:r,recordBody:s,recordPerformance:a}:void 0}},{key:"sampleRate",get:function(){var e=this.instance.get_property(ek);return T(e)?e:null}},{key:"minimumDuration",get:function(){var e=this.instance.get_property(ew);return T(e)?e:null}},{key:"status",get:function(){return this.receivedDecide?this.isRecordingEnabled?R(this._linkedFlag)||this._linkedFlagSeen?C(this.isSampled)?this.isSampled?"sampled":"disabled":"active":"buffering":"disabled":"buffering"}},{key:"startIfEnabledOrStop",value:function(){var e=this;this.isRecordingEnabled?(this._startCapture(),null==i||i.addEventListener("beforeunload",this._onBeforeUnload),null==i||i.addEventListener("offline",this._onOffline),null==i||i.addEventListener("online",this._onOnline),null==i||i.addEventListener("visibilitychange",this._onVisibilityChange),this._setupSampling(),R(this._removePageViewCaptureHook)&&(this._removePageViewCaptureHook=this.instance._addCaptureHook(function(t){try{if("$pageview"===t){var n=i?e._maskUrl(i.location.href):"";if(!n)return;e._tryAddCustomEvent("$pageview",{href:n})}}catch(e){$.error("Could not add $pageview to rrweb session",e)}})),this._onSessionIdListener||(this._onSessionIdListener=this.sessionManager.onSessionId(function(t,n,i){i&&e._tryAddCustomEvent("$session_id_change",{sessionId:t,windowId:n,changeReason:i})})),$.info(nL+" started")):this.stopRecording()}},{key:"stopRecording",value:function(){var e,t,n;this._captureStarted&&this.stopRrweb&&(this.stopRrweb(),this.stopRrweb=void 0,this._captureStarted=!1,null==i||i.removeEventListener("beforeunload",this._onBeforeUnload),null==i||i.removeEventListener("offline",this._onOffline),null==i||i.removeEventListener("online",this._onOnline),null==i||i.removeEventListener("visibilitychange",this._onVisibilityChange),this.clearBuffer(),clearInterval(this._fullSnapshotTimer),null==(e=this._removePageViewCaptureHook)||e.call(this),this._removePageViewCaptureHook=void 0,null==(t=this._onSessionIdListener)||t.call(this),this._onSessionIdListener=void 0,null==(n=this._samplingSessionListener)||n.call(this),this._samplingSessionListener=void 0,$.info(nL+" stopped"))}},{key:"makeSamplingDecision",value:function(e){var t,n,i=this.sessionId!==e,r=this.sampleRate;if(T(r)){var s,o=this.isSampled,a=i||!C(o);(s=a?Math.random()<r:o)||!a||$.warn(nL+" Sample rate (".concat(r,") has determined that this sessionId (").concat(e,") will not be sent to the server.")),this._tryAddCustomEvent("samplingDecisionMade",{sampleRate:r}),null==(n=this.instance.persistence)||n.register(H({},eE,s))}else null==(t=this.instance.persistence)||t.register(H({},eE,null))}},{key:"afterDecideResponse",value:function(e){var t,n,i,r=this;if(this._persistDecideResponse(e),this._linkedFlag=(null==(t=e.sessionRecording)?void 0:t.linkedFlag)||null,null!=(n=e.sessionRecording)&&n.endpoint&&(this._endpoint=null==(i=e.sessionRecording)?void 0:i.endpoint),this._setupSampling(),!R(this._linkedFlag)&&!this._linkedFlagSeen){var s=I(this._linkedFlag)?this._linkedFlag:this._linkedFlag.flag,o=I(this._linkedFlag)?null:this._linkedFlag.variant;this.instance.onFeatureFlags(function(e,t){var n=S(t)&&s in t,i=o?t[s]===o:n;if(i){var a={linkedFlag:s,linkedVariant:o},u="linked flag matched";$.info(nL+" "+u,a),r._tryAddCustomEvent(u,a)}r._linkedFlagSeen=i})}this.receivedDecide=!0,this.startIfEnabledOrStop()}},{key:"_setupSampling",value:function(){var e=this;T(this.sampleRate)&&R(this._samplingSessionListener)&&(this._samplingSessionListener=this.sessionManager.onSessionId(function(t){e.makeSamplingDecision(t)}))}},{key:"_persistDecideResponse",value:function(e){if(this.instance.persistence){var t,n=this.instance.persistence,i=function(){var t,i,r,s,o,a,u,l,c=null==(t=e.sessionRecording)?void 0:t.sampleRate,d=R(c)?null:parseFloat(c),h=null==(i=e.sessionRecording)?void 0:i.minimumDurationMilliseconds;n.register((H(l={},e_,!!e.sessionRecording),H(l,em,null==(r=e.sessionRecording)?void 0:r.consoleLogRecordingEnabled),H(l,ey,L({capturePerformance:e.capturePerformance},null==(s=e.sessionRecording)?void 0:s.networkPayloadCapture)),H(l,eb,{enabled:null==(o=e.sessionRecording)?void 0:o.recordCanvas,fps:null==(a=e.sessionRecording)?void 0:a.canvasFps,quality:null==(u=e.sessionRecording)?void 0:u.canvasQuality}),H(l,ek,d),H(l,ew,x(h)?null:h),l))};i(),null==(t=this._persistDecideOnSessionListener)||t.call(this),this._persistDecideOnSessionListener=this.sessionManager.onSessionId(i)}}},{key:"log",value:function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"log";null==(t=this.instance.sessionRecording)||t.onRRwebEmit({type:6,data:{plugin:"rrweb/console@1",payload:{level:n,trace:[],payload:[JSON.stringify(e)]}},timestamp:Date.now()})}},{key:"_startCapture",value:function(){var e,t,n=this;x(Object.assign)||this._captureStarted||this.instance.config.disable_session_recording||this.instance.consent.isOptedOut()||(this._captureStarted=!0,this.sessionManager.checkAndGetSessionAndWindowId(),this.rrwebRecord?this._onScriptLoaded():null==(e=v.__PosthogExtensions__)||null==(t=e.loadExternalDependency)||t.call(e,this.instance,"recorder",function(e){if(e)return $.error(nL+" could not load recorder",e);n._onScriptLoaded()}))}},{key:"isInteractiveEvent",value:function(e){var t;return 3===e.type&&-1!==nA.indexOf(null==(t=e.data)?void 0:t.source)}},{key:"_updateWindowAndSessionIds",value:function(e){var t=this.isInteractiveEvent(e);t||this.isIdle||e.timestamp-this._lastActivityTimestamp>3e5&&(this.isIdle=!0,clearInterval(this._fullSnapshotTimer),this._tryAddCustomEvent("sessionIdle",{eventTimestamp:e.timestamp,lastActivityTimestamp:this._lastActivityTimestamp,threshold:3e5,bufferLength:this.buffer.data.length,bufferSize:this.buffer.size}),this._flushBuffer());var n=!1;if(t&&(this._lastActivityTimestamp=e.timestamp,this.isIdle&&(this.isIdle=!1,this._tryAddCustomEvent("sessionNoLongerIdle",{reason:"user activity",type:e.type}),n=!0)),!this.isIdle){var i=this.sessionManager.checkAndGetSessionAndWindowId(!t,e.timestamp),r=i.windowId,s=i.sessionId,o=this.sessionId!==s,a=this.windowId!==r;this.windowId=r,this.sessionId=s,o||a?(this.stopRecording(),this.startIfEnabledOrStop()):n&&this._scheduleFullSnapshot()}}},{key:"_tryRRWebMethod",value:function(e){try{return e.rrwebMethod(),!0}catch(t){return this.queuedRRWebEvents.length<10?this.queuedRRWebEvents.push({enqueuedAt:e.enqueuedAt||Date.now(),attempt:e.attempt++,rrwebMethod:e.rrwebMethod}):$.warn(nL+" could not emit queued rrweb event.",t,e),!1}}},{key:"_tryAddCustomEvent",value:function(e,t){var n=this;return this._tryRRWebMethod(nM(function(){return n.rrwebRecord.addCustomEvent(e,t)}))}},{key:"_tryTakeFullSnapshot",value:function(){var e=this;return this._tryRRWebMethod(nM(function(){return e.rrwebRecord.takeFullSnapshot()}))}},{key:"_onScriptLoaded",value:function(){for(var e,t=this,n={blockClass:"ph-no-capture",blockSelector:void 0,ignoreClass:"ph-ignore-input",maskTextClass:"ph-mask",maskTextSelector:void 0,maskTextFn:void 0,maskAllInputs:!0,maskInputOptions:{password:!0},maskInputFn:void 0,slimDOMOptions:{},collectFonts:!1,inlineStylesheet:!0,recordCrossOriginIframes:!1},i=this.instance.config.session_recording,r=0,s=Object.entries(i||{});r<s.length;r++){var o=U(s[r],2),a=o[0],u=o[1];a in n&&("maskInputOptions"===a?n.maskInputOptions=L({password:!0},u):n[a]=u)}if(this.canvasRecording&&this.canvasRecording.enabled&&(n.recordCanvas=!0,n.sampling={canvas:this.canvasRecording.fps},n.dataURLOptions={type:"image/webp",quality:this.canvasRecording.quality}),this.rrwebRecord){this.mutationRateLimiter=null!=(e=this.mutationRateLimiter)?e:new n$(this.rrwebRecord,{onBlockedNode:function(e,n){var i="Too many mutations on node '".concat(e,"'. Rate limiting. This could be due to SVG animations or something similar");$.info(i,{node:n}),t.log(nL+" "+i,"warn")}});var l=this._gatherRRWebPlugins();this.stopRrweb=this.rrwebRecord(L({emit:function(e){t.onRRwebEmit(e)},plugins:l},n)),this._lastActivityTimestamp=Date.now(),this.isIdle=!1,this._tryAddCustomEvent("$session_options",{sessionRecordingOptions:n,activePlugins:l.map(function(e){return null==e?void 0:e.name})}),this._tryAddCustomEvent("$posthog_config",{config:this.instance.config})}else $.error(nL+"onScriptLoaded was called but rrwebRecord is not available. This indicates something has gone wrong.")}},{key:"_scheduleFullSnapshot",value:function(){var e=this;if(this._fullSnapshotTimer&&clearInterval(this._fullSnapshotTimer),!this.isIdle){var t=this.fullSnapshotIntervalMillis;t&&(this._fullSnapshotTimer=setInterval(function(){e._tryTakeFullSnapshot()},t))}}},{key:"_gatherRRWebPlugins",value:function(){var e,t,n,i,r=[],s=null==(e=v.__PosthogExtensions__)||null==(t=e.rrwebPlugins)?void 0:t.getRecordConsolePlugin;s&&this.isConsoleLogCaptureEnabled&&r.push(s());var o=null==(n=v.__PosthogExtensions__)||null==(i=n.rrwebPlugins)?void 0:i.getRecordNetworkPlugin;return this.networkPayloadCapture&&w(o)&&(!tn.includes(location.hostname)||this._forceAllowLocalhostNetworkCapture?r.push(o(nO(this.instance.config,this.networkPayloadCapture))):$.info(nL+" NetworkCapture not started because we are on localhost.")),r}},{key:"onRRwebEmit",value:function(e){if(this._processQueuedEvents(),e&&S(e)){if(e.type===t7.Meta){var t=this._maskUrl(e.data.href);if(this._lastHref=t,!t)return;e.data.href=t}else this._pageViewFallBack();e.type===t7.FullSnapshot&&this._scheduleFullSnapshot();var n=this.mutationRateLimiter?this.mutationRateLimiter.throttleMutations(e):e;if(n){var i=function(e){if(e&&S(e)&&6===e.type&&S(e.data)&&"rrweb/console@1"===e.data.plugin){e.data.payload.payload.length>10&&(e.data.payload.payload=e.data.payload.payload.slice(0,10),e.data.payload.payload.push("...[truncated]"));for(var t=[],n=0;n<e.data.payload.payload.length;n++)e.data.payload.payload[n]&&e.data.payload.payload[n].length>2e3?t.push(e.data.payload.payload[n].slice(0,2e3)+"...[truncated]"):t.push(e.data.payload.payload[n]);return e.data.payload.payload=t,e}return e}(n),r=t4(i);if(this._updateWindowAndSessionIds(i),!this.isIdle||i.type===t7.Custom){if(i.type===t7.Custom&&"sessionIdle"===i.data.tag){var s=i.data.payload;s&&(i.timestamp=s.lastActivityTimestamp+s.threshold)}var o={$snapshot_bytes:r,$snapshot_data:i,$session_id:this.sessionId,$window_id:this.windowId};"disabled"!==this.status?this._captureSnapshotBuffered(o):this.clearBuffer()}}}}},{key:"_pageViewFallBack",value:function(){if(!this.instance.config.capture_pageview&&i){var e=this._maskUrl(i.location.href);this._lastHref!==e&&(this._tryAddCustomEvent("$url_changed",{href:e}),this._lastHref=e)}}},{key:"_processQueuedEvents",value:function(){var e=this;if(this.queuedRRWebEvents.length){var t=j(this.queuedRRWebEvents);this.queuedRRWebEvents=[],t.forEach(function(t){Date.now()-t.enqueuedAt<=2e3&&e._tryRRWebMethod(t)})}}},{key:"_maskUrl",value:function(e){var t=this.instance.config.session_recording;if(t.maskNetworkRequestFn){var n,i={url:e};return null==(n=i=t.maskNetworkRequestFn(i))?void 0:n.url}return e}},{key:"clearBuffer",value:function(){return this.buffer={size:0,data:[],sessionId:this.sessionId,windowId:this.windowId},this.buffer}},{key:"_flushBuffer",value:function(){var e=this;this.flushBufferTimer&&(clearTimeout(this.flushBufferTimer),this.flushBufferTimer=void 0);var t=this.minimumDuration,n=this.sessionDuration,i=T(n)&&n>=0,r=T(t)&&i&&n<t;return"buffering"===this.status||r?(this.flushBufferTimer=setTimeout(function(){e._flushBuffer()},2e3),this.buffer):(this.buffer.data.length>0&&(function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6606028.8;if(t.size>=n&&t.data.length>1){var i=Math.floor(t.data.length/2),r=t.data.slice(0,i),s=t.data.slice(i);return[e({size:t4(r),data:r,sessionId:t.sessionId,windowId:t.windowId}),e({size:t4(s),data:s,sessionId:t.sessionId,windowId:t.windowId})].flatMap(function(e){return e})}return[t]})(this.buffer).forEach(function(t){e._captureSnapshot({$snapshot_bytes:t.size,$snapshot_data:t.data,$session_id:t.sessionId,$window_id:t.windowId})}),this.clearBuffer())}},{key:"_captureSnapshotBuffered",value:function(e){var t,n=this,i=2+((null==(t=this.buffer)?void 0:t.data.length)||0);!this.isIdle&&(this.buffer.size+e.$snapshot_bytes+i>943718.4||this.buffer.sessionId!==this.sessionId)&&(this.buffer=this._flushBuffer()),this.buffer.size+=e.$snapshot_bytes,this.buffer.data.push(e.$snapshot_data),this.flushBufferTimer||this.isIdle||(this.flushBufferTimer=setTimeout(function(){n._flushBuffer()},2e3))}},{key:"_captureSnapshot",value:function(e){this.instance.capture("$snapshot",e,{_url:this.instance.requestRouter.endpointFor("api",this._endpoint),_noTruncate:!0,_batchKey:"recordings",skip_client_rate_limiting:!0})}},{key:"overrideLinkedFlag",value:function(){this._linkedFlagSeen=!0}}]),e}(),nN=function(){function e(t){N(this,e),this.instance=t,this.instance.decideEndpointWasHit=this.instance._hasBootstrappedFeatureFlags()}return B(e,[{key:"call",value:function(){var e=this,t={token:this.instance.config.token,distinct_id:this.instance.get_distinct_id(),groups:this.instance.getGroups(),person_properties:this.instance.get_property(eF),group_properties:this.instance.get_property(eP),disable_flags:this.instance.config.advanced_disable_feature_flags||this.instance.config.advanced_disable_feature_flags_on_first_load||void 0};this.instance._send_request({method:"POST",url:this.instance.requestRouter.endpointFor("api","/decide/?v=3"),data:t,compression:this.instance.config.disable_compression?void 0:ee.Base64,timeout:this.instance.config.feature_flag_request_timeout_ms,callback:function(t){return e.parseDecideResponse(t.json)}})}},{key:"parseDecideResponse",value:function(e){var t=this;this.instance.featureFlags.setReloadingPaused(!1),this.instance.featureFlags._startReloadTimer();var n=!e;if(this.instance.config.advanced_disable_feature_flags_on_first_load||this.instance.config.advanced_disable_feature_flags||this.instance.featureFlags.receivedFeatureFlags(null!=e?e:{},n),n)$.error("Failed to fetch feature flags from PostHog.");else{if(!l||!l.body)return $.info("document not ready yet, trying again in 500 milliseconds..."),void setTimeout(function(){t.parseDecideResponse(e)},500);if(this.instance._afterDecideResponse(e),e.siteApps)if(this.instance.config.opt_in_site_apps){var i,r=V(e.siteApps);try{for(r.s();!(i=r.n()).done;)!function(){var e,n,r=i.value,s=r.id,o=r.url;v["__$$ph_site_app_".concat(s)]=t.instance,null==(e=v.__PosthogExtensions__)||null==(n=e.loadSiteApp)||n.call(e,t.instance,o,function(e){if(e)return $.error("Error while initializing PostHog app with config id ".concat(s),e)})}()}catch(e){r.e(e)}finally{r.f()}}else e.siteApps.length>0&&$.error('PostHog site apps are disabled. Enable the "opt_in_site_apps" config to proceed.')}}}]),e}(),nq=null!=i&&i.location?ta(i.location.hash,"__posthog")||ta(location.hash,"state"):null,nB="_postHogToolbarParams";!function(e){e[e.UNINITIALIZED=0]="UNINITIALIZED",e[e.LOADING=1]="LOADING",e[e.LOADED=2]="LOADED"}(nC||(nC={}));var nH=function(){function e(t){N(this,e),this.instance=t}return B(e,[{key:"setToolbarState",value:function(e){v.ph_toolbar_state=e}},{key:"getToolbarState",value:function(){var e;return null!=(e=v.ph_toolbar_state)?e:nC.UNINITIALIZED}},{key:"maybeLoadToolbar",value:function(){var e,t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;if(!i||!l)return!1;n=null!=(e=n)?e:i.location,s=null!=(t=s)?t:i.history;try{if(!r){try{i.localStorage.setItem("test","test"),i.localStorage.removeItem("test")}catch(e){return!1}r=null==i?void 0:i.localStorage}var o,a=nq||ta(n.hash,"__posthog")||ta(n.hash,"state"),u=a?et(function(){return JSON.parse(atob(decodeURIComponent(a)))})||et(function(){return JSON.parse(decodeURIComponent(a))}):null;return u&&"ph_authorize"===u.action?((o=u).source="url",o&&Object.keys(o).length>0&&(u.desiredHash?n.hash=u.desiredHash:s?s.replaceState(s.state,"",n.pathname+n.search):n.hash="")):((o=JSON.parse(r.getItem(nB)||"{}")).source="localstorage",delete o.userIntent),!(!o.token||this.instance.config.token!==o.token)&&(this.loadToolbar(o),!0)}catch(e){return!1}}},{key:"_callLoadToolbar",value:function(e){(v.ph_load_toolbar||v.ph_load_editor)(e,this.instance)}},{key:"loadToolbar",value:function(e){var t,n,r=this,s=!(null==l||!l.getElementById(eq));if(!i||s)return!1;var o="custom"===this.instance.requestRouter.region&&this.instance.config.advanced_disable_toolbar_metrics,a=L(L({token:this.instance.config.token},e),{},{apiURL:this.instance.requestRouter.endpointFor("ui")},o?{instrument:!1}:{});return(i.localStorage.setItem(nB,JSON.stringify(L(L({},a),{},{source:void 0}))),this.getToolbarState()===nC.LOADED)?this._callLoadToolbar(a):this.getToolbarState()===nC.UNINITIALIZED&&(this.setToolbarState(nC.LOADING),null==(t=v.__PosthogExtensions__)||null==(n=t.loadExternalDependency)||n.call(t,this.instance,"toolbar",function(e){if(e)return $.error("Failed to load toolbar",e),void r.setToolbarState(nC.UNINITIALIZED);r.setToolbarState(nC.LOADED),r._callLoadToolbar(a)}),eo(i,"turbolinks:load",function(){r.setToolbarState(nC.UNINITIALIZED),r.loadToolbar(a)})),!0}},{key:"_loadEditor",value:function(e){return this.loadToolbar(e)}},{key:"maybeLoadEditor",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;return this.maybeLoadToolbar(e,t,n)}}]),e}(),nU=function(){function e(t){N(this,e),H(this,"isPaused",!0),H(this,"queue",[]),H(this,"flushTimeoutMs",3e3),this.sendRequest=t}return B(e,[{key:"enqueue",value:function(e){this.queue.push(e),this.flushTimeout||this.setFlushTimeout()}},{key:"unload",value:function(){var e=this;this.clearFlushTimeout();var t=Object.values(this.queue.length>0?this.formatQueue():{});[].concat(j(t.filter(function(e){return 0===e.url.indexOf("/e")})),j(t.filter(function(e){return 0!==e.url.indexOf("/e")}))).map(function(t){e.sendRequest(L(L({},t),{},{transport:"sendBeacon"}))})}},{key:"enable",value:function(){this.isPaused=!1,this.setFlushTimeout()}},{key:"setFlushTimeout",value:function(){var e=this;this.isPaused||(this.flushTimeout=setTimeout(function(){if(e.clearFlushTimeout(),e.queue.length>0){var t=e.formatQueue();for(var n in t)!function(n){var i=t[n],r=(new Date).getTime();i.data&&k(i.data)&&Y(i.data,function(e){e.offset=Math.abs(e.timestamp-r),delete e.timestamp}),e.sendRequest(i)}(n)}},this.flushTimeoutMs))}},{key:"clearFlushTimeout",value:function(){clearTimeout(this.flushTimeout),this.flushTimeout=void 0}},{key:"formatQueue",value:function(){var e={};return Y(this.queue,function(t){var n,i=(t?t.batchKey:null)||t.url;x(e[i])&&(e[i]=L(L({},t),{},{data:[]})),null==(n=e[i].data)||n.push(t.data)}),this.queue=[],e}}]),e}(),nj=Uint8Array,nW=Uint16Array,nz=Uint32Array,nV=new nj([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),nG=new nj([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),nQ=new nj([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),nJ=function(e,t){for(var n=new nW(31),i=0;i<31;++i)n[i]=t+=1<<e[i-1];var r=new nz(n[30]);for(i=1;i<30;++i)for(var s=n[i];s<n[i+1];++s)r[s]=s-n[i]<<5|i;return[n,r]},nY=nJ(nV,2),nX=nY[0],nK=nY[1];nX[28]=258,nK[258]=28;for(var nZ=nJ(nG,0)[1],n0=new nW(32768),n1=0;n1<32768;++n1){var n2=(43690&n1)>>>1|(21845&n1)<<1;n2=(61680&(n2=(52428&n2)>>>2|(13107&n2)<<2))>>>4|(3855&n2)<<4,n0[n1]=((65280&n2)>>>8|(255&n2)<<8)>>>1}var n3=function(e,t,n){for(var i=e.length,r=0,s=new nW(t);r<i;++r)++s[e[r]-1];var o,a=new nW(t);for(r=0;r<t;++r)a[r]=a[r-1]+s[r-1]<<1;if(n){o=new nW(1<<t);var u=15-t;for(r=0;r<i;++r)if(e[r])for(var l=r<<4|e[r],c=t-e[r],d=a[e[r]-1]++<<c,h=d|(1<<c)-1;d<=h;++d)o[n0[d]>>>u]=l}else for(o=new nW(i),r=0;r<i;++r)o[r]=n0[a[e[r]-1]++]>>>15-e[r];return o},n5=new nj(288);for(n1=0;n1<144;++n1)n5[n1]=8;for(n1=144;n1<256;++n1)n5[n1]=9;for(n1=256;n1<280;++n1)n5[n1]=7;for(n1=280;n1<288;++n1)n5[n1]=8;var n6=new nj(32);for(n1=0;n1<32;++n1)n6[n1]=5;var n8=n3(n5,9,0),n4=n3(n6,5,0),n7=function(e){return(e/8|0)+(7&e&&1)},n9=function(e,t,n){(null==t||t<0)&&(t=0),(null==n||n>e.length)&&(n=e.length);var i=new(e instanceof nW?nW:e instanceof nz?nz:nj)(n-t);return i.set(e.subarray(t,n)),i},ie=function(e,t,n){n<<=7&t;var i=t/8|0;e[i]|=n,e[i+1]|=n>>>8},it=function(e,t,n){n<<=7&t;var i=t/8|0;e[i]|=n,e[i+1]|=n>>>8,e[i+2]|=n>>>16},ii=function(e,t){for(var n=[],i=0;i<e.length;++i)e[i]&&n.push({s:i,f:e[i]});var r=n.length,s=n.slice();if(!r)return[new nj(0),0];if(1==r){var o=new nj(n[0].s+1);return o[n[0].s]=1,[o,1]}n.sort(function(e,t){return e.f-t.f}),n.push({s:-1,f:25001});var a=n[0],u=n[1],l=0,c=1,d=2;for(n[0]={s:-1,f:a.f+u.f,l:a,r:u};c!=r-1;)a=n[n[l].f<n[d].f?l++:d++],u=n[l!=c&&n[l].f<n[d].f?l++:d++],n[c++]={s:-1,f:a.f+u.f,l:a,r:u};var h=s[0].s;for(i=1;i<r;++i)s[i].s>h&&(h=s[i].s);var f=new nW(h+1),p=ir(n[c-1],f,0);if(p>t){i=0;var v=0,g=p-t,_=1<<g;for(s.sort(function(e,t){return f[t.s]-f[e.s]||e.f-t.f});i<r;++i){var m=s[i].s;if(!(f[m]>t))break;v+=_-(1<<p-f[m]),f[m]=t}for(v>>>=g;v>0;){var y=s[i].s;f[y]<t?v-=1<<t-f[y]++-1:++i}for(;i>=0&&v;--i){var b=s[i].s;f[b]==t&&(--f[b],++v)}p=t}return[new nj(f),p]},ir=function e(t,n,i){return -1==t.s?Math.max(e(t.l,n,i+1),e(t.r,n,i+1)):n[t.s]=i},is=function(e){for(var t=e.length;t&&!e[--t];);for(var n=new nW(++t),i=0,r=e[0],s=1,o=function(e){n[i++]=e},a=1;a<=t;++a)if(e[a]==r&&a!=t)++s;else{if(!r&&s>2){for(;s>138;s-=138)o(32754);s>2&&(o(s>10?s-11<<5|28690:s-3<<5|12305),s=0)}else if(s>3){for(o(r),--s;s>6;s-=6)o(8304);s>2&&(o(s-3<<5|8208),s=0)}for(;s--;)o(r);s=1,r=e[a]}return[n.subarray(0,i),t]},io=function(e,t){for(var n=0,i=0;i<t.length;++i)n+=e[i]*t[i];return n},ia=function(e,t,n){var i=n.length,r=n7(t+2);e[r]=255&i,e[r+1]=i>>>8,e[r+2]=255^e[r],e[r+3]=255^e[r+1];for(var s=0;s<i;++s)e[r+s+4]=n[s];return 8*(r+4+i)},iu=function(e,t,n,i,r,s,o,a,u,l,c){ie(t,c++,n),++r[256];for(var d=ii(r,15),h=d[0],f=d[1],p=ii(s,15),v=p[0],g=p[1],_=is(h),m=_[0],y=_[1],b=is(v),k=b[0],w=b[1],S=new nW(19),E=0;E<m.length;++E)S[31&m[E]]++;for(E=0;E<k.length;++E)S[31&k[E]]++;for(var x=ii(S,7),I=x[0],F=x[1],P=19;P>4&&!I[nQ[P-1]];--P);var R,T,C,O,$=l+5<<3,A=io(r,n5)+io(s,n6)+o,M=io(r,h)+io(s,v)+o+14+3*P+io(S,I)+(2*S[16]+3*S[17]+7*S[18]);if($<=A&&$<=M)return ia(t,c,e.subarray(u,u+l));if(ie(t,c,1+(M<A)),c+=2,M<A){R=n3(h,f,0),T=h,C=n3(v,g,0),O=v;var L=n3(I,F,0);for(ie(t,c,y-257),ie(t,c+5,w-1),ie(t,c+10,P-4),c+=14,E=0;E<P;++E)ie(t,c+3*E,I[nQ[E]]);c+=3*P;for(var D=[m,k],N=0;N<2;++N){var q=D[N];for(E=0;E<q.length;++E){var B=31&q[E];ie(t,c,L[B]),c+=I[B],B>15&&(ie(t,c,q[E]>>>5&127),c+=q[E]>>>12)}}}else R=n8,T=n5,C=n4,O=n6;for(E=0;E<a;++E)if(i[E]>255){it(t,c,R[(B=i[E]>>>18&31)+257]),c+=T[B+257],B>7&&(ie(t,c,i[E]>>>23&31),c+=nV[B]);var H=31&i[E];it(t,c,C[H]),c+=O[H],H>3&&(it(t,c,i[E]>>>5&8191),c+=nG[H])}else it(t,c,R[i[E]]),c+=T[i[E]];return it(t,c,R[256]),c+T[256]},il=new nz([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),ic=new nj(0),id=function(){for(var e=new nz(256),t=0;t<256;++t){for(var n=t,i=9;--i;)n=(1&n&&0xedb88320)^n>>>1;e[t]=n}return e}(),ih=function(e,t,n){for(;n;++t)e[t]=n,n>>>=8},ip=!!h||!!d,iv="text/plain",ig=function(e,t){var n=U(e.split("?"),2),i=n[0],r=n[1],s=L({},t);null==r||r.split("&").forEach(function(e){var t=U(e.split("="),1)[0];delete s[t]});var o=ts(s);return o=o?(r?r+"&":"")+o:r,"".concat(i,"?").concat(o)},i_=function(e){var t=e.data,n=e.compression;if(t){if(n===ee.GZipJS){var i,r,s,o,a,u,l,c,d=new Blob([(i=function(e,t){var n=e.length;if("undefined"!=typeof TextEncoder)return(new TextEncoder).encode(e);for(var i=new nj(e.length+(e.length>>>1)),r=0,s=function(e){i[r++]=e},o=0;o<n;++o){if(r+5>i.length){var a=new nj(r+8+(n-o<<1));a.set(i),i=a}var u=e.charCodeAt(o);u<128?s(u):(u<2048?s(192|u>>>6):(u>55295&&u<57344?(s(240|(u=65536+(1047552&u)|1023&e.charCodeAt(++o))>>>18),s(128|u>>>12&63)):s(224|u>>>12),s(128|u>>>6&63)),s(128|63&u))}return n9(i,0,r)}(JSON.stringify(t)),r={mtime:0},s=0xffffffff,o={p:function(e){for(var t=s,n=0;n<e.length;++n)t=id[255&t^e[n]]^t>>>8;s=t},d:function(){return 0xffffffff^s}},a=i.length,o.p(i),c=(u=10+(r.filename&&r.filename.length+1||0),l=function(e,t,n,i,r,s){var o=e.length,a=new nj(i+o+5*(1+Math.floor(o/7e3))+8),u=a.subarray(i,a.length-r),l=0;if(!t||o<8)for(var c=0;c<=o;c+=65535){var d=c+65535;d<o?l=ia(u,l,e.subarray(c,d)):(u[c]=s,l=ia(u,l,e.subarray(c,o)))}else{for(var h=il[t-1],f=h>>>13,p=8191&h,v=(1<<n)-1,g=new nW(32768),_=new nW(v+1),m=Math.ceil(n/3),y=2*m,b=function(t){return(e[t]^e[t+1]<<m^e[t+2]<<y)&v},k=new nz(25e3),w=new nW(288),S=new nW(32),E=0,x=0,I=(c=0,0),F=0,P=0;c<o;++c){var R=b(c),T=32767&c,C=_[R];if(g[T]=C,_[R]=T,F<=c){var O=o-c;if((E>7e3||I>24576)&&O>423){l=iu(e,u,0,k,w,S,x,I,P,c-P,l),I=E=x=0,P=c;for(var $=0;$<286;++$)w[$]=0;for($=0;$<30;++$)S[$]=0}var A=2,M=0,L=p,D=T-C&32767;if(O>2&&R==b(c-D))for(var N=Math.min(f,O)-1,q=Math.min(32767,c),B=Math.min(258,O);D<=q&&--L&&T!=C;){if(e[c+A]==e[c+A-D]){for(var H=0;H<B&&e[c+H]==e[c+H-D];++H);if(H>A){if(A=H,M=D,H>N)break;var U=Math.min(D,H-2),j=0;for($=0;$<U;++$){var W=c-D+$+32768&32767,z=W-g[W]+32768&32767;z>j&&(j=z,C=W)}}}D+=(T=C)-(C=g[T])+32768&32767}if(M){k[I++]=0x10000000|nK[A]<<18|nZ[M];var V=31&nK[A],G=31&nZ[M];x+=nV[V]+nG[G],++w[257+V],++S[G],F=c+A,++E}else k[I++]=e[c],++w[e[c]]}}l=iu(e,u,s,k,w,S,x,I,P,c-P,l),s||(l=ia(u,l,ic))}return n9(a,0,i+n7(l)+r)}(i,null==r.level?6:r.level,null==r.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(i.length)))):12+r.mem,u,8,!0)).length,function(e,t){var n=t.filename;if(e[0]=31,e[1]=139,e[2]=8,e[8]=t.level<2?4:2*(9==t.level),e[9]=3,0!=t.mtime&&ih(e,4,Math.floor(new Date(t.mtime||Date.now())/1e3)),n){e[3]=8;for(var i=0;i<=n.length;++i)e[i+10]=n.charCodeAt(i)}}(l,r),ih(l,c-8,o.d()),ih(l,c-4,a),l)],{type:iv});return{contentType:iv,body:d,estimatedSize:d.size}}if(n===ee.Base64){var h,f="data="+encodeURIComponent("string"==typeof(h=function(e){var t,n,i,r,s,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",a=0,u=0,l="",c=[];if(!e)return e;e=es(e);do t=(s=e.charCodeAt(a++)<<16|e.charCodeAt(a++)<<8|e.charCodeAt(a++))>>18&63,n=s>>12&63,i=s>>6&63,r=63&s,c[u++]=o.charAt(t)+o.charAt(n)+o.charAt(i)+o.charAt(r);while(a<e.length);switch(l=c.join(""),e.length%3){case 1:l=l.slice(0,-2)+"==";break;case 2:l=l.slice(0,-1)+"="}return l}(JSON.stringify(t)))?h:JSON.stringify(h));return{contentType:"application/x-www-form-urlencoded",body:f,estimatedSize:new Blob([f]).size}}var p=JSON.stringify(t);return{contentType:"application/json",body:p,estimatedSize:new Blob([p]).size}}},im=[];h&&im.push({transport:"XHR",method:function(e){var t,n=new h;n.open(e.method||"GET",e.url,!0);var i=null!=(t=i_(e))?t:{},r=i.contentType,s=i.body;Y(e.headers,function(e,t){n.setRequestHeader(t,e)}),r&&n.setRequestHeader("Content-Type",r),e.timeout&&(n.timeout=e.timeout),n.withCredentials=!0,n.onreadystatechange=function(){if(4===n.readyState){var t,i={statusCode:n.status,text:n.responseText};if(200===n.status)try{i.json=JSON.parse(n.responseText)}catch(e){}null==(t=e.callback)||t.call(e,i)}},n.send(s)}}),d&&im.push({transport:"fetch",method:function(e){var t,n,i=null!=(t=i_(e))?t:{},r=i.contentType,s=i.body,o=i.estimatedSize,a=new Headers;Y(e.headers,function(e,t){a.append(t,e)}),r&&a.append("Content-Type",r);var u=e.url,l=null;if(f){var c=new f;l={signal:c.signal,timeout:setTimeout(function(){return c.abort()},e.timeout)}}d(u,{method:(null==e?void 0:e.method)||"GET",headers:a,keepalive:"POST"===e.method&&65536>(o||0),body:s,signal:null==(n=l)?void 0:n.signal}).then(function(t){return t.text().then(function(n){var i,r={statusCode:t.status,text:n};if(200===t.status)try{r.json=JSON.parse(n)}catch(e){$.error(e)}null==(i=e.callback)||i.call(e,r)})}).catch(function(t){var n;$.error(t),null==(n=e.callback)||n.call(e,{statusCode:0,text:t})}).finally(function(){return l?clearTimeout(l.timeout):null})}}),null!=u&&u.sendBeacon&&im.push({transport:"sendBeacon",method:function(e){var t=ig(e.url,{beacon:"1"});try{var n,i=null!=(n=i_(e))?n:{},r=i.contentType,s=i.body,o="string"==typeof s?new Blob([s],{type:r}):s;u.sendBeacon(t,o)}catch(e){}}});var iy,ib=["retriesPerformedSoFar"],ik=function(){function e(t){var n=this;N(this,e),H(this,"isPolling",!1),H(this,"pollIntervalMs",3e3),H(this,"queue",[]),this.instance=t,this.queue=[],this.areWeOnline=!0,!x(i)&&"onLine"in i.navigator&&(this.areWeOnline=i.navigator.onLine,i.addEventListener("online",function(){n.areWeOnline=!0,n.flush()}),i.addEventListener("offline",function(){n.areWeOnline=!1}))}return B(e,[{key:"retriableRequest",value:function(e){var t=this,n=e.retriesPerformedSoFar,i=function(e,t){if(null==e)return{};var n,i,r=function(e,t){if(null==e)return{};var n,i,r={},s=Object.keys(e);for(i=0;i<s.length;i++)n=s[i],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(i=0;i<s.length;i++)n=s[i],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}(e,ib);T(n)&&n>0&&(i.url=ig(i.url,{retry_count:n})),this.instance._send_request(L(L({},i),{},{callback:function(e){var r;200!==e.statusCode&&(e.statusCode<400||e.statusCode>=500)&&(null!=n?n:0)<10?t.enqueue(L({retriesPerformedSoFar:n},i)):null==(r=i.callback)||r.call(i,e)}}))}},{key:"enqueue",value:function(e){var t,n,i,r=e.retriesPerformedSoFar||0;e.retriesPerformedSoFar=r+1;var s=(i=(Math.random()-.5)*((n=Math.min(18e5,t=3e3*Math.pow(2,r)))-t/2),Math.ceil(n+i)),o=Date.now()+s;this.queue.push({retryAt:o,requestOptions:e});var a="Enqueued failed request for retry in ".concat(s);navigator.onLine||(a+=" (Browser is offline)"),$.warn(a),this.isPolling||(this.isPolling=!0,this.poll())}},{key:"poll",value:function(){var e=this;this.poller&&clearTimeout(this.poller),this.poller=setTimeout(function(){e.areWeOnline&&e.queue.length>0&&e.flush(),e.poll()},this.pollIntervalMs)}},{key:"flush",value:function(){var e=Date.now(),t=[],n=this.queue.filter(function(n){return n.retryAt<e||(t.push(n),!1)});if(this.queue=t,n.length>0){var i,r=V(n);try{for(r.s();!(i=r.n()).done;){var s=i.value.requestOptions;this.retriableRequest(s)}}catch(e){r.e(e)}finally{r.f()}}}},{key:"unload",value:function(){this.poller&&(clearTimeout(this.poller),this.poller=void 0);var e,t=V(this.queue);try{for(t.s();!(e=t.n()).done;){var n=e.value.requestOptions;try{this.instance._send_request(L(L({},n),{},{transport:"sendBeacon"}))}catch(e){$.error(e)}}}catch(e){t.e(e)}finally{t.f()}this.queue=[]}}]),e}(),iw=function(){function e(t,n,i,r){N(this,e),H(this,"_sessionIdChangedHandlers",[]),this.config=t,this.persistence=n,this._windowId=void 0,this._sessionId=void 0,this._sessionStartTimestamp=null,this._sessionActivityTimestamp=null,this._sessionIdGenerator=i||eZ,this._windowIdGenerator=r||eZ;var s,o=t.persistence_name||t.token,a=t.session_idle_timeout_seconds||1800;if(T(a)?a>1800?$.warn("session_idle_timeout_seconds cannot be  greater than 30 minutes. Using 30 minutes instead."):a<60&&$.warn("session_idle_timeout_seconds cannot be less than 60 seconds. Using 60 seconds instead."):($.warn("session_idle_timeout_seconds must be a number. Defaulting to 30 minutes."),a=1800),this._sessionTimeoutMs=1e3*Math.min(Math.max(a,60),1800),this._window_id_storage_key="ph_"+o+"_window_id",this._primary_window_exists_storage_key="ph_"+o+"_primary_window_exists",this._canUseSessionStorage()){var u=tt.parse(this._window_id_storage_key),l=tt.parse(this._primary_window_exists_storage_key);u&&!l?this._windowId=u:tt.remove(this._window_id_storage_key),tt.set(this._primary_window_exists_storage_key,!0)}if(null!=(s=this.config.bootstrap)&&s.sessionID)try{var c=function(e){var t=e.replace(/-/g,"");if(32!==t.length)throw Error("Not a valid UUID");if("7"!==t[12])throw Error("Not a UUIDv7");return parseInt(t.substring(0,12),16)}(this.config.bootstrap.sessionID);this._setSessionId(this.config.bootstrap.sessionID,(new Date).getTime(),c)}catch(e){$.error("Invalid sessionID in bootstrap",e)}this._listenToReloadWindow()}return B(e,[{key:"onSessionId",value:function(e){var t=this;return x(this._sessionIdChangedHandlers)&&(this._sessionIdChangedHandlers=[]),this._sessionIdChangedHandlers.push(e),this._sessionId&&e(this._sessionId,this._windowId),function(){t._sessionIdChangedHandlers=t._sessionIdChangedHandlers.filter(function(t){return t!==e})}}},{key:"_canUseSessionStorage",value:function(){return"memory"!==this.config.persistence&&!this.persistence.disabled&&tt.is_supported()}},{key:"_setWindowId",value:function(e){e!==this._windowId&&(this._windowId=e,this._canUseSessionStorage()&&tt.set(this._window_id_storage_key,e))}},{key:"_getWindowId",value:function(){return this._windowId?this._windowId:this._canUseSessionStorage()?tt.parse(this._window_id_storage_key):null}},{key:"_setSessionId",value:function(e,t,n){e===this._sessionId&&t===this._sessionActivityTimestamp&&n===this._sessionStartTimestamp||(this._sessionStartTimestamp=n,this._sessionActivityTimestamp=t,this._sessionId=e,this.persistence.register(H({},eS,[t,e,n])))}},{key:"_getSessionId",value:function(){if(this._sessionId&&this._sessionActivityTimestamp&&this._sessionStartTimestamp)return[this._sessionActivityTimestamp,this._sessionId,this._sessionStartTimestamp];var e=this.persistence.props[eS];return k(e)&&2===e.length&&e.push(e[0]),e||[0,null,0]}},{key:"resetSessionId",value:function(){this._setSessionId(null,null,null)}},{key:"_listenToReloadWindow",value:function(){var e=this;null==i||i.addEventListener("beforeunload",function(){e._canUseSessionStorage()&&tt.remove(e._primary_window_exists_storage_key)})}},{key:"checkAndGetSessionAndWindowId",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:null)||(new Date).getTime(),n=U(this._getSessionId(),3),i=n[0],r=n[1],s=n[2],o=this._getWindowId(),a=T(s)&&s>0&&Math.abs(t-s)>864e5,u=!1,l=!r,c=!e&&Math.abs(t-i)>this._sessionTimeoutMs;l||c||a?(r=this._sessionIdGenerator(),o=this._windowIdGenerator(),$.info("[SessionId] new session ID generated",{sessionId:r,windowId:o,changeReason:{noSessionId:l,activityTimeout:c,sessionPastMaximumLength:a}}),s=t,u=!0):o||(o=this._windowIdGenerator(),u=!0);var d=0===i||!e||a?t:i,h=0===s?(new Date).getTime():s;return this._setWindowId(o),this._setSessionId(r,d,h),u&&this._sessionIdChangedHandlers.forEach(function(e){return e(r,o,u?{noSessionId:l,activityTimeout:c,sessionPastMaximumLength:a}:void 0)}),{sessionId:r,windowId:o,sessionStartTimestamp:h,changeReason:u?{noSessionId:l,activityTimeout:c,sessionPastMaximumLength:a}:void 0}}}]),e}();!function(e){e.US="us",e.EU="eu",e.CUSTOM="custom"}(iy||(iy={}));var iS="i.posthog.com",iE=function(){function e(t){N(this,e),H(this,"_regionCache",{}),this.instance=t}return B(e,[{key:"apiHost",get:function(){var e=this.instance.config.api_host.trim().replace(/\/$/,"");return"https://app.posthog.com"===e?"https://us.i.posthog.com":e}},{key:"uiHost",get:function(){var e,t=null==(e=this.instance.config.ui_host)?void 0:e.replace(/\/$/,"");return t||(t=this.apiHost.replace(".".concat(iS),".posthog.com")),"https://app.posthog.com"===t?"https://us.posthog.com":t}},{key:"region",get:function(){return this._regionCache[this.apiHost]||(/https:\/\/(app|us|us-assets)(\.i)?\.posthog\.com/i.test(this.apiHost)?this._regionCache[this.apiHost]=iy.US:/https:\/\/(eu|eu-assets)(\.i)?\.posthog\.com/i.test(this.apiHost)?this._regionCache[this.apiHost]=iy.EU:this._regionCache[this.apiHost]=iy.CUSTOM),this._regionCache[this.apiHost]}},{key:"endpointFor",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(t&&(t="/"===t[0]?t:"/".concat(t)),"ui"===e)return this.uiHost+t;if(this.region===iy.CUSTOM)return this.apiHost+t;var n=iS+t;switch(e){case"assets":return"https://".concat(this.region,"-assets.").concat(n);case"api":return"https://".concat(this.region,".").concat(n)}}}]),e}(),ix="posthog-js";function iI(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.organization,i=t.projectId,r=t.prefix,s=t.severityAllowList,o=void 0===s?["error"]:s;return function(t){if(!("*"===o||o.includes(t.level))||!e.__loaded)return t;t.tags||(t.tags={});var s,a,u,l,c,d=e.requestRouter.endpointFor("ui","/project/".concat(e.config.token,"/person/").concat(e.get_distinct_id()));t.tags["PostHog Person URL"]=d,e.sessionRecordingStarted()&&(t.tags["PostHog Recording URL"]=e.get_session_replay_url({withTimestamp:!0}));var h=(null==(s=t.exception)?void 0:s.values)||[],f={$exception_message:(null==(a=h[0])?void 0:a.value)||t.message,$exception_type:null==(u=h[0])?void 0:u.type,$exception_personURL:d,$exception_level:t.level,$sentry_event_id:t.event_id,$sentry_exception:t.exception,$sentry_exception_message:(null==(l=h[0])?void 0:l.value)||t.message,$sentry_exception_type:null==(c=h[0])?void 0:c.type,$sentry_tags:t.tags,$level:t.level};return n&&i&&(f.$sentry_url=(r||"https://sentry.io/organizations/")+n+"/issues/?project="+i+"&query="+t.event_id),e.exceptions.sendExceptionEvent(f),t}}var iF,iP,iR,iT=B(function e(t,n,i,r,s){N(this,e),this.name=ix,this.setupOnce=function(e){e(iI(t,{organization:n,projectId:i,prefix:r,severityAllowList:s}))}}),iC=function(){function e(t){N(this,e),this._instance=t}return B(e,[{key:"doPageView",value:function(e){var t,n=this._previousPageViewProperties(e);return this._currentPath=null!=(t=null==i?void 0:i.location.pathname)?t:"",this._instance.scrollManager.resetContext(),this._prevPageviewTimestamp=e,n}},{key:"doPageLeave",value:function(e){return this._previousPageViewProperties(e)}},{key:"_previousPageViewProperties",value:function(e){var t=this._currentPath,n=this._prevPageviewTimestamp,i=this._instance.scrollManager.getContext();if(!n)return{};var r={};if(i){var s=i.maxScrollHeight,o=i.lastScrollY,a=i.maxScrollY,u=i.maxContentHeight,l=i.lastContentY,c=i.maxContentY;x(s)||x(o)||x(a)||x(u)||x(l)||x(c)||(s=Math.ceil(s),o=Math.ceil(o),a=Math.ceil(a),u=Math.ceil(u),l=Math.ceil(l),c=Math.ceil(c),r={$prev_pageview_last_scroll:o,$prev_pageview_last_scroll_percentage:s<=1?1:iO(o/s,0,1),$prev_pageview_max_scroll:a,$prev_pageview_max_scroll_percentage:s<=1?1:iO(a/s,0,1),$prev_pageview_last_content:l,$prev_pageview_last_content_percentage:u<=1?1:iO(l/u,0,1),$prev_pageview_max_content:c,$prev_pageview_max_content_percentage:u<=1?1:iO(c/u,0,1)})}return t&&(r.$prev_pageview_pathname=t),n&&(r.$prev_pageview_duration=(e.getTime()-n.getTime())/1e3),r}}]),e}();function iO(e,t,n){return Math.max(t,Math.min(e,n))}!function(e){e.Popover="popover",e.API="api",e.Widget="widget"}(iF||(iF={})),function(e){e.Open="open",e.MultipleChoice="multiple_choice",e.SingleChoice="single_choice",e.Rating="rating",e.Link="link"}(iP||(iP={})),function(e){e.NextQuestion="next_question",e.End="end",e.ResponseBased="response_based",e.SpecificQuestion="specific_question"}(iR||(iR={}));var i$=function(){function e(){N(this,e),H(this,"events",{}),this.events={}}return B(e,[{key:"on",value:function(e,t){var n=this;return this.events[e]||(this.events[e]=[]),this.events[e].push(t),function(){n.events[e]=n.events[e].filter(function(e){return e!==t})}}},{key:"emit",value:function(e,t){var n,i=V(this.events[e]||[]);try{for(i.s();!(n=i.n()).done;)(0,n.value)(t)}catch(e){i.e(e)}finally{i.f()}var r,s=V(this.events["*"]||[]);try{for(s.s();!(r=s.n()).done;)(0,r.value)(e,t)}catch(e){s.e(e)}finally{s.f()}}}]),e}(),iA=function(){function e(t){var n=this;N(this,e),H(this,"_debugEventEmitter",new i$),H(this,"checkStep",function(e,t){return n.checkStepEvent(e,t)&&n.checkStepUrl(e,t)&&n.checkStepElement(e,t)}),H(this,"checkStepEvent",function(e,t){return null==t||!t.event||(null==e?void 0:e.event)===(null==t?void 0:t.event)}),this.instance=t,this.actionEvents=new Set,this.actionRegistry=new Set}return B(e,[{key:"init",value:function(){var e,t,n=this;x(null==(t=this.instance)?void 0:t._addCaptureHook)||null==(e=this.instance)||e._addCaptureHook(function(e,t){n.on(e,t)})}},{key:"register",value:function(e){var t,n,i=this;if(!x(null==(t=this.instance)?void 0:t._addCaptureHook)&&(e.forEach(function(e){var t,n;null==(t=i.actionRegistry)||t.add(e),null==(n=e.steps)||n.forEach(function(e){var t;null==(t=i.actionEvents)||t.add((null==e?void 0:e.event)||"")})}),null!=(n=this.instance)&&n.autocapture)){var r,s=new Set;e.forEach(function(e){var t;null==(t=e.steps)||t.forEach(function(e){null!=e&&e.selector&&s.add(null==e?void 0:e.selector)})}),null==(r=this.instance)||r.autocapture.setElementSelectors(s)}}},{key:"on",value:function(e,t){var n,i=this;null!=t&&0!=e.length&&(this.actionEvents.has(e)||this.actionEvents.has(null==t?void 0:t.event))&&this.actionRegistry&&(null==(n=this.actionRegistry)?void 0:n.size)>0&&this.actionRegistry.forEach(function(e){i.checkAction(t,e)&&i._debugEventEmitter.emit("actionCaptured",e.name)})}},{key:"_addActionHook",value:function(e){this.onAction("actionCaptured",function(t){return e(t)})}},{key:"checkAction",value:function(e,t){if(null==(null==t?void 0:t.steps))return!1;var n,i=V(t.steps);try{for(i.s();!(n=i.n()).done;){var r=n.value;if(this.checkStep(e,r))return!0}}catch(e){i.e(e)}finally{i.f()}return!1}},{key:"onAction",value:function(e,t){return this._debugEventEmitter.on(e,t)}},{key:"checkStepUrl",value:function(t,n){if(null!=n&&n.url){var i,r=null==t||null==(i=t.properties)?void 0:i.$current_url;if(!r||"string"!=typeof r||!e.matchString(r,null==n?void 0:n.url,(null==n?void 0:n.url_matching)||"contains"))return!1}return!0}},{key:"checkStepElement",value:function(t,n){if((null!=n&&n.href||null!=n&&n.tag_name||null!=n&&n.text)&&!this.getElementsList(t).some(function(t){return!(null!=n&&n.href&&!e.matchString(t.href||"",null==n?void 0:n.href,(null==n?void 0:n.href_matching)||"exact"))&&(null==n||!n.tag_name||t.tag_name===(null==n?void 0:n.tag_name))&&!(null!=n&&n.text&&!e.matchString(t.text||"",null==n?void 0:n.text,(null==n?void 0:n.text_matching)||"exact")&&!e.matchString(t.$el_text||"",null==n?void 0:n.text,(null==n?void 0:n.text_matching)||"exact"))}))return!1;if(null!=n&&n.selector){var i,r=null==t||null==(i=t.properties)?void 0:i.$element_selectors;if(!r||!r.includes(null==n?void 0:n.selector))return!1}return!0}},{key:"getElementsList",value:function(e){return null==(null==e?void 0:e.properties.$elements)?[]:null==e?void 0:e.properties.$elements}}],[{key:"matchString",value:function(t,n,r){switch(r){case"regex":return!!i&&tr(t,n);case"exact":return n===t;case"contains":return tr(t,e.escapeStringRegexp(n).replace(/_/g,".").replace(/%/g,".*"));default:return!1}}},{key:"escapeStringRegexp",value:function(e){return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}}]),e}(),iM=function(){function e(t){N(this,e),this.instance=t,this.eventToSurveys=new Map,this.actionToSurveys=new Map}return B(e,[{key:"register",value:function(e){var t;x(null==(t=this.instance)?void 0:t._addCaptureHook)||(this.setupEventBasedSurveys(e),this.setupActionBasedSurveys(e))}},{key:"setupActionBasedSurveys",value:function(e){var t=this,n=e.filter(function(e){var t,n,i,r;return(null==(t=e.conditions)?void 0:t.actions)&&(null==(n=e.conditions)||null==(i=n.actions)||null==(r=i.values)?void 0:r.length)>0});0!==n.length&&(null==this.actionMatcher&&(this.actionMatcher=new iA(this.instance),this.actionMatcher.init(),this.actionMatcher._addActionHook(function(e){t.onAction(e)})),n.forEach(function(e){var n,i,r,s,o,a,u,l,c,d;e.conditions&&null!=(n=e.conditions)&&n.actions&&null!=(i=e.conditions)&&null!=(r=i.actions)&&r.values&&(null==(s=e.conditions)||null==(o=s.actions)||null==(a=o.values)?void 0:a.length)>0&&(null==(u=t.actionMatcher)||u.register(e.conditions.actions.values),null==(l=e.conditions)||null==(c=l.actions)||null==(d=c.values)||d.forEach(function(n){if(n&&n.name){var i=t.actionToSurveys.get(n.name);i&&i.push(e.id),t.actionToSurveys.set(n.name,i||[e.id])}}))}))}},{key:"setupEventBasedSurveys",value:function(e){var t,n=this;0!==e.filter(function(e){var t,n,i,r;return(null==(t=e.conditions)?void 0:t.events)&&(null==(n=e.conditions)||null==(i=n.events)||null==(r=i.values)?void 0:r.length)>0}).length&&(null==(t=this.instance)||t._addCaptureHook(function(e,t){n.onEvent(e,t)}),e.forEach(function(e){var t,i,r;null==(t=e.conditions)||null==(i=t.events)||null==(r=i.values)||r.forEach(function(t){if(t&&t.name){var i=n.eventToSurveys.get(t.name);i&&i.push(e.id),n.eventToSurveys.set(t.name,i||[e.id])}})}))}},{key:"onEvent",value:function(t,n){var i,r,s=(null==(i=this.instance)||null==(r=i.persistence)?void 0:r.props[eT])||[];if(e.SURVEY_SHOWN_EVENT_NAME==t&&n&&s.length>0){var o,a=null==n||null==(o=n.properties)?void 0:o.$survey_id;if(a){var u=s.indexOf(a);u>=0&&(s.splice(u,1),this._updateActivatedSurveys(s))}}else this.eventToSurveys.has(t)&&this._updateActivatedSurveys(s.concat(this.eventToSurveys.get(t)||[]))}},{key:"onAction",value:function(e){var t,n,i=(null==(t=this.instance)||null==(n=t.persistence)?void 0:n.props[eT])||[];this.actionToSurveys.has(e)&&this._updateActivatedSurveys(i.concat(this.actionToSurveys.get(e)||[]))}},{key:"_updateActivatedSurveys",value:function(e){var t,n;null==(t=this.instance)||null==(n=t.persistence)||n.register(H({},eT,j(new Set(e))))}},{key:"getSurveys",value:function(){var e,t;return(null==(e=this.instance)||null==(t=e.persistence)?void 0:t.props[eT])||[]}},{key:"getEventToSurveys",value:function(){return this.eventToSurveys}},{key:"_getActionMatcher",value:function(){return this.actionMatcher}}]),e}();H(iM,"SURVEY_SHOWN_EVENT_NAME","survey shown");var iL="[Surveys]",iD={icontains:function(e){return!!i&&i.location.href.toLowerCase().indexOf(e.toLowerCase())>-1},not_icontains:function(e){return!!i&&-1===i.location.href.toLowerCase().indexOf(e.toLowerCase())},regex:function(e){return!!i&&tr(i.location.href,e)},not_regex:function(e){return!!i&&!tr(i.location.href,e)},exact:function(e){return(null==i?void 0:i.location.href)===e},is_not:function(e){return(null==i?void 0:i.location.href)!==e}},iN=function(){function e(t){N(this,e),this.instance=t,this._surveyEventReceiver=null}return B(e,[{key:"afterDecideResponse",value:function(e){this._decideServerResponse=!!e.surveys,this.loadIfEnabled()}},{key:"loadIfEnabled",value:function(){var e,t,n,i=this,r=null==v||null==(e=v.__PosthogExtensions__)?void 0:e.generateSurveys;this.instance.config.disable_surveys||!this._decideServerResponse||r||(null==this._surveyEventReceiver&&(this._surveyEventReceiver=new iM(this.instance)),null==(t=v.__PosthogExtensions__)||null==(n=t.loadExternalDependency)||n.call(t,this.instance,"surveys",function(e){var t,n;if(e)return $.error(iL,"Could not load surveys script",e);i._surveyManager=null==(t=v.__PosthogExtensions__)||null==(n=t.generateSurveys)?void 0:n.call(t,i.instance)}))}},{key:"getSurveys",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(this.instance.config.disable_surveys)return e([]);null==this._surveyEventReceiver&&(this._surveyEventReceiver=new iM(this.instance));var i=this.instance.get_property(eR);if(i&&!n)return e(i);this.instance._send_request({url:this.instance.requestRouter.endpointFor("api","/api/surveys/?token=".concat(this.instance.config.token)),method:"GET",transport:"XHR",callback:function(n){if(200!==n.statusCode||!n.json)return e([]);var i,r,s=n.json.surveys||[],o=s.filter(function(e){var t,n,i,r,s,o,a,u,l,c,d,h;return(null==(t=e.conditions)?void 0:t.events)&&(null==(n=e.conditions)||null==(i=n.events)?void 0:i.values)&&(null==(r=e.conditions)||null==(s=r.events)||null==(o=s.values)?void 0:o.length)>0||(null==(a=e.conditions)?void 0:a.actions)&&(null==(u=e.conditions)||null==(l=u.actions)?void 0:l.values)&&(null==(c=e.conditions)||null==(d=c.actions)||null==(h=d.values)?void 0:h.length)>0});return o.length>0&&(null==(r=t._surveyEventReceiver)||r.register(o)),null==(i=t.instance.persistence)||i.register(H({},eR,s)),e(s)}})}},{key:"getActiveMatchingSurveys",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.getSurveys(function(n){var i,r=n.filter(function(e){return!(!e.start_date||e.end_date)}).filter(function(e){if(!e.conditions)return!0;var t,n,i,r,s=null==(t=e.conditions)||!t.url||iD[null!=(n=null==(i=e.conditions)?void 0:i.urlMatchType)?n:"icontains"](e.conditions.url),o=null==(r=e.conditions)||!r.selector||(null==l?void 0:l.querySelector(e.conditions.selector));return s&&o}),s=null==(i=t._surveyEventReceiver)?void 0:i.getSurveys();return e(r.filter(function(e){if(!e.linked_flag_key&&!e.targeting_flag_key&&!e.internal_targeting_flag_key)return!0;var n,i,r,o,a,u,l,c,d,h,f=!e.linked_flag_key||t.instance.featureFlags.isFeatureEnabled(e.linked_flag_key),p=!e.targeting_flag_key||t.instance.featureFlags.isFeatureEnabled(e.targeting_flag_key),v=(null==(n=e.conditions)?void 0:n.events)&&(null==(i=e.conditions)||null==(r=i.events)?void 0:r.values)&&(null==(o=e.conditions)||null==(a=o.events)?void 0:a.values.length)>0,g=(null==(u=e.conditions)?void 0:u.actions)&&(null==(l=e.conditions)||null==(c=l.actions)?void 0:c.values)&&(null==(d=e.conditions)||null==(h=d.actions)?void 0:h.values.length)>0,_=!v&&!g||(null==s?void 0:s.includes(e.id)),m=t._canActivateRepeatedly(e),y=!(e.internal_targeting_flag_key&&!m)||t.instance.featureFlags.isFeatureEnabled(e.internal_targeting_flag_key);return f&&p&&y&&_}))},n)}},{key:"getNextSurveyStep",value:function(e,t,n){var i,r=e.questions[t],s=t+1;if(null==(i=r.branching)||!i.type)return t===e.questions.length-1?iR.End:s;if(r.branching.type===iR.End)return iR.End;if(r.branching.type===iR.SpecificQuestion){if(Number.isInteger(r.branching.index))return r.branching.index}else if(r.branching.type===iR.ResponseBased){if(r.type===iP.SingleChoice){var o,a,u=r.choices.indexOf("".concat(n));if(null!=(o=r.branching)&&null!=(a=o.responseValues)&&a.hasOwnProperty(u)){var l=r.branching.responseValues[u];return Number.isInteger(l)?l:l===iR.End?iR.End:s}}else if(r.type===iP.Rating){if("number"!=typeof n||!Number.isInteger(n))throw Error("The response type must be an integer");var c,d,h=function(e,t){if(3===t){if(e<1||e>3)throw Error("The response must be in range 1-3");return 1===e?"negative":2===e?"neutral":"positive"}if(5===t){if(e<1||e>5)throw Error("The response must be in range 1-5");return e<=2?"negative":3===e?"neutral":"positive"}if(7===t){if(e<1||e>7)throw Error("The response must be in range 1-7");return e<=3?"negative":4===e?"neutral":"positive"}if(10===t){if(e<0||e>10)throw Error("The response must be in range 0-10");return e<=6?"detractors":e<=8?"passives":"promoters"}throw Error("The scale must be one of: 3, 5, 7, 10")}(n,r.scale);if(null!=(c=r.branching)&&null!=(d=c.responseValues)&&d.hasOwnProperty(h)){var f=r.branching.responseValues[h];return Number.isInteger(f)?f:f===iR.End?iR.End:s}}return s}return $.warn(iL,"Falling back to next question index due to unexpected branching type"),s}},{key:"_canActivateRepeatedly",value:function(e){var t;return R(null==(t=v.__PosthogExtensions__)?void 0:t.canActivateRepeatedly)?($.warn(iL,"canActivateRepeatedly is not defined, must init before calling"),!1):v.__PosthogExtensions__.canActivateRepeatedly(e)}},{key:"canRenderSurvey",value:function(e){var t=this;R(this._surveyManager)?$.warn(iL,"canActivateRepeatedly is not defined, must init before calling"):this.getSurveys(function(n){var i=n.filter(function(t){return t.id===e})[0];t._surveyManager.canRenderSurvey(i)})}},{key:"renderSurvey",value:function(e,t){var n=this;R(this._surveyManager)?$.warn(iL,"canActivateRepeatedly is not defined, must init before calling"):this.getSurveys(function(i){var r=i.filter(function(t){return t.id===e})[0];n._surveyManager.renderSurvey(r,null==l?void 0:l.querySelector(t))})}}]),e}(),iq=function(){function e(t){var n,i,r=this;N(this,e),H(this,"serverLimits",{}),H(this,"lastEventRateLimited",!1),H(this,"checkForLimiting",function(e){var t=e.text;if(t&&t.length)try{(JSON.parse(t).quota_limited||[]).forEach(function(e){$.info("[RateLimiter] ".concat(e||"events"," is quota limited.")),r.serverLimits[e]=(new Date).getTime()+6e4})}catch(e){return void $.warn('[RateLimiter] could not rate limit - continuing. Error: "'.concat(null==e?void 0:e.message,'"'),{text:t})}}),this.instance=t,this.captureEventsPerSecond=(null==(n=t.config.rate_limiting)?void 0:n.events_per_second)||10,this.captureEventsBurstLimit=Math.max((null==(i=t.config.rate_limiting)?void 0:i.events_burst_limit)||10*this.captureEventsPerSecond,this.captureEventsPerSecond),this.lastEventRateLimited=this.clientRateLimitContext(!0).isRateLimited}return B(e,[{key:"clientRateLimitContext",value:function(){var e,t,n,i=arguments.length>0&&void 0!==arguments[0]&&arguments[0],r=(new Date).getTime(),s=null!=(e=null==(t=this.instance.persistence)?void 0:t.get_property(eA))?e:{tokens:this.captureEventsBurstLimit,last:r};s.tokens+=(r-s.last)/1e3*this.captureEventsPerSecond,s.last=r,s.tokens>this.captureEventsBurstLimit&&(s.tokens=this.captureEventsBurstLimit);var o=s.tokens<1;return o||i||(s.tokens=Math.max(0,s.tokens-1)),!o||this.lastEventRateLimited||i||this.instance.capture("$$client_ingestion_warning",{$$client_ingestion_warning_message:"posthog-js client rate limited. Config is set to ".concat(this.captureEventsPerSecond," events per second and ").concat(this.captureEventsBurstLimit," events burst limit.")},{skip_client_rate_limiting:!0}),this.lastEventRateLimited=o,null==(n=this.instance.persistence)||n.set_property(eA,s),{isRateLimited:o,remainingTokens:s.tokens}}},{key:"isServerRateLimited",value:function(e){var t=this.serverLimits[e||"events"]||!1;return!1!==t&&(new Date).getTime()<t}}]),e}(),iB=function(){return L({initialPathName:(null==c?void 0:c.pathname)||"",referringDomain:t5.referringDomain()},t5.campaignParams())},iH=function(){function e(t,n,i){var r=this;N(this,e),H(this,"_onSessionIdCallback",function(e){var t=r._getStoredProps();if(!t||t.sessionId!==e){var n={sessionId:e,props:r._sessionSourceParamGenerator()};r._persistence.register(H({},e$,n))}}),this._sessionIdManager=t,this._persistence=n,this._sessionSourceParamGenerator=i||iB,this._sessionIdManager.onSessionId(this._onSessionIdCallback)}return B(e,[{key:"_getStoredProps",value:function(){return this._persistence.props[e$]}},{key:"getSessionProps",value:function(){var e,t=null==(e=this._getStoredProps())?void 0:e.props;return t?{$client_session_initial_referring_host:t.referringDomain,$client_session_initial_pathname:t.initialPathName,$client_session_initial_utm_source:t.utm_source,$client_session_initial_utm_campaign:t.utm_campaign,$client_session_initial_utm_medium:t.utm_medium,$client_session_initial_utm_content:t.utm_content,$client_session_initial_utm_term:t.utm_term}:{}}}]),e}(),iU=["ahrefsbot","ahrefssiteaudit","applebot","baiduspider","bingbot","bingpreview","bot.htm","bot.php","crawler","deepscan","duckduckbot","facebookexternal","facebookcatalog","gptbot","http://yandex.com/bots","hubspot","ia_archiver","linkedinbot","mj12bot","msnbot","nessus","petalbot","pinterest","prerender","rogerbot","screaming frog","semrushbot","sitebulb","slurp","turnitin","twitterbot","vercelbot","yahoo! slurp","yandexbot","headlesschrome","cypress","Google-HotelAdsVerifier","adsbot-google","apis-google","duplexweb-google","feedfetcher-google","google favicon","google web preview","google-read-aloud","googlebot","googleweblight","mediapartners-google","storebot-google","Bytespider;"],ij=function(e,t){if(!e)return!1;var n=e.toLowerCase();return iU.concat(t||[]).some(function(e){var t=e.toLowerCase();return -1!==n.indexOf(t)})},iW=function(){function e(){N(this,e),this.clicks=[]}return B(e,[{key:"isRageClick",value:function(e,t,n){var i=this.clicks[this.clicks.length-1];if(i&&Math.abs(e-i.x)+Math.abs(t-i.y)<30&&n-i.timestamp<1e3){if(this.clicks.push({x:e,y:t,timestamp:n}),3===this.clicks.length)return!0}else this.clicks=[{x:e,y:t,timestamp:n}];return!1}}]),e}();function iz(e){var t;return e.id===eq||!(null==(t=e.closest)||!t.call(e,"#"+eq))}var iV=function(){function e(t){var n,r=this;N(this,e),H(this,"rageclicks",new iW),H(this,"_enabledServerSide",!1),H(this,"_initialized",!1),H(this,"_flushInterval",null),this.instance=t,this._enabledServerSide=!(null==(n=this.instance.persistence)||!n.props[eh]),null==i||i.addEventListener("beforeunload",function(){r.flush()})}return B(e,[{key:"flushIntervalMilliseconds",get:function(){var e=5e3;return S(this.instance.config.capture_heatmaps)&&this.instance.config.capture_heatmaps.flush_interval_milliseconds&&(e=this.instance.config.capture_heatmaps.flush_interval_milliseconds),e}},{key:"isEnabled",get:function(){return x(this.instance.config.capture_heatmaps)?x(this.instance.config.enable_heatmaps)?this._enabledServerSide:this.instance.config.enable_heatmaps:!1!==this.instance.config.capture_heatmaps}},{key:"startIfEnabled",value:function(){if(this.isEnabled)this._initialized||($.info("[heatmaps] starting..."),this._setupListeners(),this._flushInterval=setInterval(this.flush.bind(this),this.flushIntervalMilliseconds));else{var e;clearInterval(null!=(e=this._flushInterval)?e:void 0),this.getAndClearBuffer()}}},{key:"afterDecideResponse",value:function(e){var t=!!e.heatmaps;this.instance.persistence&&this.instance.persistence.register(H({},eh,t)),this._enabledServerSide=t,this.startIfEnabled()}},{key:"getAndClearBuffer",value:function(){var e=this.buffer;return this.buffer=void 0,e}},{key:"_setupListeners",value:function(){var e=this;i&&l&&(eo(l,"click",function(t){return e._onClick(t||(null==i?void 0:i.event))},!1,!0),eo(l,"mousemove",function(t){return e._onMouseMove(t||(null==i?void 0:i.event))},!1,!0),this._initialized=!0)}},{key:"_getProperties",value:function(e,t){var n=this.instance.scrollManager.scrollY(),r=this.instance.scrollManager.scrollX(),s=this.instance.scrollManager.scrollElement(),o=function(e,t,n){for(var r=e;r&&no(r)&&!na(r,"body")&&r!==n;){if(K(t,null==i?void 0:i.getComputedStyle(r).position))return!0;r=nd(r)}return!1}(ns(e),["fixed","sticky"],s);return{x:e.clientX+(o?0:r),y:e.clientY+(o?0:n),target_fixed:o,type:t}}},{key:"_onClick",value:function(e){var t;if(!iz(e.target)){var n=this._getProperties(e,"click");null!=(t=this.rageclicks)&&t.isRageClick(e.clientX,e.clientY,(new Date).getTime())&&this._capture(L(L({},n),{},{type:"rageclick"})),this._capture(n)}}},{key:"_onMouseMove",value:function(e){var t=this;iz(e.target)||(clearTimeout(this._mouseMoveTimeout),this._mouseMoveTimeout=setTimeout(function(){t._capture(t._getProperties(e,"mousemove"))},500))}},{key:"_capture",value:function(e){if(i){var t=i.location.href;this.buffer=this.buffer||{},this.buffer[t]||(this.buffer[t]=[]),this.buffer[t].push(e)}}},{key:"flush",value:function(){this.buffer&&!E(this.buffer)&&this.instance.capture("$$heatmap",{$heatmap_data:this.getAndClearBuffer()})}}]),e}(),iG=function(){function e(t){var n=this;N(this,e),H(this,"_updateScrollData",function(){n.context||(n.context={});var e,t,i,r,s=n.scrollElement(),o=n.scrollY(),a=s?Math.max(0,s.scrollHeight-s.clientHeight):0,u=o+((null==s?void 0:s.clientHeight)||0),l=(null==s?void 0:s.scrollHeight)||0;n.context.lastScrollY=Math.ceil(o),n.context.maxScrollY=Math.max(o,null!=(e=n.context.maxScrollY)?e:0),n.context.maxScrollHeight=Math.max(a,null!=(t=n.context.maxScrollHeight)?t:0),n.context.lastContentY=u,n.context.maxContentY=Math.max(u,null!=(i=n.context.maxContentY)?i:0),n.context.maxContentHeight=Math.max(l,null!=(r=n.context.maxContentHeight)?r:0)}),this.instance=t}return B(e,[{key:"getContext",value:function(){return this.context}},{key:"resetContext",value:function(){var e=this.context;return setTimeout(this._updateScrollData,0),e}},{key:"startMeasuringScrollPosition",value:function(){null==i||i.addEventListener("scroll",this._updateScrollData,!0),null==i||i.addEventListener("scrollend",this._updateScrollData,!0),null==i||i.addEventListener("resize",this._updateScrollData)}},{key:"scrollElement",value:function(){if(!this.instance.config.scroll_root_selector)return null==i?void 0:i.document.documentElement;var e,t=V(k(this.instance.config.scroll_root_selector)?this.instance.config.scroll_root_selector:[this.instance.config.scroll_root_selector]);try{for(t.s();!(e=t.n()).done;){var n=e.value,r=null==i?void 0:i.document.querySelector(n);if(r)return r}}catch(e){t.e(e)}finally{t.f()}}},{key:"scrollY",value:function(){if(this.instance.config.scroll_root_selector){var e=this.scrollElement();return e&&e.scrollTop||0}return i&&(i.scrollY||i.pageYOffset||i.document.documentElement.scrollTop)||0}},{key:"scrollX",value:function(){if(this.instance.config.scroll_root_selector){var e=this.scrollElement();return e&&e.scrollLeft||0}return i&&(i.scrollX||i.pageXOffset||i.document.documentElement.scrollLeft)||0}}]),e}(),iQ="$copy_autocapture";function iJ(e,t){return t.length>e?t.slice(0,e)+"...":t}var iY,iX=function(){function e(t){N(this,e),H(this,"_initialized",!1),H(this,"_isDisabledServerSide",null),H(this,"rageclicks",new iW),H(this,"_elementsChainAsString",!1),this.instance=t,this._elementSelectors=null}return B(e,[{key:"config",get:function(){var e,t,n=S(this.instance.config.autocapture)?this.instance.config.autocapture:{};return n.url_allowlist=null==(e=n.url_allowlist)?void 0:e.map(function(e){return new RegExp(e)}),n.url_ignorelist=null==(t=n.url_ignorelist)?void 0:t.map(function(e){return new RegExp(e)}),n}},{key:"_addDomEventHandlers",value:function(){var e=this;if(this.isBrowserSupported()){if(i&&l){var t=function(t){t=t||(null==i?void 0:i.event);try{e._captureEvent(t)}catch(e){$.error("Failed to capture event",e)}},n=function(t){t=t||(null==i?void 0:i.event),e._captureEvent(t,iQ)};eo(l,"submit",t,!1,!0),eo(l,"change",t,!1,!0),eo(l,"click",t,!1,!0),this.config.capture_copied_text&&(eo(l,"copy",n,!1,!0),eo(l,"cut",n,!1,!0))}}else $.info("Disabling Automatic Event Collection because this browser is not supported")}},{key:"startIfEnabled",value:function(){this.isEnabled&&!this._initialized&&(this._addDomEventHandlers(),this._initialized=!0)}},{key:"afterDecideResponse",value:function(e){e.elementsChainAsString&&(this._elementsChainAsString=e.elementsChainAsString),this.instance.persistence&&this.instance.persistence.register(H({},ed,!!e.autocapture_opt_out)),this._isDisabledServerSide=!!e.autocapture_opt_out,this.startIfEnabled()}},{key:"setElementSelectors",value:function(e){this._elementSelectors=e}},{key:"getElementSelectors",value:function(e){var t,n=[];return null==(t=this._elementSelectors)||t.forEach(function(t){var i=null==l?void 0:l.querySelectorAll(t);null==i||i.forEach(function(i){e===i&&n.push(t)})}),n}},{key:"isEnabled",get:function(){var e,t,n=null==(e=this.instance.persistence)?void 0:e.props[ed];if(P(this._isDisabledServerSide)&&!C(n)&&!this.instance.config.advanced_disable_decide)return!1;var i=null!=(t=this._isDisabledServerSide)?t:!!n;return!!this.instance.config.autocapture&&!i}},{key:"_previousElementSibling",value:function(e){if(e.previousElementSibling)return e.previousElementSibling;var t=e;do t=t.previousSibling;while(t&&!no(t));return t}},{key:"_getAugmentPropertiesFromElement",value:function(e){if(!nh(e))return{};var t={};return Y(e.attributes,function(e){if(e.name&&0===e.name.indexOf("data-ph-capture-attribute")){var n=e.name.replace("data-ph-capture-attribute-",""),i=e.value;n&&i&&nb(i)&&(t[n]=i)}}),t}},{key:"_getPropertiesFromElement",value:function(e,t,n){var i,r=e.tagName.toLowerCase(),s={tag_name:r};nc.indexOf(r)>-1&&!n&&("a"===r.toLowerCase()||"button"===r.toLowerCase()?s.$el_text=iJ(1024,nk(e)):s.$el_text=iJ(1024,nr(e)));var o=nn(e);o.length>0&&(s.classes=o.filter(function(e){return""!==e}));var a=null==(i=this.config)?void 0:i.element_attribute_ignorelist;Y(e.attributes,function(n){var i;if((!nf(e)||-1!==["name","id","class","aria-label"].indexOf(n.name))&&(null==a||!a.includes(n.name))&&!t&&nb(n.value)&&(!I(i=n.name)||"_ngcontent"!==i.substring(0,10)&&"_nghost"!==i.substring(0,7))){var r=n.value;"class"===n.name&&(r=ne(r).join(" ")),s["attr__"+n.name]=iJ(1024,r)}});for(var u=1,l=1,c=e;c=this._previousElementSibling(c);)u++,c.tagName===e.tagName&&l++;return s.nth_child=u,s.nth_of_type=l,s}},{key:"_getDefaultProperties",value:function(e){return{$event_type:e,$ce_version:1}}},{key:"_captureEvent",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"$autocapture";if(this.isEnabled){var r,s=ns(e);nu(s)&&(s=s.parentNode||null),"$autocapture"===n&&"click"===e.type&&e instanceof MouseEvent&&this.instance.config.rageclick&&null!=(r=this.rageclicks)&&r.isRageClick(e.clientX,e.clientY,(new Date).getTime())&&this._captureEvent(e,"$rageclick");var o=n===iQ;if(s&&function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0,s=arguments.length>4?arguments[4]:void 0;if(!i||!e||na(e,"html")||!no(e)||null!=n&&n.url_allowlist&&!nt(n.url_allowlist)||null!=n&&n.url_ignorelist&&nt(n.url_ignorelist))return!1;if(null!=n&&n.dom_event_allowlist){var o=n.dom_event_allowlist;if(o&&!o.some(function(e){return t.type===e}))return!1}for(var a=!1,u=[e],l=!0,c=e;c.parentNode&&!na(c,"body");)if(nl(c.parentNode))u.push(c.parentNode.host),c=c.parentNode.host;else{if(!(l=nd(c)))break;if(r||nc.indexOf(l.tagName.toLowerCase())>-1)a=!0;else{var d=i.getComputedStyle(l);d&&"pointer"===d.getPropertyValue("cursor")&&(a=!0)}u.push(l),c=l}if(!function(e,t){var n=null==t?void 0:t.element_allowlist;if(x(n))return!0;var i,r=V(e);try{for(r.s();!(i=r.n()).done;){var s=function(){var e=i.value;if(n.some(function(t){return e.tagName.toLowerCase()===t}))return{v:!0}}();if("object"===D(s))return s.v}}catch(e){r.e(e)}finally{r.f()}return!1}(u,n)||!function(e,t){var n=null==t?void 0:t.css_selector_allowlist;if(x(n))return!0;var i,r=V(e);try{for(r.s();!(i=r.n()).done;){var s=function(){var e=i.value;if(n.some(function(t){return e.matches(t)}))return{v:!0}}();if("object"===D(s))return s.v}}catch(e){r.e(e)}finally{r.f()}return!1}(u,n))return!1;var h=i.getComputedStyle(e);if(h&&"pointer"===h.getPropertyValue("cursor")&&"click"===t.type)return!0;var f=e.tagName.toLowerCase();switch(f){case"html":return!1;case"form":return(s||["submit"]).indexOf(t.type)>=0;case"input":case"select":case"textarea":return(s||["change","click"]).indexOf(t.type)>=0;default:return a?(s||["click"]).indexOf(t.type)>=0:(s||["click"]).indexOf(t.type)>=0&&(nc.indexOf(f)>-1||"true"===e.getAttribute("contenteditable"))}}(s,e,this.config,o,o?["copy","cut"]:void 0)){for(var a,u,l=[s],c=s;c.parentNode&&!na(c,"body");)nl(c.parentNode)?(l.push(c.parentNode.host),c=c.parentNode.host):(l.push(c.parentNode),c=c.parentNode);var d,h,f=[],p={},v=!1;if(Y(l,function(e){var n=nh(e);"a"===e.tagName.toLowerCase()&&(d=e.getAttribute("href"),d=n&&nb(d)&&d),K(nn(e),"ph-no-capture")&&(v=!0),f.push(t._getPropertiesFromElement(e,t.instance.config.mask_all_element_attributes,t.instance.config.mask_all_text)),X(p,t._getAugmentPropertiesFromElement(e))}),this.instance.config.mask_all_text||("a"===s.tagName.toLowerCase()||"button"===s.tagName.toLowerCase()?f[0].$el_text=nk(s):f[0].$el_text=nr(s)),d){f[0].attr__href=d;var g,_,m=null==(g=ti(d))?void 0:g.host,y=null==i||null==(_=i.location)?void 0:_.host;m&&y&&m!==y&&(h=d)}if(v)return!1;var b=X(this._getDefaultProperties(e.type),this._elementsChainAsString?{$elements_chain:f.map(function(e){var t,n,i,r={text:null==(n=e.$el_text)?void 0:n.slice(0,400),tag_name:e.tag_name,href:null==(i=e.attr__href)?void 0:i.slice(0,2048),attr_class:(t=e.attr__class)?k(t)?t:ne(t):void 0,attr_id:e.attr__id,nth_child:e.nth_child,nth_of_type:e.nth_of_type,attributes:{}};return Z(e).filter(function(e){return 0===U(e,1)[0].indexOf("attr__")}).forEach(function(e){var t=U(e,2),n=t[0],i=t[1];return r.attributes[n]=i}),r}).map(function(e){var t,n,i="";if(e.tag_name&&(i+=e.tag_name),e.attr_class){e.attr_class.sort();var r,s=V(e.attr_class);try{for(s.s();!(r=s.n()).done;){var o=r.value;i+=".".concat(o.replace(/"/g,""))}}catch(e){s.e(e)}finally{s.f()}}var a=L(L(L(L({},e.text?{text:e.text}:{}),{},{"nth-child":null!=(t=e.nth_child)?t:0,"nth-of-type":null!=(n=e.nth_of_type)?n:0},e.href?{href:e.href}:{}),e.attr_id?{attr_id:e.attr_id}:{}),e.attributes),u={};return Z(a).sort(function(e,t){var n=U(e,1)[0],i=U(t,1)[0];return n.localeCompare(i)}).forEach(function(e){var t=U(e,2),n=t[0],i=t[1];return u[nw(n.toString())]=nw(i.toString())}),i+=":",i+=Z(a).map(function(e){var t=U(e,2),n=t[0],i=t[1];return"".concat(n,'="').concat(i,'"')}).join("")}).join(";")}:{$elements:f},null!=(a=f[0])&&a.$el_text?{$el_text:null==(u=f[0])?void 0:u.$el_text}:{},h&&"click"===e.type?{$external_click_url:h}:{},p),w=this.getElementSelectors(s);if(w&&w.length>0&&(b.$element_selectors=w),n===iQ){var S,E=ni(null==i||null==(S=i.getSelection())?void 0:S.toString()),I=e.type||"clipboard";if(!E)return!1;b.$selected_content=E,b.$copy_type=I}return this.instance.capture(n,b),!0}}}},{key:"isBrowserSupported",value:function(){return w(null==l?void 0:l.querySelectorAll)}}]),e}(),iK=function(){function e(t){var n=this;N(this,e),H(this,"_restoreXHRPatch",void 0),H(this,"_restoreFetchPatch",void 0),H(this,"_startCapturing",function(){var e,t,i,r;x(n._restoreXHRPatch)&&(null==(e=v.__PosthogExtensions__)||null==(t=e.tracingHeadersPatchFns)||t._patchXHR(n.instance.sessionManager)),x(n._restoreFetchPatch)&&(null==(i=v.__PosthogExtensions__)||null==(r=i.tracingHeadersPatchFns)||r._patchFetch(n.instance.sessionManager))}),this.instance=t}return B(e,[{key:"_loadScript",value:function(e){var t,n,i;null!=(t=v.__PosthogExtensions__)&&t.tracingHeadersPatchFns&&e(),null==(n=v.__PosthogExtensions__)||null==(i=n.loadExternalDependency)||i.call(n,this.instance,"tracing-headers",function(t){if(t)return $.error("[TRACING-HEADERS] failed to load script",t);e()})}},{key:"startIfEnabledOrStop",value:function(){var e,t;this.instance.config.__add_tracing_headers?this._loadScript(this._startCapturing):(null==(e=this._restoreXHRPatch)||e.call(this),null==(t=this._restoreFetchPatch)||t.call(this),this._restoreXHRPatch=void 0,this._restoreFetchPatch=void 0)}}]),e}();!function(e){e[e.PENDING=-1]="PENDING",e[e.DENIED=0]="DENIED",e[e.GRANTED=1]="GRANTED"}(iY||(iY={}));var iZ=function(){function e(t){N(this,e),this.instance=t}return B(e,[{key:"config",get:function(){return this.instance.config}},{key:"consent",get:function(){return this.getDnt()?iY.DENIED:this.storedConsent}},{key:"isOptedOut",value:function(){return this.consent===iY.DENIED||this.consent===iY.PENDING&&this.config.opt_out_capturing_by_default}},{key:"isOptedIn",value:function(){return!this.isOptedOut()}},{key:"optInOut",value:function(e){this.storage.set(this.storageKey,+!!e,this.config.cookie_expiration,this.config.cross_subdomain_cookie,this.config.secure_cookie)}},{key:"reset",value:function(){this.storage.remove(this.storageKey,this.config.cross_subdomain_cookie)}},{key:"storageKey",get:function(){var e=this.instance.config,t=e.token;return(e.opt_out_capturing_cookie_prefix||"__ph_opt_in_out_")+t}},{key:"storedConsent",get:function(){var e=this.storage.get(this.storageKey);return"1"===e?iY.GRANTED:"0"===e?iY.DENIED:iY.PENDING}},{key:"storage",get:function(){if(!this._storage){var e=this.config.opt_out_capturing_persistence_type;this._storage="localStorage"===e?e6:e3;var t="localStorage"===e?e3:e6;t.get(this.storageKey)&&(this._storage.get(this.storageKey)||this.optInOut("1"===t.get(this.storageKey)),t.remove(this.storageKey,this.config.cross_subdomain_cookie))}return this._storage}},{key:"getDnt",value:function(){return!!this.config.respect_dnt&&!!ea([null==u?void 0:u.doNotTrack,null==u?void 0:u.msDoNotTrack,v.doNotTrack],function(e){return K([!0,1,"1","yes"],e)})}}]),e}(),i0="[Exception Autocapture]",i1=function(){function e(t){var n,r=this;N(this,e),H(this,"originalOnUnhandledRejectionHandler",void 0),H(this,"startCapturing",function(){var e,t,n,s;if(i&&r.isEnabled&&!r.hasHandlers&&!r.isCapturing){var o=null==(e=v.__PosthogExtensions__)||null==(t=e.errorWrappingFunctions)?void 0:t.wrapOnError,a=null==(n=v.__PosthogExtensions__)||null==(s=n.errorWrappingFunctions)?void 0:s.wrapUnhandledRejection;if(o&&a)try{r.unwrapOnError=o(r.captureException.bind(r)),r.unwrapUnhandledRejection=a(r.captureException.bind(r))}catch(e){$.error(i0+" failed to start",e),r.stopCapturing()}else $.error(i0+" failed to load error wrapping functions - cannot start")}}),this.instance=t,this.remoteEnabled=!(null==(n=this.instance.persistence)||!n.props[ef]),this.startIfEnabled()}return B(e,[{key:"isEnabled",get:function(){var e;return null!=(e=this.remoteEnabled)&&e}},{key:"isCapturing",get:function(){var e;return!(null==i||null==(e=i.onerror)||!e.__POSTHOG_INSTRUMENTED__)}},{key:"hasHandlers",get:function(){return this.originalOnUnhandledRejectionHandler||this.unwrapOnError}},{key:"startIfEnabled",value:function(){this.isEnabled&&!this.isCapturing&&($.info(i0+" enabled, starting..."),this.loadScript(this.startCapturing))}},{key:"loadScript",value:function(e){var t,n;this.hasHandlers&&e(),null==(t=v.__PosthogExtensions__)||null==(n=t.loadExternalDependency)||n.call(t,this.instance,"exception-autocapture",function(t){if(t)return $.error(i0+" failed to load script",t);e()})}},{key:"stopCapturing",value:function(){var e,t;null==(e=this.unwrapOnError)||e.call(this),null==(t=this.unwrapUnhandledRejection)||t.call(this)}},{key:"afterDecideResponse",value:function(e){var t=e.autocaptureExceptions;this.remoteEnabled=!!t,this.instance.persistence&&this.instance.persistence.register(H({},ef,this.remoteEnabled)),this.startIfEnabled()}},{key:"captureException",value:function(e){var t=this.instance.requestRouter.endpointFor("ui");e.$exception_personURL="".concat(t,"/project/").concat(this.instance.config.token,"/person/").concat(this.instance.get_distinct_id()),this.instance.exceptions.sendExceptionEvent(e)}}]),e}(),i2="[Web Vitals]",i3=function(){function e(t){var n,i=this;N(this,e),H(this,"_enabledServerSide",!1),H(this,"_initialized",!1),H(this,"buffer",{url:void 0,metrics:[],firstMetricTimestamp:void 0}),H(this,"_flushToCapture",function(){clearTimeout(i._delayedFlushTimer),0!==i.buffer.metrics.length&&(i.instance.capture("$web_vitals",i.buffer.metrics.reduce(function(e,t){var n;return L(L({},e),{},(H(n={},"$web_vitals_".concat(t.name,"_event"),L({},t)),H(n,"$web_vitals_".concat(t.name,"_value"),t.value),n))},{})),i.buffer={url:void 0,metrics:[],firstMetricTimestamp:void 0})}),H(this,"_addToBuffer",function(e){var t,n=null==(t=i.instance.sessionManager)?void 0:t.checkAndGetSessionAndWindowId(!0);if(x(n))$.error(i2+"Could not read session ID. Dropping metrics!");else{i.buffer=i.buffer||{url:void 0,metrics:[],firstMetricTimestamp:void 0};var r=i._currentURL();x(r)||(R(null==e?void 0:e.name)||R(null==e?void 0:e.value)?$.error(i2+"Invalid metric received",e):i._maxAllowedValue&&e.value>=i._maxAllowedValue?$.error(i2+"Ignoring metric with value >= "+i._maxAllowedValue,e):(i.buffer.url!==r&&(i._flushToCapture(),i._delayedFlushTimer=setTimeout(i._flushToCapture,8e3)),x(i.buffer.url)&&(i.buffer.url=r),i.buffer.firstMetricTimestamp=x(i.buffer.firstMetricTimestamp)?Date.now():i.buffer.firstMetricTimestamp,i.buffer.metrics.push(L(L({},e),{},{$current_url:r,$session_id:n.sessionId,$window_id:n.windowId,timestamp:Date.now()})),i.buffer.metrics.length===i.allowedMetrics.length&&i._flushToCapture()))}}),H(this,"_startCapturing",function(){var e,t,n,r,s=v.__PosthogExtensions__;if(!x(s)&&!x(s.postHogWebVitalsCallbacks)){var o=s.postHogWebVitalsCallbacks;e=o.onLCP,t=o.onCLS,n=o.onFCP,r=o.onINP}e&&t&&n&&r?(i.allowedMetrics.indexOf("LCP")>-1&&e(i._addToBuffer.bind(i)),i.allowedMetrics.indexOf("CLS")>-1&&t(i._addToBuffer.bind(i)),i.allowedMetrics.indexOf("FCP")>-1&&n(i._addToBuffer.bind(i)),i.allowedMetrics.indexOf("INP")>-1&&r(i._addToBuffer.bind(i)),i._initialized=!0):$.error(i2+"web vitals callbacks not loaded - not starting")}),this.instance=t,this._enabledServerSide=!(null==(n=this.instance.persistence)||!n.props[ev]),this.startIfEnabled()}return B(e,[{key:"allowedMetrics",get:function(){var e,t,n=S(this.instance.config.capture_performance)?null==(e=this.instance.config.capture_performance)?void 0:e.web_vitals_allowed_metrics:void 0;return x(n)?(null==(t=this.instance.persistence)?void 0:t.props[eg])||["CLS","FCP","INP","LCP"]:n}},{key:"_maxAllowedValue",get:function(){var e=S(this.instance.config.capture_performance)&&T(this.instance.config.capture_performance.__web_vitals_max_value)?this.instance.config.capture_performance.__web_vitals_max_value:9e5;return 0<e&&e<=6e4?9e5:e}},{key:"isEnabled",get:function(){var e=S(this.instance.config.capture_performance)?this.instance.config.capture_performance.web_vitals:void 0;return C(e)?e:this._enabledServerSide}},{key:"startIfEnabled",value:function(){this.isEnabled&&!this._initialized&&($.info(i2+" enabled, starting..."),this.loadScript(this._startCapturing))}},{key:"afterDecideResponse",value:function(e){var t=S(e.capturePerformance)&&!!e.capturePerformance.web_vitals,n=S(e.capturePerformance)?e.capturePerformance.web_vitals_allowed_metrics:void 0;this.instance.persistence&&(this.instance.persistence.register(H({},ev,t)),this.instance.persistence.register(H({},eg,n))),this._enabledServerSide=t,this.startIfEnabled()}},{key:"loadScript",value:function(e){var t,n,i;null!=(t=v.__PosthogExtensions__)&&t.postHogWebVitalsCallbacks&&e(),null==(n=v.__PosthogExtensions__)||null==(i=n.loadExternalDependency)||i.call(n,this.instance,"web-vitals",function(t){t?$.error(i2+" failed to load script",t):e()})}},{key:"_currentURL",value:function(){var e=i?i.location.href:void 0;return e||$.error(i2+"Could not determine current URL"),e}}]),e}(),i5={icontains:function(e,t){return!!i&&t.href.toLowerCase().indexOf(e.toLowerCase())>-1},not_icontains:function(e,t){return!!i&&-1===t.href.toLowerCase().indexOf(e.toLowerCase())},regex:function(e,t){return!!i&&tr(t.href,e)},not_regex:function(e,t){return!!i&&!tr(t.href,e)},exact:function(e,t){return t.href===e},is_not:function(e,t){return t.href!==e}},i6=function(){function e(t){var n=this;N(this,e),H(this,"getWebExperimentsAndEvaluateDisplayLogic",function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];n.getWebExperiments(function(t){e.logInfo("retrieved web experiments from the server"),n._flagToExperiments=new Map,t.forEach(function(t){if(t.feature_flag_key&&n._featureFlags&&n._featureFlags[t.feature_flag_key]){n._flagToExperiments&&(e.logInfo("setting flag key ",t.feature_flag_key," to web experiment ",t),null==(i=n._flagToExperiments)||i.set(t.feature_flag_key,t));var i,r=n._featureFlags[t.feature_flag_key];r&&t.variants[r]&&e.applyTransforms(t.name,r,t.variants[r].transforms)}else if(t.variants)for(var s in t.variants){var o=t.variants[s];e.matchesTestVariant(o)&&e.applyTransforms(t.name,s,o.transforms)}})},t)}),this.instance=t,this.instance.onFeatureFlags&&this.instance.onFeatureFlags(function(e){n.applyFeatureFlagChanges(e)}),this._flagToExperiments=new Map}return B(e,[{key:"applyFeatureFlagChanges",value:function(t){var n=this;e.logInfo("applying feature flags",t),R(this._flagToExperiments)||this.instance.config.disable_web_experiments||t.forEach(function(t){var i;if(n._flagToExperiments&&null!=(i=n._flagToExperiments)&&i.has(t)){var r,s=n.instance.getFeatureFlag(t),o=null==(r=n._flagToExperiments)?void 0:r.get(t);s&&null!=o&&o.variants[s]&&e.applyTransforms(o.name,s,o.variants[s].transforms)}})}},{key:"afterDecideResponse",value:function(e){this._featureFlags=e.featureFlags,this.loadIfEnabled()}},{key:"loadIfEnabled",value:function(){this.instance.config.disable_web_experiments||this.getWebExperimentsAndEvaluateDisplayLogic()}},{key:"getWebExperiments",value:function(e,t){if(this.instance.config.disable_web_experiments)return e([]);var n=this.instance.get_property("$web_experiments");if(n&&!t)return e(n);this.instance._send_request({url:this.instance.requestRouter.endpointFor("api","/api/web_experiments/?token=".concat(this.instance.config.token)),method:"GET",transport:"XHR",callback:function(t){return 200===t.statusCode&&t.json?e(t.json.experiments||[]):e([])}})}}],[{key:"matchesTestVariant",value:function(t){return!R(t.conditions)&&e.matchUrlConditions(t)&&e.matchUTMConditions(t)}},{key:"matchUrlConditions",value:function(t){if(R(t.conditions)||R(null==(n=t.conditions)?void 0:n.url))return!0;var n,i,r,s,o=e.getWindowLocation();return!!o&&(null==(i=t.conditions)||!i.url||i5[null!=(r=null==(s=t.conditions)?void 0:s.urlMatchType)?r:"icontains"](t.conditions.url,o))}},{key:"getWindowLocation",value:function(){return null==i?void 0:i.location}},{key:"matchUTMConditions",value:function(e){if(R(e.conditions)||R(null==(n=e.conditions)?void 0:n.utm))return!0;var t=t5.campaignParams();if(t.utm_source){var n,i,r,s,o,a,u,l,c,d,h,f,p,v,g,_,m,y=null==(i=e.conditions)||null==(r=i.utm)||!r.utm_campaign||(null==(s=e.conditions)||null==(o=s.utm)?void 0:o.utm_campaign)==t.utm_campaign,b=null==(a=e.conditions)||null==(u=a.utm)||!u.utm_source||(null==(l=e.conditions)||null==(c=l.utm)?void 0:c.utm_source)==t.utm_source,k=null==(d=e.conditions)||null==(h=d.utm)||!h.utm_medium||(null==(f=e.conditions)||null==(p=f.utm)?void 0:p.utm_medium)==t.utm_medium,w=null==(v=e.conditions)||null==(g=v.utm)||!g.utm_term||(null==(_=e.conditions)||null==(m=_.utm)?void 0:m.utm_term)==t.utm_term;return y&&k&&w&&b}return!1}},{key:"logInfo",value:function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];$.info("[WebExperiments] ".concat(e),n)}},{key:"applyTransforms",value:function(t,n,i){i.forEach(function(i){if(i.selector){e.logInfo("applying transform of variant ".concat(n," for experiment ").concat(t," "),i);var r,s=null==(r=document)?void 0:r.querySelectorAll(i.selector);null==s||s.forEach(function(e){i.attributes&&i.attributes.forEach(function(t){switch(t.name){case"text":e.innerText=t.value;break;case"html":e.innerHTML=t.value;break;case"cssClass":e.className=t.value;break;default:e.setAttribute(t.name,t.value)}}),i.text&&(e.innerText=i.text),i.html&&(e.innerHTML=i.html),i.className&&(e.className=i.className)})}})}}]),e}(),i8=function(){function e(t){var n;N(this,e),this.instance=t,this._endpointSuffix=(null==(n=this.instance.persistence)?void 0:n.props[ep])||"/e/"}return B(e,[{key:"endpoint",get:function(){return this.instance.requestRouter.endpointFor("api",this._endpointSuffix)}},{key:"afterDecideResponse",value:function(e){var t=e.autocaptureExceptions;this._endpointSuffix=S(t)&&t.endpoint||"/e/",this.instance.persistence&&this.instance.persistence.register(H({},ep,this._endpointSuffix))}},{key:"sendExceptionEvent",value:function(e){this.instance.capture("$exception",e,{_noTruncate:!0,_batchKey:"exceptionEvent",_url:this.endpoint})}}]),e}(),i4={},i7=function(){},i9="posthog",re=!ip&&-1===(null==p?void 0:p.indexOf("MSIE"))&&-1===(null==p?void 0:p.indexOf("Mozilla")),rt=function(){var e,t,n;return{api_host:"https://us.i.posthog.com",ui_host:null,token:"",autocapture:!0,rageclick:!0,cross_subdomain_cookie:!!I(n=null==(t=null==l?void 0:l.location)?void 0:t.hostname)&&"herokuapp.com"!==n.split(".").slice(-2).join("."),persistence:"localStorage+cookie",persistence_name:"",loaded:i7,store_google:!0,custom_campaign_params:[],custom_blocked_useragents:[],save_referrer:!0,capture_pageview:!0,capture_pageleave:"if_capture_pageview",debug:c&&I(null==c?void 0:c.search)&&-1!==c.search.indexOf("__posthog_debug=true")||!1,verbose:!1,cookie_expiration:365,upgrade:!1,disable_session_recording:!1,disable_persistence:!1,disable_web_experiments:!0,disable_surveys:!1,enable_recording_console_log:void 0,secure_cookie:"https:"===(null==i||null==(e=i.location)?void 0:e.protocol),ip:!0,opt_out_capturing_by_default:!1,opt_out_persistence_by_default:!1,opt_out_useragent_filter:!1,opt_out_capturing_persistence_type:"localStorage",opt_out_capturing_cookie_prefix:null,opt_in_site_apps:!1,property_denylist:[],respect_dnt:!1,sanitize_properties:null,request_headers:{},inapp_protocol:"//",inapp_link_new_window:!1,request_batching:!0,properties_string_max_length:65535,session_recording:{},mask_all_element_attributes:!1,mask_all_text:!1,advanced_disable_decide:!1,advanced_disable_feature_flags:!1,advanced_disable_feature_flags_on_first_load:!1,advanced_disable_toolbar_metrics:!1,feature_flag_request_timeout_ms:3e3,on_request_error:function(e){var t="Bad HTTP status: "+e.statusCode+" "+e.text;$.error(t)},get_device_id:function(e){return e},_onCapture:i7,capture_performance:void 0,name:"posthog",bootstrap:{},disable_compression:!1,session_idle_timeout_seconds:1800,person_profiles:"always",__add_tracing_headers:!1}},rn=function(e){var t={};x(e.process_person)||(t.person_profiles=e.process_person),x(e.xhr_headers)||(t.request_headers=e.xhr_headers),x(e.cookie_name)||(t.persistence_name=e.cookie_name),x(e.disable_cookie)||(t.disable_persistence=e.disable_cookie);var n=X({},t,e);return k(e.property_blacklist)&&(x(e.property_denylist)?n.property_denylist=e.property_blacklist:k(e.property_denylist)?n.property_denylist=[].concat(j(e.property_blacklist),j(e.property_denylist)):$.error("Invalid value for property_denylist config: "+e.property_denylist)),n},ri=function(){function e(){N(this,e),H(this,"__forceAllowLocalhost",!1)}return B(e,[{key:"_forceAllowLocalhost",get:function(){return this.__forceAllowLocalhost},set:function(e){$.error("WebPerformanceObserver is deprecated and has no impact on network capture. Use `_forceAllowLocalhostNetworkCapture` on `posthog.sessionRecording`"),this.__forceAllowLocalhost=e}}]),e}(),rr=function(){function e(){var t=this;N(this,e),H(this,"webPerformance",new ri),H(this,"version",g.LIB_VERSION),H(this,"_internalEventEmitter",new i$),this.config=rt(),this.decideEndpointWasHit=!1,this.SentryIntegration=iT,this.sentryIntegration=function(e){var n;return n=iI(t,e),{name:ix,processEvent:function(e){return n(e)}}},this.__request_queue=[],this.__loaded=!1,this.analyticsDefaultEndpoint="/e/",this._initialPageviewCaptured=!1,this.featureFlags=new ez(this),this.toolbar=new nH(this),this.scrollManager=new iG(this),this.pageViewManager=new iC(this),this.surveys=new iN(this),this.experiments=new i6(this),this.exceptions=new i8(this),this.rateLimiter=new iq(this),this.requestRouter=new iE(this),this.consent=new iZ(this),this.people={set:function(e,n,i){var r=I(e)?H({},e,n):e;t.setPersonProperties(r),null==i||i({})},set_once:function(e,n,i){var r=I(e)?H({},e,n):e;t.setPersonProperties(void 0,r),null==i||i({})}},this.on("eventCaptured",function(e){return $.info("send",e)})}return B(e,[{key:"init",value:function(t,n,i){if(i&&i!==i9){var r,s=null!=(r=i4[i])?r:new e;return s._init(t,n,i),i4[i]=s,i4[i9][i]=s,s}return this._init(t,n,i)}},{key:"_init",value:function(e){var t,n,r=this,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=arguments.length>2?arguments[2]:void 0;if(x(e)||F(e))return $.critical("PostHog was initialized without a token. This likely indicates a misconfiguration. Please check the first argument passed to posthog.init()"),this;if(this.__loaded)return $.warn("You have already initialized PostHog! Re-initializing is a no-op"),this;this.__loaded=!0,this.config={},this._triggered_notifs=[],this.set_config(X({},rt(),rn(s),{name:o,token:e})),this.config.on_xhr_error&&$.error("[posthog] on_xhr_error is deprecated. Use on_request_error instead"),this.compression=s.disable_compression?void 0:ee.GZipJS,this.persistence=new t8(this.config),this.sessionPersistence="sessionStorage"===this.config.persistence?this.persistence:new t8(L(L({},this.config),{},{persistence:"sessionStorage"}));var a=L({},this.persistence.props),u=L({},this.sessionPersistence.props);if(this._requestQueue=new nU(function(e){return r._send_retriable_request(e)}),this._retryQueue=new ik(this),this.__request_queue=[],this.sessionManager=new iw(this.config,this.persistence),this.sessionPropsManager=new iH(this.sessionManager,this.persistence),new iK(this).startIfEnabledOrStop(),this.sessionRecording=new nD(this),this.sessionRecording.startIfEnabledOrStop(),this.config.disable_scroll_properties||this.scrollManager.startMeasuringScrollPosition(),this.autocapture=new iX(this),this.autocapture.startIfEnabled(),this.surveys.loadIfEnabled(),this.heatmaps=new iV(this),this.heatmaps.startIfEnabled(),this.webVitalsAutocapture=new i3(this),this.exceptionObserver=new i1(this),this.exceptionObserver.startIfEnabled(),g.DEBUG=g.DEBUG||this.config.debug,g.DEBUG&&$.info("Starting in debug mode",{this:this,config:s,thisC:L({},this.config),p:a,s:u}),this._sync_opt_out_with_persistence(),void 0!==(null==(t=s.bootstrap)?void 0:t.distinctID)){var l,c,d=this.config.get_device_id(eZ()),h=null!=(l=s.bootstrap)&&l.isIdentifiedID?d:s.bootstrap.distinctID;this.persistence.set_property(eO,null!=(c=s.bootstrap)&&c.isIdentifiedID?"identified":"anonymous"),this.register({distinct_id:s.bootstrap.distinctID,$device_id:h})}if(this._hasBootstrappedFeatureFlags()){var f,p,v=Object.keys((null==(f=s.bootstrap)?void 0:f.featureFlags)||{}).filter(function(e){var t,n;return!(null==(t=s.bootstrap)||null==(n=t.featureFlags)||!n[e])}).reduce(function(e,t){var n,i;return e[t]=(null==(n=s.bootstrap)||null==(i=n.featureFlags)?void 0:i[t])||!1,e},{}),_=Object.keys((null==(p=s.bootstrap)?void 0:p.featureFlagPayloads)||{}).filter(function(e){return v[e]}).reduce(function(e,t){var n,i,r,o;return null!=(n=s.bootstrap)&&null!=(i=n.featureFlagPayloads)&&i[t]&&(e[t]=null==(r=s.bootstrap)||null==(o=r.featureFlagPayloads)?void 0:o[t]),e},{});this.featureFlags.receivedFeatureFlags({featureFlags:v,featureFlagPayloads:_})}if(!this.get_distinct_id()){var m=this.config.get_device_id(eZ());this.register_once({distinct_id:m,$device_id:m},""),this.persistence.set_property(eO,"anonymous")}return null==i||null==(n=i.addEventListener)||n.call(i,"onpagehide"in self?"pagehide":"unload",this._handle_unload.bind(this)),this.toolbar.maybeLoadToolbar(),s.segment?function(e,t){var n=e.config.segment;if(!n)return t();!function(e,t){var n=e.config.segment;if(!n)return t();var i=function(n){var i=function(){return n.anonymousId()||eZ()};e.config.get_device_id=i,n.id()&&(e.register({distinct_id:n.id(),$device_id:i()}),e.persistence.set_property(eO,"identified")),t()},r=n.user();"then"in r&&w(r.then)?r.then(function(e){return i(e)}):i(r)}(e,function(){var i;n.register((Promise&&Promise.resolve||$.warn("This browser does not have Promise support, and can not use the segment integration"),i=function(t,n){if(!n)return t;t.event.userId||t.event.anonymousId===e.get_distinct_id()||($.info("Segment integration does not have a userId set, resetting PostHog"),e.reset()),t.event.userId&&t.event.userId!==e.get_distinct_id()&&($.info("Segment integration has a userId set, identifying with PostHog"),e.identify(t.event.userId));var i,r=e._calculate_event_properties(n,null!=(i=t.event.properties)?i:{},new Date);return t.event.properties=Object.assign({},r,t.event.properties),t},{name:"PostHog JS",type:"enrichment",version:"1.0.0",isLoaded:function(){return!0},load:function(){return Promise.resolve()},track:function(e){return i(e,e.event.event)},page:function(e){return i(e,"$pageview")},identify:function(e){return i(e,"$identify")},screen:function(e){return i(e,"$screen")}})).then(function(){t()})})}(this,function(){return r._loaded()}):this._loaded(),w(this.config._onCapture)&&this.on("eventCaptured",function(e){return r.config._onCapture(e.event,e)}),this}},{key:"_afterDecideResponse",value:function(e){var t,n,i,r,s,o,a,u,l;this.compression=void 0,e.supportedCompression&&!this.config.disable_compression&&(this.compression=K(e.supportedCompression,ee.GZipJS)?ee.GZipJS:K(e.supportedCompression,ee.Base64)?ee.Base64:void 0),null!=(t=e.analytics)&&t.endpoint&&(this.analyticsDefaultEndpoint=e.analytics.endpoint),null==(n=this.sessionRecording)||n.afterDecideResponse(e),null==(i=this.autocapture)||i.afterDecideResponse(e),null==(r=this.heatmaps)||r.afterDecideResponse(e),null==(s=this.experiments)||s.afterDecideResponse(e),null==(o=this.surveys)||o.afterDecideResponse(e),null==(a=this.webVitalsAutocapture)||a.afterDecideResponse(e),null==(u=this.exceptions)||u.afterDecideResponse(e),null==(l=this.exceptionObserver)||l.afterDecideResponse(e)}},{key:"_loaded",value:function(){var e=this,t=this.config.advanced_disable_decide;t||this.featureFlags.setReloadingPaused(!0);try{this.config.loaded(this)}catch(e){$.critical("`loaded` function failed",e)}this._start_queue_if_opted_in(),this.config.capture_pageview&&setTimeout(function(){e.consent.isOptedIn()&&e._captureInitialPageview()},1),t||(new nN(this).call(),this.featureFlags.resetRequestQueue())}},{key:"_start_queue_if_opted_in",value:function(){var e;this.has_opted_out_capturing()||this.config.request_batching&&(null==(e=this._requestQueue)||e.enable())}},{key:"_dom_loaded",value:function(){var e=this;this.has_opted_out_capturing()||J(this.__request_queue,function(t){return e._send_retriable_request(t)}),this.__request_queue=[],this._start_queue_if_opted_in()}},{key:"_handle_unload",value:function(){var e,t;this.config.request_batching?(this._shouldCapturePageleave()&&this.capture("$pageleave"),null==(e=this._requestQueue)||e.unload(),null==(t=this._retryQueue)||t.unload()):this._shouldCapturePageleave()&&this.capture("$pageleave",null,{transport:"sendBeacon"})}},{key:"_send_request",value:function(e){var t=this;this.__loaded&&(re?this.__request_queue.push(e):this.rateLimiter.isServerRateLimited(e.batchKey)||(e.transport=e.transport||this.config.api_transport,e.url=ig(e.url,{ip:+!!this.config.ip}),e.headers=L({},this.config.request_headers),e.compression="best-available"===e.compression?this.compression:e.compression,function(e){var t,n,i,r=L({},e);r.timeout=r.timeout||6e4,r.url=ig(r.url,{_:(new Date).getTime().toString(),ver:g.LIB_VERSION,compression:r.compression});var s=null!=(t=r.transport)?t:"XHR",o=null!=(n=null==(i=ea(im,function(e){return e.transport===s}))?void 0:i.method)?n:im[0].method;if(!o)throw Error("No available transport method");o(r)}(L(L({},e),{},{callback:function(n){var i,r,s;t.rateLimiter.checkForLimiting(n),n.statusCode>=400&&(null==(r=(s=t.config).on_request_error)||r.call(s,n)),null==(i=e.callback)||i.call(e,n)}}))))}},{key:"_send_retriable_request",value:function(e){this._retryQueue?this._retryQueue.retriableRequest(e):this._send_request(e)}},{key:"_execute_array",value:function(e){var t,n=this,i=[],r=[],s=[];J(e,function(e){e&&(k(t=e[0])?s.push(e):w(e)?e.call(n):k(e)&&"alias"===t?i.push(e):k(e)&&-1!==t.indexOf("capture")&&w(n[t])?s.push(e):r.push(e))});var o=function(e,t){J(e,function(e){if(k(e[0])){var n=t;Y(e,function(e){n=n[e[0]].apply(n,e.slice(1))})}else this[e[0]].apply(this,e.slice(1))},t)};o(i,this),o(r,this),o(s,this)}},{key:"_hasBootstrappedFeatureFlags",value:function(){var e,t;return(null==(e=this.config.bootstrap)?void 0:e.featureFlags)&&Object.keys(null==(t=this.config.bootstrap)?void 0:t.featureFlags).length>0||!1}},{key:"push",value:function(e){this._execute_array([e])}},{key:"capture",value:function(e,t,n){var i;if(this.__loaded&&this.persistence&&this.sessionPersistence&&this._requestQueue){if(!this.consent.isOptedOut())if(!x(e)&&I(e)){if(this.config.opt_out_useragent_filter||!this._is_bot()){var r=null!=n&&n.skip_client_rate_limiting?void 0:this.rateLimiter.clientRateLimitContext();if(null==r||!r.isRateLimited){this.sessionPersistence.update_search_keyword(),this.config.store_google&&this.sessionPersistence.update_campaign_params(),this.config.save_referrer&&this.sessionPersistence.update_referrer_info(),(this.config.store_google||this.config.save_referrer)&&this.persistence.set_initial_person_info();var s,o,a,u,l=new Date,c=(null==n?void 0:n.timestamp)||l,d={uuid:eZ(),event:e,properties:this._calculate_event_properties(e,t||{},c)};r&&(d.properties.$lib_rate_limit_remaining_tokens=r.remainingTokens),(null==n?void 0:n.$set)&&(d.$set=null==n?void 0:n.$set);var h=this._calculate_set_once_properties(null==n?void 0:n.$set_once);h&&(d.$set_once=h),(s=d,o=null!=n&&n._noTruncate?null:this.config.properties_string_max_length,a=function(e){return I(e)&&!P(o)?e.slice(0,o):e},u=new Set,d=function e(t,n){var i;return t!==Object(t)?a?a(t,n):t:u.has(t)?void 0:(u.add(t),k(t)?(i=[],J(t,function(t){i.push(e(t))})):(i={},Y(t,function(t,n){u.has(t)||(i[n]=e(t,n))})),i)}(s)).timestamp=c,x(null==n?void 0:n.timestamp)||(d.properties.$event_time_override_provided=!0,d.properties.$event_time_override_system_time=l);var f=L(L({},d.properties.$set),d.$set);E(f)||this.setPersonPropertiesForFlags(f),this._internalEventEmitter.emit("eventCaptured",d);var p={method:"POST",url:null!=(i=null==n?void 0:n._url)?i:this.requestRouter.endpointFor("api",this.analyticsDefaultEndpoint),data:d,compression:"best-available",batchKey:null==n?void 0:n._batchKey};return!this.config.request_batching||n&&(null==n||!n._batchKey)||null!=n&&n.send_instantly?this._send_retriable_request(p):this._requestQueue.enqueue(p),d}$.critical("This capture call is ignored due to client rate limiting.")}}else $.error("No event name provided to posthog.capture")}else $.uninitializedWarning("posthog.capture")}},{key:"_addCaptureHook",value:function(e){return this.on("eventCaptured",function(t){return e(t.event,t)})}},{key:"_calculate_event_properties",value:function(e,t,n){if(n=n||new Date,!this.persistence||!this.sessionPersistence)return t;var i=this.persistence.remove_event_timer(e),r=L({},t);if(r.token=this.config.token,"$snapshot"===e){var s=L(L({},this.persistence.properties()),this.sessionPersistence.properties());return r.distinct_id=s.distinct_id,(!I(r.distinct_id)&&!T(r.distinct_id)||F(r.distinct_id))&&$.error("Invalid distinct_id for replay event. This indicates a bug in your implementation"),r}var o=t5.properties();if(this.sessionManager){var a=this.sessionManager.checkAndGetSessionAndWindowId(),u=a.sessionId,c=a.windowId;r.$session_id=u,r.$window_id=c}if(this.requestRouter.region===iy.CUSTOM&&(r.$lib_custom_api_host=this.config.api_host),this.sessionPropsManager&&this.config.__preview_send_client_session_params&&("$pageview"===e||"$pageleave"===e||"$autocapture"===e)){var d=this.sessionPropsManager.getSessionProps();r=X(r,d)}if(!this.config.disable_scroll_properties){var h={};"$pageview"===e?h=this.pageViewManager.doPageView(n):"$pageleave"===e&&(h=this.pageViewManager.doPageLeave(n)),r=X(r,h)}if("$pageview"===e&&l&&(r.title=l.title),!x(i)){var f=n.getTime()-i;r.$duration=parseFloat((f/1e3).toFixed(3))}p&&this.config.opt_out_useragent_filter&&(r.$browser_type=this._is_bot()?"bot":"browser"),(r=X({},o,this.persistence.properties(),this.sessionPersistence.properties(),r)).$is_identified=this._isIdentified(),k(this.config.property_denylist)?Y(this.config.property_denylist,function(e){delete r[e]}):$.error("Invalid value for property_denylist config: "+this.config.property_denylist+" or property_blacklist config: "+this.config.property_blacklist);var v=this.config.sanitize_properties;return v&&(r=v(r,e)),r.$process_person_profile=this._hasPersonProcessing(),r}},{key:"_calculate_set_once_properties",value:function(e){if(!this.persistence||!this._hasPersonProcessing())return e;var t=X({},this.persistence.get_initial_props(),e||{});return E(t)?void 0:t}},{key:"register",value:function(e,t){var n;null==(n=this.persistence)||n.register(e,t)}},{key:"register_once",value:function(e,t,n){var i;null==(i=this.persistence)||i.register_once(e,t,n)}},{key:"register_for_session",value:function(e){var t;null==(t=this.sessionPersistence)||t.register(e)}},{key:"unregister",value:function(e){var t;null==(t=this.persistence)||t.unregister(e)}},{key:"unregister_for_session",value:function(e){var t;null==(t=this.sessionPersistence)||t.unregister(e)}},{key:"_register_single",value:function(e,t){this.register(H({},e,t))}},{key:"getFeatureFlag",value:function(e,t){return this.featureFlags.getFeatureFlag(e,t)}},{key:"getFeatureFlagPayload",value:function(e){var t=this.featureFlags.getFeatureFlagPayload(e);try{return JSON.parse(t)}catch(e){return t}}},{key:"isFeatureEnabled",value:function(e,t){return this.featureFlags.isFeatureEnabled(e,t)}},{key:"reloadFeatureFlags",value:function(){this.featureFlags.reloadFeatureFlags()}},{key:"updateEarlyAccessFeatureEnrollment",value:function(e,t){this.featureFlags.updateEarlyAccessFeatureEnrollment(e,t)}},{key:"getEarlyAccessFeatures",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this.featureFlags.getEarlyAccessFeatures(e,t)}},{key:"on",value:function(e,t){return this._internalEventEmitter.on(e,t)}},{key:"onFeatureFlags",value:function(e){return this.featureFlags.onFeatureFlags(e)}},{key:"onSessionId",value:function(e){var t,n;return null!=(t=null==(n=this.sessionManager)?void 0:n.onSessionId(e))?t:function(){}}},{key:"getSurveys",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.surveys.getSurveys(e,t)}},{key:"getActiveMatchingSurveys",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.surveys.getActiveMatchingSurveys(e,t)}},{key:"renderSurvey",value:function(e,t){this.surveys.renderSurvey(e,t)}},{key:"canRenderSurvey",value:function(e){this.surveys.canRenderSurvey(e)}},{key:"getNextSurveyStep",value:function(e,t,n){return this.surveys.getNextSurveyStep(e,t,n)}},{key:"identify",value:function(e,t,n){if(!this.__loaded||!this.persistence)return $.uninitializedWarning("posthog.identify");if(T(e)&&(e=e.toString(),$.warn("The first argument to posthog.identify was a number, but it should be a string. It has been converted to a string.")),e){if(["distinct_id","distinctid"].includes(e.toLowerCase()))$.critical('The string "'.concat(e,'" was set in posthog.identify which indicates an error. This ID should be unique to the user and not a hardcoded string.'));else if(this._requirePersonProcessing("posthog.identify")){var i=this.get_distinct_id();this.register({$user_id:e}),this.get_property("$device_id")||this.register_once({$had_persisted_distinct_id:!0,$device_id:i},""),e!==i&&e!==this.get_property(el)&&(this.unregister(el),this.register({distinct_id:e}));var r="anonymous"===(this.persistence.get_property(eO)||"anonymous");e!==i&&r?(this.persistence.set_property(eO,"identified"),this.setPersonPropertiesForFlags(t||{},!1),this.capture("$identify",{distinct_id:e,$anon_distinct_id:i},{$set:t||{},$set_once:n||{}}),this.featureFlags.setAnonymousDistinctId(i)):(t||n)&&this.setPersonProperties(t,n),e!==i&&(this.reloadFeatureFlags(),this.unregister(eC))}}else $.error("Unique user id has not been set in posthog.identify")}},{key:"setPersonProperties",value:function(e,t){(e||t)&&this._requirePersonProcessing("posthog.setPersonProperties")&&(this.setPersonPropertiesForFlags(e||{}),this.capture("$set",{$set:e||{},$set_once:t||{}}))}},{key:"group",value:function(e,t,n){if(e&&t){if(this._requirePersonProcessing("posthog.group")){var i=this.getGroups();i[e]!==t&&this.resetGroupPropertiesForFlags(e),this.register({$groups:L(L({},i),{},H({},e,t))}),n&&(this.capture("$groupidentify",{$group_type:e,$group_key:t,$group_set:n}),this.setGroupPropertiesForFlags(H({},e,n))),i[e]===t||n||this.reloadFeatureFlags()}}else $.error("posthog.group requires a group type and group key")}},{key:"resetGroups",value:function(){this.register({$groups:{}}),this.resetGroupPropertiesForFlags(),this.reloadFeatureFlags()}},{key:"setPersonPropertiesForFlags",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this._requirePersonProcessing("posthog.setPersonPropertiesForFlags")&&this.featureFlags.setPersonPropertiesForFlags(e,t)}},{key:"resetPersonPropertiesForFlags",value:function(){this.featureFlags.resetPersonPropertiesForFlags()}},{key:"setGroupPropertiesForFlags",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this._requirePersonProcessing("posthog.setGroupPropertiesForFlags")&&this.featureFlags.setGroupPropertiesForFlags(e,t)}},{key:"resetGroupPropertiesForFlags",value:function(e){this.featureFlags.resetGroupPropertiesForFlags(e)}},{key:"reset",value:function(e){if($.info("reset"),!this.__loaded)return $.uninitializedWarning("posthog.reset");var t,n,i,r,s=this.get_property("$device_id");this.consent.reset(),null==(t=this.persistence)||t.clear(),null==(n=this.sessionPersistence)||n.clear(),null==(i=this.persistence)||i.set_property(eO,"anonymous"),null==(r=this.sessionManager)||r.resetSessionId();var o=this.config.get_device_id(eZ());this.register_once({distinct_id:o,$device_id:e?o:s},"")}},{key:"get_distinct_id",value:function(){return this.get_property("distinct_id")}},{key:"getGroups",value:function(){return this.get_property("$groups")||{}}},{key:"get_session_id",value:function(){var e,t;return null!=(e=null==(t=this.sessionManager)?void 0:t.checkAndGetSessionAndWindowId(!0).sessionId)?e:""}},{key:"get_session_replay_url",value:function(e){if(!this.sessionManager)return"";var t=this.sessionManager.checkAndGetSessionAndWindowId(!0),n=t.sessionId,i=t.sessionStartTimestamp,r=this.requestRouter.endpointFor("ui","/project/".concat(this.config.token,"/replay/").concat(n));if(null!=e&&e.withTimestamp&&i){var s,o=null!=(s=e.timestampLookBack)?s:10;if(!i)return r;var a=Math.max(Math.floor(((new Date).getTime()-i)/1e3)-o,0);r+="?t=".concat(a)}return r}},{key:"alias",value:function(e,t){return e===this.get_property(eu)?($.critical("Attempting to create alias for existing People user - aborting."),-2):this._requirePersonProcessing("posthog.alias")?(x(t)&&(t=this.get_distinct_id()),e!==t?(this._register_single(el,e),this.capture("$create_alias",{alias:e,distinct_id:t})):($.warn("alias matches current distinct_id - skipping api call."),this.identify(e),-1)):void 0}},{key:"set_config",value:function(e){var t,n,i,r,s=L({},this.config);S(e)&&(X(this.config,rn(e)),null==(t=this.persistence)||t.update_config(this.config,s),this.sessionPersistence="sessionStorage"===this.config.persistence?this.persistence:new t8(L(L({},this.config),{},{persistence:"sessionStorage"})),e6.is_supported()&&"true"===e6.get("ph_debug")&&(this.config.debug=!0),this.config.debug&&(g.DEBUG=!0,$.info("set_config",{config:e,oldConfig:s,newConfig:L({},this.config)})),null==(n=this.sessionRecording)||n.startIfEnabledOrStop(),null==(i=this.autocapture)||i.startIfEnabled(),null==(r=this.heatmaps)||r.startIfEnabled(),this.surveys.loadIfEnabled(),this._sync_opt_out_with_persistence())}},{key:"startSessionRecording",value:function(e){var t,n=C(e)&&e;if(n||null!=e&&e.sampling){var i,r,s=null==(i=this.sessionManager)?void 0:i.checkAndGetSessionAndWindowId();null==(r=this.persistence)||r.register(H({},eE,!0)),$.info("Session recording started with sampling override for session: ",null==s?void 0:s.sessionId)}(n||null!=e&&e.linked_flag)&&(null==(t=this.sessionRecording)||t.overrideLinkedFlag(),$.info("Session recording started with linked_flags override")),this.set_config({disable_session_recording:!1})}},{key:"stopSessionRecording",value:function(){this.set_config({disable_session_recording:!0})}},{key:"sessionRecordingStarted",value:function(){var e;return!(null==(e=this.sessionRecording)||!e.started)}},{key:"captureException",value:function(e,t){var n,i=w(null==(n=v.__PosthogExtensions__)?void 0:n.parseErrorAsProperties)?v.__PosthogExtensions__.parseErrorAsProperties([e.message,void 0,void 0,void 0,e]):L({$exception_type:e.name,$exception_message:e.message,$exception_level:"error"},t);this.exceptions.sendExceptionEvent(i)}},{key:"loadToolbar",value:function(e){return this.toolbar.loadToolbar(e)}},{key:"get_property",value:function(e){var t;return null==(t=this.persistence)?void 0:t.props[e]}},{key:"getSessionProperty",value:function(e){var t;return null==(t=this.sessionPersistence)?void 0:t.props[e]}},{key:"toString",value:function(){var e,t=null!=(e=this.config.name)?e:i9;return t!==i9&&(t=i9+"."+t),t}},{key:"_isIdentified",value:function(){var e,t;return"identified"===(null==(e=this.persistence)?void 0:e.get_property(eO))||"identified"===(null==(t=this.sessionPersistence)?void 0:t.get_property(eO))}},{key:"_hasPersonProcessing",value:function(){var e,t,n,i;return!("never"===this.config.person_profiles||"identified_only"===this.config.person_profiles&&!this._isIdentified()&&E(this.getGroups())&&(null==(e=this.persistence)||null==(t=e.props)||!t[el])&&(null==(n=this.persistence)||null==(i=n.props)||!i[eN]))}},{key:"_shouldCapturePageleave",value:function(){return!0===this.config.capture_pageleave||"if_capture_pageview"===this.config.capture_pageleave&&this.config.capture_pageview}},{key:"createPersonProfile",value:function(){this._hasPersonProcessing()||this._requirePersonProcessing("posthog.createPersonProfile")&&this.setPersonProperties({},{})}},{key:"_requirePersonProcessing",value:function(e){return"never"===this.config.person_profiles?($.error(e+' was called, but process_person is set to "never". This call will be ignored.'),!1):(this._register_single(eN,!0),!0)}},{key:"_sync_opt_out_with_persistence",value:function(){var e,t,n,i,r=this.consent.isOptedOut(),s=this.config.opt_out_persistence_by_default,o=this.config.disable_persistence||r&&!!s;(null==(e=this.persistence)?void 0:e.disabled)!==o&&(null==(n=this.persistence)||n.set_disabled(o)),(null==(t=this.sessionPersistence)?void 0:t.disabled)!==o&&(null==(i=this.sessionPersistence)||i.set_disabled(o))}},{key:"opt_in_capturing",value:function(e){var t;this.consent.optInOut(!0),this._sync_opt_out_with_persistence(),(x(null==e?void 0:e.captureEventName)||null!=e&&e.captureEventName)&&this.capture(null!=(t=null==e?void 0:e.captureEventName)?t:"$opt_in",null==e?void 0:e.captureProperties,{send_instantly:!0}),this.config.capture_pageview&&this._captureInitialPageview()}},{key:"opt_out_capturing",value:function(){this.consent.optInOut(!1),this._sync_opt_out_with_persistence()}},{key:"has_opted_in_capturing",value:function(){return this.consent.isOptedIn()}},{key:"has_opted_out_capturing",value:function(){return this.consent.isOptedOut()}},{key:"clear_opt_in_out_capturing",value:function(){this.consent.reset(),this._sync_opt_out_with_persistence()}},{key:"_is_bot",value:function(){return u?function(e,t){if(!e)return!1;var n=e.userAgent;if(n&&ij(n,t))return!0;try{var i=null==e?void 0:e.userAgentData;if(null!=i&&i.brands&&i.brands.some(function(e){return ij(null==e?void 0:e.brand,t)}))return!0}catch(e){}return!!e.webdriver}(u,this.config.custom_blocked_useragents):void 0}},{key:"_captureInitialPageview",value:function(){l&&!this._initialPageviewCaptured&&(this._initialPageviewCaptured=!0,this.capture("$pageview",{title:l.title},{send_instantly:!0}))}},{key:"debug",value:function(e){!1===e?(null==i||i.console.log("You've disabled debug mode."),localStorage&&localStorage.removeItem("ph_debug"),this.set_config({debug:!1})):(null==i||i.console.log("You're now in debug mode. All calls to PostHog will be logged in your console.\nYou can disable this with `posthog.debug(false)`."),localStorage&&localStorage.setItem("ph_debug","true"),this.set_config({debug:!0}))}}]),e}();!function(e,t){for(var n=0;n<t.length;n++)e.prototype[t[n]]=en(e.prototype[t[n]])}(rr,["identify"]);var rs,ro=(rs=i4[i9]=new rr,function(){function e(){e.done||(e.done=!0,re=!1,Y(i4,function(e){e._dom_loaded()}))}null!=l&&l.addEventListener&&("complete"===l.readyState?e():l.addEventListener("DOMContentLoaded",e,!1)),i&&eo(i,"load",e,!0)}(),rs)}}]);