!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="1d40ad54-273b-4a1a-9f70-e821dd2b5ecb",e._sentryDebugIdIdentifier="sentry-dbid-1d40ad54-273b-4a1a-9f70-e821dd2b5ecb")}catch(e){}}();"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1725],{20135:(e,t,n)=>{n.d(t,{Zx:()=>H,bv:()=>M,vW:()=>E,we:()=>w});var r,l=n(7620),u=n(73466),o=n(85762),c=n(4548),f=n(97509),i=n(53093),s=n(50879);let a={...r||(r=n.t(l,2))},d=a.useInsertionEffect||(e=>e());function g(e){let t=l.useRef(()=>{});return d(()=>{t.current=e}),l.useCallback(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}var m="undefined"!=typeof document?l.useLayoutEffect:l.useEffect;let p=!1,v=0,h=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+v++,b=a.useId||function(){let[e,t]=l.useState(()=>p?h():void 0);return m(()=>{null==e&&t(h())},[]),l.useEffect(()=>{p=!0},[]),e},y=l.createContext(null),C=l.createContext(null);function w(e){void 0===e&&(e={});let{nodeId:t}=e,n=function(e){var t;let{open:n=!1,onOpenChange:r,elements:u}=e,o=b(),c=l.useRef({}),[f]=l.useState(()=>(function(){let e=new Map;return{emit(t,n){var r;null==(r=e.get(t))||r.forEach(e=>e(n))},on(t,n){e.set(t,[...e.get(t)||[],n])},off(t,n){var r;e.set(t,(null==(r=e.get(t))?void 0:r.filter(e=>e!==n))||[])}}})()),i=null!=((null==(t=l.useContext(y))?void 0:t.id)||null),[s,a]=l.useState(u.reference),d=g((e,t,n)=>{c.current.openEvent=e?t:void 0,f.emit("openchange",{open:e,event:t,reason:n,nested:i}),null==r||r(e,t,n)}),m=l.useMemo(()=>({setPositionReference:a}),[]),p=l.useMemo(()=>({reference:s||u.reference||null,floating:u.floating||null,domReference:u.reference}),[s,u.reference,u.floating]);return l.useMemo(()=>({dataRef:c,open:n,onOpenChange:d,elements:p,events:f,floatingId:o,refs:m}),[n,d,p,f,o,m])}({...e,elements:{reference:null,floating:null,...e.elements}}),r=e.rootContext||n,u=r.elements,[o,f]=l.useState(null),[s,a]=l.useState(null),d=(null==u?void 0:u.domReference)||o,p=l.useRef(null),v=l.useContext(C);m(()=>{d&&(p.current=d)},[d]);let h=(0,i.we)({...e,elements:{...u,...s&&{reference:s}}}),w=l.useCallback(e=>{let t=(0,c.vq)(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),contextElement:e}:e;a(t),h.refs.setReference(t)},[h.refs]),x=l.useCallback(e=>{((0,c.vq)(e)||null===e)&&(p.current=e,f(e)),((0,c.vq)(h.refs.reference.current)||null===h.refs.reference.current||null!==e&&!(0,c.vq)(e))&&h.refs.setReference(e)},[h.refs]),R=l.useMemo(()=>({...h.refs,setReference:x,setPositionReference:w,domReference:p}),[h.refs,x,w]),T=l.useMemo(()=>({...h.elements,domReference:d}),[h.elements,d]),M=l.useMemo(()=>({...h,...r,refs:R,elements:T,nodeId:t}),[h,R,T,t,r]);return m(()=>{r.dataRef.current.floatingContext=M;let e=null==v?void 0:v.nodesRef.current.find(e=>e.id===t);e&&(e.context=M)}),l.useMemo(()=>({...h,context:M,refs:R,elements:T}),[h,R,T,M])}let x="active",R="selected";function T(e,t,n){let r=new Map,l="item"===n,u=e;if(l&&e){let{[x]:t,[R]:n,...r}=e;u=r}return{..."floating"===n&&{tabIndex:-1,"data-floating-ui-focusable":""},...u,...t.map(t=>{let r=t?t[n]:null;return"function"==typeof r?e?r(e):null:r}).concat(e).reduce((e,t)=>(t&&Object.entries(t).forEach(t=>{let[n,u]=t;if(!(l&&[x,R].includes(n)))if(0===n.indexOf("on")){if(r.has(n)||r.set(n,[]),"function"==typeof u){var o;null==(o=r.get(n))||o.push(u),e[n]=function(){for(var e,t=arguments.length,l=Array(t),u=0;u<t;u++)l[u]=arguments[u];return null==(e=r.get(n))?void 0:e.map(e=>e(...l)).find(e=>void 0!==e)}}}else e[n]=u}),e),{})}}function M(e){void 0===e&&(e=[]);let t=e.map(e=>null==e?void 0:e.reference),n=e.map(e=>null==e?void 0:e.floating),r=e.map(e=>null==e?void 0:e.item),u=l.useCallback(t=>T(t,e,"reference"),t),o=l.useCallback(t=>T(t,e,"floating"),n),c=l.useCallback(t=>T(t,e,"item"),r);return l.useMemo(()=>({getReferenceProps:u,getFloatingProps:o,getItemProps:c}),[u,o,c])}function _(e,t){return{...e,rects:{...e.rects,floating:{...e.rects.floating,height:t}}}}let E=e=>({name:"inner",options:e,async fn(t){let{listRef:n,overflowRef:r,onFallbackChange:l,offset:u=0,index:c=0,minItemsVisible:a=4,referenceOverflowThreshold:d=0,scrollRef:g,...m}=(0,o._3)(e,t),{rects:p,elements:{floating:v}}=t,h=n.current[c],b=(null==g?void 0:g.current)||v,y=v.clientTop||b.clientTop,C=0!==v.clientTop,w=0!==b.clientTop,x=v===b;if(!h)return{};let R={...t,...await (0,i.cY)(-h.offsetTop-v.clientTop-p.reference.height/2-h.offsetHeight/2-u).fn(t)},T=await (0,s.__)(_(R,b.scrollHeight+y+v.clientTop),m),M=await (0,s.__)(R,{...m,elementContext:"reference"}),E=(0,o.T9)(0,T.top),H=R.y+E,k=(b.scrollHeight>b.clientHeight?e=>e:o.LI)((0,o.T9)(0,b.scrollHeight+(C&&x||w?2*y:0)-E-(0,o.T9)(0,T.bottom)));if(b.style.maxHeight=k+"px",b.scrollTop=E,l){let e=b.offsetHeight<h.offsetHeight*(0,o.jk)(a,n.current.length)-1||M.top>=-d||M.bottom>=-d;f.flushSync(()=>l(e))}return r&&(r.current=await (0,s.__)(_({...R,y:H},b.offsetHeight+y+v.clientTop),m)),{y:H}}});function H(e,t){let{open:n,elements:r}=e,{enabled:o=!0,overflowRef:c,scrollRef:i,onChange:s}=t,a=g(s),d=l.useRef(!1),m=l.useRef(null),p=l.useRef(null);l.useEffect(()=>{if(!o)return;function e(e){if(e.ctrlKey||!t||null==c.current)return;let n=e.deltaY,r=c.current.top>=-.5,l=c.current.bottom>=-.5,o=t.scrollHeight-t.clientHeight,i=n<0?-1:1,s=n<0?"max":"min";!(t.scrollHeight<=t.clientHeight)&&(!r&&n>0||!l&&n<0?(e.preventDefault(),f.flushSync(()=>{a(e=>e+Math[s](n,o*i))})):/firefox/i.test((0,u.$t)())&&(t.scrollTop+=n))}let t=(null==i?void 0:i.current)||r.floating;if(n&&t)return t.addEventListener("wheel",e),requestAnimationFrame(()=>{m.current=t.scrollTop,null!=c.current&&(p.current={...c.current})}),()=>{m.current=null,p.current=null,t.removeEventListener("wheel",e)}},[o,n,r.floating,c,i,a]);let v=l.useMemo(()=>({onKeyDown(){d.current=!0},onWheel(){d.current=!1},onPointerMove(){d.current=!1},onScroll(){let e=(null==i?void 0:i.current)||r.floating;if(c.current&&e&&d.current){if(null!==m.current){let t=e.scrollTop-m.current;(c.current.bottom<-.5&&t<-1||c.current.top<-.5&&t>1)&&f.flushSync(()=>a(e=>e+t))}requestAnimationFrame(()=>{m.current=e.scrollTop})}}}),[r.floating,a,c,i]);return l.useMemo(()=>o?{floating:v}:{},[o,v])}}}]);