!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="db47c8c3-9e8d-42c9-8546-051911b4ed38",e._sentryDebugIdIdentifier="sentry-dbid-db47c8c3-9e8d-42c9-8546-051911b4ed38")}catch(e){}}(),(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1350],{7744:(e,t,r)=>{"use strict";r.d(t,{NavContainer:()=>c});var s=r(54568),n=r(7620),i=r(93457),a=r(80976),o=r(31467),l=r(16600);function c({children:e}){let{banner:t}=(0,n.useContext)(a.y);return(0,s.jsx)("div",{suppressHydrationWarning:!0,className:(0,l.cn)(o.x.<PERSON>mondLayout,"top-[7rem] lg:top-[3.5rem] w-full",!!t&&"lg:top-[6.5rem]","peer-[.is-not-custom]:fixed peer-[.is-not-custom]:pb-2 peer-[.is-not-custom]:pr-2 peer-[.is-not-custom]:pt-0 peer-[.is-custom]:absolute",i.N.firstChildHiddenIfCustom),children:e})}},9855:(e,t,r)=>{"use strict";r.d(t,{ChatAssistantSheet:()=>es});var s=r(54568),n=r(92730),i=r(80584),a=r(7620),o=r(60357),l=r(12459),c=r(80976),d=r(71197),u=r(93351),h=r(88374),m=r(53132),g=r(59538),p=r(40113),x=r(31467),f=r(77830),y=r(60710),v=r(16600),b=r(71476),j=r(98736),k=r(88638),w=r(10122),N=r(44902),C=r(86493);let I=a.forwardRef(({className:e,message:t,...r},n)=>{let i=t.parts,o=(0,a.useMemo)(()=>{let e=i?.find(e=>"text"===e.type);try{return e?JSON.parse(e.text):void 0}catch(e){return}},[i]),l=(0,N.Sh)({codeString:o?.code||"",codeBlockTheme:"system",language:o?.language||"text"}),c=(0,C.c$)(l);return(0,s.jsxs)("div",{className:"flex justify-end items-end w-full flex-col gap-1",children:[o&&(0,s.jsx)(k.NG,{...o,className:"w-full my-3",hideAskAiButton:!0,isSmallText:!0,expandable:(c||0)>10,children:(0,s.jsx)(w.z,{...o,children:o.code})}),(0,s.jsx)("div",{ref:n,className:(0,v.cn)("flex px-3.5 py-2.5 items-start gap-4 w-fit rounded-2xl bg-gray-100 dark:bg-white/5",e),...r,children:(0,s.jsx)("div",{className:"flex items-start gap-4 w-full",children:(0,s.jsx)("div",{className:"flex flex-col gap-1 w-full",children:(0,s.jsx)("div",{className:"break-words text-sm text-gray-800 dark:text-gray-200",children:t.content})})})})]})});I.displayName="ChatMessage";var S=r(37469),A=r(4931),E=r(76608),z=r(55918),_=r(87255),P=r(8080),R=r(79321);function T({text:e,charDelay:t=1,onComplete:r,className:n="",disabled:i=!1,renderAsMarkdown:o=!1,markdownProps:l={}}){let{displayedText:c}=function(e,t={}){let{charDelay:r=1,onComplete:s=()=>{},disabled:n=!1}=t,[i,o]=(0,a.useState)(""),[l,c]=(0,a.useState)(!1),d=(0,a.useRef)(null),u=(0,a.useRef)(""),h=(0,a.useRef)(0),m=(0,a.useRef)(!1),g=(0,a.useRef)(!1),p=(0,a.useRef)(!0),x=(0,a.useRef)(0),f=(0,a.useRef)(null),y=(0,a.useRef)(!1);(0,a.useEffect)(()=>{let e=document.getElementById("chat-content");if(f.current=e,!e)return;let t=()=>{y.current||(y.current=!0,requestAnimationFrame(()=>{if(!f.current){y.current=!1;return}let e=f.current.scrollTop,t=e-x.current;x.current=e,t<0?p.current=!1:t>0&&f.current.scrollHeight-f.current.scrollTop-f.current.clientHeight<=30&&(p.current=!0),y.current=!1}))};return x.current=e.scrollTop,e.addEventListener("scroll",t,{passive:!0}),()=>{e.removeEventListener("scroll",t)}},[]),(0,a.useEffect)(()=>{if(m.current&&p.current){let e=f.current;e&&setTimeout(()=>{e.scrollTo({top:e.scrollHeight,behavior:"instant"}),x.current=e.scrollTop},10)}},[i]);let v=(0,a.useCallback)(()=>{d.current&&(clearTimeout(d.current),d.current=null),c(!1),m.current=!1},[]),b=(0,a.useCallback)(()=>{if(m.current)return;c(!0),m.current=!0,g.current=!0;let e=()=>{let t=u.current;h.current<t.length?(h.current+=1,o(t.slice(0,h.current)),d.current=setTimeout(e,r)):(c(!1),m.current=!1,s())};e()},[r,s]);return(0,a.useEffect)(()=>{if(u.current=e,n&&!g.current){o(e),h.current=e.length;return}!m.current&&h.current<e.length&&(!n||g.current)&&b()},[e,n,b]),(0,a.useEffect)(()=>{""===e&&(v(),o(""),h.current=0,g.current=!1,p.current=!0)},[e,v]),{displayedText:i,isRendering:l,completeRendering:(0,a.useCallback)(()=>{v(),o(e),h.current=e.length,s()},[e,v,s]),reset:(0,a.useCallback)(()=>{v(),o(""),h.current=0,u.current="",g.current=!1,p.current=!0},[v]),progress:e.length>0?h.current/e.length:0}}(e,{charDelay:t,onComplete:r,disabled:i}),d=(0,a.useMemo)(()=>i?e:c,[i,c,e]);return o?(0,s.jsx)(R.o,{...l,children:d}):(0,s.jsx)("div",{className:(0,v.cn)("relative",n),children:(0,s.jsx)("span",{className:"whitespace-pre-wrap",children:d})})}let L={className:"prose prose-sm dark:prose-invert overflow-x-auto",showCopyButton:!0,components:{h1:({children:e})=>(0,s.jsx)("h1",{className:"text-lg font-semibold text-gray-950 dark:text-gray-50",children:e}),h2:({children:e})=>(0,s.jsx)("h2",{className:"text-base font-semibold text-gray-950 dark:text-gray-50",children:e}),h3:({children:e})=>(0,s.jsx)("h3",{className:"text-base font-semibold text-gray-950 dark:text-gray-50",children:e}),h4:({children:e})=>(0,s.jsx)("h4",{className:"text-base font-semibold text-gray-950 dark:text-gray-50",children:e}),h5:({children:e})=>(0,s.jsx)("h5",{className:"text-base font-semibold text-gray-950 dark:text-gray-50",children:e}),h6:({children:e})=>(0,s.jsx)("h6",{className:"text-base font-semibold text-gray-950 dark:text-gray-50",children:e})}},H=({response:e,isLast:t})=>t?(0,s.jsx)(T,{text:e.text,renderAsMarkdown:!0,disabled:!t,charDelay:1,markdownProps:L}):(0,s.jsx)(R.o,{...L,children:e.text});var $=r(15173),M=r(27911);let W=({query:e,children:t})=>{let[r,n]=(0,a.useState)(!1),i=null!=t;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("button",{className:(0,v.cn)("group flex items-start text-left gap-2.5 text-gray-950 dark:text-gray-50 flex-shrink-0 hover:text-gray-700 dark:hover:text-gray-300 transition-colors",i?"cursor-pointer":"cursor-default text-gray-800 dark:text-gray-200"),onClick:()=>n(!r),children:[(0,s.jsx)($.A,{className:(0,v.cn)("size-3.5 mt-1 flex-shrink-0",i&&"block group-hover:hidden",r&&"hidden"),absoluteStrokeWidth:!0,strokeWidth:1.5}),(0,s.jsx)(M.A,{className:(0,v.cn)("size-3.5 mt-1 hidden flex-shrink-0",i&&"group-hover:block",r&&"block rotate-90"),absoluteStrokeWidth:!0,strokeWidth:1.5}),(0,s.jsx)("span",{className:"text-sm flex items-center gap-1.5",children:(0,s.jsx)("span",{className:"font-medium",children:i?`Searched ${e}`:`Searching for ${e}`})})]}),r&&(0,s.jsx)("div",{className:"pl-6 pt-0.5 not-prose",children:t})]})},D="rounded-md px-1.5 py-1 hover:bg-gray-100 dark:hover:bg-white/10 cursor-pointer text-gray-500",B="rounded-md px-1.5 py-1 bg-primary dark:bg-bg-primary-light text-white dark:text-gray-950",F=()=>{let{onReload:e}=(0,m.w)();return(0,s.jsxs)("button",{className:"mt-6 group text-primary dark:text-primary-light hover:text-primary-light dark:hover:text-primary-light text-sm cursor-pointer flex items-center gap-0.5 whitespace-nowrap self-start",onClick:e,children:[(0,s.jsx)(y.HD.Retry,{}),(0,s.jsx)("span",{className:"px-1",children:"Retry"})]})},V=()=>{let{onReload:e}=(0,m.w)();return(0,s.jsx)("div",{className:"rounded-md px-1.5 py-1 hover:bg-gray-100 dark:hover:bg-white/10 cursor-pointer text-gray-500",onClick:e,children:(0,s.jsx)(S.A,{size:12,absoluteStrokeWidth:!0,strokeWidth:1.5})})},K=({message:e})=>{let t=(0,h.p)("docs.assistant.copy_response"),[r,n]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{r&&setTimeout(()=>n(!1),2e3)},[r]),(0,s.jsx)("div",{className:"rounded-md px-1.5 py-1 hover:bg-gray-100 dark:hover:bg-white/10 cursor-pointer text-gray-500",onClick:()=>{navigator.clipboard.writeText(e.content).then(()=>{t({message:e.content}).catch(console.error),n(!0)}).catch(console.error)},children:r?(0,s.jsx)(A.A,{size:12,absoluteStrokeWidth:!0,strokeWidth:1.5,className:"text-primary dark:text-primary-light"}):(0,s.jsx)(E.A,{size:12,absoluteStrokeWidth:!0,strokeWidth:1.5})})},O=a.forwardRef(({className:e,message:t,isLast:r,...n},i)=>{let[o,l]=(0,a.useState)(!1),[c,d]=(0,a.useState)(!1),u=(0,g.A)(e=>e.status),m=(0,h.p)("docs.assistant.source_click"),p=(0,h.p)("docs.assistant.thumbs_up"),x=(0,h.p)("docs.assistant.thumbs_down");return"error"===u?(0,s.jsxs)("div",{ref:i,className:(0,v.cn)("py-4 text-sm",e),...n,children:[(0,s.jsx)("span",{children:"Sorry, we could not generate a response to your question."}),(0,s.jsx)(F,{})]}):(0,s.jsxs)("div",{ref:i,className:(0,v.cn)("flex flex-col py-[14px] gap-4 self-stretch",e),...n,children:[t.parts?.map((e,n)=>"text"===e.type?(0,s.jsx)(H,{response:e,isLast:r&&n===(t.parts?.length??0)-1},`text-${n}`):"tool-invocation"===e.type&&"search"===e.toolInvocation.toolName&&("call"===e.toolInvocation.state||"partial-call"===e.toolInvocation.state)?(0,s.jsx)(W,{query:e.toolInvocation.args.query},`${e.toolInvocation.toolCallId}-call`):"tool-invocation"===e.type&&"search"===e.toolInvocation.toolName&&"result"===e.toolInvocation.state?(0,s.jsx)("div",{className:(0,v.cn)("flex flex-col gap-2",0===e.toolInvocation.result.length&&"hidden"),children:(0,s.jsx)(W,{query:e.toolInvocation.args.query,children:(0,s.jsx)("div",{className:"flex gap-1 flex-col",children:e.toolInvocation.result.map(e=>(0,s.jsx)(P.k,{href:`/${e.path}`,onClick:()=>{m({url:e.path}).catch(console.error)},title:e.metadata.title,titleContainerClassName:"gap-1 py-1"},e.path))})})},`${e.toolInvocation.toolCallId}-result`):void 0),"ready"===u&&t.parts&&t.parts.length>0&&(0,s.jsx)("div",{className:"flex items-start gap-2 w-full",children:(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("div",{className:(0,v.cn)(o?B:D),onClick:()=>{o||(p({message:t.content}).catch(console.error),l(!0),d(!1))},children:(0,s.jsx)(z.A,{size:12,absoluteStrokeWidth:!0,strokeWidth:1.5})}),(0,s.jsx)("div",{className:(0,v.cn)(c?B:D),onClick:()=>{c||(x({message:t.content}).catch(console.error),l(!1),d(!0))},children:(0,s.jsx)(_.A,{size:12,absoluteStrokeWidth:!0,strokeWidth:1.5})}),(0,s.jsx)(K,{message:t}),r&&(0,s.jsx)(V,{})]})})]})});O.displayName="ChatResponse";let G=a.memo(({message:e,isLast:t})=>"user"===e.role?(0,s.jsx)(I,{message:e}):"assistant"===e.role?(0,s.jsx)(O,{message:e,isLast:t}):void 0,(e,t)=>e.message.content===t.message.content&&e.message.parts?.length===t.message.parts?.length);G.displayName="ChatItem";let Q=()=>{let[e,t]=(0,a.useState)(1),{status:r,messages:n}=(0,m.w)(),i=(0,a.useMemo)(()=>{if("submitted"===r)return!0;if("streaming"!==r)return!1;let e=n.at(-1)?.parts.at(-1);return e?.type==="step-start"||e?.type==="tool-invocation"},[r,n]);if((0,a.useEffect)(()=>{if(i){let e=setInterval(()=>{t(e=>3===e?1:e+1)},200);return()=>clearInterval(e)}},[i]),i)return(0,s.jsx)("div",{className:"py-4 text-sm",children:`Generating${".".repeat(e)}`})},q=()=>(0,g.A)((0,j.k)(e=>e.messages.slice(0,-1))).map(e=>(0,s.jsx)(G,{message:e},e.id)),X=()=>{let e=(0,g.A)((0,j.k)(e=>e.messages.at(-1)));return e?(0,s.jsx)(G,{message:e,isLast:!0},e.id):null},U=({className:e})=>(0,s.jsx)("div",{className:(0,v.cn)("flex flex-col-reverse gap-3 overflow-y-auto",e),children:(0,s.jsxs)("div",{className:"last:mb-2",children:[(0,s.jsx)(q,{}),(0,s.jsx)(X,{}),(0,s.jsx)(Q,{})]})});var J=r(58824),Z=r(9481);let Y=({chatContentRef:e,isMobile:t})=>{let{askAQuestion:r}=(0,u.n)(),{subdomain:n}=(0,a.useContext)(d.Em),i=(0,a.useRef)(null),{isChatSheetOpen:o,shouldFocusChatSheet:l,setShouldFocusChatSheet:c}=(0,a.useContext)(b.ChatAssistantContext),g=(0,h.p)("docs.assistant.enter"),[f,y]=(0,a.useState)(!1),{input:j,setInput:k,handleSubmit:w,isInProgress:N}=(0,m.w)();(0,a.useEffect)(()=>{if(o){let e=i.current;e&&l&&(e.focus(),e.setSelectionRange(e.value.length,e.value.length),c(!1))}else i.current?.blur()},[o,l,c]);let C=()=>{!N&&(e.current?.scrollTo({top:e.current.scrollHeight,behavior:"smooth"}),w(),k(""),g({subdomain:n,query:j}),t&&i.current&&i.current.blur())};return(0,s.jsx)("div",{className:(0,v.cn)(t&&f&&"fixed bottom-0 left-0 right-0 z-50 px-4 pb-4 bg-background-light dark:bg-background-dark"),children:(0,s.jsxs)("div",{className:"flex items-end gap-2 relative",children:[(0,s.jsx)(Z.A,{id:p.V.ChatAssistantTextArea,ref:i,autoComplete:"off",placeholder:r,value:j,onChange:e=>{k(e.target.value)},onFocus:()=>{t&&y(!0)},onBlur:()=>{t&&y(!1)},cacheMeasurements:!1,minRows:1,className:(0,v.cn)("grow w-full px-3.5 pr-10 py-2.5 bg-background-light dark:bg-background-dark border border-gray-200 dark:border-gray-600/30 rounded-2xl shadow-sm focus:outline-none focus:border-primary dark:focus:border-primary-light text-gray-900 dark:text-gray-100",t?"text-base":"text-sm",x.x.ChatAssistantInput),maxRows:10,onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),C())},style:{resize:"none",fontSize:t?"16px":void 0}}),(0,s.jsx)("button",{className:(0,v.cn)("absolute right-2.5 bottom-[9px] flex justify-center items-center p-1 w-6 h-6 rounded-lg",""===j.trim()||N?"bg-primary/30 dark:bg-primary-dark/30":"bg-primary dark:bg-primary-dark",x.x.ChatAssistantSendButton),"aria-label":"Send message",disabled:""===j.trim()||N,onClick:C,children:(0,s.jsx)(J.A,{className:(0,v.cn)("w-3.5 h-3.5 text-white dark:text-white")})})]})})},ee=()=>(0,s.jsxs)("div",{className:"mt-4 flex flex-col items-center text-sm justify-between",children:[(0,s.jsx)("div",{className:(0,v.cn)("mx-8 text-center text-gray-400 dark:text-gray-600 text-xs",x.x.ChatAssistantDisclaimerText),children:"Responses are generated using AI and may contain mistakes."}),(0,s.jsx)("div",{className:"flex flex-col gap-4 text-gray-800 dark:text-gray-200"})]}),et=368,er=576,es=({className:e,minWidth:t=et,maxWidth:r=er})=>{let{isChatSheetOpen:m,onChatSheetToggle:j}=(0,a.useContext)(b.ChatAssistantContext),{docsConfig:k}=(0,a.useContext)(d.H6),{divisions:w}=(0,a.useContext)(u._),{banner:N}=(0,a.useContext)(c.y),{assistant:C}=(0,u.n)(),I=(0,g.A)(e=>e.messages.length),S=(0,a.useRef)(null),A=(0,a.useRef)(null),[E,z]=((e,t)=>{let[r,s]=(0,o.Mj)(e,t),[n,i]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{i(!0)},[]),[(0,a.useMemo)(()=>n?r:t,[n,r,t]),s,n]})("chat-assistant-sheet-width",t),[_,P]=(0,a.useState)(!1),R=(0,h.p)("docs.assistant.maximize_click"),T=(0,h.p)("docs.assistant.minimize_click"),L=(0,a.useMemo)(()=>E>=r,[E,r]),H=w.tabs.length>0,$=(0,o.Ub)("(max-width: 1024px)"),M=(0,a.useCallback)(e=>{e.preventDefault(),P(!0)},[]),W=(0,a.useCallback)(e=>{_&&A.current&&z(Math.min(Math.max(A.current.getBoundingClientRect().right-e.clientX,t),r))},[_,t,r,z]),D=(0,a.useCallback)(()=>{P(!1)},[]),B=(0,a.useCallback)(()=>{L?T({}).catch(console.error):R({}).catch(console.error),z(L?t:r)},[L,t,r,z,R,T]);return(0,a.useEffect)(()=>(_&&(document.addEventListener("mousemove",W),document.addEventListener("mouseup",D)),()=>{document.removeEventListener("mousemove",W),document.removeEventListener("mouseup",D)}),[_,W,D]),(0,s.jsxs)("div",{suppressHydrationWarning:!0,className:(0,v.cn)((0,f.$d)(k,H,!!N,$),!_&&"transition-[width] duration-300 ease-in-out",!m&&"invisible",$&&l.f.Popup,e),style:{width:$?void 0:`${E}px`,minWidth:$?void 0:`${t}px`,maxWidth:$?void 0:`${r}px`},children:[!$&&(0,s.jsx)("div",{className:(0,v.cn)("absolute -left-1 top-0 bottom-0 w-1 cursor-col-resize hover:bg-gray-200/70 dark:hover:bg-white/[0.07] z-10",_&&"bg-gray-200/70 dark:bg-white/[0.07]"),onMouseDown:M,style:{cursor:"col-resize"}}),(0,s.jsx)("div",{ref:A,id:p.V.ChatAssistantSheet,className:(0,v.cn)("absolute inset-0 -top-px min-h-full flex flex-col overflow-hidden shrink-0",x.x.ChatAssistantSheet),"aria-hidden":!m,children:(0,s.jsxs)("div",{className:(0,v.cn)("w-full flex flex-col pb-4 h-full",$?"pt-3":"lg:pt-3"),children:[(0,s.jsxs)("div",{className:(0,v.cn)(x.x.ChatAssistantSheetHeader,"flex items-center justify-between pb-3 px-4"),children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(y.BZ,{className:"size-5 text-primary dark:text-primary-light"}),(0,s.jsx)("span",{className:"font-semibold text-gray-900 dark:text-gray-100",children:C})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("button",{onClick:B,className:"group hover:bg-gray-100 dark:hover:bg-white/10 p-1.5 rounded-lg",children:L?(0,s.jsx)(n.A,{className:"size-[13px] text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-300"}):(0,s.jsx)(i.A,{className:"size-[13px] text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-300"})}),I>0&&(0,s.jsx)(en,{}),(0,s.jsx)("button",{onClick:j,className:"group hover:bg-gray-100 dark:hover:bg-white/10 p-1.5 rounded-lg",children:(0,s.jsx)(y.eg,{className:"size-3.5 text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-300"})})]})]}),(0,s.jsx)("div",{ref:S,id:"chat-content",className:(0,v.cn)(x.x.ChatAssistantSheetContent,"flex-1 overflow-y-auto relative px-4"),children:I>0?(0,s.jsx)(U,{}):(0,s.jsx)(ee,{})}),(0,s.jsx)("div",{className:"px-4",children:(0,s.jsx)(Y,{chatContentRef:S,isMobile:$})})]})})]})},en=()=>{let{onClear:e}=(0,m.w)();return(0,s.jsx)("button",{onClick:e,className:"group hover:bg-gray-100 dark:hover:bg-white/10 p-1.5 rounded-lg",children:(0,s.jsx)(y.sy,{className:"size-3.5 text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-300"})})}},11350:(e,t,r)=>{Promise.resolve().then(r.bind(r,87681)),Promise.resolve().then(r.bind(r,42013)),Promise.resolve().then(r.bind(r,46826)),Promise.resolve().then(r.bind(r,99543)),Promise.resolve().then(r.bind(r,80976)),Promise.resolve().then(r.bind(r,71197)),Promise.resolve().then(r.bind(r,25327)),Promise.resolve().then(r.bind(r,57862)),Promise.resolve().then(r.bind(r,93351)),Promise.resolve().then(r.bind(r,85506)),Promise.resolve().then(r.bind(r,63792)),Promise.resolve().then(r.bind(r,27115)),Promise.resolve().then(r.bind(r,7744)),Promise.resolve().then(r.bind(r,43715)),Promise.resolve().then(r.bind(r,29875)),Promise.resolve().then(r.bind(r,47576)),Promise.resolve().then(r.bind(r,26635)),Promise.resolve().then(r.bind(r,33464)),Promise.resolve().then(r.bind(r,59672)),Promise.resolve().then(r.bind(r,59349)),Promise.resolve().then(r.bind(r,40774)),Promise.resolve().then(r.bind(r,76068)),Promise.resolve().then(r.bind(r,9855)),Promise.resolve().then(r.bind(r,71476)),Promise.resolve().then(r.t.bind(r,44760,23))},25327:(e,t,r)=>{"use strict";r.d(t,{LocalStorageAndAnalyticsProviders:()=>F});var s=r(54568),n=r(7620),i=r(88301),a=r(8139);function o({clearbit:e}){return e?.publicApiKey?(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(a.default,{strategy:"afterInteractive",src:`https://tag.clearbitscripts.com/v1/${e.publicApiKey}/tags.js`})}):null}function l({ga4:e}){return e?.measurementId?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.default,{strategy:"afterInteractive",src:`https://www.googletagmanager.com/gtag/js?id=${e.measurementId}`}),(0,s.jsx)(a.default,{strategy:"afterInteractive",id:"ga4",dangerouslySetInnerHTML:{__html:`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${e.measurementId}', {
              page_path: window.location.pathname,
            });
          `}})]}):null}function c({gtm:e}){return e?.tagId?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.default,{id:"gtm",strategy:"afterInteractive",dangerouslySetInnerHTML:{__html:`
          (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','${e.tagId}');`}}),(0,s.jsx)("noscript",{children:(0,s.jsx)("iframe",{src:`https://www.googletagmanager.com/ns.html?id=${e.tagId}`,height:"0",width:"0",style:{display:"none",visibility:"hidden"}})})]}):null}function d({heap:e}){return e?.appId?(0,s.jsx)(a.default,{id:"heap",type:"text/javascript",dangerouslySetInnerHTML:{__html:`
          window.heap=window.heap||[],heap.load=function(e,t){window.heap.appid=e,window.heap.config=t=t||{};var r=document.createElement("script");r.type="text/javascript",r.async=!0,r.src="https://cdn.heapanalytics.com/js/heap-"+e+".js";var a=document.getElementsByTagName("script")[0];a.parentNode.insertBefore(r,a);for(var n=function(e){return function(){heap.push([e].concat(Array.prototype.slice.call(arguments,0)))}},p=["addEventProperties","addUserProperties","clearEventProperties","identify","resetIdentity","removeEventProperty","setEventProperties","track","unsetEventProperty"],o=0;o<p.length;o++)heap[p[o]]=n(p[o])};
          heap.load("${e.appId}");
          `}}):null}function u({koala:e}){return e?.publicApiKey?(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(a.default,{strategy:"afterInteractive",id:"koala",dangerouslySetInnerHTML:{__html:`
          !(function (t) {
            if (window.ko) return;
            (window.ko = []),
              [
                "identify",
                "track",
                "removeListeners",
                "open",
                "on",
                "off",
                "qualify",
                "ready",
              ].forEach(function (t) {
                ko[t] = function () {
                  var n = [].slice.call(arguments);
                  return n.unshift(t), ko.push(n), ko;
                };
              });
            var n = document.createElement("script");
            (n.async = !0),
              n.setAttribute(
                "src",
                "https://cdn.getkoala.com/v1/${e.publicApiKey}/sdk.js"
              ),
              (document.body || document.head).appendChild(n);
          })();
          `}})}):null}function h({plausible:e}){return e?.domain?(0,s.jsx)(a.default,{strategy:"afterInteractive","data-domain":e.domain,src:`https://${e.server??"plausible.io"}/js/script.js`}):null}var m=r(90487),g=r(71197);function p({segment:e}){return e?.key?(0,s.jsx)(a.default,{strategy:"afterInteractive",id:"segment",dangerouslySetInnerHTML:{__html:`
  !function(){var i="analytics",analytics=window[i]=window[i]||[];if(!analytics.initialize)if(analytics.invoked)window.console&&console.error&&console.error("Segment snippet included twice.");else{analytics.invoked=!0;analytics.methods=["trackSubmit","trackClick","trackLink","trackForm","pageview","identify","reset","group","track","ready","alias","debug","page","screen","once","off","on","addSourceMiddleware","addIntegrationMiddleware","setAnonymousId","addDestinationMiddleware","register"];analytics.factory=function(e){return function(){if(window[i].initialized)return window[i][e].apply(window[i],arguments);var n=Array.prototype.slice.call(arguments);if(["track","screen","alias","group","page","identify"].indexOf(e)>-1){n.push({__t:"bpc",c:location.href,p:location.pathname,u:location.href,s:location.search,t:document.title,r:document.referrer})}n.unshift(e);analytics.push(n);return analytics}};for(var n=0;n<analytics.methods.length;n++){var key=analytics.methods[n];analytics[key]=analytics.factory(key)}analytics.load=function(key,n){var t=document.createElement("script");t.type="text/javascript";t.async=!0;t.setAttribute("data-global-segment-analytics-key",i);t.src="https://cdn.segment.com/analytics.js/v1/" + key + "/analytics.min.js";var r=document.getElementsByTagName("script")[0];r.parentNode.insertBefore(t,r);analytics._loadOptions=n};analytics._writeKey="${e.key}";;analytics.SNIPPET_VERSION="5.2.0";
  analytics.load("${e.key}");
  analytics.page();
  }}();`}}):null}let x=()=>{let{docsConfig:e}=(0,n.useContext)(g.H6);return m.db?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o,{clearbit:e?.integrations?.clearbit}),(0,s.jsx)(l,{ga4:e?.integrations?.ga4}),(0,s.jsx)(c,{gtm:e?.integrations?.gtm}),(0,s.jsx)(d,{heap:e?.integrations?.heap}),(0,s.jsx)(u,{koala:e?.integrations?.koala}),(0,s.jsx)(h,{plausible:e?.integrations?.plausible}),(0,s.jsx)(p,{segment:e?.integrations?.segment})]}):null};var f=r(62942),y=r(73920);class v{}var b=r(59060);class j extends v{init(e,t=!1){e.apiKey&&(this.initialized=!0,y.Ay.init(e.apiKey,{api_host:e.apiHost||"https://events-proxy.mintlify.com",ui_host:"https://us.posthog.com",capture_pageview:!1,disable_session_recording:!t,loaded:e=>{m.db||e.opt_out_capturing()}}))}captureEvent(e){return async t=>{y.Ay.capture(e,{...t})}}createEventListener(e){return this.initialized?this.captureEvent(e):async function(e){}}onRouteChange(e,t){y.Ay.capture("$pageview"),y.Ay.capture(`$${b.b}`)}constructor(...e){super(...e),this.initialized=!1}}class k extends j{constructor(e){super(),this.initialized=!1,this.subdomain=e}captureEvent(e){return async t=>{y.Ay.capture(e,{...t,subdomain:this.subdomain})}}onRouteChange(e,t){y.Ay.capture("$pageview",{subdomain:this.subdomain,title:document.title}),y.Ay.capture(`$${b.b}`,{subdomain:this.subdomain,title:document.title})}}class w extends v{init(e){if(!e.apiKey)return;let{apiKey:t}=e;r.e(1457).then(r.bind(r,21457)).then(e=>{this.initialized||(e.init(t),this.track=e.track,this.initialized=!0,this.eventQueue.forEach(({eventName:e,eventProperties:t})=>{this.track(e,t)}),this.eventQueue=[])}).catch(e=>{this.track=()=>{},console.error(e)})}createEventListener(e){return(async function(t){this.track(e,t)}).bind(this)}onRouteChange(e,t){t.shallow||(this.track("page_view",{url:e}),this.track(b.b,{url:e}))}constructor(...e){super(...e),this.initialized=!1,this.eventQueue=[],this.track=(e,t)=>{"string"==typeof e&&t&&this.eventQueue.push({eventName:e,eventProperties:t})}}}class N extends v{init(e){if(!e.siteId)return;let{siteId:t}=e;r.e(8447).then(r.bind(r,18447)).then(e=>{this.initialized||(e.load(t),this.trackPageview=e.trackPageview,this.initialized=!0)}).catch(e=>{console.error(e)})}createEventListener(e){return()=>Promise.resolve()}onRouteChange(e,t){this.trackPageview&&!t.shallow&&this.trackPageview()}constructor(...e){super(...e),this.initialized=!1}}class C extends v{init(e){this.measurementId=e.measurementId}createEventListener(e){return this.measurementId&&"gtag"in window?async function(t){window.gtag("event",e,{})}:async function(e){}}onRouteChange(e){this.measurementId&&"gtag"in window&&window.gtag("config",this.measurementId,{page_path:e})}}class I extends v{init(e){if(!e.hjid||!e.hjsv)return;let t=parseInt(e.hjid,10),s=parseInt(e.hjsv,10);r.e(3659).then(r.t.bind(r,53659,23)).then(e=>{this.initialized||(this.hotjar=e.hotjar,this.hotjar.initialize(t,s),this.initialized=!0,this.eventQueue.forEach(e=>{this.hotjar&&this.hotjar.event(e)}))}).catch(e=>{console.error(e)})}createEventListener(e){return(async function(t){this.hotjar?this.hotjar.event(e):this.eventQueue.push(e)}).bind(this)}onRouteChange(e,t){}constructor(...e){super(...e),this.initialized=!1,this.eventQueue=[]}}var S=r(70170),A=r.n(S);class E extends v{init(e){if(e.appId)try{!this.initialized&&e.appId&&(A().init(e.appId),this.trackEvent=A().track,this.initialized=!0)}catch(e){console.error(e)}}createEventListener(e){if(this.initialized){let t=this.trackEvent;return async function(r){t&&t(e,r)}}return async function(e){}}onRouteChange(e,t){}constructor(...e){super(...e),this.initialized=!1}}class z extends v{init(e){e.projectToken?r.e(5284).then(r.t.bind(r,25633,23)).then(t=>{if(!this.initialized){let r=t.default;r.init(e.projectToken,{secure_cookie:!0}),this.initialized=!0,this.mixpanel=r,this.waitTracking.forEach(e=>{this.mixpanel.track(e.name,e.properties)})}}).catch(e=>{console.error(e)}):this.mixpanel.track=(e,t)=>{}}createEventListener(e){return(async function(t){this.mixpanel.track(e,t)}).bind(this)}onRouteChange(e){this.mixpanel.track("pageview",{path:e}),this.mixpanel.track(b.b,{path:e})}constructor(...e){super(...e),this.initialized=!1,this.waitTracking=[],this.mixpanel={track:(e,t)=>{this.waitTracking.push({name:e,properties:t})}}}}class _ extends v{init(e){if(!e.id)return;let t=e.id;r.e(3069).then(r.t.bind(r,73069,23)).then(e=>{this.initialized||(this.pirsch=new e.Pirsch({identificationCode:t}),this.initialized=!0,this.eventQueue.forEach(({eventName:e,eventProperties:t})=>{this.pirsch&&this.pirsch.event(e,void 0,t)}))}).catch(e=>{console.error(e)})}createEventListener(e){return(async function(t){this.pirsch?this.pirsch.event(e,void 0,t):this.eventQueue.push({eventName:e,eventProperties:t})}).bind(this)}onRouteChange(e){this.pirsch&&this.pirsch.hit()}constructor(...e){super(...e),this.initialized=!1,this.eventQueue=[]}}class P extends v{init(e){if(!e.key)return;let{key:t}=e;r.e(7483).then(r.bind(r,57483)).then(e=>{if(!this.initialized){let r=new e.AnalyticsBrowser;r.load({writeKey:t}),this.track=r.track,this.initialized=!0,this.eventQueue.forEach(({eventName:e,eventProperties:t})=>{this.track(e,t)}),this.eventQueue=[]}}).catch(e=>{this.track=()=>{},console.error(e)})}createEventListener(e){return(async function(t){this.track(e,t)}).bind(this)}onRouteChange(e,t){t.shallow||this.track(b.b,{url:e})}constructor(...e){super(...e),this.initialized=!1,this.eventQueue=[],this.track=(e,t)=>{e&&t&&"object"==typeof t&&this.eventQueue.push({eventName:e,eventProperties:t})}}}class R{constructor(e,t){this.analyticsIntegrations=[];let r=!!e?.amplitude?.apiKey,s=!!e?.fathom?.siteId,n=!!e?.ga4?.measurementId,i=!!(e?.hotjar?.hjid&&e.hotjar.hjsv),a=!!e?.logrocket?.appId,o=!!e?.mixpanel?.projectToken,l=!!e?.pirsch?.id,c=!!e?.posthog?.apiKey,d=!!e?.segment?.key;if(r&&e?.amplitude){let t=new w;t.init(e.amplitude),this.analyticsIntegrations.push(t)}if(s&&e?.fathom){let t=new N;t.init(e.fathom),this.analyticsIntegrations.push(t)}if(n&&e?.ga4){let t=new C;t.init(e.ga4),this.analyticsIntegrations.push(t)}if(i&&e?.hotjar){let t=new I;t.init(e.hotjar),this.analyticsIntegrations.push(t)}if(a&&e?.logrocket){let t=new E;t.init(e.logrocket),this.analyticsIntegrations.push(t)}if(o&&e?.mixpanel){let t=new z;t.init(e.mixpanel),this.analyticsIntegrations.push(t)}if(l&&e?.pirsch){let t=new _;t.init(e.pirsch),this.analyticsIntegrations.push(t)}if(c&&e?.posthog){let t=new j;t.init(e.posthog,!0),this.analyticsIntegrations.push(t)}if(t&&!c){let{internalAnalyticsWriteKey:e,subdomain:r}=t,s=new k(r);s.init({apiKey:e}),this.analyticsIntegrations.push(s)}if(d&&e?.segment){let t=new P;t.init(e.segment),this.analyticsIntegrations.push(t)}}createEventListener(e){let t=this.analyticsIntegrations.map(t=>t.createEventListener(e));return async function(e){t.forEach(t=>t(e))}}onRouteChange(e,t){this.analyticsIntegrations.forEach(r=>r.onRouteChange(e,t))}}var T=r(13592),L=r(60357);function H({frontchat:e}){return e?.snippetId?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(a.default,{strategy:"beforeInteractive",src:"https://chat-assets.frontapp.com/v1/chat.bundle.js"}),(0,s.jsx)(a.default,{id:"frontchat",strategy:"afterInteractive",children:`window.FrontChat('init', {chatId: '${e.snippetId}', useDefaultLauncher: true});`})]}):null}function $({osano:e}){return e?.scriptSource&&e.scriptSource.startsWith("https://cmp.osano.com/")&&e.scriptSource.endsWith("/osano.js")?(0,s.jsx)(a.default,{strategy:"beforeInteractive",id:"osano",src:e.scriptSource}):null}let M=()=>{let{docsConfig:e}=(0,n.useContext)(g.H6);return m.db?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(function(){return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)($,{osano:e?.integrations?.osano})})},{}),(0,s.jsx)(H,{frontchat:e?.integrations?.frontchat})]}):null},W=async e=>{if(m.HL||!e||y.Ay.sessionRecordingStarted())return;y.Ay.opt_in_capturing();let{plan:t,createdAt:r}=e;"hobby"!==t&&y.Ay.startSessionRecording(),(e=>!!e&&Date.now()-new Date(e).getTime()<m.eP)(r)&&y.Ay.startSessionRecording()},D=async e=>{!m.HL&&e&&(y.Ay.opt_out_capturing(),y.Ay.sessionRecordingStarted()&&y.Ay.stopSessionRecording())};var B=r(28963);function F({children:e,subdomain:t,internalAnalyticsWriteKey:r,org:a}){let{docsConfig:o}=(0,n.useContext)(g.H6),l=o?.integrations?.telemetry?.enabled===!1,c=(e=>{let t=void 0==e||e.integrations?.cookies!==void 0,r=e?.integrations?.cookies?.key??"",s=e?.integrations?.cookies?.value??"",[i,a]=(0,n.useState)(!t),[o,l]=(0,L.Mj)(r,"");return(0,n.useEffect)(()=>{t&&""!==r&&""!==s&&(o.toString()===s.toString()?a(!0):o.toString()!==s.toString()&&a(!1))},[o,t,r,s]),i})(o),d=function(e,t,r,s,i){let a=(0,f.useRouter)(),o=(0,T.G)(),[l,c]=(0,n.useState)(!1),[d,u]=(0,n.useState)();return(0,n.useEffect)(()=>{let n;!s&&i&&m.db&&!l&&(r&&t&&(n={internalAnalyticsWriteKey:r,subdomain:t}),u(new R(e,n)),c(!0))},[l,e,r,t,s,i]),(0,n.useEffect)(()=>{d&&d.onRouteChange(o,{initialLoad:!0})},[o,d,a]),d}(o?.integrations??{},t,r,l,c);return!l&&c?W(a):D(a),(0,s.jsx)(i.y,{value:{analyticsMediator:d},children:(0,s.jsxs)(B.i,{cookiesEnabled:c,children:[(0,s.jsx)(M,{}),(0,s.jsx)(x,{}),e]})})}},26635:(e,t,r)=>{"use strict";r.d(t,{SideNav:()=>f});var s=r(54568),n=r(98354),i=r(7620),a=r(71197),o=r(93351),l=r(13592),c=r(66415),d=r(40113),u=r(43460),h=r(16600),m=r(11161),g=r(97870),p=r(57390),x=r(40774);function f({mobile:e=!1}){let t=(0,l.G)(),{divisions:r}=(0,i.useContext)(o._),{docsConfig:f}=(0,i.useContext)(a.H6),y=(0,i.useRef)(null),v=(0,i.useRef)(null),b=(0,i.useRef)(null),j=r.groupsOrPages.reduce((e,t)=>(0,n.y)(t)?e+1:e+t.pages.length,0);return(0,c.E)(()=>{function e(){v.current=y.current}if(y.current){if(y.current===v.current)return void e();e();let t=b.current?(0,m.L)(b.current):document.body,r=t.getBoundingClientRect(),s=y.current.getBoundingClientRect(),n=y.current.offsetTop,i=n-r.height+s.height;(t.scrollTop>n||t.scrollTop<i)&&(t.scrollTop=n-r.height/2+s.height/2)}},[t]),(0,s.jsxs)("div",{ref:b,className:"relative lg:text-sm lg:leading-6",children:[!e&&(0,s.jsx)("div",{className:(0,h.cn)("sticky top-0 h-8",f?.background?.image==null&&f?.background?.decoration==null&&"z-10 bg-gradient-to-b from-background-light dark:from-background-dark")}),(0,s.jsxs)("div",{id:d.V.NavigationItems,children:[(0,s.jsx)(g.X,{triggerClassName:"rounded-lg"}),r.tabs.length>0&&e&&(0,s.jsx)(p.T,{}),r.anchors.length>0&&(0,s.jsx)(x.d,{}),j>0&&(0,s.jsx)(u.r,{nav:r.groupsOrPages,...(0,u.f)({theme:f?.theme})})]})]})}},27115:(e,t,r)=>{"use strict";r.d(t,{AlmondNav:()=>y});var s=r(54568),n=r(7620),i=r(80976),a=r(71197),o=r(93351),l=r(26901),c=r(40113),d=r(31467),u=r(71476),h=r(16600),m=r(43460),g=r(76059),p=r(97870),x=r(1216),f=r(20776);function y({className:e}){let{docsConfig:t}=(0,n.useContext)(a.H6),{banner:r}=(0,n.useContext)(i.y),{divisions:l}=(0,n.useContext)(o._),x=l.languages,y=!!r,b=()=>2.5*!!y+3.5;return(0,s.jsxs)("div",{id:c.V.SidebarContent,suppressHydrationWarning:!0,className:(0,h.cn)("hidden fixed lg:flex flex-col left-0 bottom-0 right-auto transition-transform duration-100 w-[16.5rem] pl-2",e),style:{top:`${b()}rem`,height:`calc(100vh - ${b()}rem)`},children:[(0,s.jsx)("div",{className:"flex-1 p-2 relative",children:(0,s.jsxs)("div",{className:"text-sm flex flex-col px-2",children:[(0,s.jsx)(u.DesktopSearchEntry,{}),(0,s.jsxs)("div",{className:"overflow-y-auto absolute inset-x-0 top-11 bottom-0 px-2 pr-4 max-h-full py-6",id:c.V.NavigationItems,children:[(0,s.jsx)("div",{className:"px-2",children:(0,s.jsx)(p.X,{})}),(0,s.jsx)(v,{}),(0,s.jsx)(m.r,{nav:l.groupsOrPages,...(0,m.f)({theme:t?.theme})})]})]})}),(0,s.jsxs)("div",{className:(0,h.cn)(d.x.AlmondNavBottomSection,"w-full px-4",t?.appearance?.strict&&!x.length&&"hidden"),children:[(0,s.jsx)("div",{className:(0,h.cn)(d.x.AlmondNavBottomSectionDivider,"h-px bg-gray-200/70 dark:bg-gray-800/70 w-full")}),(0,s.jsxs)("div",{className:"flex items-center gap-3 py-3",children:[x.length>0&&(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)(f.K,{triggerClassName:"border-none px-0 font-medium"})}),!t?.appearance?.strict&&(0,s.jsx)(g.U,{})]})]})]})}function v(){let{divisions:e}=(0,n.useContext)(o._),{anchorDefault:t}=(0,l.G)();return(0,s.jsx)("div",{className:d.x.Anchors,children:e.anchors.map(e=>(0,s.jsx)("li",{className:"list-none",children:(0,s.jsx)(x.h,{href:e.href,name:e.name,icon:"string"==typeof e.icon?e.icon:e.icon?.name,iconType:"string"==typeof e.icon?"solid":e.icon?.style,color:e.color?.light??t,isActive:e.isActive},e.name)},e.name))})}},29875:(e,t,r)=>{"use strict";r.d(t,{ScrollTop:()=>i});var s=r(7620),n=r(80976);function i(){let{banner:e}=(0,s.useContext)(n.y),t=!!e;return(0,s.useEffect)(()=>{t&&window.document.documentElement.classList.add("lg:[--scroll-mt:9.5rem]")},[t]),null}},33464:(e,t,r)=>{"use strict";r.d(t,{MapleNav:()=>w});var s=r(54568),n=r(21067),i=r(7620),a=r(80976),o=r(71197),l=r(93351),c=r(40113),d=r(22765),u=r(71476),h=r(16600),m=r(67849),g=r(22625),p=r(43460),x=r(76059),f=r(97870),y=r(78719),v=r(81031),b=r(26901),j=r(1216);function k(){let{divisions:e}=(0,i.useContext)(l._),{anchorDefault:t}=(0,b.G)();return(0,s.jsx)("div",{className:"mt-8",children:e.anchors.map(e=>(0,s.jsx)("li",{className:"list-none",children:(0,s.jsx)(j.h,{href:e.href,name:e.name,icon:"string"==typeof e.icon?e.icon:e.icon?.name,iconType:"string"==typeof e.icon?"solid":e.icon?.style,color:e.color?.light??t,isActive:e.isActive},e.name)},e.name))})}function w(){let{divisions:e}=(0,i.useContext)(l._),{docsConfig:t}=(0,i.useContext)(o.H6),{banner:r}=(0,i.useContext)(a.y);return(0,s.jsxs)("div",{className:(0,h.cn)("hidden lg:flex fixed flex-col left-0 top-0 bottom-0 w-[19rem] border-r border-gray-200/70 dark:border-white/[0.07]",!!r&&"top-10"),id:c.V.Sidebar,children:[(0,s.jsxs)("div",{className:"flex-1 overflow-y-auto px-7 py-6",id:c.V.SidebarContent,children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(d.l,{href:t?(0,m.A)(t):"/",name:t?.name,logoClassName:"px-1 h-6 max-w-48"}),(0,s.jsx)(x.U,{})]}),(0,s.jsx)(u.DesktopSearchEntry,{className:"mt-6"}),(0,s.jsx)(f.X,{triggerClassName:"mt-4"}),(0,s.jsxs)("div",{className:"-mx-3 text-sm",id:c.V.NavigationItems,children:[(0,s.jsx)(k,{}),(0,s.jsx)(p.r,{nav:e.groupsOrPages,...(0,p.f)({theme:t?.theme})})]})]}),(0,s.jsxs)("ul",{className:"px-4 py-3 w-[calc(19rem-1px)] left-0 right-0 bottom-0 bg-background-light dark:bg-background-dark border-t border-gray-200/70 dark:border-white/[0.07] text-sm",children:[t?.navbar?.links?.map(e=>(0,s.jsx)(g.j,{entry:{href:e.href,title:e.label,icon:e.icon},shouldAutoNavigateOnGroupClick:!0,sidebarItemStyle:"undecorated",trailingIcon:(0,s.jsx)(n.A,{className:"size-3.5 text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-400"})},e.href)),t?.navbar?.primary?.type==="button"&&(0,s.jsx)(g.j,{entry:{href:t.navbar.primary.href,title:t.navbar.primary.label},shouldAutoNavigateOnGroupClick:!0,sidebarItemStyle:"undecorated",trailingIcon:(0,s.jsx)(n.A,{className:"size-3.5 text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-400"})}),t?.navbar?.primary?.type==="github"&&(0,s.jsx)(y.s,{className:"hidden lg:flex px-2"}),(0,s.jsx)(v.X,{triggerClassName:"mt-1 rounded-lg py-1.5 justify-between w-full border-gray-200/70 dark:border-white/10",triggerLabelClassName:"flex-1 text-left px-1"})]})]})}},40774:(e,t,r)=>{"use strict";r.d(t,{PalmNav:()=>f,d:()=>y});var s=r(54568),n=r(7620),i=r(80976),a=r(71197),o=r(93351),l=r(26901),c=r(40113),d=r(89619),u=r(16600),h=r(43460),m=r(76059),g=r(97870),p=r(1216),x=r(20776);function f(){let{docsConfig:e}=(0,n.useContext)(a.H6),{banner:t}=(0,n.useContext)(i.y),{divisions:r,hasAdvancedTabs:l}=(0,n.useContext)(o._),[p,f]=(0,n.useState)(!0),v=r.tabs.length>0,b=!!t,j=()=>2.5*!!b+3*!!v+4;return(0,s.jsxs)("div",{id:c.V.SidebarContent,suppressHydrationWarning:!0,className:(0,u.cn)("hidden sticky lg:flex flex-col left-0 top-[7rem] bottom-0 right-auto border-r border-gray-200/70 dark:border-white/[0.07] transition-transform duration-100",p?"w-[19rem]":"w-[4rem]"),style:{top:`${j()}rem`,height:`calc(100vh - ${j()}rem)`},children:[(0,s.jsx)("div",{className:"flex-1 px-7 py-6 overflow-y-auto",id:c.V.NavigationItems,children:(0,s.jsxs)("div",{className:(0,u.cn)("text-sm relative",!p&&"hidden"),children:[(0,s.jsx)(g.X,{}),(0,s.jsx)(y,{}),(0,s.jsx)(h.r,{nav:r.groupsOrPages,...(0,h.f)({theme:e?.theme})})]})}),(0,s.jsxs)("div",{className:(0,u.cn)("w-full flex items-center px-7 py-4 border-t border-gray-200/70 dark:border-white/[0.07]",(!p||e?.appearance?.strict===!0)&&"hidden"),children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)(x.K,{triggerClassName:"border-none px-0 font-medium"})}),(0,s.jsx)(m.U,{})]}),(0,s.jsx)("button",{className:(0,u.cn)("absolute top-5 right-5 p-1.5 rounded-md hover:bg-neutral-950/5 dark:hover:bg-white/5 cursor-pointer",r.dropdowns.length>0&&!l&&p&&"hidden"),onClick:()=>f(!p),children:p?(0,s.jsx)(d.Ay,{icon:"arrow-left-from-line",className:"h-3.5 w-3.5 bg-gray-700 dark:bg-gray-300"}):(0,s.jsx)(d.Ay,{icon:"arrow-right-to-line",className:"h-3.5 w-3.5 bg-gray-700 dark:bg-gray-300"})})]})}function y(){let{divisions:e}=(0,n.useContext)(o._),{anchorDefault:t}=(0,l.G)();return(0,s.jsx)("div",{children:e.anchors.map(e=>(0,s.jsx)("li",{className:"list-none",children:(0,s.jsx)(p.h,{href:e.href,name:e.name,icon:"string"==typeof e.icon?e.icon:e.icon?.name,iconType:"string"==typeof e.icon?"solid":e.icon?.style,color:e.color?.light??t,isActive:e.isActive},e.name)},e.name))})}},42013:(e,t,r)=>{"use strict";r.d(t,{HotReloader:()=>a});var s=r(54568),n=r(62942),i=r(7620);let a=()=>((()=>{let e=(0,n.useRouter)();(0,i.useEffect)(()=>{let t,s=!1;return(async()=>{let{default:n}=await r.e(60).then(r.bind(r,60060));s||(console.warn("Connected to Socket.io"),(t=n()).on("reload",()=>{console.warn("Received change, reloading page now"),e.refresh()}))})(),()=>{s=!0,t?.disconnect()}},[e])})(),(0,s.jsx)(s.Fragment,{}))},43715:(e,t,r)=>{"use strict";r.d(t,{AspenNav:()=>g});var s=r(54568),n=r(7620),i=r(80976),a=r(71197),o=r(93351),l=r(26901),c=r(40113),d=r(16600),u=r(43460),h=r(97870),m=r(1216);function g({className:e}){let{banner:t}=(0,n.useContext)(i.y),{divisions:r}=(0,n.useContext)(o._),{docsConfig:l}=(0,n.useContext)(a.H6),m=r.tabs.length>0,g=!!t,x=()=>2.5*!!g+2.5*!!m+3.5;return(0,s.jsx)("div",{id:c.V.SidebarContent,suppressHydrationWarning:!0,className:(0,d.cn)("hidden sticky shrink-0 w-[18rem] lg:flex flex-col left-0 top-[7rem] bottom-0 right-auto border-r border-gray-100 dark:border-white/10 transition-transform duration-100",e),style:{top:`${x()}rem`,height:`calc(100vh - ${x()}rem)`},children:(0,s.jsx)("div",{className:"flex-1 pr-5 pt-5 pb-4 overflow-y-auto",id:c.V.NavigationItems,children:(0,s.jsxs)("div",{className:"text-sm relative",children:[(0,s.jsx)("div",{className:"pl-2",children:(0,s.jsx)(h.X,{})}),(0,s.jsx)(p,{}),(0,s.jsx)(u.r,{nav:r.groupsOrPages,...(0,u.f)({theme:l?.theme})})]})})})}function p(){let{divisions:e}=(0,n.useContext)(o._),{anchorDefault:t}=(0,l.G)();return(0,s.jsx)("div",{children:e.anchors.map(e=>(0,s.jsx)("li",{className:"list-none",children:(0,s.jsx)(m.h,{href:e.href,name:e.name,icon:"string"==typeof e.icon?e.icon:e.icon?.name,iconType:"string"==typeof e.icon?"solid":e.icon?.style,color:e.color?.light??t,isActive:e.isActive})},e.name))})}},46826:(e,t,r)=>{"use strict";r.d(t,{NavScroller:()=>l});var s=r(54568),n=r(7620),i=r(13592),a=r(40113);function o(e){let t=document.querySelectorAll(`[id="${function(e){if(e)return e.split("#")[0]||void 0}(e)}"]`);if(1===t.length&&t[0]instanceof HTMLElement){let e=t[0];r.e(8097).then(r.bind(r,98097)).then(t=>{let{default:r}=t;r(e,{scrollMode:"if-needed",boundary:e=>e.id!==a.V.SidebarContent})}).catch(()=>"Error auto scroll the sidebar")}}function l(){let e=(0,i.G)(),t=(0,n.useRef)(void 0);(0,n.useEffect)(()=>o(e),[]);let r=(0,n.useCallback)(()=>{void 0!==t.current&&clearTimeout(t.current),t.current=window.setTimeout(()=>{o(e)},10)},[e]);return(0,n.useEffect)(()=>{r()},[r]),(0,s.jsx)(s.Fragment,{})}},47576:(e,t,r)=>{"use strict";r.d(t,{SidebarContainer:()=>c});var s=r(54568),n=r(7620),i=r(12459),a=r(80976),o=r(40113),l=r(16600);function c({children:e}){let{banner:t}=(0,n.useContext)(a.y);return(0,s.jsx)("div",{suppressHydrationWarning:!0,className:(0,l.cn)(i.f.PrimaryNav,"hidden lg:block fixed bottom-0 right-auto w-[18rem]"),id:o.V.Sidebar,style:{top:`${2.5*!!t+4}rem`},children:e})}},59349:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ThemeBanner:()=>c});var s=r(54568),n=r(7620),i=r(38972),a=r(12459),o=r(80976),l=r(16600);function c(){let{banner:e}=(0,n.useContext)(o.y);return(0,s.jsx)("div",{className:(0,l.cn)(a.f.Banner,e?"w-full sticky top-0 hidden lg:block":"hidden"),children:(0,s.jsx)(i.l,{})})}},59672:(e,t,r)=>{"use strict";r.d(t,{MainContentLayout:()=>m});var s=r(54568),n=r(7620),i=r(87681),a=r(93457),o=r(12459),l=r(80976),c=r(93351),d=r(40113),u=r(16600),h=r(69921);function m({children:e}){let{banner:t}=(0,n.useContext)(l.y),{divisions:r}=(0,n.useContext)(c._),m=r.tabs.length>0;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:(0,u.cn)("peer-[.is-not-center]:max-w-8xl peer-[.is-center]:max-w-3xl peer-[.is-not-custom]:px-4 peer-[.is-not-custom]:mx-auto peer-[.is-not-custom]:lg:px-8 peer-[.is-wide]:[&>div:last-child]:max-w-6xl","peer-[.is-custom]:contents",a.N.firstChildHiddenIfCustom,a.N.firstChildHiddenIfCenter),children:[(0,s.jsx)("div",{className:(0,u.cn)(o.f.SecondaryNav,"hidden lg:block fixed bottom-0 right-auto w-[18rem]"),id:d.V.Sidebar,style:{top:`${2.5*!!t+4+3*!!m}rem`},children:(0,s.jsx)("div",{className:"absolute inset-0 z-10 stable-scrollbar-gutter overflow-auto pr-8 pb-10",id:d.V.SidebarContent,children:(0,s.jsx)(h.f,{})})}),(0,s.jsx)("div",{id:d.V.ContentContainer,children:e})]}),(0,s.jsx)(i.AdvancedFooter,{})]})}},63792:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var s=r(54568),n=r(51844);function i(e){let{appId:t,children:r}=e;return void 0===t?(0,s.jsx)(s.Fragment,{children:r}):(0,s.jsx)(n.F,{...e,appId:t})}},76059:(e,t,r)=>{"use strict";r.d(t,{U:()=>c});var s=r(54568),n=r(68309),i=r(7620),a=r(71197),o=r(60710),l=r(16600);function c(){let{docsConfig:e}=(0,i.useContext)(a.H6),{resolvedTheme:t,setTheme:r}=(0,n.D)();return e?.appearance?.strict===!0?null:(0,s.jsxs)("button",{onClick:function(){r("dark"===t?"light":"dark")},className:"relative flex h-7 w-[3.25rem] items-center rounded-full border border-gray-200/70 dark:border-white/[0.07] hover:border-gray-200 dark:hover:border-white/10 p-1","aria-label":"Toggle dark mode",children:[(0,s.jsxs)("div",{className:"z-10 flex w-full items-center justify-between px-1",children:[(0,s.jsx)(o.gL,{className:"size-3 text-gray-600 dark:text-gray-600 fill-current"}),(0,s.jsx)(o.rR,{className:"size-3 text-gray-300 dark:text-gray-300 fill-current"})]}),(0,s.jsx)("div",{className:(0,l.cn)("absolute left-1 h-5 w-5 rounded-full bg-gray-100 dark:bg-white/[0.07] transition-transform duration-200","dark:translate-x-[1.40rem]")})]})}},76068:(e,t,r)=>{"use strict";r.d(t,{WillowNav:()=>N});var s=r(54568),n=r(21067),i=r(7620),a=r(80976),o=r(71197),l=r(93351),c=r(26901),d=r(40113),u=r(22625),h=r(22765),m=r(71476),g=r(16600),p=r(67849),x=r(43460),f=r(97870),y=r(78719),v=r(1216),b=r(20776),j=r(4434),k=r(67468);function w(){let{docsConfig:e}=(0,i.useContext)(o.H6),{divisions:t}=(0,i.useContext)(l._);return t.tabs.length>0?null:(0,s.jsxs)("ul",{className:"bg-zinc-950/5 dark:bg-white/5 px-4 py-3 w-[calc(19rem-1px)] left-0 right-0 bottom-0 bg-background-light dark:bg-background-dark border-t border-gray-200/50 dark:border-white/[0.07] text-sm",children:[e?.navbar?.links?.map(e=>(0,s.jsx)(u.j,{entry:{href:e.href,title:e.label,icon:e.icon},shouldAutoNavigateOnGroupClick:!0,sidebarItemStyle:"undecorated",trailingIcon:(0,s.jsx)(n.A,{className:"size-3.5 text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-400"})},e.href)),e?.navbar?.primary?.type==="button"&&(0,s.jsx)(u.j,{entry:{href:e.navbar.primary.href,title:e.navbar.primary.label},shouldAutoNavigateOnGroupClick:!0,sidebarItemStyle:"undecorated",trailingIcon:(0,s.jsx)(n.A,{className:"size-3.5 text-gray-400 dark:text-gray-500 group-hover:text-gray-500 dark:group-hover:text-gray-400"})}),e?.navbar?.primary?.type==="github"&&(0,s.jsx)(y.s,{className:"hidden lg:flex px-2"})]})}function N(){let{divisions:e}=(0,i.useContext)(l._),{docsConfig:t}=(0,i.useContext)(o.H6),{banner:r}=(0,i.useContext)(a.y),n=e.tabs.length>0;return(0,s.jsxs)("div",{className:(0,g.cn)("hidden lg:flex fixed flex-col left-0 top-0 bottom-0 w-[19rem]",!!r&&"top-10"),id:d.V.Sidebar,children:[(0,s.jsxs)("div",{className:"flex-1 overflow-y-auto px-7 py-6 bg-gray-950/5 dark:bg-white/5",id:d.V.SidebarContent,children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center gap-x-4",children:[(0,s.jsx)(h.l,{href:t?(0,p.A)(t):"/",name:t?.name,logoClassName:"px-1 h-6"}),(0,s.jsx)(k.t,{}),(0,s.jsx)(b.K,{})]}),!n&&(0,s.jsx)(j.c,{})]}),(0,s.jsx)(m.DesktopSearchEntry,{className:"mt-6",searchButtonClassName:"bg-white"}),(0,s.jsx)(f.X,{triggerClassName:"mt-4"}),(0,s.jsxs)("div",{className:"-mx-3 text-sm",id:d.V.NavigationItems,children:[(0,s.jsx)(C,{}),(0,s.jsx)(x.r,{nav:e.groupsOrPages,...(0,x.f)({theme:t?.theme})})]})]}),(0,s.jsx)(w,{})]})}function C(){let{divisions:e}=(0,i.useContext)(l._),{anchorDefault:t}=(0,c.G)();return(0,s.jsx)("div",{className:"mt-8",children:e.anchors.map(e=>(0,s.jsx)("li",{className:"list-none",children:(0,s.jsx)(v.h,{href:e.href,name:e.name,icon:"string"==typeof e.icon?e.icon:e.icon?.name,iconType:"string"==typeof e.icon?"solid":e.icon?.style,color:e.color?.light??t,isActive:e.isActive},e.name)},e.name))})}},85506:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>i});var s=r(54568),n=r(68309);function i({children:e,appearance:t,...r}){return(0,s.jsx)(n.N,{attribute:"class",disableTransitionOnChange:!0,defaultTheme:t?.default,forcedTheme:t?.strict?t.default:void 0,storageKey:"isDarkMode",themes:["dark","light","true","false","system"],value:{true:"dark",false:"light",dark:"dark",light:"light"},enableSystem:!0,...r,children:e})}r(7620)},93457:(e,t,r)=>{"use strict";r.d(t,{N:()=>s});let s={isCustom:"peer is-custom",isCenter:"peer is-center",isWide:"peer is-wide",isFrame:"peer is-frame",isNotCustom:"peer is-not-custom",isNotCenter:"peer is-not-center",isNotWide:"peer is-not-wide",isNotFrame:"peer is-not-frame",hiddenIfCustom:"peer-[.is-custom]:!hidden peer-[.is-custom]:sm:!hidden peer-[.is-custom]:md:!hidden peer-[.is-custom]:lg:!hidden peer-[.is-custom]:xl:!hidden",hiddenIfNotCustom:"peer-[.is-not-custom]:!hidden peer-[.is-not-custom]:sm:!hidden peer-[.is-not-custom]:md:!hidden peer-[.is-not-custom]:lg:!hidden peer-[.is-not-custom]:xl:!hidden",hiddenIfCenter:"peer-[.is-center]:!hidden peer-[.is-center]:sm:!hidden peer-[.is-center]:md:!hidden peer-[.is-center]:lg:!hidden peer-[.is-center]:xl:!hidden",firstChildHiddenIfCustom:"peer-[.is-custom]:[&>div:first-child]:!hidden peer-[.is-custom]:[&>div:first-child]:sm:!hidden peer-[.is-custom]:[&>div:first-child]:md:!hidden peer-[.is-custom]:[&>div:first-child]:lg:!hidden peer-[.is-custom]:[&>div:first-child]:xl:!hidden",firstChildHiddenIfCenter:"peer-[.is-center]:[&>div:first-child]:!hidden peer-[.is-center]:[&>div:first-child]:sm:!hidden peer-[.is-center]:[&>div:first-child]:md:!hidden peer-[.is-center]:[&>div:first-child]:lg:!hidden peer-[.is-center]:[&>div:first-child]:xl:!hidden"}}}]);