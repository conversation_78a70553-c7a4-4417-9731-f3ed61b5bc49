!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="aac87dbb-92b3-462b-b677-da27b10a463a",e._sentryDebugIdIdentifier="sentry-dbid-aac87dbb-92b3-462b-b677-da27b10a463a")}catch(e){}}(),(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3619],{7058:(e,t,r)=>{"use strict";r.d(t,{gQ:()=>d,l$:()=>c,zC:()=>a});var o=r(43910),s=r.n(o),n=r(37932),i=r(73874),l=r.n(i);let a="mintlify-auth-key";function d(e,t){let r=h(),o=JSON.stringify(t),n=s().AES.encrypt(o,r).toString();localStorage.setItem(e,n)}function c(e,t){let r=h(),o=localStorage.getItem(e);if(null!=o)try{let e=s().AES.decrypt(o,r).toString(s().enc.Utf8),n=JSON.parse(e);if(!t||t(n))return n}catch(e){console.log(`unable to decrypt stored credentials: ${e}`)}}let h=()=>{let e=n.A.get(a);if(void 0!==e)return e;let t=l()();return n.A.set(a,t,{secure:"https:"===location.protocol,sameSite:"strict"}),t}},16600:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var o=r(32987),s=r(60607);function n(...e){return(0,s.QP)((0,o.$)(e))}},25156:()=>{},41384:(e,t,r)=>{"use strict";r.d(t,{cD:()=>l,X$:()=>i});var o=r(78249),s=r(93696),n=r(40459);n.env.REVALIDATION_TOKEN,n.env.EXPORT_TOKEN,n.env.INTERNAL_ANALYTICS_WRITE_KEY,n.env.BASE_PATH&&(0,s.f)((0,o.M)(n.env.BASE_PATH)),n.env.HOST_NAME;let i="production";n.env.IS_MULTI_TENANT,n.env.VERCEL,n.env.SENTRY_DSN;let l={SENTRY_DSN:n.env.NEXT_PUBLIC_SENTRY_DSN,ENV:"production",TRIEVE_API_KEY:"tr-Ef0O1GG473PDFHfclCabtti5n0mHNolw",POSTHOG_KEY:n.env.NEXT_PUBLIC_POSTHOG_KEY,AUTH_ENABLED:n.env.NEXT_PUBLIC_AUTH_ENABLED,CUSTOM_JS_DISABLED:n.env.NEXT_PUBLIC_CUSTOM_JS_DISABLED,ASSET_PREFIX:"/mintlify-assets",AI_MESSAGE_HOST:"https://leaves.mintlify.com",BASE_PATH:n.env.NEXT_PUBLIC_BASE_PATH?(0,s.f)((0,o.M)(n.env.NEXT_PUBLIC_BASE_PATH)):""},a={HOSTNAME:n.env.AXIOM_DOMAIN,API_TOKEN:n.env.AXIOM_API_TOKEN,DATASET_NAME:n.env.AXIOM_DATASET_NAME};"true"===n.env.OTEL_ENABLED||l.ENV,a.HOSTNAME},60710:(e,t,r)=>{"use strict";r.d(t,{BZ:()=>V,Cc:()=>N,HD:()=>u,Hb:()=>j,Mm:()=>m,Nb:()=>l,Nl:()=>T,US:()=>i,W1:()=>E,WH:()=>_,WL:()=>c,Y3:()=>h,ZB:()=>d,Zw:()=>S,bM:()=>H,eg:()=>B,fl:()=>a,gL:()=>C,hA:()=>y,j:()=>g,k3:()=>b,mu:()=>k,oA:()=>M,oD:()=>P,pc:()=>v,qY:()=>w,rR:()=>x,sy:()=>W,t9:()=>A,uc:()=>L,w6:()=>f,wi:()=>Z,y$:()=>I,yo:()=>p});var o=r(54568),s=r(37469),n=r(16600);let i=()=>(0,o.jsx)("svg",{className:"w-3.5 overflow-visible",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 384 512",children:(0,o.jsx)("path",{d:"M345 137c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-119 119L73 103c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l119 119L39 375c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l119-119L311 409c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-119-119L345 137z"})}),l=({className:e})=>(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1024",height:"1024",viewBox:"0 0 1024 1024",fill:"currentColor",className:e,children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8 0C3.58 0 0 3.58 0 8C0 11.54 2.29 14.53 5.47 15.59C5.87 15.66 6.02 15.42 6.02 15.21C6.02 15.02 6.01 14.39 6.01 13.72C4 14.09 3.48 13.23 3.32 12.78C3.23 12.55 2.84 11.84 2.5 11.65C2.22 11.5 1.82 11.13 2.49 11.12C3.12 11.11 3.57 11.7 3.72 11.94C4.44 13.15 5.59 12.81 6.05 12.6C6.12 12.08 6.33 11.73 6.56 11.53C4.78 11.33 2.92 10.64 2.92 7.58C2.92 6.71 3.23 5.99 3.74 5.43C3.66 5.23 3.38 4.41 3.82 3.31C3.82 3.31 4.49 3.1 6.02 4.13C6.66 3.95 7.34 3.86 8.02 3.86C8.7 3.86 9.38 3.95 10.02 4.13C11.55 3.09 12.22 3.31 12.22 3.31C12.66 4.41 12.38 5.23 12.3 5.43C12.81 5.99 13.12 6.7 13.12 7.58C13.12 10.65 11.25 11.33 9.47 11.53C9.76 11.78 10.01 12.26 10.01 13.01C10.01 14.08 10 14.94 10 15.21C10 15.42 10.15 15.67 10.55 15.59C13.71 14.53 16 11.53 16 8C16 3.58 12.42 0 8 0Z",transform:"scale(64)"})}),a=({className:e})=>(0,o.jsx)("svg",{width:"3",height:"24",viewBox:"0 -9 3 24",className:(0,n.cn)("h-5 rotate-0 overflow-visible",e),children:(0,o.jsx)("path",{d:"M0 0L3 3L0 6",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round"})}),d=()=>(0,o.jsx)("svg",{className:"h-4",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",children:(0,o.jsx)("path",{d:"M0 96C0 78.3 14.3 64 32 64H416c17.7 0 32 14.3 32 32s-14.3 32-32 32H32C14.3 128 0 113.7 0 96zM0 256c0-17.7 14.3-32 32-32H416c17.7 0 32 14.3 32 32s-14.3 32-32 32H32c-17.7 0-32-14.3-32-32zM448 416c0 17.7-14.3 32-32 32H32c-17.7 0-32-14.3-32-32s14.3-32 32-32H416c17.7 0 32 14.3 32 32z"})}),c=({className:e})=>(0,o.jsx)("svg",{className:(0,n.cn)("h-2.5 text-gray-400 overflow-visible group-hover:text-gray-600 dark:text-gray-600 dark:group-hover:text-gray-400",e),xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 384 512",fill:"currentColor",children:(0,o.jsx)("path",{d:"M328 96c13.3 0 24 10.7 24 24V360c0 13.3-10.7 24-24 24s-24-10.7-24-24V177.9L73 409c-9.4 9.4-24.6 9.4-33.9 0s-9.4-24.6 0-33.9l231-231H88c-13.3 0-24-10.7-24-24s10.7-24 24-24H328z"})}),h=()=>(0,o.jsx)("svg",{className:"w-3.5 overflow-visible",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 384 512",children:(0,o.jsx)("path",{d:"M345 137c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-119 119L73 103c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l119 119L39 375c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l119-119L311 409c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-119-119L345 137z"})}),u={Ready:()=>(0,o.jsxs)("svg",{width:"20",height:"20",className:"text-primary dark:text-primary-light",viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("path",{opacity:"0.7",d:"M11.5 2.5L12.1422 1.21554C12.2896 0.920722 12.7104 0.920722 12.8578 1.21554L13.5 2.5L14.7845 3.14223C15.0793 3.28964 15.0793 3.71036 14.7845 3.85777L13.5 4.5L12.8578 5.78446C12.7104 6.07928 12.2896 6.07928 12.1422 5.78446L11.5 4.5L10.2155 3.85777C9.92072 3.71036 9.92072 3.28964 10.2155 3.14223L11.5 2.5Z",fill:"currentColor"}),(0,o.jsx)("path",{opacity:"0.7",d:"M12.5 12.5L13.1286 10.9285C13.2627 10.5932 13.7373 10.5932 13.8714 10.9285L14.5 12.5L16.0715 13.1286C16.4068 13.2627 16.4068 13.7373 16.0715 13.8714L14.5 14.5L13.8714 16.0715C13.7373 16.4068 13.2627 16.4068 13.1286 16.0715L12.5 14.5L10.9285 13.8714C10.5932 13.7373 10.5932 13.2627 10.9285 13.1286L12.5 12.5Z",fill:"currentColor"}),(0,o.jsx)("path",{d:"M4 7L5.59751 3.80498C5.76334 3.47331 6.23666 3.47331 6.40249 3.80498L8 7L11.195 8.59751C11.5267 8.76334 11.5267 9.23666 11.195 9.40249L8 11L6.40249 14.195C6.23666 14.5267 5.76334 14.5267 5.59751 14.195L4 11L0.804984 9.40249C0.473313 9.23666 0.473312 8.76334 0.804984 8.59751L4 7Z",fill:"currentColor"})]}),Generating:()=>(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsxs)("svg",{className:"size-5 text-primary/25 dark:text-primary-light/25 animate-spin fill-primary dark:fill-primary-light",viewBox:"0 0 100 100",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("path",{d:"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z",fill:"currentColor"}),(0,o.jsx)("path",{d:"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z",fill:"currentFill"})]}),(0,o.jsx)("svg",{className:"absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2 text-primary dark:text-primary-light",width:"10",height:"10",viewBox:"0 0 10 10",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,o.jsx)("path",{d:"M3.5 3.5L4.63234 0.857869C4.77087 0.534626 5.22913 0.534626 5.36766 0.857869L6.5 3.5L9.14213 4.63234C9.46537 4.77087 9.46537 5.22913 9.14213 5.36766L6.5 6.5L5.36766 9.14213C5.22913 9.46537 4.77087 9.46537 4.63234 9.14213L3.5 6.5L0.857869 5.36766C0.534626 5.22913 0.534626 4.77087 0.857869 4.63234L3.5 3.5Z",fill:"currentColor"})})]}),Generated:()=>(0,o.jsx)("svg",{width:"20",height:"20",className:"text-primary dark:text-primary-light",viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9 17C13.4183 17 17 13.4183 17 9C17 4.58172 13.4183 1 9 1C4.58172 1 1 4.58172 1 9C1 13.4183 4.58172 17 9 17ZM13.0805 6.97495C13.3428 6.65437 13.2955 6.18185 12.9749 5.91955C12.6543 5.65726 12.1818 5.70451 11.9195 6.02509L7.94432 10.8837L6.03033 8.96969C5.73744 8.6768 5.26256 8.6768 4.96967 8.96969C4.67678 9.26259 4.67678 9.73746 4.96967 10.0304L7.46967 12.5304C7.6195 12.6802 7.82573 12.7596 8.03736 12.7491C8.24899 12.7385 8.44629 12.639 8.58047 12.475L13.0805 6.97495Z",fill:"currentColor"})}),Error:()=>(0,o.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 18 18",className:"text-red-600 dark:text-red-400",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9 17C13.4183 17 17 13.4183 17 9C17 4.58172 13.4183 1 9 1C4.58172 1 1 4.58172 1 9C1 13.4183 4.58172 17 9 17ZM9 4.75C9.41421 4.75 9.75 5.08579 9.75 5.5V9C9.75 9.41421 9.41421 9.75 9 9.75C8.58579 9.75 8.25 9.41421 8.25 9V5.5C8.25 5.08579 8.58579 4.75 9 4.75ZM10 11.5C10 12.0523 9.55229 12.5 9 12.5C8.44771 12.5 8 12.0523 8 11.5C8 10.9477 8.44771 10.5 9 10.5C9.55229 10.5 10 10.9477 10 11.5Z",fill:"currentColor"})}),Retryable:()=>(0,o.jsx)("svg",{width:"20",height:"20",className:"text-orange-600 dark:text-orange-400",viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.75681 3.09907L1.17651 13.0196C0.426579 14.3528 1.39001 16.0001 2.91966 16.0001H14.0803C15.6099 16.0001 16.5733 14.3528 15.8234 13.0196L10.2431 3.09907C9.47851 1.73976 7.52142 1.73976 6.75681 3.09907ZM8.49996 5.75013C8.91418 5.75013 9.24996 6.08592 9.24996 6.50013V10.0001C9.24996 10.4143 8.91418 10.7501 8.49996 10.7501C8.08575 10.7501 7.74996 10.4143 7.74996 10.0001V6.50013C7.74996 6.08592 8.08575 5.75013 8.49996 5.75013ZM9.49996 12.5001C9.49996 13.0524 9.05225 13.5001 8.49996 13.5001C7.94768 13.5001 7.49996 13.0524 7.49996 12.5001C7.49996 11.9478 7.94768 11.5001 8.49996 11.5001C9.05225 11.5001 9.49996 11.9478 9.49996 12.5001Z",fill:"currentColor"})}),Retry:()=>(0,o.jsx)(s.A,{size:12,absoluteStrokeWidth:!0,strokeWidth:1.5})},p=({className:e})=>(0,o.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,o.jsxs)("g",{clipPath:"url(#clip0_2736_5814)",children:[(0,o.jsx)("path",{d:"M5.11133 14.4444C5.78511 14.232 6.78066 14 8.00022 14C8.70688 14 9.72555 14.0782 10.8891 14.4444",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M8 11.7778V14.0001",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M12.6668 2.44434H3.33344C2.3516 2.44434 1.55566 3.24027 1.55566 4.22211V9.99989C1.55566 10.9817 2.3516 11.7777 3.33344 11.7777H12.6668C13.6486 11.7777 14.4446 10.9817 14.4446 9.99989V4.22211C14.4446 3.24027 13.6486 2.44434 12.6668 2.44434Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),(0,o.jsx)("defs",{children:(0,o.jsx)("clipPath",{id:"clip0_2736_5814",children:(0,o.jsx)("rect",{width:"16",height:"16",fill:"white"})})})]}),C=({className:e})=>(0,o.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",stroke:"currentColor",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,o.jsxs)("g",{clipPath:"url(#clip0_2880_7340)",children:[(0,o.jsx)("path",{d:"M8 1.11133V2.00022",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M12.8711 3.12891L12.2427 3.75735",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M14.8889 8H14",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M12.8711 12.8711L12.2427 12.2427",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M8 14.8889V14",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M3.12891 12.8711L3.75735 12.2427",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M1.11133 8H2.00022",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M3.12891 3.12891L3.75735 3.75735",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M8.00043 11.7782C10.0868 11.7782 11.7782 10.0868 11.7782 8.00043C11.7782 5.91402 10.0868 4.22266 8.00043 4.22266C5.91402 4.22266 4.22266 5.91402 4.22266 8.00043C4.22266 10.0868 5.91402 11.7782 8.00043 11.7782Z",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),(0,o.jsx)("defs",{children:(0,o.jsx)("clipPath",{id:"clip0_2880_7340",children:(0,o.jsx)("rect",{width:"16",height:"16",fill:"white"})})})]}),x=({className:e})=>(0,o.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",stroke:"currentColor",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,o.jsx)("g",{clipPath:"url(#clip0_2880_7355)",children:(0,o.jsx)("path",{d:"M11.5556 10.4445C8.48717 10.4445 6.00005 7.95743 6.00005 4.88899C6.00005 3.68721 6.38494 2.57877 7.03294 1.66943C4.04272 2.22766 1.77783 4.84721 1.77783 8.0001C1.77783 11.5592 4.66317 14.4445 8.22228 14.4445C11.2196 14.4445 13.7316 12.3948 14.4525 9.62321C13.6081 10.1414 12.6187 10.4445 11.5556 10.4445Z",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),(0,o.jsx)("defs",{children:(0,o.jsx)("clipPath",{id:"clip0_2880_7355",children:(0,o.jsx)("rect",{width:"16",height:"16",fill:"white"})})})]}),w=()=>(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"gray",height:"12px",viewBox:"0 0 576 512",children:(0,o.jsx)("path",{d:"M0 256C0 167.6 71.6 96 160 96h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C98.1 144 48 194.1 48 256s50.1 112 112 112h72c13.3 0 24 10.7 24 24s-10.7 24-24 24H160C71.6 416 0 344.4 0 256zm576 0c0 88.4-71.6 160-160 160H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c61.9 0 112-50.1 112-112s-50.1-112-112-112H344c-13.3 0-24-10.7-24-24s10.7-24 24-24h72c88.4 0 160 71.6 160 160zM184 232H392c13.3 0 24 10.7 24 24s-10.7 24-24 24H184c-13.3 0-24-10.7-24-24s10.7-24 24-24z"})}),L=({className:e})=>(0,o.jsxs)("svg",{width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,o.jsxs)("g",{clipPath:"url(#clip0_15727_39602)",children:[(0,o.jsx)("path",{d:"M10.9798 6.62695L10.6889 12.1531C10.6454 12.9791 9.9625 13.627 9.13572 13.627H5.86517C5.03761 13.627 4.3555 12.9791 4.31195 12.1531L4.02106 6.62695",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M2.63892 4.68359H12.3611",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M5.75 4.68294V3.12739C5.75 2.69805 6.09844 2.34961 6.52778 2.34961H8.47222C8.90156 2.34961 9.25 2.69805 9.25 3.12739V4.68294",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),(0,o.jsx)("defs",{children:(0,o.jsx)("clipPath",{id:"clip0_15727_39602",children:(0,o.jsx)("rect",{width:"14",height:"14",fill:"white",transform:"translate(0.5 0.988281)"})})})]}),k=({className:e})=>(0,o.jsx)("svg",{width:"8",height:"24",viewBox:"0 -9 3 24",className:(0,n.cn)("transition-transform text-gray-400 overflow-visible group-hover:text-gray-600 dark:text-gray-600 dark:group-hover:text-gray-400",e),children:(0,o.jsx)("path",{d:"M0 0L3 3L0 6",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round"})}),g=({className:e})=>(0,o.jsxs)("svg",{width:"18",height:"18",viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,o.jsx)("path",{d:"M14.25 5.25H7.25C6.14543 5.25 5.25 6.14543 5.25 7.25V14.25C5.25 15.3546 6.14543 16.25 7.25 16.25H14.25C15.3546 16.25 16.25 15.3546 16.25 14.25V7.25C16.25 6.14543 15.3546 5.25 14.25 5.25Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M2.80103 11.998L1.77203 5.07397C1.61003 3.98097 2.36403 2.96397 3.45603 2.80197L10.38 1.77297C11.313 1.63397 12.19 2.16297 12.528 3.00097",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),j=({className:e})=>(0,o.jsxs)("svg",{width:"18",height:"18",viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,o.jsx)("path",{d:"M15.25 3.75H2.75C1.64543 3.75 0.75 4.64543 0.75 5.75V12.25C0.75 13.3546 1.64543 14.25 2.75 14.25H15.25C16.3546 14.25 17.25 13.3546 17.25 12.25V5.75C17.25 4.64543 16.3546 3.75 15.25 3.75Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M8.75 11.25V6.75H8.356L6.25 9.5L4.144 6.75H3.75V11.25",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M11.5 9.5L13.25 11.25L15 9.5",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M13.25 11.25V6.75",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),v=({className:e})=>(0,o.jsxs)("svg",{fill:"currentColor",fillRule:"evenodd",height:"1em",viewBox:"0 0 24 24",width:"1em",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,o.jsx)("title",{children:"OpenAI"}),(0,o.jsx)("path",{d:"M21.55 10.004a5.416 5.416 0 00-.478-4.501c-1.217-2.09-3.662-3.166-6.05-2.66A5.59 5.59 0 0010.831 1C8.39.995 6.224 2.546 5.473 4.838A5.553 5.553 0 001.76 7.496a5.487 5.487 0 00.691 6.5 5.416 5.416 0 00.477 4.502c1.217 2.09 3.662 3.165 6.05 2.66A5.586 5.586 0 0013.168 23c2.443.006 4.61-1.546 5.361-3.84a5.553 5.553 0 003.715-2.66 5.488 5.488 0 00-.693-6.497v.001zm-8.381 11.558a4.199 4.199 0 01-2.675-.954c.034-.018.093-.05.132-.074l4.44-2.53a.71.71 0 00.364-.623v-6.176l1.877 1.069c.02.01.033.029.036.05v5.115c-.003 2.274-1.87 4.118-4.174 4.123zM4.192 17.78a4.059 4.059 0 01-.498-2.763c.032.02.09.055.131.078l4.44 2.53c.225.13.504.13.73 0l5.42-3.088v2.138a.068.068 0 01-.027.057L9.9 19.288c-1.999 1.136-4.552.46-5.707-1.51h-.001zM3.023 8.216A4.15 4.15 0 015.198 6.41l-.002.151v5.06a.711.711 0 00.364.624l5.42 3.087-1.876 1.07a.067.067 0 01-.063.005l-4.489-2.559c-1.995-1.14-2.679-3.658-1.53-5.63h.001zm15.417 3.54l-5.42-3.088L14.896 7.6a.067.067 0 01.063-.006l4.489 2.557c1.998 1.14 2.683 3.662 1.529 5.633a4.163 4.163 0 01-2.174 1.807V12.38a.71.71 0 00-.363-.623zm1.867-2.773a6.04 6.04 0 00-.132-.078l-4.44-2.53a.731.731 0 00-.729 0l-5.42 3.088V7.325a.068.068 0 01.027-.057L14.1 4.713c2-1.137 4.555-.46 5.707 1.513.487.833.664 1.809.499 2.757h.001zm-11.741 3.81l-1.877-1.068a.065.065 0 01-.036-.051V6.559c.001-2.277 1.873-4.122 4.181-4.12.976 0 1.92.338 2.671.954-.034.018-.092.05-.131.073l-4.44 2.53a.71.71 0 00-.365.623l-.003 6.173v.002zm1.02-2.168L12 9.25l2.414 1.375v2.75L12 14.75l-2.415-1.375v-2.75z"})]}),f=({className:e})=>(0,o.jsxs)("svg",{fill:"currentColor",fillRule:"evenodd",height:"1em",viewBox:"0 0 24 24",width:"1em",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,o.jsx)("title",{children:"Anthropic"}),(0,o.jsx)("path",{d:"M13.827 3.52h3.603L24 20h-3.603l-6.57-16.48zm-7.258 0h3.767L16.906 20h-3.674l-1.343-3.461H5.017l-1.344 3.46H0L6.57 3.522zm4.132 9.959L8.453 7.687 6.205 13.48H10.7z"})]}),m=({className:e})=>(0,o.jsx)("svg",{width:"34",height:"38",viewBox:"0 0 34 38",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",className:e,children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.12114 0.0400391L15.919 9.98864V9.98636V0.062995H18.0209V10.0332L28.8671 0.0400391V11.3829H33.3202V27.744H28.8808V37.8442L18.0209 28.303V37.9538H15.919V28.4604L5.13338 37.96V27.744H0.680176V11.3829H5.12114V0.0400391ZM14.3344 13.4592H2.78208V25.6677H5.13074V21.8167L14.3344 13.4592ZM7.23518 22.7379V33.3271L15.919 25.6786V14.8506L7.23518 22.7379ZM18.0814 25.5775V14.8404L26.7677 22.7282V27.744H26.7789V33.219L18.0814 25.5775ZM28.8808 25.6677H31.2183V13.4592H19.752L28.8808 21.7302V25.6677ZM26.7652 11.3829V4.81584L19.6374 11.3829H26.7652ZM14.3507 11.3829H7.22306V4.81584L14.3507 11.3829Z",fill:"currentColor"})}),y=({className:e})=>(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",fillRule:"evenodd",viewBox:"0 0 24 24",className:e,children:[(0,o.jsx)("path",{d:"M15.688 2.343a2.588 2.588 0 00-3.61 0l-9.626 9.44a.863.863 0 01-1.203 0 .823.823 0 010-1.18l9.626-9.44a4.313 4.313 0 016.016 0 4.116 4.116 0 011.204 3.54 4.3 4.3 0 013.609 1.18l.05.05a4.115 4.115 0 010 5.9l-8.706 8.537a.274.274 0 000 .393l1.788 1.754a.823.823 0 010 1.18.863.863 0 01-1.203 0l-1.788-1.753a1.92 1.92 0 010-2.754l8.706-8.538a2.47 2.47 0 000-3.54l-.05-.049a2.588 2.588 0 00-3.607-.003l-7.172 7.034-.002.002-.098.097a.863.863 0 01-1.204 0 .823.823 0 010-1.18l7.273-7.133a2.47 2.47 0 00-.003-3.537z"}),(0,o.jsx)("path",{d:"M14.485 4.703a.823.823 0 000-1.18.863.863 0 00-1.204 0l-7.119 6.982a4.115 4.115 0 000 5.9 4.314 4.314 0 006.016 0l7.12-6.982a.823.823 0 000-1.18.863.863 0 00-1.204 0l-7.119 6.982a2.588 2.588 0 01-3.61 0 2.47 2.47 0 010-3.54l7.12-6.982z"})]}),M=({className:e})=>(0,o.jsxs)("svg",{height:"1em",style:{flex:"none",lineHeight:"1"},viewBox:"0 0 24 24",width:"1em",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,o.jsx)("title",{children:"Cursor"}),(0,o.jsx)("path",{d:"M11.925 24l10.425-6-10.425-6L1.5 18l10.425 6z",fill:"url(#cursor-fill-0)"}),(0,o.jsx)("path",{d:"M22.35 18V6L11.925 0v12l10.425 6z",fill:"url(#cursor-fill-1)"}),(0,o.jsx)("path",{d:"M11.925 0L1.5 6v12l10.425-6V0z",fill:"url(#cursor-fill-2)"}),(0,o.jsx)("path",{d:"M22.35 6L11.925 24V12L22.35 6z",fill:"currentColor",opacity:"0.6"}),(0,o.jsx)("path",{d:"M22.35 6l-10.425 6L1.5 6h20.85z",fill:"currentColor"}),(0,o.jsxs)("defs",{children:[(0,o.jsxs)("linearGradient",{gradientUnits:"userSpaceOnUse",id:"cursor-fill-0",x1:"11.925",x2:"11.925",y1:"12",y2:"24",children:[(0,o.jsx)("stop",{offset:".16",stopColor:"currentColor",stopOpacity:".39"}),(0,o.jsx)("stop",{offset:".658",stopColor:"currentColor",stopOpacity:".8"})]}),(0,o.jsxs)("linearGradient",{gradientUnits:"userSpaceOnUse",id:"cursor-fill-1",x1:"22.35",x2:"11.925",y1:"6.037",y2:"12.15",children:[(0,o.jsx)("stop",{offset:".182",stopColor:"currentColor",stopOpacity:".31"}),(0,o.jsx)("stop",{offset:".715",stopColor:"currentColor",stopOpacity:"0"})]}),(0,o.jsxs)("linearGradient",{gradientUnits:"userSpaceOnUse",id:"cursor-fill-2",x1:"11.925",x2:"1.5",y1:"0",y2:"18",children:[(0,o.jsx)("stop",{stopColor:"currentColor",stopOpacity:".6"}),(0,o.jsx)("stop",{offset:".667",stopColor:"currentColor",stopOpacity:".22"})]})]})]}),_=({className:e})=>(0,o.jsx)("svg",{width:"100",height:"100",viewBox:"0 0 100 100",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",className:e,children:(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M70.9119 99.3171C72.4869 99.9307 74.2828 99.8914 75.8725 99.1264L96.4608 89.2197C98.6242 88.1787 100 85.9892 100 83.5872V16.4133C100 14.0113 98.6243 11.8218 96.4609 10.7808L75.8725 0.873756C73.7862 -0.130129 71.3446 0.11576 69.5135 1.44695C69.252 1.63711 69.0028 1.84943 68.769 2.08341L29.3551 38.0415L12.1872 25.0096C10.589 23.7965 8.35363 23.8959 6.86933 25.2461L1.36303 30.2549C-0.452552 31.9064 -0.454633 34.7627 1.35853 36.417L16.2471 50.0001L1.35853 63.5832C-0.454633 65.2374 -0.452552 68.0938 1.36303 69.7453L6.86933 74.7541C8.35363 76.1043 10.589 76.2037 12.1872 74.9905L29.3551 61.9587L68.769 97.9167C69.3925 98.5406 70.1246 99.0104 70.9119 99.3171ZM75.0152 27.2989L45.1091 50.0001L75.0152 72.7012V27.2989Z"})}),A=()=>(0,o.jsx)("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,o.jsx)("path",{d:"M8 0C3.589 0 0 3.589 0 8C0 9.936 0.692 11.713 1.841 13.099L8.44 6.5H5.751C5.337 6.5 5.001 6.164 5.001 5.75C5.001 5.336 5.337 5 5.751 5H10.251C10.665 5 11.001 5.336 11.001 5.75V10.25C11.001 10.664 10.665 11 10.251 11C9.837 11 9.501 10.664 9.501 10.25V7.561L2.901 14.159C4.286 15.308 6.064 16 8 16C12.411 16 16 12.411 16 8C16 3.589 12.411 0 8 0Z",className:"fill-[#00A63E] dark:fill-[#00c951]"})}),N=()=>(0,o.jsx)("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,o.jsx)("path",{d:"M7.561 9.5H10.25C10.664 9.5 11 9.836 11 10.25C11 10.664 10.664 11 10.25 11H5.75C5.336 11 5 10.664 5 10.25V5.75C5 5.336 5.336 5 5.75 5C6.164 5 6.5 5.336 6.5 5.75V8.439L13.099 1.841C11.714 0.692 9.936 0 8 0C3.589 0 0 3.589 0 8C0 12.411 3.589 16 8 16C12.411 16 16 12.411 16 8C16 6.064 15.308 4.287 14.159 2.902L7.561 9.5Z",className:"fill-[#71717B] dark:fill-[#d4d4d8]"})}),H=()=>(0,o.jsxs)("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("g",{clipPath:"url(#clip0_17722_3167)",children:(0,o.jsx)("path",{d:"M12.8633 5.79886C12.0107 4.50456 10.1153 2.33301 6.99997 2.33301C3.88808 2.33301 1.99077 4.50379 1.13662 5.79809C0.655795 6.52881 0.655795 7.4714 1.13662 8.20126C1.98922 9.49556 3.88466 11.6671 6.99997 11.6671C10.1119 11.6671 12.0092 9.49634 12.8633 8.20204C13.3442 7.47132 13.3442 6.52873 12.8633 5.79886ZM6.99997 9.33378C5.7133 9.33378 4.66664 8.28713 4.66664 7.00045C4.66664 5.71377 5.7133 4.66712 6.99997 4.66712C8.28665 4.66712 9.33331 5.71377 9.33331 7.00045C9.33331 8.28713 8.28665 9.33378 6.99997 9.33378Z",className:"fill-black dark:fill-white"})}),(0,o.jsx)("defs",{children:(0,o.jsx)("clipPath",{id:"clip0_17722_3167",children:(0,o.jsx)("rect",{width:"14",height:"14",fill:"white"})})})]}),P=()=>(0,o.jsxs)("svg",{width:"19",height:"18",viewBox:"0 0 19 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("path",{d:"M9.5 10.75C10.4665 10.75 11.25 9.9665 11.25 9C11.25 8.0335 10.4665 7.25 9.5 7.25C8.5335 7.25 7.75 8.0335 7.75 9C7.75 9.9665 8.5335 10.75 9.5 10.75Z",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M6.14101 12.3586C4.28601 10.5036 4.28601 7.49563 6.14101 5.64062",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M4.02001 14.4795C0.993006 11.4535 0.993006 6.54553 4.02001 3.51953",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M12.859 12.3586C14.714 10.5036 14.714 7.49563 12.859 5.64062",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M14.98 14.4795C18.007 11.4525 18.007 6.54553 14.98 3.51953",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),E=()=>(0,o.jsxs)("svg",{width:"17",height:"16",viewBox:"0 0 17 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("path",{d:"M7.263 9.237C6.946 8.92 6.75 8.483 6.75 8C6.75 7.034 7.534 6.25 8.5 6.25C8.983 6.25 9.421 6.446 9.737 6.763",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M5.14101 11.3586C3.28601 9.50363 3.28601 6.49563 5.14101 4.64062",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M3.02001 13.4795C-0.00699425 10.4535 -0.00699425 5.54553 3.02001 2.51953",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M11.8589 11.3591C13.0649 10.1531 13.4869 8.45913 13.1239 6.91113",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M13.98 13.4801C16.388 11.0721 16.88 7.4731 15.456 4.5791",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M1.5 15L15.5 1",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),V=({className:e})=>(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 18 18",className:e,children:(0,o.jsxs)("g",{fill:"currentColor",children:[(0,o.jsx)("path",{d:"M5.658,2.99l-1.263-.421-.421-1.263c-.137-.408-.812-.408-.949,0l-.421,1.263-1.263,.421c-.204,.068-.342,.259-.342,.474s.138,.406,.342,.474l1.263,.421,.421,1.263c.068,.204,.26,.342,.475,.342s.406-.138,.475-.342l.421-1.263,1.263-.421c.204-.068,.342-.259,.342-.474s-.138-.406-.342-.474Z",fill:"currentColor","data-stroke":"none",stroke:"none"}),(0,o.jsx)("polygon",{points:"9.5 2.75 11.412 7.587 16.25 9.5 11.412 11.413 9.5 16.25 7.587 11.413 2.75 9.5 7.587 7.587 9.5 2.75",fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5"})]})}),W=({className:e})=>(0,o.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,o.jsx)("path",{d:"M11.9769 6.44434L11.6444 12.7599C11.5947 13.7039 10.8142 14.4443 9.86932 14.4443H6.13154C5.18576 14.4443 4.40621 13.7039 4.35643 12.7599L4.02399 6.44434",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M2.44446 4.22217H13.5556",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M6 4.22233V2.44455C6 1.95389 6.39822 1.55566 6.88889 1.55566H9.11111C9.60178 1.55566 10 1.95389 10 2.44455V4.22233",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M6.55554 7.77783L6.77776 11.7778",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M9.44445 7.77783L9.22223 11.7778",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),B=({className:e})=>(0,o.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,o.jsx)("path",{d:"M12.4444 3.55566L3.55554 12.4446",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M3.55554 3.55566L12.4444 12.4446",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),S=({className:e})=>(0,o.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,o.jsx)("path",{d:"M5.11115 6H6.88892",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M5.11115 8.66675H10.8889",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M5.11115 11.3335H10.8889",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M2.44446 12.6668V3.33344C2.44446 2.35122 3.24001 1.55566 4.22224 1.55566H9.18757C9.42312 1.55566 9.64979 1.649 9.81601 1.81611L13.2951 5.29522C13.4622 5.46233 13.5556 5.68811 13.5556 5.92366V12.6668C13.5556 13.649 12.76 14.4446 11.7778 14.4446H4.22224C3.24001 14.4446 2.44446 13.649 2.44446 12.6668Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M13.4755 5.55557H10.4444C9.95376 5.55557 9.55554 5.15735 9.55554 4.66668V1.64624",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),b=({className:e})=>(0,o.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,o.jsx)("path",{d:"M11.7778 2.44434H4.22224C3.2404 2.44434 2.44446 3.24027 2.44446 4.22211V11.7777C2.44446 12.7595 3.2404 13.5554 4.22224 13.5554H11.7778C12.7596 13.5554 13.5556 12.7595 13.5556 11.7777V4.22211C13.5556 3.24027 12.7596 2.44434 11.7778 2.44434Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M8.66669 10.8887H10.8889",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M5.11115 10.8888L7.33337 8.66656L5.11115 6.44434",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),I=({className:e})=>(0,o.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:e,children:[(0,o.jsx)("path",{d:"M3.33337 5.55542H13.5556",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M2.44446 10.4443H12.6667",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M6.78491 2.44434L4.70135 13.5554",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.jsx)("path",{d:"M11.2987 2.44434L9.21515 13.5554",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]});function T(){return(0,o.jsxs)("svg",{className:"w-4 h-4 text-inherit animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,o.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,o.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}let Z=({className:e})=>(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:e,children:[(0,o.jsx)("path",{d:"M4 11a9 9 0 0 1 9 9"}),(0,o.jsx)("path",{d:"M4 4a16 16 0 0 1 16 16"}),(0,o.jsx)("circle",{cx:"5",cy:"19",r:"1"})]})},67290:(e,t,r)=>{"use strict";r.d(t,{A:()=>a,o:()=>l});var o=r(37932),s=r(42683),n=r(90487),i=r(41384);let l="mintlify-pkce-code-verifier";async function a(e){let{code_challenge:t,code_verifier:r}=await (0,s.Ay)(),a=new URL(e.authorizationUrl);a.searchParams.append("response_type","code"),a.searchParams.append("client_id",e.clientId),a.searchParams.append("redirect_uri",window.location.origin+i.cD.BASE_PATH+n.AX),e.scopes.length&&a.searchParams.append("scope",e.scopes.join(" ")),a.searchParams.append("code_challenge",t),a.searchParams.append("code_challenge_method","S256");let d=new Date(Date.now()+10*n.Az);o.A.set(l,r,{secure:!0,expires:d}),window.location.href=a.href}},71197:(e,t,r)=>{"use strict";r.d(t,{ApiReferenceProvider:()=>x,AuthProvider:()=>C,DeploymentMetadataProvider:()=>u,DocsConfigProvider:()=>p,Em:()=>l,H6:()=>a,NQ:()=>i,PageProvider:()=>h,cy:()=>d,fq:()=>c});var o=r(54568),s=r(7620),n=r(74402);let i=(0,s.createContext)({pageMetadata:{}}),l=(0,s.createContext)({}),a=(0,s.createContext)({}),d=(0,s.createContext)({}),c=(0,s.createContext)({apiReferenceData:{}});function h({value:e,children:t}){return(0,o.jsx)(i.Provider,{value:e,children:t})}function u({value:e,children:t}){return(0,o.jsx)(l.Provider,{value:e,children:t})}function p({value:e,children:t}){return(0,o.jsx)(a.Provider,{value:e,children:t})}function C({value:e,children:t}){let{userInfo:r,isFetchingUserInfo:s}=(0,n.P)(e.userAuth);return(0,o.jsx)(d.Provider,{value:{...e,userInfo:r,isFetchingUserInfo:s},children:t})}function x({value:e,children:t}){return(0,o.jsx)(c.Provider,{value:e,children:t})}i.displayName="PageContext",l.displayName="DeploymentMetadataContext",a.displayName="DocsConfigContext",d.displayName="AuthContext",c.displayName="ApiReferenceContext"},74402:(e,t,r)=>{"use strict";r.d(t,{P:()=>j});var o=r(62942),s=r(7620),n=r(7058),i=r(41384),l=r(90487);let a="mintlify-user-info";function d(e){if(!e)return null;let t=(0,n.l$)(a,h);if(!t)return null;let r="shared-session"===e.type?l.yU:l.eP,o=t.data.expiresAt?1e3*t.data.expiresAt:t.retrievedAt+r;return Date.now()>o||e.invalidatedAt&&e.invalidatedAt>t.retrievedAt?null:t.data}function c(e){return!!e&&"object"==typeof e&&(!("expiresAt"in e)||"number"==typeof e.expiresAt)&&(!("groups"in e)||Array.isArray(e.groups)&&e.groups.every(e=>"string"==typeof e))&&(!("content"in e)||!!e.content&&"object"==typeof e.content)&&(!("apiPlaygroundInputs"in e)||!!e.apiPlaygroundInputs&&"object"==typeof e.apiPlaygroundInputs&&(!("header"in e.apiPlaygroundInputs)||!!e.apiPlaygroundInputs.header&&"object"==typeof e.apiPlaygroundInputs.header)&&(!("cookie"in e.apiPlaygroundInputs)||!!e.apiPlaygroundInputs.cookie&&"object"==typeof e.apiPlaygroundInputs.cookie)&&(!("query"in e.apiPlaygroundInputs)||!!e.apiPlaygroundInputs.query&&"object"==typeof e.apiPlaygroundInputs.query)&&(!("server"in e.apiPlaygroundInputs)||!!e.apiPlaygroundInputs.server&&"object"==typeof e.apiPlaygroundInputs.server))}function h(e){return!!e&&"object"==typeof e&&"retrievedAt"in e&&"number"==typeof e.retrievedAt&&"data"in e&&c(e.data)}async function u(e,t,o){let s,[n,i]=function(e){if(p(e))return[e,null];let t=new URLSearchParams(e),r=t.get("jwt");return r&&p(r)?[r,t.get("anchor")]:[null,null]}(location.hash.slice(1));if(!n){let t=d(e);return t?void o(t):void localStorage.removeItem(a)}let{importSPKI:l,jwtVerify:h}=await r.e(6127).then(r.bind(r,36127));for(let t of e.signingKeys)try{let e=178===t.publicKey.length?"ES256":"EdDSA",r=await l(t.publicKey,e),{payload:o}=await h(n,r);for(let e of["aud","exp","iat","iss","jti","nbf","sub"])delete o[e];s=o;break}catch(e){console.error(e)}c(s)&&o(s);let u=i?`#${i}`:"";t.replace(`${location.pathname}${location.search}${u}`)}function p(e){return e.startsWith("ey")&&e.match(/\./g)?.length===2}var C=r(37932),x=r(67290);async function w(e,t,r){let o=new URL(window.location.href),s=o.searchParams.get("code");if(!s){let t=d(e);return t?void r(t):void localStorage.removeItem(a)}let n=await L(e,s);c(n)&&r(n),o.search="",t.push(o.toString())}async function L(e,t){let r,o,s=C.A.get(x.o);if(!s)return void console.error("missing code verifier");try{r=await k(e,t,s)}catch(e){console.error(`unable to complete oauth exchange request: ${e}`);return}try{let t=await fetch(e.apiUrl,{headers:{Authorization:`Bearer ${r}`}});o=await t.json()}catch(e){console.error(`unable to complete oauth api request: ${e}`);return}return o}async function k(e,t,r){let o=new URL(e.tokenUrl);o.searchParams.append("grant_type","authorization_code"),o.searchParams.append("client_id",e.clientId),o.searchParams.append("redirect_uri",window.location.origin),o.searchParams.append("code",t),o.searchParams.append("code_verifier",r);let s=await fetch(o,{method:"POST"}),{access_token:n}=await s.json();if("string"!=typeof n)throw Error("unable to parse access_token from oauth exchange response");return n}async function g(e,t){let r,o=d(e);if(o)return void t(o);localStorage.removeItem(a);try{let t=await fetch(e.apiUrl,{credentials:"include"});if(!t.ok)return;r=await t.json()}catch{return}c(r)&&t(r)}function j(e){let t=(0,o.useRouter)(),[r,l]=(0,s.useState)(),[d,c]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{let r=e=>{l(e);let t={retrievedAt:Date.now(),data:e};(0,n.gQ)(a,t)},o=async()=>{try{c(!0);let e=await fetch("/api/user"),t=await e.json();null!=t.user&&r(t.user)}catch{}finally{c(!1)}};switch((i.cD.AUTH_ENABLED||"cli"===i.cD.ENV||"development"===i.cD.ENV)&&o(),e?.type){case"shared-session":return void g(e,r);case"jwt":return void u(e,t,r);case"oauth":return void w(e,t,r);case void 0:return void localStorage.removeItem(a)}},[]),{userInfo:r,isFetchingUserInfo:d}}},78249:(e,t,r)=>{"use strict";function o(e){return e.startsWith("/")?e:`/${e}`}r.d(t,{M:()=>o})},90487:(e,t,r)=>{"use strict";r.d(t,{AX:()=>c,Az:()=>i,HL:()=>n,S5:()=>h,db:()=>s,eP:()=>a,rQ:()=>d,yU:()=>l});var o=r(41384);let s="production"===o.X$,n="development"===o.X$,i=6e4,l=864e5,a=12096e5,d=36e5,c="/mintlify-oauth-callback",h=5e4},93696:(e,t,r)=>{"use strict";function o(e){return!e||e.endsWith("/")?e.slice(0,-1):e}r.d(t,{f:()=>o})}}]);