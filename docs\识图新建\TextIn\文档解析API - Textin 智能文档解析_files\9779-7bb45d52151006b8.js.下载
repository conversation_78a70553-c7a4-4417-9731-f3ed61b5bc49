!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="63dfe25a-7c9f-40e9-9af4-d5ec565599e6",e._sentryDebugIdIdentifier="sentry-dbid-63dfe25a-7c9f-40e9-9af4-d5ec565599e6")}catch(e){}}(),(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9779],{8139:(e,t,r)=>{"use strict";r.d(t,{default:()=>o.a});var n=r(44760),o=r.n(n)},9481:(e,t,r)=>{"use strict";function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}r.d(t,{A:()=>k});var o=r(7620),i=o.useLayoutEffect,a=function(e){var t=o.useRef(e);return i(function(){t.current=e}),t},s=function(e,t){if("function"==typeof e)return void e(t);e.current=t},u=function(e,t){var r=o.useRef();return o.useCallback(function(n){e.current=n,r.current&&s(r.current,null),r.current=t,t&&s(t,n)},[t])},c={"min-height":"0","max-height":"none",height:"0",visibility:"hidden",overflow:"hidden",position:"absolute","z-index":"-1000",top:"0",right:"0",display:"block"},l=function(e){Object.keys(c).forEach(function(t){e.style.setProperty(t,c[t],"important")})},d=null,f=function(e,t){var r=e.scrollHeight;return"border-box"===t.sizingStyle.boxSizing?r+t.borderSize:r-t.paddingSize},p=function(){},g=["borderBottomWidth","borderLeftWidth","borderRightWidth","borderTopWidth","boxSizing","fontFamily","fontSize","fontStyle","fontWeight","letterSpacing","lineHeight","paddingBottom","paddingLeft","paddingRight","paddingTop","tabSize","textIndent","textRendering","textTransform","width","wordBreak","wordSpacing","scrollbarGutter"],h=!!document.documentElement.currentStyle,v=function(e){var t=window.getComputedStyle(e);if(null===t)return null;var r=g.reduce(function(e,r){return e[r]=t[r],e},{}),n=r.boxSizing;if(""===n)return null;h&&"border-box"===n&&(r.width=parseFloat(r.width)+parseFloat(r.borderRightWidth)+parseFloat(r.borderLeftWidth)+parseFloat(r.paddingRight)+parseFloat(r.paddingLeft)+"px");var o=parseFloat(r.paddingBottom)+parseFloat(r.paddingTop),i=parseFloat(r.borderBottomWidth)+parseFloat(r.borderTopWidth);return{sizingStyle:r,paddingSize:o,borderSize:i}};function b(e,t,r){var n=a(r);o.useLayoutEffect(function(){var r=function(e){return n.current(e)};if(e)return e.addEventListener(t,r),function(){return e.removeEventListener(t,r)}},[])}var m=function(e,t){b(document.body,"reset",function(r){e.current.form===r.target&&t(r)})},y=function(e){b(window,"resize",e)},_=function(e){b(document.fonts,"loadingdone",e)},w=["cacheMeasurements","maxRows","minRows","onChange","onHeightChange"],k=o.forwardRef(function(e,t){var r=e.cacheMeasurements,i=e.maxRows,a=e.minRows,s=e.onChange,c=void 0===s?p:s,g=e.onHeightChange,h=void 0===g?p:g,b=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,w),k=void 0!==b.value,j=o.useRef(null),x=u(j,t),E=o.useRef(0),O=o.useRef(),S=function(){var e=j.current,t=r&&O.current?O.current:v(e);if(t){O.current=t;var n,o,s,u,c,p,g,b,m,y,_,w=(n=e.value||e.placeholder||"x",void 0===(o=a)&&(o=1),void 0===(s=i)&&(s=1/0),d||((d=document.createElement("textarea")).setAttribute("tabindex","-1"),d.setAttribute("aria-hidden","true"),l(d)),null===d.parentNode&&document.body.appendChild(d),u=t.paddingSize,c=t.borderSize,g=(p=t.sizingStyle).boxSizing,Object.keys(p).forEach(function(e){d.style[e]=p[e]}),l(d),d.value=n,b=f(d,t),d.value=n,b=f(d,t),d.value="x",y=(m=d.scrollHeight-u)*o,"border-box"===g&&(y=y+u+c),b=Math.max(y,b),_=m*s,"border-box"===g&&(_=_+u+c),[b=Math.min(_,b),m]),k=w[0],x=w[1];E.current!==k&&(E.current=k,e.style.setProperty("height",k+"px","important"),h(k,{rowHeight:x}))}};return o.useLayoutEffect(S),m(j,function(){if(!k){var e=j.current.value;requestAnimationFrame(function(){var t=j.current;t&&e!==t.value&&S()})}}),y(S),_(S),o.createElement("textarea",n({},b,{onChange:function(e){k||S(),c(e)},ref:x}))})},44760:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return b},handleClientScriptLoad:function(){return g},initScriptLoader:function(){return h}});let n=r(16841),o=r(33378),i=r(54568),a=n._(r(97509)),s=o._(r(7620)),u=r(45227),c=r(86575),l=r(95307),d=new Map,f=new Set,p=e=>{let{src:t,id:r,onLoad:n=()=>{},onReady:o=null,dangerouslySetInnerHTML:i,children:s="",strategy:u="afterInteractive",onError:l,stylesheets:p}=e,g=r||t;if(g&&f.has(g))return;if(d.has(t)){f.add(g),d.get(t).then(n,l);return}let h=()=>{o&&o(),f.add(g)},v=document.createElement("script"),b=new Promise((e,t)=>{v.addEventListener("load",function(t){e(),n&&n.call(this,t),h()}),v.addEventListener("error",function(e){t(e)})}).catch(function(e){l&&l(e)});i?(v.innerHTML=i.__html||"",h()):s?(v.textContent="string"==typeof s?s:Array.isArray(s)?s.join(""):"",h()):t&&(v.src=t,d.set(t,b)),(0,c.setAttributesFromProps)(v,e),"worker"===u&&v.setAttribute("type","text/partytown"),v.setAttribute("data-nscript",u),p&&(e=>{if(a.default.preinit)return e.forEach(e=>{a.default.preinit(e,{as:"style"})});{let t=document.head;e.forEach(e=>{let r=document.createElement("link");r.type="text/css",r.rel="stylesheet",r.href=e,t.appendChild(r)})}})(p),document.body.appendChild(v)};function g(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,l.requestIdleCallback)(()=>p(e))}):p(e)}function h(e){e.forEach(g),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");f.add(t)})}function v(e){let{id:t,src:r="",onLoad:n=()=>{},onReady:o=null,strategy:c="afterInteractive",onError:d,stylesheets:g,...h}=e,{updateScripts:v,scripts:b,getIsSsr:m,appDir:y,nonce:_}=(0,s.useContext)(u.HeadManagerContext);_=h.nonce||_;let w=(0,s.useRef)(!1);(0,s.useEffect)(()=>{let e=t||r;w.current||(o&&e&&f.has(e)&&o(),w.current=!0)},[o,t,r]);let k=(0,s.useRef)(!1);if((0,s.useEffect)(()=>{if(!k.current){if("afterInteractive"===c)p(e);else"lazyOnload"===c&&("complete"===document.readyState?(0,l.requestIdleCallback)(()=>p(e)):window.addEventListener("load",()=>{(0,l.requestIdleCallback)(()=>p(e))}));k.current=!0}},[e,c]),("beforeInteractive"===c||"worker"===c)&&(v?(b[c]=(b[c]||[]).concat([{id:t,src:r,onLoad:n,onReady:o,onError:d,...h,nonce:_}]),v(b)):m&&m()?f.add(t||r):m&&!m()&&p({...e,nonce:_})),y){if(g&&g.forEach(e=>{a.default.preinit(e,{as:"style"})}),"beforeInteractive"===c)if(!r)return h.dangerouslySetInnerHTML&&(h.children=h.dangerouslySetInnerHTML.__html,delete h.dangerouslySetInnerHTML),(0,i.jsx)("script",{nonce:_,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...h,id:t}])+")"}});else return a.default.preload(r,h.integrity?{as:"script",integrity:h.integrity,nonce:_,crossOrigin:h.crossOrigin}:{as:"script",nonce:_,crossOrigin:h.crossOrigin}),(0,i.jsx)("script",{nonce:_,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([r,{...h,id:t}])+")"}});"afterInteractive"===c&&r&&a.default.preload(r,h.integrity?{as:"script",integrity:h.integrity,nonce:_,crossOrigin:h.crossOrigin}:{as:"script",nonce:_,crossOrigin:h.crossOrigin})}return null}Object.defineProperty(v,"__nextScript",{value:!0});let b=v;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51844:(e,t,r)=>{"use strict";r.d(t,{F:()=>b});var n=r(7620),o=Object.defineProperty,i=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable,u=(e,t,r)=>t in e?o(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,c=(e,t)=>{for(var r in t||(t={}))a.call(t,r)&&u(e,r,t[r]);if(i)for(var r of i(t))s.call(t,r)&&u(e,r,t[r]);return e},l=(e,t)=>{let r="[react-use-intercom]";switch(e){case"info":default:console.log(`${r} ${t}`);break;case"warn":console.warn(`${r} ${t}`);break;case"error":console.error(`${r} ${t}`)}},d="undefined"==typeof window,f=e=>(Object.keys(e).forEach(t=>{e[t]&&"object"==typeof e[t]?f(e[t]):void 0===e[t]&&delete e[t]}),e),p=(e,...t)=>{if(!d&&window.Intercom)return window.Intercom.apply(null,[e,...t]);l("error",`${e} Intercom instance is not initalized yet`)},g=n.createContext(void 0),h=e=>c({company_id:e.companyId,name:e.name,created_at:e.createdAt,plan:e.plan,monthly_spend:e.monthlySpend,user_count:e.userCount,size:e.size,website:e.website,industry:e.industry},e.customAttributes),v=e=>f(c(c({},(e=>({custom_launcher_selector:e.customLauncherSelector,alignment:e.alignment,vertical_padding:e.verticalPadding,horizontal_padding:e.horizontalPadding,hide_default_launcher:e.hideDefaultLauncher,session_duration:e.sessionDuration,action_color:e.actionColor,background_color:e.backgroundColor}))(e)),(e=>{var t;return c({email:e.email,user_id:e.userId,created_at:e.createdAt,name:e.name,phone:e.phone,last_request_at:e.lastRequestAt,unsubscribed_from_emails:e.unsubscribedFromEmails,language_override:e.languageOverride,utm_campaign:e.utmCampaign,utm_content:e.utmContent,utm_medium:e.utmMedium,utm_source:e.utmSource,utm_term:e.utmTerm,avatar:e.avatar&&(e=>({type:e.type,image_url:e.imageUrl}))(e.avatar),user_hash:e.userHash,company:e.company&&h(e.company),companies:null==(t=e.companies)?void 0:t.map(h)},e.customAttributes)})(e))),b=e=>{var{appId:t,autoBoot:r=!1,autoBootProps:o,children:u,onHide:f,onShow:h,onUnreadCountChange:b,onUserEmailSupplied:m,shouldInitialize:y=!d,apiBase:_,initializeDelay:w}=e,k=((e,t)=>{var r={};for(var n in e)a.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&i)for(var n of i(e))0>t.indexOf(n)&&s.call(e,n)&&(r[n]=e[n]);return r})(e,["appId","autoBoot","autoBootProps","children","onHide","onShow","onUnreadCountChange","onUserEmailSupplied","shouldInitialize","apiBase","initializeDelay"]);let j=n.useRef(!1),x=n.useRef(!1),E=Object.keys(k).filter(e=>!e.startsWith("data-"));E.length>0&&l("warn",`some invalid props were passed to IntercomProvider. Please check following props: ${E.join(", ")}.`);let O=n.useCallback(e=>{if(!window.Intercom&&!y)return void l("warn","Intercom instance is not initialized because `shouldInitialize` is set to `false` in `IntercomProvider`");let r=c(c({app_id:t},_&&{api_base:_}),e&&v(e));window.intercomSettings=r,p("boot",r),j.current=!0},[_,t,y]),[S,R]=n.useState(!1),P=n.useCallback(()=>{R(!1),f&&f()},[f,R]),I=n.useCallback(()=>{R(!0),h&&h()},[h,R]);d||!y||x.current||(((e,t=0)=>{var r=window,n=r.Intercom;if("function"==typeof n)n("reattach_activator"),n("update",r.intercomSettings);else{var o=document,i=function(){i.c(arguments)};i.q=[],i.c=function(e){i.q.push(e)},r.Intercom=i;var a=function(){setTimeout(function(){var t=o.createElement("script");t.type="text/javascript",t.async=!0,t.src="https://widget.intercom.io/widget/"+e;var r=o.getElementsByTagName("script")[0];r.parentNode.insertBefore(t,r)},t)};"complete"===document.readyState?a():r.attachEvent?r.attachEvent("onload",a):r.addEventListener("load",a,!1)}})(t,w),p("onHide",P),p("onShow",I),p("onUserEmailSupplied",m),b&&p("onUnreadCountChange",b),r&&O(o),x.current=!0);let M=n.useCallback((e,t)=>window.Intercom||y?j.current?t():void l("warn",`"${e}" was called but Intercom has not booted yet. Please call 'boot' before calling '${e}' or set 'autoBoot' to true in the IntercomProvider.`):void l("warn","Intercom instance is not initialized because `shouldInitialize` is set to `false` in `IntercomProvider`"),[y]),L=n.useCallback(()=>{j.current&&(p("shutdown"),delete window.intercomSettings,j.current=!1)},[]),C=n.useCallback(()=>{j.current&&(p("shutdown"),delete window.Intercom,delete window.intercomSettings,j.current=!1)},[]),D=n.useCallback(()=>{M("update",()=>{p("update",{last_request_at:Math.floor(new Date().getTime()/1e3)})})},[M]),A=n.useCallback(e=>{M("update",()=>{if(!e)return void D();let t=v(e);window.intercomSettings=c(c({},window.intercomSettings),t),p("update",t)})},[M,D]),T=n.useCallback(()=>{M("hide",()=>{p("hide")})},[M]),N=n.useCallback(()=>{M("show",()=>p("show"))},[M]),q=n.useCallback(()=>{M("showMessages",()=>{p("showMessages")})},[M]),H=n.useCallback(e=>{M("showNewMessage",()=>{e?p("showNewMessage",e):p("showNewMessage")})},[M]),U=n.useCallback(()=>M("getVisitorId",()=>p("getVisitorId")),[M]),F=n.useCallback(e=>{M("startTour",()=>{p("startTour",e)})},[M]),z=n.useCallback(e=>{M("startChecklist",()=>{p("startChecklist",e)})},[M]),W=n.useCallback((e,t)=>{M("trackEvent",()=>{t?p("trackEvent",e,t):p("trackEvent",e)})},[M]),B=n.useCallback(e=>M("showArticle",()=>{p("showArticle",e)}),[M]),X=n.useCallback(e=>M("showSpace",()=>{p("showSpace",e)}),[M]),G=n.useCallback(e=>{M("startSurvey",()=>{p("startSurvey",e)})},[M]),$=n.useCallback(e=>M("showNews",()=>{p("showNews",e)}),[M]),K=n.useMemo(()=>({boot:O,shutdown:L,hardShutdown:C,update:A,hide:T,show:N,isOpen:S,showMessages:q,showNewMessage:H,getVisitorId:U,startTour:F,startChecklist:z,trackEvent:W,showArticle:B,startSurvey:G,showSpace:X,showNews:$}),[O,L,C,A,T,N,S,q,H,U,F,z,W,B,G,X,$]);return n.createElement(g.Provider,{value:K},u)}},55918:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(98889).A)("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2a3.13 3.13 0 0 1 3 3.88Z",key:"emmmcr"}]])},58824:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(98889).A)("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]])},70170:function(e){e.exports=function(){var e={"./node_modules/@babel/runtime/helpers/arrayLikeToArray.js":function(e,t){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n},e.exports.default=e.exports,e.exports.__esModule=!0},"./node_modules/@babel/runtime/helpers/arrayWithoutHoles.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/arrayLikeToArray.js");e.exports=function(e){if(Array.isArray(e))return n(e)},e.exports.default=e.exports,e.exports.__esModule=!0},"./node_modules/@babel/runtime/helpers/classCallCheck.js":function(e,t){e.exports=function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")},e.exports.default=e.exports,e.exports.__esModule=!0},"./node_modules/@babel/runtime/helpers/createClass.js":function(e,t){function r(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}e.exports=function(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),e},e.exports.default=e.exports,e.exports.__esModule=!0},"./node_modules/@babel/runtime/helpers/defineProperty.js":function(e,t){e.exports=function(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e},e.exports.default=e.exports,e.exports.__esModule=!0},"./node_modules/@babel/runtime/helpers/interopRequireDefault.js":function(e,t){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.default=e.exports,e.exports.__esModule=!0},"./node_modules/@babel/runtime/helpers/interopRequireWildcard.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/typeof.js").default;function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(o=function(e){return e?r:t})(e)}e.exports=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==n(e)&&"function"!=typeof e)return{default:e};var r=o(t);if(r&&r.has(e))return r.get(e);var i={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&Object.prototype.hasOwnProperty.call(e,s)){var u=a?Object.getOwnPropertyDescriptor(e,s):null;u&&(u.get||u.set)?Object.defineProperty(i,s,u):i[s]=e[s]}return i.default=e,r&&r.set(e,i),i},e.exports.default=e.exports,e.exports.__esModule=!0},"./node_modules/@babel/runtime/helpers/iterableToArray.js":function(e,t){e.exports=function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.default=e.exports,e.exports.__esModule=!0},"./node_modules/@babel/runtime/helpers/nonIterableSpread.js":function(e,t){e.exports=function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.default=e.exports,e.exports.__esModule=!0},"./node_modules/@babel/runtime/helpers/objectWithoutProperties.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js");e.exports=function(e,t){if(null==e)return{};var r,o,i=n(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)r=a[o],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i},e.exports.default=e.exports,e.exports.__esModule=!0},"./node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js":function(e,t){e.exports=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o},e.exports.default=e.exports,e.exports.__esModule=!0},"./node_modules/@babel/runtime/helpers/toConsumableArray.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/arrayWithoutHoles.js"),o=r("./node_modules/@babel/runtime/helpers/iterableToArray.js"),i=r("./node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js"),a=r("./node_modules/@babel/runtime/helpers/nonIterableSpread.js");e.exports=function(e){return n(e)||o(e)||i(e)||a()},e.exports.default=e.exports,e.exports.__esModule=!0},"./node_modules/@babel/runtime/helpers/typeof.js":function(e,t){function r(t){return"function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?e.exports=r=function(e){return typeof e}:e.exports=r=function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.default=e.exports,e.exports.__esModule=!0,r(t)}e.exports=r,e.exports.default=e.exports,e.exports.__esModule=!0},"./node_modules/@babel/runtime/helpers/unsupportedIterableToArray.js":function(e,t,r){var n=r("./node_modules/@babel/runtime/helpers/arrayLikeToArray.js");e.exports=function(e,t){if(e){if("string"==typeof e)return n(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return n(e,t)}},e.exports.default=e.exports,e.exports.__esModule=!0},"./node_modules/webpack/buildin/global.js":function(e,t){var r;r=function(){return this}();try{r=r||Function("return this")()}catch(e){"object"==typeof window&&(r=window)}e.exports=r},"./packages/@logrocket/console/src/index.js":function(e,t,r){"use strict";var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.default=n(r("./packages/@logrocket/console/src/registerConsole.js")).default,e.exports=t.default},"./packages/@logrocket/console/src/registerConsole.js":function(e,t,r){"use strict";var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=[];return["log","warn","info","error","debug"].forEach(function(r){t.push((0,i.default)(console,r,function(){for(var t=arguments.length,n=Array(t),i=0;i<t;i++)n[i]=arguments[i];e.addEvent("lr.core.LogEvent",function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=t.isEnabled;return"object"===(0,o.default)(i)&&!1===i[r]||!1===i?null:("error"===r&&t.shouldAggregateConsoleErrors&&a.Capture.captureMessage(e,n[0],n,{},!0),{logLevel:r.toUpperCase(),args:n})})}))}),function(){t.forEach(function(e){return e()})}};var o=n(r("./node_modules/@babel/runtime/helpers/typeof.js")),i=n(r("./packages/@logrocket/utils/src/enhanceFunc.js")),a=r("./packages/@logrocket/exceptions/src/index.js");e.exports=t.default},"./packages/@logrocket/exceptions/src/Capture.js":function(e,t,r){"use strict";var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.captureMessage=function(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],i={exceptionType:o?"CONSOLE":"MESSAGE",message:t,messageArgs:r,browserHref:window.location?window.location.href:""};u(i,n),e.addEvent("lr.core.Exception",function(){return i})},t.captureException=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,o=n||i.default.computeStackTrace(t),s={exceptionType:"WINDOW",errorType:o.name,message:o.message,browserHref:window.location?window.location.href:""};u(s,r);var c={_stackTrace:(0,a.default)(o)};e.addEvent("lr.core.Exception",function(){return s},c)};var o=n(r("./node_modules/@babel/runtime/helpers/typeof.js")),i=n(r("./packages/@logrocket/utils/src/TraceKit.js")),a=n(r("./packages/@logrocket/exceptions/src/stackTraceFromError.js"));function s(e){return/boolean|number|string/.test((0,o.default)(e))}function u(e,t){if(t){for(var r=0,n=["level","logger"];r<n.length;r++){var o=n[r],i=t[o];s(i)&&(e[o]=i.toString())}for(var a=0,u=["tags","extra"];a<u.length;a++){for(var c=u[a],l=t[c]||{},d={},f=0,p=Object.keys(l);f<p.length;f++){var g=p[f],h=l[g];s(h)&&(d[g.toString()]=h.toString())}e[c]=d}}}},"./packages/@logrocket/exceptions/src/index.js":function(e,t,r){"use strict";var n=r("./node_modules/@babel/runtime/helpers/interopRequireWildcard.js"),o=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"registerExceptions",{enumerable:!0,get:function(){return i.default}}),t.Capture=void 0;var i=o(r("./packages/@logrocket/exceptions/src/registerExceptions.js"));t.Capture=n(r("./packages/@logrocket/exceptions/src/Capture.js"))},"./packages/@logrocket/exceptions/src/raven/raven.js":function(e,t,r){"use strict";(function(n){var o=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=o(r("./node_modules/@babel/runtime/helpers/classCallCheck.js")),a=o(r("./node_modules/@babel/runtime/helpers/createClass.js")),s=o(r("./packages/@logrocket/utils/src/TraceKit.js")),u=Object.prototype;function c(e){return"function"==typeof e}function l(e,t,r,n){var o=e[t];e[t]=r(o),n&&n.push([e,t,o])}var d="undefined"!=typeof window?window:void 0!==n?n:"undefined"!=typeof self?self:{};d.document,t.default=function(){function e(t){var r=t.captureException;(0,i.default)(this,e),this._errorHandler=this._errorHandler.bind(this),this._ignoreOnError=0,this._wrappedBuiltIns=[],this.captureException=r,s.default.report.subscribe(this._errorHandler),this._instrumentTryCatch()}return(0,a.default)(e,[{key:"uninstall",value:function(){for(s.default.report.unsubscribe(this._errorHandler);this._wrappedBuiltIns.length;){var e=this._wrappedBuiltIns.shift(),t=e[0],r=e[1],n=e[2];t[r]=n}}},{key:"_errorHandler",value:function(e){this._ignoreOnError||this.captureException(e)}},{key:"_ignoreNextOnError",value:function(){var e=this;this._ignoreOnError+=1,setTimeout(function(){e._ignoreOnError-=1})}},{key:"context",value:function(e,t,r){return c(e)&&(r=t||[],t=e,e=void 0),this.wrap(e,t).apply(this,r)}},{key:"wrap",value:function(e,t,r){var n,o=this;if(void 0===t&&!c(e))return e;if(c(e)&&(t=e,e=void 0),!c(t))return t;try{if(t.__lr__)return t;if(t.__lr_wrapper__)return t.__lr_wrapper__;if(!Object.isExtensible(t))return t}catch(e){return t}function i(){var n=[],i=arguments.length,a=!e||e&&!1!==e.deep;for(r&&c(r)&&r.apply(this,arguments);i--;)n[i]=a?o.wrap(e,arguments[i]):arguments[i];try{return t.apply(this,n)}catch(t){throw o._ignoreNextOnError(),o.captureException(s.default.computeStackTrace(t),e),t}}for(var a in t)n=t,u.hasOwnProperty.call(n,a)&&(i[a]=t[a]);return i.prototype=t.prototype,t.__lr_wrapper__=i,i.__lr__=!0,i.__inner__=t,i}},{key:"_instrumentTryCatch",value:function(){var e=this,t=e._wrappedBuiltIns;function r(t){return function(r,n){for(var o=Array(arguments.length),i=0;i<o.length;++i)o[i]=arguments[i];var a=o[0];return(c(a)&&(o[0]=e.wrap(a)),t.apply)?t.apply(this,o):t(o[0],o[1])}}l(d,"setTimeout",r,t),l(d,"setInterval",r,t),d.requestAnimationFrame&&l(d,"requestAnimationFrame",function(t){return function(r){return t(e.wrap(r))}},t);for(var n=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],o=0;o<n.length;o++)!function(r){var n=d[r]&&d[r].prototype;n&&n.hasOwnProperty&&n.hasOwnProperty("addEventListener")&&(l(n,"addEventListener",function(t){return function(r,n,o,i){var a;try{n&&n.handleEvent&&(n.handleEvent=e.wrap(n.handleEvent))}catch(e){}return t.call(this,r,e.wrap(n,void 0,a),o,i)}},t),l(n,"removeEventListener",function(e){return function(t,r,n,o){try{r=r&&(r.__lr_wrapper__?r.__lr_wrapper__:r)}catch(e){}return e.call(this,t,r,n,o)}},t))}(n[o]);var i=d.jQuery||d.$;i&&i.fn&&i.fn.ready&&l(i.fn,"ready",function(t){return function(r){return t.call(this,e.wrap(r))}},t)}}]),e}(),e.exports=t.default}).call(this,r("./node_modules/webpack/buildin/global.js"))},"./packages/@logrocket/exceptions/src/registerExceptions.js":function(e,t,r){"use strict";var n=r("./node_modules/@babel/runtime/helpers/interopRequireWildcard.js"),o=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=new i.default({captureException:function(t){a.captureException(e,null,null,t)}}),r=function(t){e.addEvent("lr.core.Exception",function(){return{exceptionType:"UNHANDLED_REJECTION",message:t.reason||"Unhandled Promise rejection"}})};return window.addEventListener("unhandledrejection",r),function(){window.removeEventListener("unhandledrejection",r),t.uninstall()}};var i=o(r("./packages/@logrocket/exceptions/src/raven/raven.js")),a=n(r("./packages/@logrocket/exceptions/src/Capture.js"));e.exports=t.default},"./packages/@logrocket/exceptions/src/stackTraceFromError.js":function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){function t(e){return null===e?void 0:e}return e.stack?e.stack.map(function(e){return{lineNumber:t(e.line),columnNumber:t(e.column),fileName:t(e.url),functionName:t(e.func)}}):void 0},e.exports=t.default},"./packages/@logrocket/network/src/fetchIntercept.js":function(e,t,r){"use strict";var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=n(r("./node_modules/@babel/runtime/helpers/toConsumableArray.js")),i=r("./packages/@logrocket/network/src/registerXHR.js"),a=[];function s(e,t){for(var r=a.reduce(function(e,t){return[t].concat(e)},[]),n=arguments.length,s=Array(n>2?n-2:0),u=2;u<n;u++)s[u-2]=arguments[u];var c=Promise.resolve(s);return r.forEach(function(e){var r=e.request,n=e.requestError;(r||n)&&(c=c.then(function(e){return r.apply(void 0,[t].concat((0,o.default)(e)))},function(e){return n.apply(void 0,[t].concat((0,o.default)(e)))}))}),c=c.then(function(t){var r,n;(0,i.setActive)(!1);try{r=e.apply(void 0,(0,o.default)(t))}catch(e){n=e}if((0,i.setActive)(!0),n)throw n;return r}),r.forEach(function(e){var r=e.response,n=e.responseError;(r||n)&&(c=c.then(function(e){return r(t,e)},function(e){return n&&n(t,e)}))}),c}var u=!1;t.default={register:function(e){if(!u){u=!0;var t=window;if(t.fetch&&t.Promise){var r,n,o=t.fetch.polyfill;r=t.fetch,n=0,t.fetch=function(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];return s.apply(void 0,[r,n++].concat(t))},o&&(t.fetch.polyfill=o)}}return a.push(e),function(){var t=a.indexOf(e);t>=0&&a.splice(t,1)}},clear:function(){a=[]}},e.exports=t.default},"./packages/@logrocket/network/src/index.js":function(e,t,r){"use strict";var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{isReactNative:!1},r=t.isReactNative,n=t.shouldAugmentNPS,o=t.shouldParseXHRBlob,l={},f=function(e){var t=e;if("object"===(0,i.default)(e)&&null!=e){var r=Object.getPrototypeOf(e);(r===Object.prototype||null===r)&&(t=JSON.stringify(e))}if(t&&t.length&&t.length>4096e3&&"string"==typeof t){var n=t.substring(0,1e3);return"".concat(n," ... LogRocket truncating to first 1000 characters.\n      Keep data under 4MB to prevent truncation. https://docs.logrocket.com/reference#network")}return e},p=function(t,r){var n=r.method;e.addEvent("lr.network.RequestEvent",function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=e.isEnabled,i=e.requestSanitizer;if(!(void 0===o||o))return null;var a=null;try{a=(void 0===i?function(e){return e}:i)(d(d({},r),{},{reqId:t}))}catch(e){console.error(e)}if(a){var s=a.url;if("undefined"!=typeof document&&"function"==typeof document.createElement){var u=document.createElement("a");u.href=a.url,s=u.href}return{reqId:t,url:s,headers:(0,c.default)(a.headers,function(e){return"".concat(e)}),body:f(a.body),method:n,referrer:a.referrer||void 0,mode:a.mode||void 0,credentials:a.credentials||void 0}}return l[t]=!0,null})},g=function(t,r){var n=r.method,o=r.status;e.addEvent("lr.network.ResponseEvent",function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=e.isEnabled,a=e.responseSanitizer;if(!(void 0===i||i))return null;if(l[t])return delete l[t],null;var s=null;try{s=(void 0===a?function(e){return e}:a)(d(d({},r),{},{reqId:t}))}catch(e){console.error(e)}return s?{reqId:t,status:s.status,headers:(0,c.default)(s.headers,function(e){return"".concat(e)}),body:f(s.body),method:n}:{reqId:t,status:o,headers:{},body:null,method:n}})},h=function(t){return e.isDisabled||!0===l[t]},v=(0,a.default)({addRequest:p,addResponse:g,isIgnored:h}),b=(0,u.default)({addRequest:p,addResponse:g,isIgnored:h,logger:e,shouldAugmentNPS:n,shouldParseXHRBlob:o}),m=r?function(){}:(0,s.default)(e);return function(){m(),v(),b()}};var o=n(r("./node_modules/@babel/runtime/helpers/defineProperty.js")),i=n(r("./node_modules/@babel/runtime/helpers/typeof.js")),a=n(r("./packages/@logrocket/network/src/registerFetch.js")),s=n(r("./packages/@logrocket/network/src/registerNetworkInformation.js")),u=n(r("./packages/@logrocket/network/src/registerXHR.js")),c=n(r("./packages/@logrocket/utils/src/mapValues.js"));function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach(function(t){(0,o.default)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}e.exports=t.default},"./packages/@logrocket/network/src/registerFetch.js":function(e,t,r){"use strict";var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=e.addRequest,r=e.addResponse,n=e.isIgnored,o="fetch-",i={};return a.default.register({request:function(e){for(var r,n,a=arguments.length,s=Array(a>1?a-1:0),c=1;c<a;c++)s[c-1]=arguments[c];if("undefined"!=typeof Request&&s[0]instanceof Request){try{n=s[0].clone().text()}catch(e){n=Promise.resolve("LogRocket fetch error: ".concat(e.message))}r=n.then(function(e){return u(u({},l(s[0])),{},{body:e})},function(e){return u(u({},l(s[0])),{},{body:"LogRocket fetch error: ".concat(e.message)})})}else r=Promise.resolve(u(u({},l(s[1])),{},{url:"".concat(s[0]),body:(s[1]||{}).body}));return r.then(function(r){return i[e]=r.method,t("".concat(o).concat(e),r),s})},requestError:function(e,t){return Promise.reject(t)},response:function(e,t){if(n("".concat(o).concat(e)))return t;try{a=t.clone()}catch(n){var a,s,u={url:t.url,status:t.status,headers:c(t.headers),body:"LogRocket fetch error: ".concat(n.message),method:i[e]};return delete i[e],r("".concat(o).concat(e),u),t}try{if(window.TextDecoder){var l=a.body.getReader(),d=new window.TextDecoder("utf-8"),f="";s=l.read().then(function e(t){var r=t.done,n=t.value;if(r)return f;var o=n?d.decode(n,{stream:!0}):"";return f+=o,l.read().then(e)})}else s=a.text()}catch(e){s=Promise.resolve("LogRocket error reading body: ".concat(e.message))}return s.catch(function(e){if(!("AbortError"===e.name&&e instanceof DOMException))return"LogRocket error reading body: ".concat(e.message)}).then(function(n){var a={url:t.url,status:t.status,headers:c(t.headers),body:n,method:i[e]};delete i[e],r("".concat(o).concat(e),a)}),t},responseError:function(e,t){return r("".concat(o).concat(e),{url:void 0,status:0,headers:{},body:"".concat(t)}),Promise.reject(t)}})};var o=n(r("./node_modules/@babel/runtime/helpers/defineProperty.js")),i=n(r("./packages/@logrocket/utils/src/mapValues.js")),a=n(r("./packages/@logrocket/network/src/fetchIntercept.js"));function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function u(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){(0,o.default)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var c=function(e){return(0,i.default)(function(e){if(null==e||"function"!=typeof e.forEach)return e;var t={};return e.forEach(function(e,r){t[r]?t[r]="".concat(t[r],",").concat(e):t[r]="".concat(e)}),t}(e),function(e){return"".concat(e)})};function l(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{url:e.url,headers:c(e.headers),method:e.method&&e.method.toUpperCase(),referrer:e.referrer||void 0,mode:e.mode||void 0,credentials:e.credentials||void 0}}e.exports=t.default},"./packages/@logrocket/network/src/registerNetworkInformation.js":function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=void 0;function r(){var r={online:window.navigator.onLine,effectiveType:"UNKOWN"};window.navigator.onLine?window.navigator.connection&&window.navigator.connection.effectiveType&&(r.effectiveType=n[window.navigator.connection.effectiveType]||"UNKNOWN"):r.effectiveType="NONE",t&&r.online===t.online&&r.effectiveType===t.effectiveType||(t=r,e.addEvent("lr.network.NetworkStatusEvent",function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.isEnabled;return void 0===t||t?r:null}))}return setTimeout(r),window.navigator.connection&&"function"==typeof window.navigator.connection.addEventListener&&window.navigator.connection.addEventListener("change",r),window.addEventListener("online",r),window.addEventListener("offline",r),function(){window.removeEventListener("offline",r),window.removeEventListener("online",r),window.navigator.connection&&"function"==typeof window.navigator.connection.removeEventListener&&window.navigator.connection.removeEventListener("change",r)}};var n={"slow-2g":"SLOW2G","2g":"TWOG","3g":"THREEG","4g":"FOURG"};e.exports=t.default},"./packages/@logrocket/network/src/registerXHR.js":function(e,t,r){"use strict";var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.setActive=function(e){u=e},t.default=function(e){var t=e.addRequest,r=e.addResponse,n=e.isIgnored,l=e.logger,d=e.shouldAugmentNPS,f=void 0===d||d,p=e.shouldParseXHRBlob,g=void 0!==p&&p,h=XMLHttpRequest,v=new WeakMap,b=!1,m="xhr-";return window._lrXMLHttpRequest=XMLHttpRequest,(XMLHttpRequest=function(e,d){var p=new h(e,d);if(!u)return p;v.set(p,{xhrId:++c,headers:{}});var y=p.open,_=p.send;f&&(p.open=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];try{var n=t[1];if(window.URL&&"function"==typeof window.URL&&0===n.search(s.WOOTRIC_RESPONSES_REGEX)){var o=new window.URL(l.recordingURL);o.searchParams.set("nps","wootric");var i=new window.URL(n),a=i.searchParams.get("response[text]");i.searchParams.set("response[text]","".concat(a?"".concat(a,"\n\n"):"","<").concat(o.href,"|View LogRocket session>")),t[1]=i.href}}catch(e){}return y.apply(this,t)},p.send=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];try{var n=v.get(p);if(window.URL&&"function"==typeof window.URL&&n&&n.url&&0===n.url.search(s.DELIGHTED_RESPONSES_REGEX)&&t.length&&-1!==t[0].indexOf(s.DELIGHTED_FEEDBACK_PREFIX)){var o=new window.URL(l.recordingURL);o.searchParams.set("nps","delighted");var i=encodeURIComponent(o.href),u=t[0].split("&").map(function(e){if((0,a.default)(e,s.DELIGHTED_FEEDBACK_PREFIX)){var t=e===s.DELIGHTED_FEEDBACK_PREFIX;return"".concat(e).concat(t?"":"\n\n","<").concat(i,"|View LogRocket session>")}return e}).join("&");t[0]=u}}catch(e){}return _.apply(this,t)}),(0,i.default)(p,"open",function(e,t){if(!b){var r=v.get(p);r.method=e,r.url=t}}),(0,i.default)(p,"send",function(e){if(!b){var r=v.get(p);if(r){var n={url:r.url,method:r.method&&r.method.toUpperCase(),headers:(0,o.default)(r.headers||{},function(e){return e.join(", ")}),body:e};t("".concat(m).concat(r.xhrId),n)}}}),(0,i.default)(p,"setRequestHeader",function(e,t){if(!b){var r=v.get(p);r&&(r.headers=r.headers||{},r.headers[e]=r.headers[e]||[],r.headers[e].push(t))}});var w={readystatechange:function(){if(!b&&4===p.readyState){var e,t=v.get(p);if(!(!t||n("".concat(m).concat(t.xhrId)))){var o=(p.getAllResponseHeaders()||"").split(/[\r\n]+/).reduce(function(e,t){var r=t.split(": ");if(r.length>0){var n=r.shift(),o=r.join(": ");e[n]?e[n]+=", ".concat(o):e[n]=o}return e},{});try{switch(p.responseType){case"json":e=l._shouldCloneResponse?JSON.parse(JSON.stringify(p.response)):p.response;break;case"arraybuffer":case"blob":e=p.response;break;case"document":e=p.responseXML;break;case"text":case"":e=p.responseText;break;default:e=""}}catch(t){e="LogRocket: Error accessing response."}var i={url:t.url,status:p.status,headers:o,body:e,method:(t.method||"").toUpperCase()};if(g&&i.body instanceof Blob){var a=new FileReader;a.readAsText(i.body),a.onload=function(){try{i.body=JSON.parse(a.result)}catch(e){}r("".concat(m).concat(t.xhrId),i)}}else r("".concat(m).concat(t.xhrId),i)}}}};return Object.keys(w).forEach(function(e){p.addEventListener(e,w[e])}),p}).prototype=h.prototype,["UNSENT","OPENED","HEADERS_RECEIVED","LOADING","DONE"].forEach(function(e){XMLHttpRequest[e]=h[e]}),function(){b=!0,XMLHttpRequest=h}};var o=n(r("./packages/@logrocket/utils/src/mapValues.js")),i=n(r("./packages/@logrocket/utils/src/enhanceFunc.js")),a=n(r("./packages/@logrocket/utils/src/startsWith.js")),s=r("./packages/@logrocket/utils/src/constants/nps.js"),u=!0,c=0},"./packages/@logrocket/now/src/index.js":function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=Date.now.bind(Date),o=n();t.default="undefined"!=typeof performance&&performance.now?performance.now.bind(performance):function(){return n()-o},e.exports=t.default},"./packages/@logrocket/redux/src/createEnhancer.js":function(e,t,r){"use strict";var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.stateSanitizer,n=void 0===r?function(e){return e}:r,o=t.actionSanitizer,a=void 0===o?function(e){return e}:o;return function(t){return function(r,o,c){var l=t(r,o,c),d=l.dispatch,f=u++;return e.addEvent("lr.redux.InitialState",function(){var e;try{e=n(l.getState())}catch(e){console.error(e.toString())}return{state:e,storeId:f}}),s(s({},l),{},{dispatch:function(t){var r,o,s=(0,i.default)();try{o=d(t)}catch(e){r=e}finally{var u=(0,i.default)()-s;e.addEvent("lr.redux.ReduxAction",function(){var e=null,r=null;try{e=n(l.getState()),r=a(t)}catch(e){console.error(e.toString())}return e&&r?{storeId:f,action:r,duration:u,stateDelta:e}:null})}if(r)throw r;return o}})}}};var o=n(r("./node_modules/@babel/runtime/helpers/defineProperty.js")),i=n(r("./packages/@logrocket/now/src/index.js"));function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){(0,o.default)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var u=0;e.exports=t.default},"./packages/@logrocket/redux/src/createMiddleware.js":function(e,t,r){"use strict";var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.stateSanitizer,n=void 0===r?function(e){return e}:r,a=t.actionSanitizer,s=void 0===a?function(e){return e}:a;return function(t){var r=i++;return e.addEvent("lr.redux.InitialState",function(){var e;try{e=n(t.getState())}catch(e){console.error(e.toString())}return{state:e,storeId:r}}),function(i){return function(a){var u,c,l=(0,o.default)();try{c=i(a)}catch(e){u=e}finally{var d=(0,o.default)()-l;e.addEvent("lr.redux.ReduxAction",function(){var e=null,o=null;try{e=n(t.getState()),o=s(a)}catch(e){console.error(e.toString())}return e&&o?{storeId:r,action:o,duration:d,stateDelta:e}:null})}if(u)throw u;return c}}}};var o=n(r("./packages/@logrocket/now/src/index.js")),i=0;e.exports=t.default},"./packages/@logrocket/redux/src/index.js":function(e,t,r){"use strict";var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createEnhancer",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(t,"createMiddleware",{enumerable:!0,get:function(){return i.default}});var o=n(r("./packages/@logrocket/redux/src/createEnhancer.js")),i=n(r("./packages/@logrocket/redux/src/createMiddleware.js"))},"./packages/@logrocket/utils/src/TraceKit.js":function(e,t,r){"use strict";(function(r){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={collectWindowErrors:!0,debug:!1},o="undefined"!=typeof window?window:void 0!==r?r:"undefined"!=typeof self?self:{},i=[].slice,a=/^(?:Uncaught (?:exception: )?)?((?:Eval|Internal|Range|Reference|Syntax|Type|URI)Error): ?(.*)$/;function s(){return"undefined"==typeof document||void 0===document.location?"":document.location.href}n.report=function(){var e,t,r=[],u=null,c=null,l=null;function d(e,t){var o=null;if(!t||n.collectWindowErrors){for(var a in r)if(r.hasOwnProperty(a))try{r[a].apply(null,[e].concat(i.call(arguments,2)))}catch(e){o=e}if(o)throw o}}function f(t,r,o,i,u){if(l)n.computeStackTrace.augmentStackTraceWithInitialElement(l,r,o,t),p();else if(u)d(n.computeStackTrace(u),!0);else{var c,f={url:r,line:o,column:i},g=void 0,h=t;if("[object String]"===({}).toString.call(t)){var c=t.match(a);c&&(g=c[1],h=c[2])}f.func="?",d({name:g,message:h,url:s(),stack:[f]},!0)}return!!e&&e.apply(this,arguments)}function p(){var e=l,t=u;u=null,l=null,c=null,d.apply(null,[e,!1].concat(t))}function g(e,t){var r=i.call(arguments,1);if(l)if(c===e)return;else p();var o=n.computeStackTrace(e);if(l=o,c=e,u=r,setTimeout(function(){c===e&&p()},2e3*!!o.incomplete),!1!==t)throw e}return g.subscribe=function(n){t||(e=o.onerror,o.onerror=f,t=!0),r.push(n)},g.unsubscribe=function(e){for(var t=r.length-1;t>=0;--t)r[t]===e&&r.splice(t,1)},g.uninstall=function(){t&&(o.onerror=e,t=!1,e=void 0),r=[]},g}(),n.computeStackTrace=function(){function e(e){if(void 0!==e.stack&&e.stack){var t,r,n=/^\s*at (.*?) ?\(((?:file|https?|blob|chrome-extension|native|eval|<anonymous>).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,o=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|resource|\[native).*?)(?::(\d+))?(?::(\d+))?\s*$/i,i=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i,a=e.stack.split("\n"),u=[];/^(.*) is undefined$/.exec(e.message);for(var c=0,l=a.length;c<l;++c){if(t=n.exec(a[c])){var d=t[2]&&-1!==t[2].indexOf("native");r={url:d?null:t[2],func:t[1]||"?",args:d?[t[2]]:[],line:t[3]?+t[3]:null,column:t[4]?+t[4]:null}}else if(t=i.exec(a[c]))r={url:t[2],func:t[1]||"?",args:[],line:+t[3],column:t[4]?+t[4]:null};else{if(!(t=o.exec(a[c])))continue;r={url:t[3],func:t[1]||"?",args:t[2]?t[2].split(","):[],line:t[4]?+t[4]:null,column:t[5]?+t[5]:null}}!r.func&&r.line&&(r.func="?"),u.push(r)}return u.length?(u[0].column||void 0===e.columnNumber||(u[0].column=e.columnNumber+1),{name:e.name,message:e.message,url:s(),stack:u}):null}}function t(e,t,r,n){var o={url:t,line:r};if(o.url&&o.line){if(e.incomplete=!1,o.func||(o.func="?"),e.stack.length>0&&e.stack[0].url===o.url){if(e.stack[0].line===o.line)return!1;else if(!e.stack[0].line&&e.stack[0].func===o.func)return e.stack[0].line=o.line,!1}return e.stack.unshift(o),e.partial=!0,!0}return e.incomplete=!0,!1}function r(o,i){var a=null;i=null==i?0:+i;try{if(a=e(o))return a}catch(e){if(n.debug)throw e}try{if(a=function e(o,i){for(var a,u,c=/function\s+([_$a-zA-Z\xA0-\uFFFF][_$a-zA-Z0-9\xA0-\uFFFF]*)?\s*\(/i,l=[],d={},f=!1,p=e.caller;p&&!f;p=p.caller)if(p!==r&&p!==n.report){if(u={url:null,func:"?",line:null,column:null},p.name?u.func=p.name:(a=c.exec(p.toString()))&&(u.func=a[1]),void 0===u.func)try{u.func=a.input.substring(0,a.input.indexOf("{"))}catch(e){}d[""+p]?f=!0:d[""+p]=!0,l.push(u)}i&&l.splice(0,i);var g={name:o.name,message:o.message,url:s(),stack:l};return t(g,o.sourceURL||o.fileName,o.line||o.lineNumber,o.message||o.description),g}(o,i+1))return a}catch(e){if(n.debug)throw e}return{name:o.name,message:o.message,url:s()}}return r.augmentStackTraceWithInitialElement=t,r.computeStackTraceFromStackProp=e,r}(),t.default=n,e.exports=t.default}).call(this,r("./node_modules/webpack/buildin/global.js"))},"./packages/@logrocket/utils/src/constants/nps.js":function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DELIGHTED_FEEDBACK_PREFIX=t.DELIGHTED_RESPONSES_REGEX=t.WOOTRIC_RESPONSES_REGEX=void 0,t.WOOTRIC_RESPONSES_REGEX=/^https:\/\/production.wootric.com\/responses/,t.DELIGHTED_RESPONSES_REGEX=/^https:\/\/web.delighted.com\/e\/[a-zA-Z-]*\/c/,t.DELIGHTED_FEEDBACK_PREFIX="comment="},"./packages/@logrocket/utils/src/enhanceFunc.js":function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){var n=e[t];return e[t]=function(){for(var e,t=arguments.length,o=Array(t),i=0;i<t;i++)o[i]=arguments[i];return n&&(e=n.apply(this,o)),r.apply(this,o),e},function(){e[t]=n}},e.exports=t.default},"./packages/@logrocket/utils/src/mapValues.js":function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if(null==e)return{};var r={};return Object.keys(e).forEach(function(n){r[n]=t(e[n])}),r},e.exports=t.default},"./packages/@logrocket/utils/src/startsWith.js":function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return e&&t&&e.substring(r,r+t.length)===t},e.exports=t.default},"./packages/logrocket/src/LogRocket.js":function(e,t,r){"use strict";var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.MAX_QUEUE_SIZE=void 0;var o=n(r("./node_modules/@babel/runtime/helpers/classCallCheck.js")),i=n(r("./node_modules/@babel/runtime/helpers/createClass.js")),a=n(r("./node_modules/@babel/runtime/helpers/defineProperty.js")),s=n(r("./node_modules/@babel/runtime/helpers/objectWithoutProperties.js")),u=n(r("./packages/@logrocket/network/src/index.js")),c=r("./packages/@logrocket/exceptions/src/index.js"),l=n(r("./packages/@logrocket/console/src/index.js")),d=r("./packages/@logrocket/redux/src/index.js");function f(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?f(Object(r),!0).forEach(function(t){(0,a.default)(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}t.MAX_QUEUE_SIZE=1e3;var g=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.ingestServer,r=(0,s.default)(e,["ingestServer"]);return t?p({serverURL:"".concat(t,"/i"),statsURL:"".concat(t,"/s")},r):r};t.default=function(){function e(){var t=this;(0,o.default)(this,e),this._buffer=[],["log","info","warn","error","debug"].forEach(function(e){t[e]=function(){for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];t.addEvent("lr.core.LogEvent",function(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return"error"===e&&r.shouldAggregateConsoleErrors&&c.Capture.captureMessage(t,n[0],n,{},!0),{logLevel:e.toUpperCase(),args:n}},{shouldCaptureStackTrace:!0})}}),this._isInitialized=!1,this._installed=[],window._lr_surl_cb=this.getSessionURL.bind(this)}return(0,i.default)(e,[{key:"addEvent",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=Date.now();this._run(function(o){o.addEvent(e,t,p(p({},r),{},{timeOverride:n}))})}},{key:"onLogger",value:function(e){for(this._logger=e;this._buffer.length>0;)this._buffer.shift()(this._logger)}},{key:"_run",value:function(e){if(!this._isDisabled)if(this._logger)e(this._logger);else{if(this._buffer.length>=1e3){this._isDisabled=!0,console.warn("LogRocket: script did not load. Check that you have a valid network connection."),this.uninstall();return}this._buffer.push(e.bind(this))}}},{key:"init",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this._isInitialized){var r=t.shouldAugmentNPS,n=t.shouldParseXHRBlob;this._installed.push((0,c.registerExceptions)(this)),this._installed.push((0,u.default)(this,{shouldAugmentNPS:!!(void 0===r||r),shouldParseXHRBlob:!!(void 0!==n&&n)})),this._installed.push((0,l.default)(this)),this._isInitialized=!0,this._run(function(r){r.init(e,g(t))})}}},{key:"start",value:function(){this._run(function(e){e.start()})}},{key:"uninstall",value:function(){this._installed.forEach(function(e){return e()}),this._buffer=[],this._run(function(e){e.uninstall()})}},{key:"identify",value:function(e,t){this._run(function(r){r.identify(e,t)})}},{key:"startNewSession",value:function(){this._run(function(e){e.startNewSession()})}},{key:"track",value:function(e,t){this._run(function(r){r.track(e,t)})}},{key:"getSessionURL",value:function(e){if("function"!=typeof e)throw Error("LogRocket: must pass callback to getSessionURL()");this._run(function(t){t.getSessionURL?t.getSessionURL(e):e(t.recordingURL)})}},{key:"getVersion",value:function(e){this._run(function(t){e(t.version)})}},{key:"captureMessage",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};c.Capture.captureMessage(this,e,[e],t)}},{key:"captureException",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};c.Capture.captureException(this,e,t)}},{key:"version",get:function(){return this._logger&&this._logger.version}},{key:"sessionURL",get:function(){return this._logger&&this._logger.recordingURL}},{key:"recordingURL",get:function(){return this._logger&&this._logger.recordingURL}},{key:"recordingID",get:function(){return this._logger&&this._logger.recordingID}},{key:"threadID",get:function(){return this._logger&&this._logger.threadID}},{key:"tabID",get:function(){return this._logger&&this._logger.tabID}},{key:"reduxEnhancer",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,d.createEnhancer)(this,e)}},{key:"reduxMiddleware",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,d.createMiddleware)(this,e)}},{key:"isDisabled",get:function(){return!!(this._isDisabled||this._logger&&this._logger._isDisabled)}}]),e}()},"./packages/logrocket/src/makeLogRocket.js":function(e,t,r){"use strict";var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(){};if("undefined"!=typeof navigator&&"ReactNative"===navigator.product)throw Error("LogRocket does not yet support React Native.");if("undefined"!=typeof window){if(window._disableLogRocket)return i();if(window.MutationObserver&&window.WeakMap){window._lrMutationObserver=window.MutationObserver;var t=new o.default;return e(t),t}}return i()};var o=n(r("./packages/logrocket/src/LogRocket.js")),i=function(){return{init:function(){},uninstall:function(){},log:function(){},info:function(){},warn:function(){},error:function(){},debug:function(){},addEvent:function(){},identify:function(){},start:function(){},get threadID(){return null},get recordingID(){return null},get recordingURL(){return null},reduxEnhancer:function(){return function(e){return function(){return e.apply(void 0,arguments)}}},reduxMiddleware:function(){return function(){return function(e){return function(t){return e(t)}}}},track:function(){},getSessionURL:function(){},getVersion:function(){},startNewSession:function(){},onLogger:function(){},setClock:function(){},captureMessage:function(){},captureException:function(){}}};e.exports=t.default},"./packages/logrocket/src/module-npm.js":function(e,t,r){"use strict";var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.default=(0,n(r("./packages/logrocket/src/setup.js")).default)(),e.exports=t.default},"./packages/logrocket/src/setup.js":function(e,t,r){"use strict";var n=r("./node_modules/@babel/runtime/helpers/interopRequireDefault.js");Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.enterpriseServer,n=t.sdkVersion,s=void 0===n?"3.0.1":n,u=(0,o.default)(t,["enterpriseServer","sdkVersion"]),c="https://cdn.logrocket.io";if("script"===s)try{var l=document.currentScript.src.match(/^(https?:\/\/([^\\]+))\/.+$/),d=l&&l[2];d&&a[d]&&(c=l&&l[1],e=a[d])}catch(e){}else c="https://cdn.lr-in-prod.com",e="https://r.lr-in-prod.com";var f=u.sdkServer||r,p=u.ingestServer||r||e,g=(0,i.default)(function(){var e=document.createElement("script");p&&(void 0===window.__SDKCONFIG__&&(window.__SDKCONFIG__={}),window.__SDKCONFIG__.serverURL="".concat(p,"/i"),window.__SDKCONFIG__.statsURL="".concat(p,"/s")),f?e.src="".concat(f,"/logger.min.js"):window.__SDKCONFIG__&&window.__SDKCONFIG__.loggerURL?e.src=window.__SDKCONFIG__.loggerURL:window._lrAsyncScript?e.src=window._lrAsyncScript:e.src="".concat(c,"/logger-1.min.js"),e.async=!0,document.head.appendChild(e),e.onload=function(){"function"==typeof window._LRLogger?g.onLogger(new window._LRLogger({sdkVersion:s})):(console.warn("LogRocket: script execution has been blocked by a product or service."),g.uninstall())},e.onerror=function(){console.warn("LogRocket: script could not load. Check that you have a valid network connection."),g.uninstall()}});return g};var o=n(r("./node_modules/@babel/runtime/helpers/objectWithoutProperties.js")),i=n(r("./packages/logrocket/src/makeLogRocket.js")),a={"cdn.logrocket.io":"https://r.logrocket.io","cdn.lr-ingest.io":"https://r.lr-ingest.io","cdn.lr-in.com":"https://r.lr-in.com","cdn.lr-in-prod.com":"https://r.lr-in-prod.com","cdn-staging.logrocket.io":"https://staging-i.logrocket.io","cdn-staging.lr-ingest.io":"https://staging-i.lr-ingest.io","cdn-staging.lr-in.com":"https://staging-i.lr-in.com","cdn-staging.lr-in-prod.com":"https://staging-i.lr-in-prod.com"};e.exports=t.default},0:function(e,t,r){e.exports=r("./packages/logrocket/src/module-npm.js")}},t={};function r(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t||4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)r.d(n,o,(function(t){return e[t]}).bind(null,o));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=0)}()},76608:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(98889).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},80584:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(98889).A)("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]])},86575:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return i}});let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},n=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function o(e){return["async","defer","noModule"].includes(e)}function i(e,t){for(let[i,a]of Object.entries(t)){if(!t.hasOwnProperty(i)||n.includes(i)||void 0===a)continue;let s=r[i]||i.toLowerCase();"SCRIPT"===e.tagName&&o(s)?e[s]=!!a:e.setAttribute(s,String(a)),(!1===a||"SCRIPT"===e.tagName&&o(s)&&(!a||"false"===a))&&(e.setAttribute(s,""),e.removeAttribute(s))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},87255:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(98889).A)("ThumbsDown",[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22a3.13 3.13 0 0 1-3-3.88Z",key:"m61m77"}]])},92730:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(98889).A)("Minimize2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]])},95307:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},98736:(e,t,r)=>{"use strict";r.d(t,{k:()=>i});var n=r(7620);let o=(e,t)=>{let r=e instanceof Map?e:new Map(e.entries()),n=t instanceof Map?t:new Map(t.entries());if(r.size!==n.size)return!1;for(let[e,t]of r)if(!Object.is(t,n.get(e)))return!1;return!0};function i(e){let t=n.useRef(void 0);return r=>{let n=e(r);return!function(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t||Object.getPrototypeOf(e)!==Object.getPrototypeOf(t))return!1;if(Symbol.iterator in e&&Symbol.iterator in t){if("entries"in e&&"entries"in t)return o(e,t);let r=e[Symbol.iterator](),n=t[Symbol.iterator](),i=r.next(),a=n.next();for(;!i.done&&!a.done;){if(!Object.is(i.value,a.value))return!1;i=r.next(),a=n.next()}return!!i.done&&!!a.done}return o({entries:()=>Object.entries(e)},{entries:()=>Object.entries(t)})}(t.current,n)?t.current=n:t.current}}}}]);