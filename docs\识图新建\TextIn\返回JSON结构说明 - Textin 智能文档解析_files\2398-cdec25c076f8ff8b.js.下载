!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="ee4fd66d-7467-4213-8c55-78b112a4463e",e._sentryDebugIdIdentifier="sentry-dbid-ee4fd66d-7467-4213-8c55-78b112a4463e")}catch(e){}}(),(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2398,5391],{5121:(e,t,r)=>{"use strict";r.d(t,{y:()=>h});var a=r(54568),n=r(6),s=r.n(n),i=r(7620),o=r(71197),l=r(88374),c=r(53132),u=r(40113),d=r(71476),p=r(16600),g=r(88638);function h({code:e,className:t,...r}){let{hasChatPermissions:n,assistantConfig:h}=(0,i.useContext)(d.SearchContext),{isChatSheetOpen:f,onChatSheetToggle:m}=(0,i.useContext)(d.ChatAssistantContext),{docsConfig:v}=(0,i.useContext)(o.H6),{subdomain:y}=(0,i.useContext)(o.Em),b=v?.styling?.codeblocks,x=(0,l.p)("docs.code_block.ask_ai"),{append:k,isInProgress:C}=(0,c.w)();return n&&e&&!r.hideAskAiButton&&h?.askAiOnCodeBlock!==!1?(0,a.jsxs)("div",{className:(0,p.cn)("z-10 relative",t),children:[(0,a.jsx)("button",{className:"h-[26px] w-[26px] flex items-center justify-center rounded-md backdrop-blur peer group/ask-ai-button",id:u.V.AskAiCodeBlockButton,"aria-label":"Ask AI",onClick:()=>{if(C||!e||!n)return;f||m();let t=`\`\`\`${r.language||""}
${e}
\`\`\``,a="Explain this code snippet",i={code:e,content:a,...r};k({id:s()(),role:"user",content:a,parts:[{type:"text",text:JSON.stringify(i)}]}),x({subdomain:y,query:t+"\n\nExplain this code snippet."})},children:(0,a.jsxs)("svg",{width:"18",height:"18",viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:(0,p.cn)("w-4 h-4","system"===b&&"text-gray-400 group-hover/ask-ai-button:text-gray-500 dark:text-white/40 dark:group-hover/ask-ai-button:text-white/60",("dark"===b||void 0==b)&&"text-white/40 group-hover/ask-ai-button:text-white/60"),children:[(0,a.jsx)("path",{d:"M3.51169 1.50098L3.92087 2.72754L3.99997 2.96387L5.49997 3.46387L4.23825 3.88477L4.00095 3.96387L3.92184 4.20117L3.50095 5.46191L3.49997 5.46387H3.49899L2.99899 3.96387L1.49899 3.46387L2.99899 2.96387L3.07809 2.72754L3.4863 1.50098C3.49031 1.50045 3.49522 1.5 3.49997 1.5C3.50416 1.50002 3.50807 1.50054 3.51169 1.50098Z",stroke:"currentColor"}),(0,a.jsx)("path",{d:"M9.5 2.75L11.412 7.587L16.25 9.5L11.412 11.413L9.5 16.25L7.587 11.413L2.75 9.5L7.587 7.587L9.5 2.75Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),(0,a.jsx)(g.iG,{text:"Ask AI"})]}):null}},8080:(e,t,r)=>{"use strict";r.d(t,{k:()=>o});var a=r(54568),n=r(27261),s=r.n(n),i=r(16600);let o=({href:e,onClick:t,title:r,titleContainerClassName:n})=>(0,a.jsx)(s(),{href:e,onClick:t,children:(0,a.jsx)("span",{className:(0,i.cn)("flex items-center text-xs hover:brightness-[0.75] dark:hover:brightness-[1.35] text-primary dark:text-primary-light",n),children:(0,a.jsx)("span",{className:"truncate font-medium",children:r})})})},12459:(e,t,r)=>{"use strict";r.d(t,{f:()=>a});let a={Overlay:"z-50",Popup:"z-40",PrimaryNav:"z-30",SecondaryNav:"z-20",Control:"z-10",Banner:"z-[45]"}},13592:(e,t,r)=>{"use strict";r.d(t,{G:()=>i});var a=r(62942),n=r(7620),s=r(78249);function i(){let e=(0,a.useParams)()?.slug;return(0,n.useMemo)(()=>{let t="string"==typeof e?decodeURIComponent(e):(e=>{if(e)return e.map(e=>decodeURIComponent(e))})(e)?.join("/");return(0,s.M)(t??"")},[e])}},18764:(e,t,r)=>{"use strict";function a(e){return!e||e.startsWith("/")?e.substring(1):e}r.d(t,{$:()=>a})},25050:(e,t,r)=>{"use strict";r.d(t,{O:()=>S,Y:()=>P});var a=r(38668),n=r(31721);let s={tab:"Documentation"},i={anchor:"Documentation",icon:"book-open"},o=(e,t)=>e.filter(e=>e.version===t||void 0===e.version||!t),l=(e,t)=>t?{name:e,style:t}:e,c=e=>!!e&&(e.startsWith("https://")||e.startsWith("http://")),u=(e,t,r,a=[])=>{let n=[],s=[],i=null==t?void 0:t.url;return c(i)?{matchedGroups:n,unmatchedGroups:e}:(e.forEach(e=>{let o=[],c=[];e.version!==r&&void 0!==e.version&&r||e.pages.forEach(e=>{if("string"==typeof e)(!i||g(h(e)).startsWith(g(h(i))))&&!a.some(t=>g(h(e)).startsWith(g(h(t.url))))?o.push(e):c.push(e);else{let{matchedGroups:n,unmatchedGroups:s}=u([e],t,r,a);o.push(...n),s.length&&c.push(e)}}),o.length&&n.push(Object.assign(Object.assign({group:e.group},e.icon?{icon:l(e.icon,e.iconType)}:{}),{pages:o})),c.length&&s.push(Object.assign(Object.assign({},e),{pages:c}))}),{matchedGroups:n,unmatchedGroups:s})},d=(e,t=[],r=[],a=!1,s,i,p)=>{let h=o(r,s),m=t.map(a=>{let o;if(a.version!==s&&s&&a.version)return;a.openapi&&(o=c(a.url)?a.openapi:{source:a.openapi,directory:a.url});let f=Object.assign(Object.assign(Object.assign(Object.assign({},"tabs"===e?{tab:a.name}:{anchor:a.name}),{href:a.url}),a.isDefaultHidden?{hidden:a.isDefaultHidden}:{}),o?{openapi:o}:{});if("anchors"===e&&("icon"in a&&a.icon&&(f.icon=l(a.icon,a.iconType)),"color"in a&&a.color)){let e="string"==typeof a.color?a.color:a.color.from;f.color={light:e,dark:e}}let m=t.filter(e=>e.url&&g(e.url).startsWith(g(a.url))&&e.url!==a.url),v=[],y=[];if(null==p?void 0:p.length){let t=p.filter(e=>g(e.url).startsWith(g(a.url)));if(t.length){let{tabs:a,anchors:n}=d("tabs"===e?"anchors":"tabs",t,r,!1,s,i);v=a,y=n}}let{matchedGroups:b,unmatchedGroups:x}=u(h,a,s,m);h=x;let k=n.omit(f,"href");return v.length?Object.assign(Object.assign({},k),{tabs:v}):y.length?Object.assign(Object.assign({},k),{anchors:y}):b.length?Object.assign(Object.assign({},k),{groups:b}):b.length||(e=>{if(!e||"string"!=typeof e)return!1;try{return URL.canParse(e)}catch(e){return!1}})(a.url)||a.openapi?a.openapi&&!c(a.url)?n.omit(f,"href"):f:void 0}).filter(Boolean);if(h.length&&a){let{matchedGroups:t}=u(h,void 0,s,[]);m.unshift(Object.assign(Object.assign({},f("tabs"===e?"tab":"anchor",i)),{groups:t}))}return"tabs"===e?{tabs:m,anchors:[],remainingGroups:h}:{anchors:m,tabs:[],remainingGroups:h}},p=(e,t,r)=>{let a=[];return e.forEach(e=>{let n=[];(e.version===t||void 0===e.version)&&e.pages.forEach(e=>{if("string"==typeof e){let a=Object.entries(r).some(([r,a])=>a.includes(t)&&e.startsWith(r)),s=Object.keys(r).every(t=>!e.startsWith(t));(a||s)&&n.push(e)}else{let{matchedGroups:a}=p([e],t,r);n.push(...a)}}),n.length&&a.push(Object.assign(Object.assign({group:e.group},e.icon?{icon:l(e.icon,e.iconType)}:{}),{pages:n}))}),{matchedGroups:a}};function g(e){return e.startsWith("/")?e:`/${e}`}function h(e){return e.endsWith("/")?e:`${e}/`}let f=(e,t)=>{var r,a;return"anchor"===e?Object.assign(Object.assign({},(null==(r=null==t?void 0:t.topAnchor)?void 0:r.name)?{anchor:t.topAnchor.name}:i),(null==(a=null==t?void 0:t.topAnchor)?void 0:a.icon)?{icon:l(t.topAnchor.icon,t.topAnchor.iconType)}:{}):Object.assign({},(null==t?void 0:t.primaryTab)?Object.assign({tab:t.primaryTab.name},void 0!==t.primaryTab.isDefaultHidden&&{hidden:t.primaryTab.isDefaultHidden}):s)};var m=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,a=Object.getOwnPropertySymbols(e);n<a.length;n++)0>t.indexOf(a[n])&&Object.prototype.propertyIsEnumerable.call(e,a[n])&&(r[a[n]]=e[a[n]]);return r},v=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,a=Object.getOwnPropertySymbols(e);n<a.length;n++)0>t.indexOf(a[n])&&Object.prototype.propertyIsEnumerable.call(e,a[n])&&(r[a[n]]=e[a[n]]);return r};let y=(e,t)=>{if(b(e))for(let r of e.pages)y(r,t);else t[e.href]=e},b=e=>e.hasOwnProperty("group")&&e.hasOwnProperty("pages"),x=e=>{let t=e.indexOf("?");t>-1&&(e=e.substring(0,t));let r=e.split("/"),a=r[r.length-1].split("-").join(" ");return(a=a.split("_").join(" ")).charAt(0).toUpperCase()+a.slice(1)};var k=r(7620),C=r(71197),w=r(13592);let S="mintlify-navigation-cache";function P(e){let[t,r]=(0,k.useState)(),s=(0,w.G)(),{docsConfig:i}=(0,k.useContext)(C.H6),{buildId:h}=(0,k.useContext)(C.Em),[b,P]=(0,k.useState)(!1);return(0,k.useEffect)(()=>{r(e)},[e]),{navigationData:t,updateCache:(0,k.useCallback)(async t=>{if(P(!0),!t.partial){P(!1),r(e);return}try{let t;P(!0);let s=localStorage.getItem(S);if(s){let e=JSON.parse(s);if(!Array.isArray(e.navigation)){let t=Date.now()-e.timestamp>9e5,a=e.buildId===h;if(!t&&a){r(e.navigation),P(!1);return}localStorage.removeItem(S)}}let b=await fetch("/_mintlify/navigation");if(401===b.status){r(e),P(!1);return}let k=await b.json(),C={navigation:t=k.docsDecoratedNav?k.docsDecoratedNav:Array.isArray(k.navigation)&&i?(e=>{let t,{type:r,decoratedNavigation:s,ignoreUnknownPages:i}=e;if("mint"===r){let{mintConfig:r}=e;t=function(e,t){var r,a,s,i,h,v,y,b,x,k,C,w,S,P,A,j,M,N,T,O,I,_,L,E,F,H,B;let D=function(e){let t=e.font,r={};if(t)return"family"in t?a(t):(t.headings&&(r.heading=a(t.headings)),t.body&&(r.body=a(t.body)),r);function a(e){let{url:t}=e;return Object.assign(Object.assign({},m(e,["url"])),t?{source:t}:{})}}(e),V=function(e){var t,r,a,n;let s=null==(r=null==(t=e.api)?void 0:t.playground)?void 0:r.mode,i=null==(n=null==(a=e.api)?void 0:a.playground)?void 0:n.disableProxy;if(s||void 0!==i)return{display:(()=>{switch(s){case"show":return"interactive";case"hide":return"none";case void 0:return;default:return s}})(),proxy:void 0==i?void 0:!i}}(e),R=function(e){var t;if((null==(t=e.eyebrow)?void 0:t.display)!=void 0)return"breadcrumbs"===e.eyebrow.display?"breadcrumbs":"section"}(e),G=function(e){var t;if((null==(t=e.codeBlock)?void 0:t.mode)!=void 0)return"dark"===e.codeBlock.mode?"dark":"system"}(e);return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({$schema:"https://mintlify.com/docs.json"},function(e,t){let r;if(null==t?void 0:t.shouldUpgradeTheme){if(e.theme)switch(e.theme){case"venus":r="palm";break;case"quill":r="willow";break;default:r="mint"}"sidenav"===e.layout?r="maple":"solidSidenav"===e.layout&&(r="willow")}return{theme:null!=r?r:"mint"}}(e,void 0)),{name:e.name}),e.colors?{colors:{primary:e.colors.primary,light:e.colors.light,dark:e.colors.dark}}:{colors:{primary:"#16A34A"}}),{favicon:e.favicon,navigation:(e=>{let{tabs:t,anchors:r,versions:a}=e,s=e.navigation,{tabs:i,anchors:h,versions:m}=(e=>{let{tabs:t,anchors:r,versions:a}=e,n={};return(null==a?void 0:a.length)&&a.every(e=>"object"==typeof e&&c(e.url))&&(n.versions=a.map(e=>({version:e.name,href:e.url}))),(null==t?void 0:t.length)&&t.every(e=>!e.version&&c(e.url))&&(n.tabs=t.map(e=>Object.assign({tab:e.name,href:e.url},e.isDefaultHidden?{hidden:e.isDefaultHidden}:{}))),(null==r?void 0:r.length)&&r.every(e=>!e.version&&c(e.url))&&(n.anchors=r.map(e=>Object.assign(Object.assign(Object.assign({anchor:e.name,href:e.url},e.icon?{icon:l(e.icon,e.iconType)}:{}),"string"==typeof e.color?{color:{light:e.color,dark:e.color}}:{}),e.isDefaultHidden?{hidden:e.isDefaultHidden}:{}))),n})(e),v=null==t?void 0:t.length,y=null==r?void 0:r.length;t=(null==i?void 0:i.length)?[]:t,r=(null==h?void 0:h.length)?[]:r,a=(null==m?void 0:m.length)?[]:a;let b=(e,a,n)=>{if("anchors"===function(e,t){if(!(null==e?void 0:e.length))return"anchors";if(!(null==t?void 0:t.length))return"tabs";let r=e.filter(e=>t.some(t=>g(e.url).startsWith(g(t.url)))),a=t.filter(t=>e.some(e=>g(t.url).startsWith(g(e.url))));return r.length===e.length?"tabs":a.length===t.length?"anchors":r.length>a.length?"tabs":"anchors"}(t,r)){if(y){let{anchors:s,remainingGroups:i}=d("anchors",r,e,!1,n,a,t);if(i.length||(null==t?void 0:t.length))if(null==t?void 0:t.length){let{tabs:r}=d("tabs",t,i.length?i:e,!0,n,a);s.unshift(Object.assign(Object.assign({},f("anchor",a)),{tabs:r}))}else{let{matchedGroups:e}=u(i,void 0,n);s.unshift(Object.assign(Object.assign({},f("anchor",a)),{groups:e}))}return{anchors:s}}if(v){let{tabs:r}=d("tabs",t,e,!0,n,a);return{tabs:r}}}else{if(v){let{tabs:s,remainingGroups:i}=d("tabs",t,e,!1,n,a,r);if(i.length||(null==r?void 0:r.length))if(null==r?void 0:r.length){let{anchors:t}=d("anchors",r,i.length?i:e,!0,n,a);s.unshift(Object.assign(Object.assign({},f("tab",a)),{anchors:t}))}else{let{matchedGroups:e}=u(i,void 0,n);s.unshift(Object.assign(Object.assign({},f("tab",a)),{groups:e}))}return{tabs:s}}if(y){let{anchors:t}=d("anchors",r,e,!0,n,a);return{anchors:t}}}if(e.length)return{groups:o(e,n).map(e=>Object.assign({group:e.group,pages:e.pages},e.icon?{icon:l(e.icon,e.iconType)}:{}))}};if((null==a?void 0:a.length)&&!(null==m?void 0:m.length)){let o=a.reduce((e,a)=>{let n="string"==typeof a?a:a.name,s=null==r?void 0:r.filter(e=>e.version===n&&!c(e.url)),i=null==t?void 0:t.filter(e=>e.version===n&&!c(e.url));return null==s||s.forEach(t=>e[t.url]=[...e[t.url]||[],n]),null==i||i.forEach(t=>e[t.url]=[...e[t.url]||[],n]),e},{}),{versions:l,isLocale:u}=((e=[],t=[],r)=>{let a=e.every(e=>"object"==typeof e&&e.locale),n=e.map(e=>{if(a&&"object"==typeof e&&e.locale){let a=Object.assign({language:e.locale},e.default?{default:e.default}:{});if(e.url)return Object.assign(Object.assign({},a),{href:e.url});let{matchedGroups:n}=p(t,e.name,r);return Object.assign(Object.assign({},a),{groups:n})}{let a;if("object"==typeof e){if(a={version:e.name,default:e.default},e.url)return Object.assign(Object.assign({},a),{href:e.url})}else a={version:e};let{matchedGroups:n}=p(t,a.version,r);return Object.assign(Object.assign({},a),{groups:n})}});return{isLocale:a,versions:n}})(a,s,o);l.forEach((t,r)=>{var i;if("object"==typeof t&&"href"in t)return;let o="string"==typeof a[r]?t.version:(null==(i=a[r])?void 0:i.name)||t.language;if("groups"in t&&t.groups.length){let a=b(t.groups,e,o);a&&(l[r]=Object.assign(Object.assign({},n.omit(t,"groups")),a))}else{let a=b(s,e,o);a&&(l[r]=Object.assign(Object.assign({},n.omit(t,"groups")),a))}});let d=u?{languages:l}:{versions:l};return((null==i?void 0:i.length)||(null==h?void 0:h.length))&&(d.global=Object.assign(Object.assign({},(null==i?void 0:i.length)?{tabs:i}:{}),(null==h?void 0:h.length)?{anchors:h}:{})),d}let x=b(s,e)||{groups:[]};return((null==i?void 0:i.length)||(null==h?void 0:h.length))&&(x.global=Object.assign(Object.assign({},(null==i?void 0:i.length)?{tabs:i}:{}),(null==h?void 0:h.length)?{anchors:h}:{})),x})(e)}),R||G?{styling:{eyebrows:R,codeblocks:G}}:{}),{logo:e.logo}),V||(null==(s=null==(a=null==(r=e.api)?void 0:r.request)?void 0:a.example)?void 0:s.languages)||(null==(i=e.api)?void 0:i.baseUrl)||(null==(v=null==(h=e.api)?void 0:h.auth)?void 0:v.method)||e.openapi?{api:Object.assign(Object.assign(Object.assign(Object.assign({},e.openapi?{openapi:e.openapi}:{}),V?{playground:Object.assign(Object.assign({},V.display?{display:V.display}:{}),void 0!==V.proxy?{proxy:V.proxy}:{})}:{}),(null==(x=null==(b=null==(y=e.api)?void 0:y.request)?void 0:b.example)?void 0:x.languages)?{examples:{languages:e.api.request.example.languages}}:{}),(null==(k=e.api)?void 0:k.baseUrl)||(null==(w=null==(C=e.api)?void 0:C.auth)?void 0:w.method)||(null==(P=null==(S=e.api)?void 0:S.auth)?void 0:P.name)?{mdx:Object.assign({server:e.api.baseUrl},(null==(A=e.api.auth)?void 0:A.method)||(null==(j=e.api.auth)?void 0:j.name)?{auth:{method:e.api.auth.method,name:e.api.auth.name}}:{})}:{})}:{}),(null==(M=e.modeToggle)?void 0:M.default)||(null==(N=e.modeToggle)?void 0:N.isHidden)!=void 0?{appearance:{default:e.modeToggle.default,strict:e.modeToggle.isHidden}}:{}),e.background||e.backgroundImage||e.colors&&e.colors.background?{background:Object.assign(Object.assign(Object.assign({},e.backgroundImage?{image:e.backgroundImage}:{}),{decoration:null==(T=e.background)?void 0:T.style}),e.colors&&e.colors.background?{color:{light:e.colors.background.light,dark:e.colors.background.dark}}:{})}:{}),e.topbarLinks||e.topbarCtaButton?{navbar:Object.assign(Object.assign({},e.topbarLinks?{links:e.topbarLinks.map(e=>"link"===e.type||void 0===e.type?{label:e.name,href:e.url}:{label:n.capitalize(e.type),href:e.url})}:{}),{primary:function(e){let t=e.topbarCtaButton;if(t)return void 0===t.type||"link"===t.type?{type:"button",label:t.name,href:t.url}:{type:"github",href:t.url}}(e)})}:{}),(null==(O=e.search)?void 0:O.prompt)?{search:{prompt:e.search.prompt}}:{}),e.metadata||(null==(I=e.seo)?void 0:I.indexHiddenPages)!==void 0?{seo:Object.assign(Object.assign({},e.metadata?{metatags:e.metadata}:{}),{indexing:(null==(_=e.seo)?void 0:_.indexHiddenPages)?"all":"navigable"})}:{}),e.footer||e.footerSocials?{footer:Object.assign(Object.assign({},(null==(L=e.footer)?void 0:L.socials)||e.footerSocials?{socials:function(e){var t,r,a;if(!(null==(t=e.footer)?void 0:t.socials)&&!e.footerSocials)return;let n=null!=(a=null==(r=e.footer)?void 0:r.socials)?a:e.footerSocials;return Array.isArray(n)?n.reduce((e,t)=>(e[t.type]=t.url,e),{}):n}(e)}:{}),(null==(E=e.footer)?void 0:E.links)?{links:e.footer.links.map(e=>({header:e.title,items:e.links.map(e=>({label:e.label,href:e.url}))}))}:{})}:{}),e.integrations||e.analytics?{integrations:Object.assign(Object.assign(Object.assign(Object.assign({},e.analytics),(null==(F=e.integrations)?void 0:F.intercom)?{intercom:{appId:e.integrations.intercom}}:{}),(null==(H=e.integrations)?void 0:H.frontchat)?{frontchat:{snippetId:e.integrations.frontchat}}:{}),(null==(B=e.integrations)?void 0:B.osano)?{osano:{scriptSource:e.integrations.osano}}:{})}:{}),D?{fonts:D}:{}),e.redirects?{redirects:e.redirects}:{})}(r)}else t=e.docsConfig;let h={};for(let e of s)y(e,h);return((e,t,r)=>{let{global:n}=t,s=v(t,["global"]),i=t=>{let n=Object.assign({},t);if("openapi"in n){let{openapi:e}=n;n=Object.assign({},v(n,["openapi"]))}return"pages"in n&&(n.pages=n.pages.map(t=>"string"==typeof t?(t=>{let a=function(e){return e.startsWith("/")?e:"/"+e}(t),n=e[a];return n||(r?void 0:(console.log(`⚠️   "${t}" is defined in the docs.json navigation but the file does not exist.`),{title:x(t),href:a}))})(t):i(t)).filter(e=>void 0!==e)),["groups",...a.J].forEach(e=>{let t=n[e];Array.isArray(t)&&(n=Object.assign(Object.assign({},n),{[e]:t.map(e=>i(e))}))}),n};return Object.assign({global:n},i(s))})(h,t.navigation,i)})({type:"docs",docsConfig:i,decoratedNavigation:k.navigation,ignoreUnknownPages:!0}):k.navigation,timestamp:Date.now(),buildId:h??"PLACEHOLDER"};localStorage.setItem(S,JSON.stringify(C)),r(t)}catch(t){console.error("Error fetching navigation:",t),r(e)}finally{P(!1)}},[e,s,S]),isUpdatingCache:b}}},26997:(e,t,r)=>{"use strict";async function a(e){e||console.warn("Called copyToClipboard() with empty text");try{return await navigator.clipboard.writeText(e),"success"}catch(e){return console.error("Failed to copy: ",e),"error"}}r.d(t,{l:()=>a})},27622:(e,t,r)=>{"use strict";r.d(t,{f:()=>s});var a=r(7620),n=r(71197);let s=()=>{let{userAuth:e,auth:t,userInfo:r}=(0,a.useContext)(n.cy);return(0,a.useMemo)(()=>{if(e||t)return[...r?.groups??[],"*"]},[t,e,r?.groups])}},28963:(e,t,r)=>{"use strict";r.d(t,{O:()=>i,i:()=>o});var a=r(54568),n=r(7620),s=r(60357);let i=(0,n.createContext)({preferredCodeLanguage:"",setPreferredCodeLanguage:()=>{}});i.displayName="LocalStorageContext";let o=({cookiesEnabled:e,children:t})=>{let[r,n]=(0,s.Mj)("code","");return(0,a.jsx)(i.Provider,{value:{preferredCodeLanguage:r,setPreferredCodeLanguage:n,cookiesEnabled:e},children:t})}},31467:(e,t,r)=>{"use strict";r.d(t,{x:()=>a});let a=Object.fromEntries(Object.entries({Accordion:"accordion",AccordionGroup:"accordion-group",Callout:"callout",Card:"card",CardGroup:"card-group",CodeBlock:"code-block",CodeBlockIcon:"code-block-icon",CodeGroup:"code-group",Expandable:"expandable",Field:"field",Frame:"frame",Icon:"icon",Mermaid:"mermaid",Step:"step",Steps:"steps",Tab:"tab",Tabs:"tabs",TabIcon:"tab-icon",Update:"update",Tooltip:"tooltip",Panel:"panel",APISection:"api-section",APISectionHeading:"api-section-heading",APISectionHeadingTitle:"api-section-heading-title",APISectionHeadingSubtitle:"api-section-heading-subtitle",OptionDropdown:"option-dropdown",TryitButton:"tryit-button",MethodPill:"method-pill",MethodNavPill:"method-nav-pill",NavTagPill:"nav-tag-pill",NavTagPillText:"nav-tag-pill-text",Anchors:"nav-anchors",Anchor:"nav-anchor",TabsBar:"nav-tabs",TabsBarItem:"nav-tabs-item",MobileNavTabsBarItem:"mobile-nav-tabs-item",TableOfContents:"toc",TableOfContentsItem:"toc-item",Footer:"footer",AdvancedFooter:"advanced-footer",SidebarGroupIcon:"sidebar-group-icon",SidebarGroupHeader:"sidebar-group-header",SidebarTitle:"sidebar-title",SidebarGroup:"sidebar-group",NavBarLink:"navbar-link",TopbarRightContainer:"topbar-right-container",Logo:"nav-logo",PaginationPrev:"pagination-prev",PaginationNext:"pagination-next",PaginationTitle:"pagination-title",FeedbackToolbar:"feedback-toolbar",Eyebrow:"eyebrow",Content:"mdx-content",DropdownTrigger:"nav-dropdown-trigger",DropdownContent:"nav-dropdown-content",DropdownItem:"nav-dropdown-item",DropdownItemTextContainer:"nav-dropdown-item-text-container",DropdownItemTitle:"nav-dropdown-item-title",DropdownItemDescription:"nav-dropdown-item-description",DropdownItemIcon:"nav-dropdown-item-icon",Link:"link",AlmondLayout:"almond-layout",AlmondNavBottomSection:"almond-nav-bottom-section",AlmondNavBottomSectionDivider:"almond-nav-bottom-section-divider",ChatAssistantSheet:"chat-assistant-sheet",ChatAssistantSheetHeader:"chat-assistant-sheet-header",ChatAssistantSheetContent:"chat-assistant-sheet-content",ChatAssistantInput:"chat-assistant-input",ChatAssistantSendButton:"chat-assistant-send-button",ChatAssistantDisclaimerText:"chat-assistant-disclaimer-text",LoginLink:"login-link",LogoutLink:"logout-link"}).map(([e,t])=>[e,`${t}`]))},37050:(e,t,r)=>{"use strict";r.d(t,{N:()=>o});var a=r(77573),n=r(18764),s=r(93696);function i(e){if(""===e)return"index";if("index"===e)return e;if(e.endsWith("/index")){let t=(0,a.C)(e);return""===t?"index":t}return e}function o(e,t){if(null==e||null==t||"string"!=typeof e||"string"!=typeof t)return!1;let r=(0,s.f)((0,n.$)(e)),a=(0,s.f)((0,n.$)(t));return i(r)===i(a)}},38668:(e,t,r)=>{"use strict";r.d(t,{J:()=>a});let a=["anchors","dropdowns","languages","tabs","versions","menu"]},40113:(e,t,r)=>{"use strict";r.d(t,{V:()=>a});var a=function(e){return e.Navbar="navbar",e.Header="header",e.SearchBarEntry="search-bar-entry",e.SearchBarEntryMobile="search-bar-entry-mobile",e.AssistantEntry="assistant-entry",e.AssistantEntryMobile="assistant-entry-mobile",e.RequestExample="request-example",e.ResponseExample="response-example",e.TopbarCtaButton="topbar-cta-button",e.Sidebar="sidebar",e.Footer="footer",e.Banner="banner",e.ContentArea="content-area",e.SidebarContent="sidebar-content",e.NavBarTransition="navbar-transition",e.NavigationItems="navigation-items",e.ContentContainer="content-container",e.ContentSideLayout="content-side-layout",e.TableOfContents="table-of-contents",e.TableOfContentsLayout="table-of-contents-layout",e.TableOfContentsContent="table-of-contents-content",e.ChangelogFilters="changelog-filters",e.ChangelogFiltersContent="changelog-filters-content",e.Pagination="pagination",e.PageTitle="page-title",e.PageContextMenu="page-context-menu",e.PageContextMenuButton="page-context-menu-button",e.Panel="panel",e.ChatAssistantSheet="chat-assistant-sheet",e.ChatAssistantTextArea="chat-assistant-textarea",e.SearchInput="search-input",e.APIPlaygroundInput="api-playground-input",e.AskAiCodeBlockButton="ask-ai-code-block-button",e.EndpointsMenuTrigger="endpoints-menu-trigger",e.MobileNav="mobile-nav",e.MobileNavContent="mobile-nav-content",e.MobileDivider="mobile-divider",e}({})},44902:(e,t,r)=>{"use strict";let a,n,s;r.d(t,{G4:()=>k,DL:()=>x,Sh:()=>C});var i=r(22605),o=r(7620),l=r(34063),c=r(32630),u=r(47949),d=r(90487),p=r(66415),g=r(95391),h=r(82077);let f={matchAlgorithm:"v3"},m=[(0,i.transformerMetaHighlight)({className:g.LINE_HIGHLIGHT_CLASS_NAME}),(0,i.transformerNotationHighlight)({...f,classActiveLine:g.LINE_HIGHLIGHT_CLASS_NAME}),(0,i.transformerNotationFocus)({...f,classActiveLine:g.LINE_FOCUS_CLASS_NAME}),(0,i.transformerNotationDiff)({...f,classLineAdd:g.LINE_DIFF_ADD_CLASS_NAME,classLineRemove:g.LINE_DIFF_REMOVE_CLASS_NAME})],v=(0,u.l)({forgiving:!0,cache:new Map}),y=!1,b=(0,l.O_)({themes:g.THEMES,langs:g.LANGS,engine:v}).then(e=>{n=e}).catch(e=>{console.error(e),s=e??Error("Unknown error occurred initializing highlighter")}).finally(()=>{y=!0});function x(e){return g.shikiDisplayLangMap[e]??""}function k(e,t){let r=/language-(\w+)/.exec(e??"");return r?r[1]??"text":t??"text"}function C(e){let t=function e(t){let i,o;if(!t.codeString||void 0!==s)return;if(void 0===n){if(t.opts?.noAsync||y)return;return b.then(()=>e(t)).catch(()=>{})}if("language"in t){if("text"===t.language)return;i=t.language}else{if("lang-text"===t.className)return;i=k(t.className,t.fileName)}if(t.codeString.length>5*d.S5)return;if(t.codeString.length>d.S5){if(t.opts?.noAsync===!0)return;try{let e=function(){if("undefined"==typeof Worker)return;if(a)return a;let e=new Worker(r.tu(new URL(r.p+r.u(1139),r.b)),{type:void 0});return a=(0,h.LV)(e)}();return void 0==e?void 0:e.highlight(t)}catch{return}}if(i)try{let e=t.opts?.highlightedLines?.length||t.opts?.focusedLines?.length?"codeToHast":"codeToHtml",r=t.codeString.trim();o=n[e](r,{lang:function(e){let t="text",r=Number(e);return!isNaN(r)&&isFinite(r)&&r>99&&r<600?"json":void 0===e?t:g.shikiLangMap[e.toLowerCase()]??t}(i),themes:{light:"dark"===t.codeBlockTheme?g.shikiThemeMap.dark:g.shikiThemeMap.light,dark:g.shikiThemeMap.dark},colorReplacements:{...g.shikiColorReplacements},transformers:m,tabindex:!1,...t.opts})}catch{}if("object"!=typeof o)return o;let l=o.children[0];if(l)return"element"===l.type&&"pre"===l.tagName&&l.children[0].children.filter(e=>"element"===e.type&&"span"===e.tagName).forEach((e,r)=>{let a=r+1;"string"==typeof e.properties.class?(t.opts?.highlightedLines?.includes(a)&&(e.properties.class+=` ${g.LINE_HIGHLIGHT_CLASS_NAME}`),t.opts?.focusedLines?.includes(a)&&(e.properties.class+=` ${g.LINE_FOCUS_CLASS_NAME}`)):Array.isArray(e.properties.class)&&(t.opts?.highlightedLines?.includes(a)&&e.properties.class.push(g.LINE_HIGHLIGHT_CLASS_NAME),t.opts?.focusedLines?.includes(a)&&e.properties.class.push(g.LINE_FOCUS_CLASS_NAME))}),(0,c.jx)(o)}(e),[i,l]=(0,o.useState)(t instanceof Promise?void 0:t);return(0,p.E)(()=>{if(!(t instanceof Promise))return void l(t);let e=!1;return async function(){if(!(t instanceof Promise))return l(t);try{let r=await t;e||l(r)}catch(t){console.error(t),e||l(void 0)}}(),()=>{e=!0}},[e,t]),i}},45978:(e,t,r)=>{"use strict";r.d(t,{f:()=>n});var a=r(7620);let n=({key:e,callback:t,isSingleKey:r,excludedActiveElementId:n,isDisabled:s})=>{let i=(0,a.useCallback)(a=>{if(s)return;let i=!1;if(n){let e=document.activeElement;i=!!e?.id&&n.includes(e.id)}a.key===e&&(r||a.metaKey||a.ctrlKey)&&!i&&(a.preventDefault(),t?.())},[t,e,r,n,s]);(0,a.useEffect)(()=>(addEventListener("keydown",i),()=>removeEventListener("keydown",i)),[i])}},46060:(e,t,r)=>{"use strict";r.d(t,{l:()=>l});var a=r(54568),n=r(7620),s=r(31467),i=r(16600),o=r(74913);let l=n.forwardRef(({className:e,method:t,shortMethod:r=!1,active:n=!1,...l},c)=>{let u=t.toUpperCase(),d=(0,o._)(u),{activeNavPillBg:p,activeNavPillText:g,inactiveNavPillText:h,inactiveNavPillBg:f}=(0,o.H)(t);return(0,a.jsx)("div",{ref:c,className:(0,i.cn)(s.x.MethodPill,"rounded-lg font-bold px-1.5 py-0.5 text-sm leading-5",n?`${p} ${g}`:`${f} ${h}`,e),...l,children:r?d:u})});l.displayName="MethodPill"},48836:(e,t,r)=>{"use strict";r.d(t,{v:()=>a});let a=e=>{if(!e||"string"!=typeof e)return!1;try{return URL.canParse(e)}catch(e){return!1}}},49341:(e,t,r)=>{"use strict";r.d(t,{SQ:()=>u,_2:()=>d,rI:()=>l,ty:()=>c});var a=r(54568),n=r(42586),s=r(4931),i=r(7620),o=r(16600);let l=e=>(0,a.jsx)(n.bL,{modal:!1,...e});l.displayName=n.bL.displayName;let c=i.forwardRef(({className:e,children:t,...r},s)=>(0,a.jsx)(n.l9,{ref:s,className:(0,o.cn)("group bg-background-light dark:bg-background-dark disabled:pointer-events-none [&>span]:line-clamp-1 rounded-lg overflow-hidden group outline-none","flex items-center py-0.5 gap-1 text-sm text-gray-950/50 dark:text-white/50 group-hover:text-gray-950/70 dark:group-hover:text-white/70",e),...r,children:t}));c.displayName=n.l9.displayName,n.ZL;let u=i.forwardRef(({className:e,sideOffset:t=4,...r},s)=>(0,a.jsx)(n.ZL,{children:(0,a.jsx)(n.UC,{ref:s,sideOffset:t,collisionPadding:16,align:"start",style:{textRendering:"geometricPrecision"},className:(0,o.cn)("shadow-xl shadow-gray-500/5 dark:shadow-gray-500/5 bg-background-light dark:bg-background-dark p-1 relative z-50 max-h-96 max-w-[var(--radix-dropdown-menu-content-available-width)] min-w-[var(--radix-dropdown-menu-trigger-width)] overflow-y-auto rounded-xl border-standard text-gray-950/70 dark:text-white/70","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})}));u.displayName=n.UC.displayName;let d=i.forwardRef(({className:e,isSelected:t,...r},i)=>(0,a.jsxs)(n.q7,{ref:i,style:{fontWeight:"normal !important"},className:(0,o.cn)("flex items-center justify-between px-2 py-1 gap-2 text-sm rounded-md group hover:bg-background-dark/[0.03] dark:hover:bg-background-light/[0.03] relative w-full cursor-pointer select-none outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:cursor-default data-[disabled]:opacity-50",t?"text-primary dark:text-primary-light font-medium":"text-gray-950/50 dark:text-white/50",e),...r,children:[r.children,(0,a.jsx)(s.A,{className:(0,o.cn)("size-3.5 text-primary dark:text-primary-light",!t&&"opacity-0"),strokeWidth:2.5})]}));d.displayName=n.q7.displayName},53132:(e,t,r)=>{"use strict";r.d(t,{w:()=>d});var a=r(43651),n=r(7620),s=r(41384),i=r(71197),o=r(93351);r(90487);var l=r(88374),c=r(59538),u=r(27622);let d=()=>{let{subdomain:e}=(0,n.useContext)(i.Em),{userInfo:t}=(0,n.useContext)(i.cy),{selectedVersion:r}=(0,n.useContext)(o._),d=(0,u.f)(),p=(0,l.p)("docs.assistant.completed"),[g,h]=(0,n.useState)(void 0),{input:f,status:m,handleSubmit:v,setInput:y,messages:b,setMessages:x,append:k,data:C,setData:w,reload:S,stop:P}=(0,a.Y_)({api:function({subdomain:e,isAuthenticated:t}){if(s.cD.AUTH_ENABLED)if(t)return"/_mintlify/api/assistant/message";else return`/_mintlify/api-public/assistant/${e}/message`;let r=`/api/assistant/${e}/message`;return`${s.cD.AI_MESSAGE_HOST}${r}`}({subdomain:e??"",isAuthenticated:!!t}),id:e,body:{fp:e,threadId:g,...(({userGroups:e,version:t})=>{let r={};return e&&(r.groups=e),t&&(r.version=t),Object.keys(r).length>0?{filter:r}:{}})({userGroups:d,version:r})},sendExtraMessageFields:!0,initialMessages:[],streamProtocol:"data",onFinish:t=>{p({query:f,content:t.content,citations:(e=>{let t=[];return e?.forEach(e=>{"tool-invocation"===e.type&&"result"===e.toolInvocation.state&&"search"===e.toolInvocation.toolName&&e.toolInvocation.result.forEach(e=>{t.push({title:e.metadata.title,url:e.path})})}),t})(t.parts),sessionId:t.id,subdomain:e})}});(0,n.useEffect)(()=>{c.A.setState({messages:b})},[b]),(0,n.useEffect)(()=>{c.A.setState({status:m})},[m]);let A="submitted"===m||"streaming"===m&&C&&0===C.length,j="submitted"===m||"streaming"===m;return{input:f,status:m,handleSubmit:v,setInput:y,messages:b,setMessages:x,append:k,isLoading:A,onClear:()=>{P(),x([]),w([]),h(void 0)},onReload:()=>{S()},stop:P,threadId:g,isInProgress:j}}},53330:(e,t,r)=>{"use strict";r.d(t,{S7:()=>p,pO:()=>d});var a=r(54568),n=r(71690),s=r(7620),i=r(71197),o=r(16600),l=r(60502),c=r(44902),u=r(86493);let d=7,p=({expandable:e=!1,shouldHighlight:t=!0,children:r,isParentCodeGroup:n,isSmallText:p,numberOfLines:h,...f})=>{let{focus:m,highlight:v,lang:y,language:b,className:x,wrap:k,lines:C}=f,{docsConfig:w}=(0,s.useContext)(i.H6),S=(0,u.Fr)(r,f.className),P=w?.styling?.codeblocks,{isExpanded:A,calculatedHeight:j,contentRef:M,toggleExpanded:N}=((e,t)=>{let[r,a]=(0,s.useState)(!1),[n,i]=(0,s.useState)(0),o=(0,s.useRef)(null);return(0,s.useEffect)(()=>{o.current&&e&&i(o.current.scrollHeight+(t&&t<d?30:40))},[e,o.current?.scrollHeight,t]),{isExpanded:r,calculatedHeight:n,contentRef:o,toggleExpanded:()=>a(e=>!e)}})(e,h),T={},O=[],I=[];if(m)try{O=JSON.parse(m)}catch{O=[]}if(v)try{I=JSON.parse(v)}catch{I=[]}I.length>0&&(T.highlightedLines=I),O.length>0&&(T.focusedLines=O);let _=y||b,L=(0,c.Sh)(_?{codeString:S,codeBlockTheme:P,language:t?_:"text",opts:T}:{codeString:S,codeBlockTheme:P,className:t?x:"lang-text",opts:T});h=(0,u.c$)(L,h);let E=O.length>0,F=I.length>0;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:(0,o.cn)("w-0 min-w-full max-w-full py-3.5 px-4 h-full  dark:bg-codeblock relative text-sm leading-6 children:!my-0 children:!shadow-none children:!bg-transparent","transition-[height] duration-300 ease-in-out overflow-x-auto","[&_*]:ring-0 [&_*]:outline-none [&_*]:focus:ring-0 [&_*]:focus:outline-none","[&_pre>code]:pr-[3rem] [&_pre>code>span.line-highlight]:min-w-[calc(100%+3rem)] [&_pre>code>span.line-diff]:min-w-[calc(100%+3rem)]",f.filename?"rounded-[14px]":"rounded-2xl","system"===P?"bg-white":"bg-codeblock",(e||n)&&"overflow-auto",e&&(A?"overflow-y-auto":"overflow-y-hidden"),k?"code-block-wrap overflow-x-hidden whitespace-pre-wrap":"overflow-x-auto",C&&"has-line-numbers",E&&"has-focused",F&&"has-highlighted",(0,l.O)(P)),"data-component-part":"code-block-root",style:{fontVariantLigatures:"none",height:e?A?`${j}px`:`${h&&h<d?45:190}px`:n?"100%":"auto"},children:(0,a.jsx)(()=>L?(0,a.jsx)("div",{suppressHydrationWarning:!0,ref:M,className:(0,o.cn)("font-mono",k?"whitespace-pre-wrap":"whitespace-pre",n&&"flex-none text-sm h-full",p?"text-xs leading-[1.35rem]":"leading-6"),...n&&{"data-component-part":"code-group-tab-content"},dangerouslySetInnerHTML:{__html:L}}):(0,a.jsx)("div",{suppressHydrationWarning:!0,ref:M,className:(0,o.cn)("font-mono",k?"whitespace-pre-wrap":"whitespace-pre",n&&"flex-none text-sm h-full",p?"text-xs leading-[1.35rem]":"leading-6"),...n&&{"data-component-part":"code-group-tab-content"},children:r}),{})}),e&&(0,a.jsx)(g,{isExpanded:A,toggleExpanded:N,numberOfLines:h})]})};function g({numberOfLines:e,isExpanded:t,toggleExpanded:r}){return e?(0,a.jsx)("div",{"data-component-part":"code-block-footer",className:"px-3 py-1 flex items-center text-xs font-medium text-gray-500 dark:text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,a.jsxs)("button",{"data-component-part":"code-block-footer-button",className:"flex-1 gap-1.5 flex items-center py-1.5",onClick:r,children:[(0,a.jsx)(n.A,{className:"h-3.5 w-3.5 shrink-0 text-gray-500 dark:text-gray-400"}),t?"Collapse":`See all ${e} line${1===e?"":"s"}`]})}):null}},55372:(e,t,r)=>{"use strict";r.d(t,{EO:()=>y,Jm:()=>v,SQ:()=>x});var a=r(37932),n=r(7620),s=r(90487),i=r(71197),o=r(93351),l=r(41384),c=r(84873),u=r(46346);let d=async({datasetId:e,query:t,apiKey:r,filters:a,signal:n})=>{let s=2*t.trim().split(/\s+/).length,i=await fetch("https://api.mintlifytrieve.com/api/chunk_group/group_oriented_autocomplete",{method:"POST",headers:{"Content-Type":"application/json","TR-Dataset":e,Authorization:r,"X-API-Version":"V2"},signal:n,body:JSON.stringify({query:t,search_type:"fulltext",extend_results:!0,highlight_options:{highlight_window:10,highlight_max_num:1,highlight_max_length:s,highlight_strategy:"exactmatch",highlight_delimiters:["?",",",".","!","\n"]},score_threshold:.2,filters:{must_not:[{field:"tag_set",match:["code"]}],...f(a)},page_size:6,group_size:3})});return p(await i.json())},p=e=>"object"==typeof e&&"results"in e&&Array.isArray(e.results)?g(e.results.map(e=>{if(!e.chunks||0===e.chunks.length)return;let{chunk:t,highlights:r,score:a}=e.chunks.reduce((e,t)=>e.score>t.score?e:t);if(!t.chunk_html||!t.link)return;let n=t.chunk_html.indexOf("\n"),s=t.chunk_html.substring(0,n),i=t.chunk_html.substring(n+1),o=r[0];return o?.startsWith(s)&&(o=o.replace(s,"")),{id:t.id,header:s,content:o?o.substring(0,100):i.substring(0,100),page:t.link,link:h(t),metadata:t.metadata,score:a}}).filter(e=>void 0!==e)):[],g=e=>{let t=new Map;for(let r of e)r&&(t.has(r.page)||t.set(r.page,r));return[...t.values()]},h=e=>{if(!("metadata"in e&&e.metadata&&"title"in e.metadata&&"link"in e&&"string"==typeof e.link))return"";let t=e.metadata.title,r=(0,c.M)(e.link);if(t&&"string"==typeof t)return`${r}#${(0,u.A)(t,{decamelize:!1})}`;return r},f=e=>{let{groups:t,language:r,version:a}=e;if(!t&&!r&&!a)return{};let n=[];return t&&n.push({field:"tag_set",match_any:[...t.map(e=>`USERAUTH:${e}`)]}),r&&n.push({field:"tag_set",match_any:[`LANGUAGE:${r}`,"LANGUAGE:*"]}),a&&n.push({field:"tag_set",match_any:[`VERSION:${a}`,"VERSION:*"]}),{must:n}};var m=r(27622);let v="search_api_key",y="public_search_api_key",b=e=>{let t=a.A.get(e?v:y);if(!t)return{key:null,expiresAt:null,locale:void 0,version:void 0};try{return JSON.parse(t)}catch{return{key:null,expiresAt:null,locale:void 0,version:void 0}}};function x(){let{subdomain:e,trieve:t}=(0,n.useContext)(i.Em),{userInfo:r,auth:c,isFetchingUserInfo:u}=(0,n.useContext)(i.cy),p=(0,m.f)(),{selectedLocale:g,selectedVersion:h,isUpdatingCache:f}=(0,n.useContext)(o._),[x,k]=(0,n.useState)(()=>{if(!l.cD.AUTH_ENABLED)return l.cD.TRIEVE_API_KEY||"";let{key:e}=b(!!r);return e||""}),C=(0,n.useRef)(0),w=(0,n.useRef)(null),[S,P]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{let n;if(!l.cD.AUTH_ENABLED||!t?.datasetId||u||f||S||C.current>=3)return;let{key:i,expiresAt:o,locale:d,version:p}=b(!!r),m=d!==g||p!==h,x=!i||o&&Date.now()>o;!i||m||x||k(i);let w=async()=>{if(l.cD.AUTH_ENABLED){P(!0);try{var t,n;let i=c?.partial&&!r?`/_mintlify/api-public/${e}/search-api-key`:"/_mintlify/api/search-api-key",o=await fetch(i,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...g||h?{filters:{language:g,version:h.length?h:void 0}}:{}})}),l=await o.json(),u=Date.now()+s.rQ;t=l.key,n=!!r,a.A.set(n?v:y,JSON.stringify({key:t,expiresAt:u,locale:g,version:h}),{expires:new Date(u),secure:!0,sameSite:"strict"}),k(l.key),C.current=0}catch(e){console.error("Failed to fetch search API key",e),C.current=C.current+1}finally{P(!1)}}};(m||x)&&w();let A=setTimeout(()=>{w(),n=setInterval(()=>void w(),s.rQ-s.Az)},Math.max(0,(o||Date.now()+s.rQ)-Date.now()-s.Az));return()=>{clearTimeout(A),clearInterval(n)}},[r,g,h,e,c?.partial,u,f,S,t?.datasetId]),(0,n.useEffect)(()=>()=>{w.current&&w.current.abort()},[]),{search:(0,n.useCallback)(async e=>{if(!t?.datasetId)return[];w.current&&w.current.abort();let r=new AbortController;w.current=r;try{return await d({datasetId:t.datasetId,query:e,apiKey:x,filters:{groups:p,version:""!==h?h:void 0,language:g},signal:r.signal})}catch(e){if(e instanceof Error&&"AbortError"===e.name)return[];throw e}},[t?.datasetId,x,p,h,g])}}},58397:(e,t,r)=>{"use strict";r.d(t,{O:()=>a});let a=e=>"string"==typeof e||"number"==typeof e?e.toString():e instanceof Array?e.map(a).join(""):e&&"object"==typeof e&&"props"in e&&e.props&&"object"==typeof e.props&&"children"in e.props?a(e.props.children):""},59060:(e,t,r)=>{"use strict";r.d(t,{b:()=>a,r:()=>n});let a="docs.content.view",n={"docs.expandable.open":"expandable_open","docs.expandable.close":"expandable_close","docs.accordion.open":"accordion_open","docs.accordion.close":"accordion_close","docs.navitem.click":"header_nav_item_click","docs.navitem.cta_click":"cta_click","docs.search.close":"search_close","docs.api_playground.request":"api_playground_call","docs.search.result_click":"search_result_click","docs.code_block.copy":"code_block_copy","docs.code_block.ask_ai":"ask_ai_code_block","docs.footer.powered_by_mintlify_click":"powered_by_mintlify_click","docs.assistant.enter":"chat_enter","docs.assistant.completed":"chat_completed","docs.assistant.shared":"chat_shared","docs.assistant.source_click":"ai_chat_citation_click","docs.assistant.thumbs_up":"ai_chat_feedback_positive_click","docs.assistant.thumbs_down":"ai_chat_feedback_negative_click"}},59538:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(91326).v)(e=>({status:"ready",messages:[]}))},60502:(e,t,r)=>{"use strict";r.d(t,{O:()=>s});let a="scrollbar-thin scrollbar-thumb-rounded",n={dark:`${a} scrollbar-thumb-white/20 dark:scrollbar-thumb-white/20 hover:scrollbar-thumb-white/25 dark:hover:scrollbar-thumb-white/25 active:scrollbar-thumb-white/25 dark:active:scrollbar-thumb-white/25`,system:`${a} scrollbar-thumb-black/15 hover:scrollbar-thumb-black/20 active:scrollbar-thumb-black/20 dark:scrollbar-thumb-white/20 dark:hover:scrollbar-thumb-white/25 dark:active:scrollbar-thumb-white/25`},s=e=>"system"===e?n.system:n.dark},65042:()=>{},66415:(e,t,r)=>{"use strict";r.d(t,{E:()=>a});let a=r(7620).useLayoutEffect},68718:(e,t,r)=>{"use strict";r.d(t,{f:()=>n});let a={language:!0,filename:!0,icon:!0,lang:!0,lines:!0,wrap:!0,expandable:!0,highlight:!0,focus:!0,numberOfLines:!0,hideAskAiButton:!0,isSmallText:!0},n=e=>{if(!e)return{};let t={};return Object.keys(a).forEach(r=>{void 0!==e[r]&&(t[r]=e[r])}),t}},71476:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ChatAssistantContext:()=>es,DEBOUNCE_TIME_IN_MS:()=>ea,DesktopSearchEntry:()=>er,SearchButton:()=>W,SearchContext:()=>ei,SearchProvider:()=>eo});var a=r(54568),n=r(85523),s=r(52317),i=r(68090),o=r(20591),l=r.n(o),c=r(6),u=r.n(c),d=r(15173),p=r(62942),g=r(7620),h=r(12459),f=r(71197),m=r(93351),v=r(41384),y=r(88374),b=r(53132),x=r(45978);let k="mintlify-recent-searches";var C=r(55372),w=r(40113),S=r(60710),P=r(16600),A=r(92451),j=r(27911),M=r(46060),N=r(89619);let T=({type:e,icon:t})=>{let r="text-gray-500 dark:text-gray-400 h-4 w-4 shrink-0";return"api-playground"===e?(0,a.jsx)(S.k3,{className:(0,P.cn)(r,"text-primary dark:text-primary-light")}):"section"===e?(0,a.jsx)(S.y$,{className:r}):t?(0,a.jsx)(N.VE,{icon:t,iconType:"regular",className:(0,P.cn)(r,"bg-gray-500 dark:bg-gray-400"),overrideColor:!0}):(0,a.jsx)(S.Zw,{className:r})},O="truncate text-sm leading-[18px] text-gray-800 dark:text-gray-200",I="text-xs truncate w-full text-gray-500 [&_mark]:text-gray-500 [&_mark]:bg-transparent",_="[&_mark]:bg-transparent",L="[&_span.font-medium]:text-primary dark:[&_span.font-medium]:text-primary-light",E=(0,P.cn)(O,_,"[&_mark_b]:font-medium [&_mark_b]:text-md [&_mark_b]:text-primary dark:[&_mark_b]:text-primary-light",L),F=(0,P.cn)(O,_,"[&_mark_b]:font-medium [&_mark_b]:text-md [&_mark_b]:text-gray-800 dark:[&_mark_b]:text-gray-200","[&_span.font-medium]:text-gray-800 dark:[&_span.font-medium]:text-gray-200"),H=(0,P.cn)(I,"[&_mark_b]:font-normal [&_mark_b]:text-primary dark:[&_mark_b]:text-primary-light [&_b_mark]:font-normal [&_b_mark]:text-primary dark:[&_b_mark]:text-primary-light",L),B=(0,P.cn)(I,"[&_mark_b]:font-normal [&_mark_b]:text-gray-500 dark:[&_mark_b]:text-gray-400 [&_b_mark]:font-normal [&_b_mark]:text-gray-500 dark:[&_b_mark]:text-gray-400","[&_span.font-medium]:text-gray-500 dark:[&_span.font-medium]:text-gray-400"),D=(0,P.cn)(O,_,"[&_mark_b]:font-normal [&_mark_b]:text-md [&_mark_b]:text-gray-500 dark:[&_mark_b]:text-gray-400","[&_span.font-medium]:text-gray-500 dark:[&_span.font-medium]:text-gray-400");function V({isActive:e,header:t,description:r,metadata:n,duplicatedHeaders:s,commonBreadcrumbs:i,shouldHighlightContext:o=!0}){let l=n&&"openapi"in n?String(n.openapi):void 0,c=l?(0,A.s)(l):void 0,u=!!s?.has(t),d=n?z(n,t,u,i):[t];return(0,a.jsxs)("div",{className:(0,P.cn)("cursor-pointer relative rounded-xl flex gap-3 px-2.5 py-2 items-center",e&&"bg-[#F7F7F8] dark:bg-white/5"),children:[(0,a.jsx)(T,{type:c?"api-playground":n?.hash?"section":"page",icon:n?.icon}),(0,a.jsxs)("div",{className:"flex flex-col flex-1 min-w-0 gap-1",children:[(0,a.jsx)("div",{className:"flex gap-1 items-center",children:(0,a.jsx)(G,{crumbs:d,shouldHighlightContext:o})}),(0,a.jsxs)("div",{className:"flex gap-1 items-center text-gray-800 dark:text-gray-200",children:[c&&(0,a.jsx)(M.l,{method:c.method,className:"text-xs font-mono rounded px-1 py-0"}),(0,a.jsx)("div",{className:(0,P.cn)(o?E:F,"font-medium"),dangerouslySetInnerHTML:{__html:t.trim()}})]}),r&&(0,a.jsx)("p",{className:(0,P.cn)(o?H:B),dangerouslySetInnerHTML:{__html:r}})]}),(0,a.jsx)(j.A,{size:20,className:(0,P.cn)("text-transparent",e&&"text-gray-500 dark:text-gray-400")})]})}function R(){return(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"16",height:"16",fill:"currentColor",className:"mx-0.5 flex-shrink-0 size-3 text-gray-500 dark:text-gray-400",children:(0,a.jsx)("path",{d:"M13.1717 12.0007L8.22192 7.05093L9.63614 5.63672L16.0001 12.0007L9.63614 18.3646L8.22192 16.9504L13.1717 12.0007Z"})})}function G({crumbs:e,shouldHighlightContext:t}){return(0,a.jsx)("div",{className:"flex items-center min-w-0 flex-1",children:e.map((r,n)=>{let s=n>=e.length-2;return(0,a.jsxs)("div",{className:(0,P.cn)("flex items-center",s?"min-w-0 flex-shrink":"flex-shrink-0"),children:[!!n&&(0,a.jsx)(R,{}),(0,a.jsx)("div",{className:(0,P.cn)(t?E:D,"text-xs text-gray-500 dark:text-gray-400",s?"truncate":"w-fit"),dangerouslySetInnerHTML:{__html:r.trim()}})]},n)})})}let z=(e,t,r,a)=>{let n="title"in e&&"string"==typeof e.title?e.title:void 0,s="breadcrumbs"in e&&Array.isArray(e.breadcrumbs)?e.breadcrumbs:void 0;return s?r?(s.at(-1)===n?s.slice(0,-1):s).concat([t]):s.at(-1)!==n?s.concat([t]).slice(-2):s.filter(e=>!a?.has(e)):[t]},q=["Ctrl ","Control"],U=["⌘","Command"];function $(){let[e,t]=(0,g.useState)(U);return(0,g.useEffect)(()=>{"undefined"!=typeof navigator&&(/(Mac|iPhone|iPod|iPad)/i.test(navigator.platform)?t(U):t(q))},[]),e}function W({className:e,children:t,id:r}){let n=$(),{onSearchModalOpen:s}=(0,g.useContext)(ei);return(0,a.jsx)("button",{type:"button",onClick:s,className:e,id:r,children:"function"==typeof t?t({actionKey:n}):t})}var Q=r(60654),J=r(44908);let K=Q.Kq,Y=Q.bL,Z=g.forwardRef(({...e},t)=>(0,a.jsx)(Q.l9,{ref:t,...e}));Z.displayName=Q.l9.displayName;let X=g.forwardRef(({...e},t)=>(0,a.jsx)(Q.i3,{ref:t,asChild:!0,...e,children:(0,a.jsxs)("div",{className:"absolute -translate-y-[2px] -translate-x-1/2",children:[(0,a.jsx)("div",{className:"top-0 left-0 right-0 w-full h-px bg-white dark:bg-background-dark absolute z-10"}),(0,a.jsx)("svg",{width:"16",height:"6",viewBox:"0 0 16 6",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"stroke-[1px] text-white dark:text-background-dark filter stroke-gray-400/20 dark:stroke-gray-600/30",children:(0,a.jsx)("path",{d:"M15.0078 -0.5C15.4952 -0.0122439 15.0229 0.490129 15.0049 0.5C14.9992 0.500053 14.9231 0.499973 14.7764 0.5C13.1909 0.500305 11.5933 1.41218 10.3467 2.39355C9.73304 2.87665 9.22446 3.36099 8.86914 3.72461C8.69187 3.90602 8.55326 4.05671 8.45996 4.16113C8.41333 4.21333 8.37773 4.25423 8.35449 4.28125C8.34291 4.29472 8.33456 4.30506 8.3291 4.31152C8.3264 4.31472 8.3244 4.31697 8.32324 4.31836L8.32227 4.31934L7.94922 4.76855L7.5625 4.33105L7.56152 4.3291C7.56022 4.32764 7.55765 4.32557 7.55469 4.32227C7.54879 4.31568 7.53965 4.3055 7.52734 4.29199C7.50247 4.26468 7.46452 4.2234 7.41504 4.1709C7.31605 4.06586 7.1694 3.91458 6.9834 3.73242C6.61066 3.3674 6.08072 2.88124 5.45215 2.39648C4.17101 1.40851 2.57521 0.500515 1.09766 0.5H0.995117C0.978923 0.487615 0.506451 -0.00437714 0.984375 -0.499023C0.989411 -0.499154 0.995327 -0.499982 0.99707 -0.5H15.0078Z",fill:"currentColor"})})]})}));X.displayName=Q.i3.displayName;let ee=(0,J.F)("z-50 overflow-hidden bg-white dark:bg-background-dark border border-gray-400/20 dark:border-gray-600/30 animate-fade-in",{variants:{size:{sm:"px-2 py-1 rounded",md:"px-3 py-2 rounded-[10px]",lg:"px-4 py-3 rounded-xl"}}}),et=g.forwardRef(({className:e,headline:t,size:r,children:n,showArrow:s=!0,...i},o)=>{r=t&&!r?"lg":r??"sm";let l=i.sideOffset??4;return(0,a.jsx)(Q.ZL,{children:(0,a.jsxs)(Q.UC,{ref:o,onPointerDownOutside:e=>e.preventDefault(),sideOffset:l,className:(0,P.cn)(ee({size:r}),t&&"flex flex-col gap-1",e),...i,children:[s&&(0,a.jsx)(X,{}),t&&(0,a.jsx)("div",{children:t}),n]})})});function er({className:e,searchButtonClassName:t,includeAskAiText:r}){let{searchPrompt:n,hasChatPermissions:s}=(0,g.useContext)(ei),{onChatSheetToggle:i}=(0,g.useContext)(es),o=$(),l=(0,g.useMemo)(()=>o[0]?.toUpperCase().trim()??"⌘",[o]);return(0,a.jsxs)("div",{className:(0,P.cn)("relative hidden lg:flex items-center gap-2.5 flex-1",e),children:[(0,a.jsx)(W,{className:(0,P.cn)("flex dark:bg-gray-900 pointer-events-auto rounded-xl w-full items-center text-sm leading-6 h-9 pl-3.5 pr-3 shadow-sm text-gray-500 dark:text-white/50 bg-background-light dark:bg-background-dark dark:brightness-[1.1] dark:ring-1 dark:hover:brightness-[1.25] ring-1 ring-gray-400/20 hover:ring-gray-600/25 dark:ring-gray-600/30 dark:hover:ring-gray-500/30 focus:outline-primary justify-between truncate gap-2 min-w-[43px]",t),id:w.V.SearchBarEntry,children:({actionKey:e})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 min-w-[42px]",children:[(0,a.jsx)(d.A,{size:16,className:"min-w-4 flex-none text-gray-700 hover:text-gray-800 dark:text-gray-400 hover:dark:text-gray-200"}),(0,a.jsx)("div",{className:"truncate min-w-0",children:n})]}),e&&(0,a.jsxs)("span",{className:"flex-none text-xs font-semibold",children:[e[0],"K"]})]})}),s&&(0,a.jsx)(K,{children:(0,a.jsxs)(Y,{children:[(0,a.jsx)(Z,{asChild:!0,children:(0,a.jsxs)("button",{type:"button",className:(0,P.cn)("flex-none hidden lg:flex items-center justify-center gap-1.5 pl-3 pr-3.5 h-9 rounded-xl shadow-sm bg-background-light dark:bg-background-dark dark:brightness-[1.1] dark:ring-1 dark:hover:brightness-[1.25] ring-1 ring-gray-400/20 hover:ring-gray-600/25 dark:ring-gray-600/30 dark:hover:ring-gray-500/30 focus:outline-primary",!r&&"w-9",t),onClick:i,id:w.V.AssistantEntry,children:[(0,a.jsx)(S.BZ,{className:(0,P.cn)("w-4 h-4","shrink-0 text-gray-700 hover:text-gray-800 dark:text-gray-400 hover:dark:text-gray-200")}),r&&(0,a.jsx)("span",{className:"text-sm text-gray-500 dark:text-white/50 whitespace-nowrap",children:"Ask AI"})]})}),(0,a.jsxs)(et,{size:"md",side:r?"bottom":"right",sideOffset:8,className:"flex gap-2 items-center bg-white opacity-100 dark:bg-background-dark font-normal shadow-xl shadow-gray-500/5 dark:shadow-gray-500/5 rounded-xl",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-white/70 whitespace-nowrap",children:"Toggle assistant panel"}),(0,a.jsxs)("span",{className:"flex items-center gap-1 text-sm text-gray-500 dark:text-white/50 whitespace-nowrap",children:[(0,a.jsx)("p",{className:"text-gray-500 dark:text-white/70 min-w-5 h-5 px-1 bg-gray-100 dark:bg-white/15 flex items-center justify-center rounded-md font-mono",children:l}),(0,a.jsx)("p",{children:"+"}),(0,a.jsx)("span",{className:"text-gray-500 dark:text-white/70 w-5 h-5 bg-gray-100 dark:bg-white/15 flex items-center justify-center rounded-md font-mono text-xs",children:"I"})]})]})]})})]})}et.displayName=Q.UC.displayName;let ea=20,en="_mintlify-ask-ai-assistant",es=(0,g.createContext)({isChatSheetOpen:!1,shouldFocusChatSheet:!1,onChatSheetToggle:()=>{},setShouldFocusChatSheet:e=>{}}),ei=(0,g.createContext)({onSearchModalOpen:()=>{},hasChatPermissions:!1,assistantConfig:void 0,searchPrompt:"Search..."});function eo({subdomain:e,hasChatPermissions:t=!1,assistantConfig:r,children:o}){let c=(0,p.useRouter)(),{docsConfig:A}=(0,g.useContext)(f.H6),{trieve:j}=(0,g.useContext)(f.Em),{search:M,askAIAssistant:N,canYouTellMeAbout:T,recentSearches:O}=(0,m.n)(),I=A?.search?.prompt??M,_=(0,g.useRef)(null),[L,E]=(0,g.useState)(!1),[F,H]=(0,g.useState)(!1),[B,D]=(0,g.useState)(!1),[R,G]=(0,g.useState)(""),[z,q]=(0,g.useState)([]),[U,$]=(0,g.useState)(!1),[W,Q]=(0,g.useState)(),[J,K]=(0,g.useState)(),Y=(0,y.p)("docs.search.result_click"),Z=(0,y.p)("docs.search.close"),{append:X}=(0,b.w)(),{recentSearches:ee,addRecentSearch:et}=function(){let[e,t]=(0,g.useState)([]);(0,g.useEffect)(()=>{try{let e=localStorage.getItem(k);if(e){let r=JSON.parse(e);if(Array.isArray(r)){let e=r.filter(e=>e&&"object"==typeof e&&"string"==typeof e.id&&"string"==typeof e.query&&"number"==typeof e.timestamp&&e.searchHit&&"object"==typeof e.searchHit&&"string"==typeof e.searchHit.id);t(e)}}}catch(e){console.error("Failed to load recent searches from localStorage:",e),t([])}},[]);let r=(0,g.useCallback)(e=>{try{localStorage.setItem(k,JSON.stringify(e))}catch(e){console.error("Failed to save recent searches to localStorage:",e)}},[]),a=(0,g.useCallback)((e,a)=>{e.trim()&&a.id&&t(t=>{let n=t.filter(e=>e.searchHit.id!==a.id),s=[{id:`${a.id}-${Date.now()}`,query:e.trim(),searchHit:a,timestamp:Date.now()},...n].slice(0,8);return r(s),s})},[r]);return{recentSearches:e,addRecentSearch:a,removeRecentSearch:(0,g.useCallback)(e=>{t(t=>{let a=t.filter(t=>t.id!==e);return r(a),a})},[r]),clearRecentSearches:(0,g.useCallback)(()=>{t([]),r([])},[r])}}(),{search:er}=(0,C.SQ)(),eo="cli"===v.cD.ENV,el=(0,g.useCallback)(()=>E(!0),[]),ec=(0,g.useCallback)(()=>E(!1),[]),eu=(0,g.useCallback)(()=>E(e=>!e),[]),ed=(0,g.useCallback)(()=>{t&&H(e=>(D(!e),!e))},[t]),[ep,eg]=(0,g.useState)(!1);(0,x.f)({key:"k",callback:eu}),(0,x.f)({key:"i",callback:ed,isDisabled:!t}),(0,x.f)({key:"?",callback:ed,isSingleKey:!0,excludedActiveElementId:[w.V.ChatAssistantTextArea,w.V.SearchInput,w.V.APIPlaygroundInput],isDisabled:!t}),(0,g.useEffect)(()=>{L||eg(!1)},[L]);let eh=(0,g.useMemo)(()=>l()(async t=>{if(G(t),!t||null==e)return void q([]);if(j?.datasetId){$(!0);try{let e=await er(t);q(e),K(((e,t=!1)=>{if(e.length<=1)return new Set;for(let t of e)if(0===t.length)return new Set;let r=new Set,a=Math.min(...e.map(e=>e.length)),n=t?a-1:a;for(let t=0;t<n;t++){let a=e[0]?.[t];if(void 0===a)continue;let n=!0;for(let r=1;r<e.length;r++)if(e[r]?.[t]!==a){n=!1;break}n&&r.add(a)}return r})(e.map(e=>Array.isArray(e.metadata?.breadcrumbs)?e.metadata.breadcrumbs:[]),!0)),Q((e=>{let t=new Set,r=new Set;for(let a of e)t.has(a)?r.add(a):t.add(a);return r})(e.map(e=>e.header)))}catch(e){console.error("Search request failed:",e),q([])}finally{$(!1)}return}},ea),[e,j?.datasetId,er]),ef=(0,g.useCallback)(e=>`${T} ${e}?`,[T]),em=(0,g.useCallback)(async(e,t)=>{if(e===en)ec(),H(!0),D(!0),X({id:u()(),role:"user",content:t});else{if(!e||"string"==typeof e)return;t&&e.id&&et(t,e),Y({query:t,path:e.link,section:e.header}),c.push(e.link),ec(),Z({query:t,numHits:z.length}).catch(console.error)}G(""),q([])},[ec,c,Z,Y,z.length,X,et]),ev=(0,g.useMemo)(()=>({isChatSheetOpen:F,shouldFocusChatSheet:B,onChatSheetToggle:ed,setShouldFocusChatSheet:D}),[F,B,ed,D]);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(es.Provider,{value:ev,children:(0,a.jsx)(ei.Provider,{value:(0,g.useMemo)(()=>({onSearchModalOpen:el,hasChatPermissions:t,searchPrompt:I,assistantConfig:r}),[el,t,I,r]),children:o})}),(0,a.jsx)(n.e,{show:L,as:g.Fragment,afterLeave:()=>{G(""),q([])},appear:!0,children:(0,a.jsxs)(s.lG,{as:"div",className:(0,P.cn)(h.f.Popup,"relative"),onClose:ec,children:[(0,a.jsx)(n._,{as:g.Fragment,enter:"ease-out duration-100",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-50",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,a.jsx)("div",{className:"fixed inset-0 bg-background-dark/20 transition-opacity backdrop-blur-sm"})}),(0,a.jsx)("div",{className:"fixed inset-0 w-screen h-screen z-10 overflow-y-auto p-4 sm:p-6 md:p-12",onClick:ec,children:(0,a.jsx)(n._,{as:g.Fragment,enter:"ease-out duration-100",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-50",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,a.jsx)(s.Lj,{className:"flex flex-col mx-auto max-w-[640px] transform overflow-hidden border border-gray-200 dark:border-white/10 bg-background-light dark:bg-background-dark shadow-search rounded-search",children:(0,a.jsxs)(i.G3,{onChange:e=>em(e,R),value:R,children:[(0,a.jsxs)("div",{className:(0,P.cn)("p-1.5 relative z-10 border-b border-transparent h-14 transition",ep&&"border-gray-200 dark:border-white/10"),children:[eo?(0,a.jsx)("div",{className:"flex items-center rounded-xl border border-gray-200 dark:border-white/10 bg-background-light dark:bg-background-dark h-full w-full outline-none text-gray-950/50 dark:text-white/50 tracking-tight pl-12 pr-14",children:(0,a.jsx)("span",{children:"Not available on local preview"})}):(0,a.jsx)(i.oK,{ref:_,id:w.V.SearchInput,className:(0,P.cn)("peer rounded-xl border border-gray-200 dark:border-white/10 bg-background-light dark:bg-background-dark h-full w-full outline-none text-gray-950 dark:text-white placeholder:text-gray-400 placeholder:dark:text-white/50 tracking-tight pl-11 pr-14 focus:border-slate-950 dark:focus:border-white transition",!ep&&R&&"shadow-search"),autoFocus:!0,placeholder:I,onChange:e=>{eh(e.target.value)},autoComplete:"off",onKeyDown:e=>{(z.length>0||ee.length>0||R&&t)&&"Tab"===e.key&&(e.preventDefault(),e.currentTarget.setSelectionRange(e.currentTarget.value.length,e.currentTarget.value.length)),"Escape"===e.key&&(ec(),Z({query:R,numHits:z.length}).catch(console.error))}}),(0,a.jsx)(d.A,{size:18,className:"absolute left-5 top-1/2 -translate-y-1/2 text-gray-950 dark:text-white opacity-50 peer-focus:opacity-100 peer-disabled:opacity-30"}),R&&(0,a.jsx)("div",{className:"top-1/2 -translate-y-1/2 absolute right-6 flex items-center justify-center rounded-md gap-1 px-1.5 py-1.5 bg-gray-950/5 dark:bg-white/5 text-zinc-950/70 dark:text-white/70 font-medium text-xs leading-[9px]",children:"ESC"})]}),(0,a.jsxs)(i.Kc,{static:!0,className:"max-h-[calc(100vh-10rem)] overflow-y-auto mx-1.5",onScroll:e=>{e.currentTarget.scrollTop?eg(!0):eg(!1)},children:[0===z.length&&!R&&ee.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"flex items-center justify-between px-2.5 py-2",children:(0,a.jsx)("span",{className:"text-gray-500 text-sm truncate",children:O})}),ee.map(e=>(0,a.jsx)(i.jO,{value:e.searchHit,className:"last:mb-2 group",children:({focus:t})=>(0,a.jsx)(V,{isActive:t,header:e.searchHit.header,description:e.searchHit.content,metadata:e.searchHit.metadata,duplicatedHeaders:W,shouldHighlightContext:!1})},e.searchHit.id))]}),!!R&&z.length>0&&(0,a.jsx)(a.Fragment,{children:z.map(e=>(0,a.jsx)(i.jO,{value:e,className:"last:mb-2",children:({focus:t})=>(0,a.jsx)(V,{isActive:t,header:e.header,description:e.content,metadata:e.metadata,duplicatedHeaders:W,commonBreadcrumbs:J})},e.id))}),t&&!U&&R&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"px-2.5 py-2 text-gray-500 text-sm truncate w-full",children:N}),(0,a.jsx)(i.jO,{value:en,className:"last:mb-2",children:({focus:e})=>(0,a.jsxs)("div",{className:(0,P.cn)("flex items-center gap-2 px-2.5 py-2 w-full cursor-pointer rounded-xl",e&&"bg-[#F7F7F8] dark:bg-white/5"),children:[(0,a.jsx)(S.BZ,{className:"text-primary dark:text-primary-dark h-4 w-4 shrink-0"})," ",(0,a.jsx)("span",{className:"font-medium text-gray-800 dark:text-gray-200 text-sm truncate",children:ef(R)})]})},en)]})]},"search-results-options")]})})})})]})})]})}ei.displayName="SearchContext"},74913:(e,t,r)=>{"use strict";r.d(t,{H:()=>a,_:()=>n});let a=e=>{switch(e?.toUpperCase()){case"GET":return{activeNavPillBg:"bg-[#2AB673]",activeNavPillText:"text-[#FFFFFF]",inactiveNavPillText:"text-green-700 dark:text-green-400",inactiveNavPillBg:"bg-green-400/20 dark:bg-green-400/20",paramStyle:"text-[#2AB673] bg-[#2AB673]/10 border-[#2AB673]/30"};case"POST":case"WEBHOOK":return{activeNavPillBg:"bg-[#3064E3]",activeNavPillText:"text-[#FFFFFF]",inactiveNavPillText:"text-blue-700 dark:text-blue-400",inactiveNavPillBg:"bg-blue-400/20 dark:bg-blue-400/20",paramStyle:"text-[#3064E3] bg-[#3064E3]/10 border-[#3064E3]/30"};case"PUT":return{activeNavPillBg:"bg-[#C28C30]",activeNavPillText:"text-[#FFFFFF]",inactiveNavPillText:"text-yellow-700 dark:text-yellow-400",inactiveNavPillBg:"bg-yellow-400/20 dark:bg-yellow-400/20",paramStyle:"text-[#C28C30] bg-[#C28C30]/10 border-[#C28C30]/30"};case"DELETE":return{activeNavPillBg:"bg-[#CB3A32]",activeNavPillText:"text-[#FFFFFF]",inactiveNavPillText:"text-red-700 dark:text-red-400",inactiveNavPillBg:"bg-red-400/20 dark:bg-red-400/20",paramStyle:"text-[#CB3A32] bg-[#CB3A32]/10 border-[#CB3A32]/30"};case"OPTIONS":return{activeNavPillBg:"bg-[#9341FB]",activeNavPillText:"text-[#FFFFFF]",inactiveNavPillText:"text-purple-700 dark:text-purple-400",inactiveNavPillBg:"bg-purple-400/20 dark:bg-purple-400/20",paramStyle:"text-[#9341FB] bg-[#9341FB]/10 border-[#9341FB]/30"};case"HEAD":return{activeNavPillBg:"bg-[#0e7490]",activeNavPillText:"text-[#FFFFFF]",inactiveNavPillText:"text-cyan-700 dark:text-cyan-400",inactiveNavPillBg:"bg-cyan-400/20 dark:bg-cyan-400/20",paramStyle:"text-[#0e7490] dark:text-[#00d3f2] bg-[#00d3f2]/10 border-[#00d3f2]/30"};case"PATCH":return{activeNavPillBg:"bg-[#DA622B]",activeNavPillText:"text-[#FFFFFF]",inactiveNavPillText:"text-orange-700 dark:text-orange-400",inactiveNavPillBg:"bg-orange-400/20 dark:bg-orange-400/20",paramStyle:"text-[#DA622B] bg-[#DA622B]/10 border-[#DA622B]/30"};case"TRACE":return{activeNavPillBg:"bg-[#BD30D3]",activeNavPillText:"text-[#FFFFFF]",inactiveNavPillText:"text-fuchsia-700 dark:text-fuchsia-400",inactiveNavPillBg:"bg-fuchsia-400/20 dark:bg-fuchsia-400/20",paramStyle:"text-[#BD30D3] bg-[#BD30D3]/10 border-[#BD30D3]/30"};case"WEBSOCKET":return{activeNavPillBg:"bg-[#F54A00] dark:bg-[#FF6900]",activeNavPillText:"text-[#FFFFFF]",inactiveNavPillText:"text-[#F54A00] dark:text-[#FF6900]",inactiveNavPillBg:"bg-[#FFEDD4] dark:bg-[#FF6900]/20",paramStyle:"text-[#F54A00] bg-[#FFEDD4]/10 border-[#FFEDD4]/30 dark:text-[#FF6900] dark:bg-[#FF6900]/10 dark:border-[#FF6900]/30"};case"DEPRECATED":return{activeNavPillBg:"bg-[#F7F7F8] dark:bg-[#14151A]",activeNavPillText:"text-[#14151A] dark:text-[#FFFFFF]",inactiveNavPillText:"text-[#14151A] dark:text-[#FFFFFF]",inactiveNavPillBg:"bg-[#F7F7F8] dark:bg-[#14151A]"};default:return{activeNavPillBg:"bg-gray-600",activeNavPillText:"text-[#FFFFFF]",inactiveNavPillText:"text-gray-700 dark:text-gray-400",inactiveNavPillBg:"bg-gray-400/20 dark:bg-gray-400/20",paramStyle:"text-gray-600 bg-gray-600/10 border-gray-600/30"}}},n=e=>{let t=e?.toUpperCase();switch(t){case"DELETE":return"DEL";case"WEBHOOK":return"HOOK";case"WEBSOCKET":return"WSS";default:return t}}},77573:(e,t,r)=>{"use strict";function a(e){return"string"!=typeof e?e:e.endsWith("/index")?e.slice(0,-6):"index"===e?"":e}r.d(t,{C:()=>a})},79321:(e,t,r)=>{"use strict";r.d(t,{o:()=>x,w:()=>k});var a=r(54568),n=r(7620),s=r(81802),i=r(71131),o=r(88638);r(65042);var l=r(44902),c=r(86493);let u=({className:e,children:t,codeBlockTheme:r,...n})=>{let s=(0,c.Fr)(t,e),i=(0,l.Sh)({codeString:s,codeBlockTheme:r,className:e,opts:{structure:"inline"}});return i?(0,a.jsx)("code",{...n,dangerouslySetInnerHTML:{__html:i}}):(0,a.jsx)("code",{...n,className:e,children:t})};var d=r(58397),p=r(71197),g=r(88374),h=r(53132),f=r(8080);let m=({markdownLinks:e})=>{let{threadId:t}=(0,h.w)(),r=(0,g.p)("docs.assistant.suggestion_click"),n=e.split("\n").map(e=>{let r=e.match(/\(([^)]*)\)\[([^\]]*)\]/);if(r&&r[1]&&r[2])return{text:r[1],url:r[2],threadId:t}}).filter(e=>void 0!==e);return(0,a.jsx)("div",{className:"flex flex-col gap-3 not-prose",children:n.map(e=>(0,a.jsx)(f.k,{href:e.url,onClick:()=>r({text:e.text,url:e.url}).catch(console.error),title:e.text},e.url))})};var v=r(16600),y=r(95391);let b=[i.A];function x({children:e,className:t,components:r,showCopyButton:i=!1}){let{docsConfig:l}=(0,n.useContext)(p.H6),c=l?.styling?.codeblocks;return(0,a.jsx)(s.oz,{remarkPlugins:b,components:{p:({children:e})=>(0,a.jsx)("p",{className:"whitespace-pre-line",children:e}),pre:({children:e,...t})=>{if("object"==typeof e&&null!==e&&"props"in e&&"object"==typeof e.props&&null!==e.props&&"className"in e.props&&"language-suggestions"===e.props.className)return e;let r=(0,d.O)(e);return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("pre",{...t,className:(0,v.cn)(y.SHIKI_CLASSNAME,"p-3 text-sm rounded-xl","system"===c&&"text-gray-950 bg-white codeblock-light dark:codeblock-dark dark:bg-codeblock dark:text-gray-50 border border-gray-200/70 dark:border-white/[0.14]",("dark"===c||void 0==c)&&"text-gray-50 bg-[#0F1117] dark:codeblock-dark dark:bg-codeblock border border-transparent dark:border-white/[0.14] codeblock-dark",t.className),children:e}),i&&(0,a.jsx)("div",{className:"absolute top-2 right-2",children:(0,a.jsx)(o.TN,{textToCopy:r,showTooltip:!1})})]})},code:({children:e,...t})=>{if("className"in t){let r=/language-(\w+)/.exec(t.className||"");if("suggestions"===(r?r[1]:void 0)&&"string"==typeof e)return(0,a.jsx)(m,{markdownLinks:e})}return(0,a.jsx)(u,{suppressHydrationWarning:!0,codeBlockTheme:c,...t,children:e})},...r},className:t,children:e})}let k=({children:e,className:t})=>{let r="text-base font-medium";return(0,a.jsx)(x,{components:{h1:({children:e})=>(0,a.jsx)("h1",{className:r,children:e}),h2:({children:e})=>(0,a.jsx)("h2",{className:r,children:e}),h3:({children:e})=>(0,a.jsx)("h3",{className:r,children:e}),h4:({children:e})=>(0,a.jsx)("h4",{className:r,children:e}),h5:({children:e})=>(0,a.jsx)("h5",{className:r,children:e}),h6:({children:e})=>(0,a.jsx)("h6",{className:r,children:e})},className:t,children:e})}},80976:(e,t,r)=>{"use strict";r.d(t,{y:()=>c,BannerProvider:()=>u});var a=r(54568),n=r(7620);let s="bannerDismissed",i="data-banner-state";function o(e){return void 0!==e?`${e}-${s}`:s}let l=o("__mintlify"),c=(0,n.createContext)({banner:null,initialBanner:null,dismissBanner:()=>{}}),u=({children:e,initialBanner:t,config:r,subdomain:s})=>{let u=o(s),[d,p]=(0,n.useState)(()=>{let e=document.documentElement.getAttribute(i);if("hidden"===e)return null;if("visible"===e&&t)return t;if(!t||!r?.content)return null;try{let e=localStorage.getItem(u);return e&&e===r.content?null:t}catch(e){return null}});return(0,n.useEffect)(()=>{!d&&t&&r?.content&&"visible"===document.documentElement.getAttribute(i)&&p(t)},[t,r?.content,d]),(0,a.jsx)(c.Provider,{value:{banner:d,initialBanner:t,dismissBanner:()=>{if(r?.dismissible){p(null);let e=document.getElementById("banner")?.innerText??r.content;try{localStorage.setItem(u,e),localStorage.setItem(l,e)}catch(e){console.error(e)}document.documentElement.setAttribute(i,"hidden")}}},children:e})}},84873:(e,t,r)=>{"use strict";function a(e){return e.startsWith("/")||e.startsWith("https:")||e.startsWith("http:")?e:"/"+e}r.d(t,{M:()=>a})},86493:(e,t,r)=>{"use strict";r.d(t,{Fr:()=>i,c$:()=>o});var a=r(7620),n=r(58397),s=r(95391);function i(e,t){return t?.includes(s.SHIKI_CLASSNAME)||function e(t){if(!t||"object"!=typeof t)return!1;if(Array.isArray(t)){for(let r of t)if(e(r))return!0}if("props"in t&&t.props&&"object"==typeof t.props){if("className"in t.props&&"string"==typeof t.props.className&&t.props.className.includes(s.SHIKI_CLASSNAME))return!0;if("children"in t.props)return e(t.props.children)}return!1}(e)?"":(0,n.O)(e)}function o(e,t){return(0,a.useMemo)(()=>void 0!==t?t:e?function(e){if(!e||"string"!=typeof e)return 0;let t=e.match(/<span class="line/g);return t?t.length:0}(e):void 0,[e,t])}},88301:(e,t,r)=>{"use strict";r.d(t,{A:()=>i,y:()=>s});var a=r(54568);let n=(0,r(7620).createContext)({});function s({value:e,children:t}){return(0,a.jsx)(n.Provider,{value:e??{},children:t})}n.displayName="AnalyticsContext";let i=n},88374:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var a=r(7620),n=r(88301),s=r(59060);let i=e=>{let{analyticsMediator:t}=(0,a.useContext)(n.A);return(0,a.useMemo)(()=>{if(!t)return e=>Promise.resolve();let r=t.createEventListener(e),a=function(e){if(s.r.hasOwnProperty(e))return s.r[e]}(e);if(a){let e=t.createEventListener(a);return t=>Promise.all([r(t),e(t)])}return r},[t,e])}},88638:(e,t,r)=>{"use strict";r.d(t,{NG:()=>h,TN:()=>m,iG:()=>y,rZ:()=>v});var a=r(54568),n=r(7620),s=r(71197),i=r(31467),o=r(89619),l=r(16600),c=r(68718),u=r(26997),d=r(5121),p=r(53330),g=r(58397);let h=(0,n.forwardRef)(function(e,t){let{filename:r,onCopied:o,children:u,className:h,icon:v,isSmallText:y,hideAskAiButton:b,...x}=e,{docsConfig:k}=(0,n.useContext)(s.H6),C=(0,g.O)(u),w=e=>(0,a.jsx)(m,{textToCopy:C,onCopied:o,...e}),S=k?.styling?.codeblocks;return(0,a.jsxs)("div",{className:(0,l.cn)(i.x.CodeBlock,"mt-5 mb-8 not-prose rounded-2xl relative group","system"===S&&"text-gray-950 bg-gray-50 dark:bg-white/5 dark:text-gray-50 codeblock-light border border-gray-950/10 dark:border-white/10",("dark"===S||void 0==S)&&"text-gray-50 bg-codeblock dark:bg-white/5 ring-1 ring-transparent dark:ring-white/[0.14] codeblock-dark",r||v?"p-0.5":"bg-transparent dark:bg-transparent",h),ref:t,...x,children:[r||v?(0,a.jsxs)(f,{filename:r,icon:v,children:[(0,a.jsx)(w,{}),(0,a.jsx)(d.y,{code:C,...(0,c.f)(e)})]}):(0,a.jsxs)("div",{className:"absolute top-3 right-4 flex items-center gap-1.5",children:[(0,a.jsx)(w,{}),(0,a.jsx)(d.y,{code:C,...(0,c.f)(e)})]}),(0,a.jsx)(p.S7,{isSmallText:y,hideAskAiButton:b,...e})]})});function f({filename:e,icon:t,children:r}){let{docsConfig:c}=(0,n.useContext)(s.H6),u=c?.styling?.codeblocks;return(0,a.jsxs)("div",{className:"flex text-gray-400 text-xs rounded-t-[14px] leading-6 font-medium pl-4 pr-2.5 py-1","data-component-part":"code-block-header",children:[(0,a.jsxs)("div",{className:(0,l.cn)("flex-none flex items-center gap-1.5 text-gray-700 dark:text-gray-300",("dark"===u||void 0==u)&&"text-gray-300"),"data-component-part":"code-block-header-filename",children:[t&&(0,a.jsx)(o.VE,{icon:t,iconType:"regular",className:(0,l.cn)("h-3.5 w-3.5 bg-gray-500 dark:bg-gray-400",i.x.CodeBlockIcon),overrideColor:!0}),e]}),(0,a.jsx)("div",{className:"flex-1 flex items-center justify-end gap-1.5",children:r})]})}function m({textToCopy:e,onCopied:t,className:r,showTooltip:s=!0}){let i=e.trim(),[o,l]=(0,n.useState)(!1),[c,d]=(0,n.useState)(!1);if((0,n.useEffect)(()=>{navigator.clipboard?d(!1):(console.warn("The browser's Clipboard API is unavailable. The Clipboard API is only available on HTTPS."),d(!0))},[]),!i||c)return null;let p=async()=>{let e=await (0,u.l)(i);t&&t(e,i),"success"===e&&(l(!0),setTimeout(()=>{l(!1)},2e3))};return(0,a.jsx)(v,{onClick:p,isCopiedActive:o,className:r,showTooltip:s})}function v({onClick:e,isCopiedActive:t,showTooltip:r=!0,className:i}){let{docsConfig:o}=(0,n.useContext)(s.H6),c=o?.styling?.codeblocks;return(0,a.jsxs)("div",{className:(0,l.cn)("z-10 relative",i),children:[(0,a.jsx)("button",{className:"h-[26px] w-[26px] flex items-center justify-center rounded-md backdrop-blur peer group/copy-button",onClick:e,"data-testid":"copy-code-button","aria-label":"Copy the contents from the code block",children:t?(0,a.jsx)("svg",{className:(0,l.cn)("system"===c&&"fill-primary dark:fill-primary-light",("dark"===c||void 0==c)&&"fill-primary-light"),width:"16",height:"11",viewBox:"0 0 16 11",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{d:"M14.7813 1.21873C15.0751 1.51248 15.0751 1.98748 14.7813 2.2781L6.53135 10.5312C6.2376 10.825 5.7626 10.825 5.47197 10.5312L1.21885 6.28123C0.925098 5.98748 0.925098 5.51248 1.21885 5.22185C1.5126 4.93123 1.9876 4.9281 2.27822 5.22185L5.99697 8.9406L13.7188 1.21873C14.0126 0.924976 14.4876 0.924976 14.7782 1.21873H14.7813Z"})}):(0,a.jsxs)("svg",{width:"18",height:"18",viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:(0,l.cn)("w-4 h-4","system"===c&&"text-gray-400 group-hover/copy-button:text-gray-500 dark:text-white/40 dark:group-hover/copy-button:text-white/60",("dark"===c||void 0==c)&&"text-white/40 group-hover/copy-button:text-white/60"),children:[(0,a.jsx)("path",{d:"M14.25 5.25H7.25C6.14543 5.25 5.25 6.14543 5.25 7.25V14.25C5.25 15.3546 6.14543 16.25 7.25 16.25H14.25C15.3546 16.25 16.25 15.3546 16.25 14.25V7.25C16.25 6.14543 15.3546 5.25 14.25 5.25Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M2.80103 11.998L1.77203 5.07397C1.61003 3.98097 2.36403 2.96397 3.45603 2.80197L10.38 1.77297C11.313 1.63397 12.19 2.16297 12.528 3.00097",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),r&&(0,a.jsx)(y,{text:t?"Copied!":"Copy"})]})}function y({text:e}){return(0,a.jsx)("div",{"aria-hidden":!0,className:"absolute top-11 left-1/2 transform whitespace-nowrap -translate-x-1/2 -translate-y-1/2 peer-hover:opacity-100 opacity-0 text-white rounded-lg px-1.5 py-0.5 text-xs bg-primary-dark",children:e})}},89619:(e,t,r)=>{"use strict";r.d(t,{VE:()=>u,Ay:()=>c});var a=r(54568),n=r(48836);let s=["brands","duotone","light","regular","sharp-duotone-solid","sharp-light","sharp-regular","sharp-solid","sharp-thin","solid","thin"];var i=r(7620),o=r(71197),l=r(16600);function c({style:e,icon:t,iconType:r,iconLibrary:n,className:s,color:l}){var c;let{pageType:u}=(0,i.useContext)(o.NQ),d=(c=t)&&["42-group","500px","accessible-icon","accusoft","adn","adversal","affiliatetheme","airbnb","algolia","alipay","amazon","amazon-pay","amilia","android","angellist","angrycreative","angular","app-store","app-store-ios","apper","apple","apple-pay","artstation","asymmetrik","atlassian","audible","autoprefixer","avianex","aviato","aws","bandcamp","battle-net","behance","bilibili","bimobject","bitbucket","bitcoin","bity","black-tie","blackberry","blogger","blogger-b","bluesky","bluetooth","bluetooth-b","bootstrap","bots","brave","brave-reverse","btc","buffer","buromobelexperte","buy-n-large","buysellads","canadian-maple-leaf","cc-amazon-pay","cc-amex","cc-apple-pay","cc-diners-club","cc-discover","cc-jcb","cc-mastercard","cc-paypal","cc-stripe","cc-visa","centercode","centos","chrome","chromecast","cloudflare","cloudscale","cloudsmith","cloudversify","cmplid","codepen","codiepie","confluence","connectdevelop","contao","cotton-bureau","cpanel","creative-commons","creative-commons-by","creative-commons-nc","creative-commons-nc-eu","creative-commons-nc-jp","creative-commons-nd","creative-commons-pd","creative-commons-pd-alt","creative-commons-remix","creative-commons-sa","creative-commons-sampling","creative-commons-sampling-plus","creative-commons-share","creative-commons-zero","critical-role","css3","css3-alt","cuttlefish","d-and-d","d-and-d-beyond","dailymotion","dart-lang","dashcube","debian","deezer","delicious","deploydog","deskpro","dev","deviantart","dhl","diaspora","digg","digital-ocean","discord","discourse","dochub","docker","draft2digital","dribbble","dropbox","drupal","dyalog","earlybirds","ebay","edge","edge-legacy","elementor","ello","ember","empire","envira","erlang","ethereum","etsy","evernote","expeditedssl","facebook","facebook-f","facebook-messenger","fantasy-flight-games","fedex","fedora","figma","firefox","firefox-browser","first-order","first-order-alt","firstdraft","flickr","flipboard","flutter","fly","font-awesome","fonticons","fonticons-fi","fort-awesome","fort-awesome-alt","forumbee","foursquare","free-code-camp","freebsd","fulcrum","galactic-republic","galactic-senate","get-pocket","gg","gg-circle","git","git-alt","github","github-alt","gitkraken","gitlab","gitter","glide","glide-g","gofore","golang","goodreads","goodreads-g","google","google-drive","google-pay","google-play","google-plus","google-plus-g","google-scholar","google-wallet","gratipay","grav","gripfire","grunt","guilded","gulp","hacker-news","hackerrank","hashnode","hips","hire-a-helper","hive","hooli","hornbill","hotjar","houzz","html5","hubspot","ideal","imdb","instagram","instalod","intercom","internet-explorer","invision","ioxhost","itch-io","itunes","itunes-note","java","jedi-order","jenkins","jira","joget","joomla","js","jsfiddle","jxl","kaggle","keybase","keycdn","kickstarter","kickstarter-k","korvue","laravel","lastfm","leanpub","less","letterboxd","line","linkedin","linkedin-in","linode","linux","lyft","magento","mailchimp","mandalorian","markdown","mastodon","maxcdn","mdb","medapps","medium","medrt","meetup","megaport","mendeley","meta","microblog","microsoft","mintbit","mix","mixcloud","mixer","mizuni","modx","monero","napster","neos","nfc-directional","nfc-symbol","nimblr","node","node-js","npm","ns8","nutritionix","octopus-deploy","odnoklassniki","odysee","old-republic","opencart","openid","opensuse","opera","optin-monster","orcid","osi","padlet","page4","pagelines","palfed","patreon","paypal","perbyte","periscope","phabricator","phoenix-framework","phoenix-squadron","php","pied-piper","pied-piper-alt","pied-piper-hat","pied-piper-pp","pinterest","pinterest-p","pix","pixiv","playstation","product-hunt","pushed","python","qq","quinscape","quora","r-project","raspberry-pi","ravelry","react","reacteurope","readme","rebel","red-river","reddit","reddit-alien","redhat","renren","replyd","researchgate","resolving","rev","rocketchat","rockrms","rust","safari","salesforce","sass","schlix","screenpal","scribd","searchengin","sellcast","sellsy","servicestack","shirtsinbulk","shoelace","shopify","shopware","signal-messenger","simplybuilt","sistrix","sith","sitrox","sketch","skyatlas","skype","slack","slideshare","snapchat","soundcloud","sourcetree","space-awesome","speakap","speaker-deck","spotify","square-behance","square-dribbble","square-facebook","square-font-awesome","square-font-awesome-stroke","square-git","square-github","square-gitlab","square-google-plus","square-hacker-news","square-instagram","square-js","square-lastfm","square-letterboxd","square-odnoklassniki","square-pied-piper","square-pinterest","square-reddit","square-snapchat","square-steam","square-threads","square-tumblr","square-twitter","square-upwork","square-viadeo","square-vimeo","square-web-awesome","square-web-awesome-stroke","square-whatsapp","square-x-twitter","square-xing","square-youtube","squarespace","stack-exchange","stack-overflow","stackpath","staylinked","steam","steam-symbol","sticker-mule","strava","stripe","stripe-s","stubber","studiovinari","stumbleupon","stumbleupon-circle","superpowers","supple","suse","swift","symfony","teamspeak","telegram","tencent-weibo","the-red-yeti","themeco","themeisle","think-peaks","threads","tiktok","trade-federation","trello","tumblr","twitch","twitter","typo3","uber","ubuntu","uikit","umbraco","uncharted","uniregistry","unity","unsplash","untappd","ups","upwork","usb","usps","ussunnah","vaadin","viacoin","viadeo","viber","vimeo","vimeo-v","vine","vk","vnv","vuejs","watchman-monitoring","waze","web-awesome","webflow","weebly","weibo","weixin","whatsapp","whmcs","wikipedia-w","windows","wirsindhandwerk","wix","wizards-of-the-coast","wodu","wolf-pack-battalion","wordpress","wordpress-simple","wpbeginner","wpexplorer","wpforms","wpressr","x-twitter","xbox","xing","y-combinator","yahoo","yammer","yandex","yandex-international","yarn","yelp","yoast","youtube","zhihu"].includes(c.toLowerCase())?`https://mintlify.b-cdn.net/v6.6.0/brands/${t}.svg`:"lucide"===n?`https://mintlify.b-cdn.net/v6.6.0/lucide/${t}.svg`:`https://mintlify.b-cdn.net/v6.6.0/${r??"regular"}/${t}.svg`;return"pdf"===u?(0,a.jsx)("img",{src:d,className:s,style:{backgroundColor:"transparent",...e},alt:t}):(0,a.jsx)("svg",{className:s,style:{WebkitMaskImage:`url(${d})`,WebkitMaskRepeat:"no-repeat",WebkitMaskPosition:"center",maskImage:`url(${d})`,maskRepeat:"no-repeat",maskPosition:"center",maskSize:"lucide"==n?"100%":void 0,backgroundColor:l,...e}})}function u({icon:e,iconType:t,className:r,style:u,color:d,overrideColor:p}){let{docsConfig:g}=(0,i.useContext)(o.H6),h=g?.icons?.library;return t&&!s.includes(t)?(console.log(`Invalid iconType ${t} expected a string equal to one of: brands, duotone, light, regular, sharp-solid, solid, thin`),null):"string"==typeof e&&((0,n.v)(e)||e.startsWith("/"))?e.startsWith("https://mintlify.b-cdn.net")?(0,a.jsx)("svg",{className:r,style:{WebkitMaskImage:`url(${e})`,WebkitMaskRepeat:"no-repeat",WebkitMaskPosition:"center",maskImage:`url(${e})`,maskRepeat:"no-repeat",maskPosition:"center",maskSize:"100%",backgroundColor:"currentColor",...u}}):(0,a.jsx)("img",{src:e,alt:e,className:(0,l.cn)(r,"bg-transparent"),style:u}):(0,a.jsx)(c,{icon:e.toLowerCase(),iconType:t,iconLibrary:h,className:(0,l.cn)(r,!d&&!p&&"bg-gray-800 dark:bg-gray-100"),style:u,color:d})}},92451:(e,t,r)=>{"use strict";r.d(t,{s:()=>n});let a=Object.values(r(63116).M5.HttpMethods).concat("webhook"),n=e=>{let t,r,n,s=e.trim().split(/\s+/);if(!(s.length>3)){if(s[0]&&s[1]&&s[2])[t,r,n]=s;else{if(!s[0]||!s[1])return;[r,n]=s}if(!r||(r=r.toLowerCase(),a.includes(r)))return{method:r,endpoint:n,filename:t}}}},92859:(e,t,r)=>{"use strict";r.d(t,{J:()=>x,U:()=>k});let a={language:"العربية",yes:"نعم",no:"لا",wasThisPageHelpful:"هل كانت هذه الصفحة مفيدة؟",onThisPage:"في هذه الصفحة",suggestEdits:"اقترح تعديلات",raiseIssue:"اطرح مشكلة",search:"...ابحث",poweredByMintlify:"مدعوم من Mintlify",filters:"المرشحات",clear:"مسح",previous:"السابق",next:"التالي",copyPage:"نسخ الصفحة",viewAsMarkdown:"عرض بصيغة Markdown",openInChatGPT:"فتح في ChatGPT",openInClaude:"فتح في Claude",openInPerplexity:"فتح في Perplexity",copyPageAsMarkdown:"نسخ الصفحة بصيغة Markdown لـ LLMs",viewPageAsMarkdown:"عرض هذه الصفحة كنص عادي",askQuestionsAboutPage:"اطرح أسئلة حول هذه الصفحة",copyMCPServer:"Copy MCP Server",copyMCPServerDescription:"نسخ MCP Server URL إلى الحافظة",connectToCursor:"Connect to Cursor",installMCPServerOnCursor:"تثبيت MCP Server على Cursor",connectToVSCode:"Connect to VSCode",installMCPServerOnVSCode:"تثبيت MCP Server على VSCode",assistant:"المساعد",askAQuestion:"اسأل سؤال...",askAIAssistant:"اسأل المساعد الذكي",canYouTellMeAbout:"هل يمكنك أن تخبرني عن",recentSearches:"البحث الأخير"},n={language:"Deutsch",yes:"Ja",no:"Nein",wasThisPageHelpful:"War diese Seite hilfreich?",onThisPage:"Auf dieser Seite",suggestEdits:"\xc4nderungen vorschlagen",raiseIssue:"Problem melden",search:"Suchen...",poweredByMintlify:"Bereitgestellt von Mintlify",filters:"Filter",clear:"L\xf6schen",previous:"Zur\xfcck",next:"Weiter",copyPage:"Seite kopieren",viewAsMarkdown:"Als Markdown anzeigen",openInChatGPT:"In ChatGPT \xf6ffnen",openInClaude:"In Claude \xf6ffnen",openInPerplexity:"In Perplexity \xf6ffnen",copyPageAsMarkdown:"Seite als Markdown f\xfcr LLMs kopieren",viewPageAsMarkdown:"Diese Seite als Klartext anzeigen",askQuestionsAboutPage:"Fragen zu dieser Seite stellen",copyMCPServer:"Copy MCP Server",copyMCPServerDescription:"MCP Server URL in die Zwischenablage kopieren",connectToCursor:"Connect to Cursor",installMCPServerOnCursor:"MCP Server in Cursor installieren",connectToVSCode:"Connect to VSCode",installMCPServerOnVSCode:"MCP Server in VSCode installieren",assistant:"Assistent",askAQuestion:"Frage stellen...",askAIAssistant:"Frage den AI-Assistenten",canYouTellMeAbout:"Kannst du mir erkl\xe4ren, was",recentSearches:"Zuletzt gesucht"},s={language:"English",yes:"Yes",no:"No",wasThisPageHelpful:"Was this page helpful?",onThisPage:"On this page",suggestEdits:"Suggest edits",raiseIssue:"Raise issue",search:"Search...",poweredByMintlify:"Powered by Mintlify",filters:"Filters",clear:"Clear",previous:"Previous",next:"Next",copyPage:"Copy page",viewAsMarkdown:"View as Markdown",openInChatGPT:"Open in ChatGPT",openInClaude:"Open in Claude",openInPerplexity:"Open in Perplexity",copyPageAsMarkdown:"Copy page as Markdown for LLMs",viewPageAsMarkdown:"View this page as plain text",askQuestionsAboutPage:"Ask questions about this page",copyMCPServer:"Copy MCP Server",copyMCPServerDescription:"Copy MCP Server URL to clipboard",connectToCursor:"Connect to Cursor",installMCPServerOnCursor:"Install MCP Server on Cursor",connectToVSCode:"Connect to VSCode",installMCPServerOnVSCode:"Install MCP Server on VSCode",assistant:"Assistant",askAQuestion:"Ask a question...",askAIAssistant:"Ask AI assistant",canYouTellMeAbout:"Can you tell me about",recentSearches:"Recent searches"},i={language:"Espa\xf1ol",yes:"S\xed",no:"No",wasThisPageHelpful:"\xbfEsta p\xe1gina le ayud\xf3?",onThisPage:"En esta p\xe1gina",suggestEdits:"Sugerir cambios",raiseIssue:"Reportar problema",search:"Busca...",poweredByMintlify:"Impulsado por Mintlify",filters:"Filtros",clear:"Limpiar",previous:"Anterior",next:"Siguiente",copyPage:"Copiar p\xe1gina",viewAsMarkdown:"Ver como Markdown",openInChatGPT:"Abrir en ChatGPT",openInClaude:"Abrir en Claude",openInPerplexity:"Abrir en Perplexity",copyPageAsMarkdown:"Copiar p\xe1gina como Markdown para LLMs",viewPageAsMarkdown:"Ver esta p\xe1gina como texto plano",askQuestionsAboutPage:"Hacer preguntas sobre esta p\xe1gina",copyMCPServer:"Copy MCP Server",copyMCPServerDescription:"Copiar URL del MCP Server al portapapeles",connectToCursor:"Connect to Cursor",installMCPServerOnCursor:"Instalar MCP Server en Cursor",connectToVSCode:"Connect to VSCode",installMCPServerOnVSCode:"Instalar MCP Server en VSCode",assistant:"Asistente",askAQuestion:"Hacer una pregunta...",askAIAssistant:"Hacer una pregunta al asistente",canYouTellMeAbout:"\xbfPuedes decirme sobre",recentSearches:"Busquedas recientes"},o={language:"Fran\xe7ais",yes:"Oui",no:"Non",wasThisPageHelpful:"Cette page vous a-t-elle \xe9t\xe9 utile ?",onThisPage:"Sur cette page",suggestEdits:"Sugg\xe9rer des modifications",raiseIssue:"Signaler un probl\xe8me",search:"Rechercher...",poweredByMintlify:"Propuls\xe9 par Mintlify",filters:"Filtres",clear:"Effacer",previous:"Pr\xe9c\xe9dent",next:"Suivant",copyPage:"Copier la page",viewAsMarkdown:"Voir en Markdown",openInChatGPT:"Ouvrir dans ChatGPT",openInClaude:"Ouvrir dans Claude",openInPerplexity:"Ouvrir dans Perplexity",copyPageAsMarkdown:"Copier la page en Markdown pour les LLMs",viewPageAsMarkdown:"Voir cette page en texte brut",askQuestionsAboutPage:"Poser des questions sur cette page",copyMCPServer:"Copy MCP Server",copyMCPServerDescription:"Copier l’URL du MCP Server dans le presse-papiers",connectToCursor:"Connect to Cursor",installMCPServerOnCursor:"Installer MCP Server sur Cursor",connectToVSCode:"Connect to VSCode",installMCPServerOnVSCode:"Installer MCP Server sur VSCode",assistant:"Assistant",askAQuestion:"Poser une question...",askAIAssistant:"Poser une question \xe0 l'assistant",canYouTellMeAbout:"Peux-tu me parler de",recentSearches:"Recherches r\xe9centes"},l={language:"Fran\xe7ais canadien",yes:"Oui",no:"Non",wasThisPageHelpful:"Cette page vous a-t-elle \xe9t\xe9 utile ?",onThisPage:"Sur cette page",suggestEdits:"Sugg\xe9rer des modifications",raiseIssue:"Signaler un probl\xe8me",search:"Rechercher...",poweredByMintlify:"Propuls\xe9 par Mintlify",filters:"Filtres",clear:"Effacer",previous:"Pr\xe9c\xe9dent",next:"Suivant",copyPage:"Copier la page",viewAsMarkdown:"Voir en Markdown",openInChatGPT:"Ouvrir dans ChatGPT",openInClaude:"Ouvrir dans Claude",openInPerplexity:"Ouvrir dans Perplexity",copyPageAsMarkdown:"Copier la page en Markdown pour les LLMs",viewPageAsMarkdown:"Voir cette page en texte brut",askQuestionsAboutPage:"Poser des questions sur cette page",copyMCPServer:"Copy MCP Server",copyMCPServerDescription:"Copier l’URL du MCP Server dans le presse-papiers",connectToCursor:"Connect to Cursor",installMCPServerOnCursor:"Installer MCP Server sur Cursor",connectToVSCode:"Connect to VSCode",installMCPServerOnVSCode:"Installer MCP Server sur VSCode",assistant:"Assistant",askAQuestion:"Poser une question...",askAIAssistant:"Poser une question \xe0 l'assistant",canYouTellMeAbout:"Peux-tu me parler de",recentSearches:"Recherches r\xe9centes"},c={language:"Hindi",yes:"यस",no:"नो",wasThisPageHelpful:"वाज़ दिस पेज हेल्पफुल",onThisPage:"ऑन दिस पेज",suggestEdits:"सजेस्ट एडिट्स",raiseIssue:"रेज़ इश्यू",search:"सर्च...",poweredByMintlify:"पावर्ड बाय मिंटलिफाई",filters:"फिल्टर्स",clear:"क्लियर फिल्टर्स",previous:"प्रीवियस",next:"नेक्स्ट",copyPage:"कॉपी पेज",viewAsMarkdown:"व्यू एज़ मार्कडाउन",openInChatGPT:"ओपन इन चैटजीपीटी",openInClaude:"ओपन इन क्लॉड",openInPerplexity:"ओपन इन पर्प्लेक्सिटी",copyPageAsMarkdown:"कॉपी पेज एज़ मार्कडाउन",viewPageAsMarkdown:"व्यू पेज एज़ मार्कडाउन",askQuestionsAboutPage:"आस्क क्वेश्चन्स अबाउट पेज",copyMCPServer:"Copy MCP Server",copyMCPServerDescription:"MCP Server URL को क्लिपबोर्ड में कॉपी करें",connectToCursor:"Connect to Cursor",installMCPServerOnCursor:"Cursor में MCP Server इंस्टॉल करें",connectToVSCode:"Connect to VSCode",installMCPServerOnVSCode:"VSCode में MCP Server इंस्टॉल करें",assistant:"असिस्टेंट",askAQuestion:"आस्क अ क्वेश्चन...",askAIAssistant:"आस्क एआई असिस्टेंट",canYouTellMeAbout:"कैन यू टेल मी अबाउट",recentSearches:"हालिया खोज"},u={language:"Bahasa Indonesia",yes:"Ya",no:"Tidak",wasThisPageHelpful:"Apakah halaman ini membantu?",onThisPage:"Di halaman ini",suggestEdits:"Sarankan suntingan",raiseIssue:"Ajukan masalah",search:"Cari...",poweredByMintlify:"Didukung oleh Mintlify",filters:"Filter",clear:"Hapus",previous:"Sebelumnya",next:"Selanjutnya",copyPage:"Salin halaman",viewAsMarkdown:"Lihat sebagai Markdown",openInChatGPT:"Buka di ChatGPT",openInClaude:"Buka di Claude",openInPerplexity:"Buka di Perplexity",copyPageAsMarkdown:"Salin halaman sebagai Markdown untuk LLMs",viewPageAsMarkdown:"Lihat halaman ini sebagai teks biasa",askQuestionsAboutPage:"Ajukan pertanyaan tentang halaman ini",copyMCPServer:"Copy MCP Server",copyMCPServerDescription:"Salin URL MCP Server ke clipboard",connectToCursor:"Connect to Cursor",installMCPServerOnCursor:"Instal MCP Server di Cursor",connectToVSCode:"Connect to VSCode",installMCPServerOnVSCode:"Instal MCP Server di VSCode",assistant:"Asisten",askAQuestion:"Ajukan pertanyaan...",askAIAssistant:"Ajukan pertanyaan ke asisten",canYouTellMeAbout:"Bisakah kamu memberitahu saya tentang",recentSearches:"Pencarian terbaru"},d={language:"Italiano",yes:"S\xec",no:"No",wasThisPageHelpful:"Questa pagina \xe8 stata utile?",onThisPage:"In questa pagina",suggestEdits:"Suggerisci modifiche",raiseIssue:"Segnala un problema",search:"Cerca...",poweredByMintlify:"Offerto da Mintlify",filters:"Filtri",clear:"Cancella",previous:"Precedente",next:"Successivo",copyPage:"Copia pagina",viewAsMarkdown:"Visualizza come Markdown",openInChatGPT:"Apri in ChatGPT",openInClaude:"Apri in Claude",openInPerplexity:"Apri in Perplexity",copyPageAsMarkdown:"Copia pagina come Markdown per LLMs",viewPageAsMarkdown:"Visualizza questa pagina come testo semplice",askQuestionsAboutPage:"Fai domande su questa pagina",copyMCPServer:"Copy MCP Server",copyMCPServerDescription:"Copia URL del MCP Server negli appunti",connectToCursor:"Connect to Cursor",installMCPServerOnCursor:"Installa MCP Server su Cursor",connectToVSCode:"Connect to VSCode",installMCPServerOnVSCode:"Installa MCP Server su VSCode",assistant:"Assistente",askAQuestion:"Fai una domanda...",askAIAssistant:"Fai una domanda all'assistente",canYouTellMeAbout:"Puoi spiegarmi cosa",recentSearches:"Cerca recenti"},p={language:"日本語",yes:"はい",no:"いいえ",wasThisPageHelpful:"このページは役に立ちましたか？",onThisPage:"このページの内容",suggestEdits:"編集を提案",raiseIssue:"問題を報告",search:"検索...",poweredByMintlify:"Mintlifyによるサポート",filters:"フィルター",clear:"クリア",previous:"前へ",next:"次へ",copyPage:"ページをコピー",viewAsMarkdown:"Markdownで表示",openInChatGPT:"ChatGPTで開く",openInClaude:"Claudeで開く",openInPerplexity:"Perplexityで開く",copyPageAsMarkdown:"LLMs用にMarkdownでページをコピー",viewPageAsMarkdown:"このページをプレーンテキストで表示",askQuestionsAboutPage:"このページについて質問する",copyMCPServer:"Copy MCP Server",copyMCPServerDescription:"MCP Server URLをクリップボードにコピー",connectToCursor:"Connect to Cursor",installMCPServerOnCursor:"CursorにMCP Serverをインストール",connectToVSCode:"Connect to VSCode",installMCPServerOnVSCode:"VSCodeにMCP Serverをインストール",assistant:"アシスタント",askAQuestion:"質問する...",askAIAssistant:"AIアシスタントに質問する",canYouTellMeAbout:"このページについて教えてください",recentSearches:"最近の検索"},g={language:"한국어",yes:"예",no:"아니오",wasThisPageHelpful:"이 페이지가 도움이 되었나요?",onThisPage:"이 페이지에서",suggestEdits:"수정 제안",raiseIssue:"문제 보고",search:"검색...",poweredByMintlify:"Mintlify에 의해 지원됨",filters:"필터",clear:"지우기",previous:"이전",next:"다음",copyPage:"페이지 복사",viewAsMarkdown:"Markdown으로 보기",openInChatGPT:"ChatGPT에서 열기",openInClaude:"Claude에서 열기",openInPerplexity:"Perplexity에서 열기",copyPageAsMarkdown:"LLMs용 Markdown으로 페이지 복사",viewPageAsMarkdown:"이 페이지를 일반 텍스트로 보기",askQuestionsAboutPage:"이 페이지에 대해 질문하기",copyMCPServer:"Copy MCP Server",copyMCPServerDescription:"MCP Server URL을 클립보드에 복사",connectToCursor:"Connect to Cursor",installMCPServerOnCursor:"Cursor에 MCP Server 설치",connectToVSCode:"Connect to VSCode",installMCPServerOnVSCode:"VSCode에 MCP Server 설치",assistant:"어시스턴트",askAQuestion:"질문하기...",askAIAssistant:"AI 어시스턴트에 질문하기",canYouTellMeAbout:"이 페이지에 대해 설명해주세요",recentSearches:"최근 검색"},h={language:"Portugu\xeas",yes:"Sim",no:"N\xe3o",wasThisPageHelpful:"Esta p\xe1gina foi \xfatil?",onThisPage:"Nesta p\xe1gina",suggestEdits:"Sugerir edi\xe7\xf5es",raiseIssue:"Reportar problema",search:"Pesquisar...",poweredByMintlify:"Suportado pelo Mintlify",filters:"Filtros",clear:"Limpar",previous:"Anterior",next:"Pr\xf3ximo",copyPage:"Copiar p\xe1gina",viewAsMarkdown:"Ver como Markdown",openInChatGPT:"Abrir no ChatGPT",openInClaude:"Abrir no Claude",openInPerplexity:"Abrir no Perplexity",copyPageAsMarkdown:"Copiar p\xe1gina como Markdown para LLMs",viewPageAsMarkdown:"Ver esta p\xe1gina como texto simples",askQuestionsAboutPage:"Fazer perguntas sobre esta p\xe1gina",copyMCPServer:"Copy MCP Server",copyMCPServerDescription:"Copiar URL do MCP Server para a \xe1rea de transfer\xeancia",connectToCursor:"Connect to Cursor",installMCPServerOnCursor:"Instalar MCP Server no Cursor",connectToVSCode:"Connect to VSCode",installMCPServerOnVSCode:"Instalar MCP Server no VSCode",assistant:"Assistente",askAQuestion:"Fazer uma pergunta...",askAIAssistant:"Fazer uma pergunta ao assistente",canYouTellMeAbout:"Voc\xea pode me falar sobre",recentSearches:"Pesquisas recentes"},f={language:"Portugu\xeas (BR)",yes:"Sim",no:"N\xe3o",wasThisPageHelpful:"Esta p\xe1gina foi \xfatil?",onThisPage:"Na p\xe1gina",suggestEdits:"Sugerir edi\xe7\xf5es",raiseIssue:"Reportar problema",search:"Pesquisar...",poweredByMintlify:"Suportado pelo Mintlify",filters:"Filtro",clear:"Limpar",previous:"Anterior",next:"Pr\xf3ximo",copyPage:"Copiar p\xe1gina",viewAsMarkdown:"Ver como Markdown",openInChatGPT:"Abrir no ChatGPT",openInClaude:"Abrir no Claude",openInPerplexity:"Abrir no Perplexity",copyPageAsMarkdown:"Copiar p\xe1gina como Markdown para LLMs",viewPageAsMarkdown:"Ver esta p\xe1gina como texto simples",askQuestionsAboutPage:"Fazer perguntas sobre esta p\xe1gina",copyMCPServer:"Copy MCP Server",copyMCPServerDescription:"Copiar URL do MCP Server para a \xe1rea de transfer\xeancia",connectToCursor:"Connect to Cursor",installMCPServerOnCursor:"Instalar MCP Server no Cursor",connectToVSCode:"Connect to VSCode",installMCPServerOnVSCode:"Instalar MCP Server no VSCode",assistant:"Assistente",askAQuestion:"Fazer uma pergunta...",askAIAssistant:"Fazer uma pergunta ao assistente",canYouTellMeAbout:"Voc\xea pode me falar sobre",recentSearches:"Pesquisas recentes"},m={language:"Русский",yes:"Да",no:"Нет",wasThisPageHelpful:"Была ли эта страница полезной?",onThisPage:"На этой странице",suggestEdits:"Предложить правки",raiseIssue:"Поднять вопрос",search:"Поиск...",poweredByMintlify:"Работает на Mintlify",filters:"Фильтры",clear:"Очистить",previous:"Предыдущий",next:"Следующий",copyPage:"Копировать страницу",viewAsMarkdown:"Просмотр в формате Markdown",openInChatGPT:"Открыть в ChatGPT",openInClaude:"Открыть в Claude",openInPerplexity:"Открыть в Perplexity",copyPageAsMarkdown:"Копировать страницу в формате Markdown для LLMs",viewPageAsMarkdown:"Просмотреть эту страницу как обычный текст",askQuestionsAboutPage:"Задать вопросы об этой странице",copyMCPServer:"Copy MCP Server",copyMCPServerDescription:"Копировать URL MCP Server в буфер обмена",connectToCursor:"Connect to Cursor",installMCPServerOnCursor:"Установить MCP Server в Cursor",connectToVSCode:"Connect to VSCode",installMCPServerOnVSCode:"Установить MCP Server в VSCode",assistant:"Помощник",askAQuestion:"Задать вопрос...",askAIAssistant:"Задать вопрос AI-помощнику",canYouTellMeAbout:"Можете рассказать мне о",recentSearches:"Последние поиски"},v={language:"T\xfcrk\xe7e",yes:"Evet",no:"Hayır",wasThisPageHelpful:"Bu sayfa yararlı mıydı?",onThisPage:"Bu sayfada",suggestEdits:"D\xfczenleme \xf6ner",raiseIssue:"Sorun oluştur",search:"Ara...",poweredByMintlify:"Mintlify tarafından desteklenmektedir",filters:"Filtreler",clear:"Temizle",previous:"\xd6nceki",next:"Sonraki",copyPage:"Sayfayı kopyala",viewAsMarkdown:"Markdown olarak g\xf6r\xfcnt\xfcle",openInChatGPT:"ChatGPT'de a\xe7",openInClaude:"Claude'da a\xe7",openInPerplexity:"Perplexity'de a\xe7",copyPageAsMarkdown:"Sayfayı LLMs i\xe7in Markdown olarak kopyala",viewPageAsMarkdown:"Bu sayfayı d\xfcz metin olarak g\xf6r\xfcnt\xfcle",askQuestionsAboutPage:"Bu sayfa hakkında sorular sor",copyMCPServer:"Copy MCP Server",copyMCPServerDescription:"MCP Server URL’sini panoya kopyala",connectToCursor:"Connect to Cursor",installMCPServerOnCursor:"Cursor’a MCP Server kur",connectToVSCode:"Connect to VSCode",installMCPServerOnVSCode:"VSCode’a MCP Server kur",assistant:"Asistan",askAQuestion:"Bir soru sor...",askAIAssistant:"AI asistanına sor",canYouTellMeAbout:"Bana bu sayfa hakkında bilgi ver",recentSearches:"Son aramalar"},y={language:"简体中文",yes:"是",no:"否",wasThisPageHelpful:"此页面对您有帮助吗？",onThisPage:"在此页面",suggestEdits:"建议编辑",raiseIssue:"提出问题",search:"搜索...",poweredByMintlify:"由Mintlify提供支持",filters:"筛选",clear:"清除",previous:"上一页",next:"下一页",copyPage:"复制页面",viewAsMarkdown:"以 Markdown 格式查看",openInChatGPT:"在 ChatGPT 中打开",openInClaude:"在 Claude 中打开",openInPerplexity:"在 Perplexity 中打开",copyPageAsMarkdown:"将页面以 Markdown 格式复制给 LLMs",viewPageAsMarkdown:"以纯文本查看此页面",askQuestionsAboutPage:"询问有关此页面的问题",copyMCPServer:"Copy MCP Server",copyMCPServerDescription:"复制 MCP Server URL 到剪贴板",connectToCursor:"Connect to Cursor",installMCPServerOnCursor:"在 Cursor 中安装 MCP Server",connectToVSCode:"Connect to VSCode",installMCPServerOnVSCode:"在 VSCode 中安装 MCP Server",assistant:"助手",askAQuestion:"提出问题...",askAIAssistant:"询问 AI 助手",canYouTellMeAbout:"您能告诉我关于",recentSearches:"最近搜索"},b={language:"繁體中文",yes:"是",no:"否",wasThisPageHelpful:"這個頁面有幫助嗎?",onThisPage:"在此頁",suggestEdits:"建議編輯",raiseIssue:"問問題",search:"搜尋...",poweredByMintlify:"由 Mintlify 提供支援",filters:"篩選",clear:"清除",previous:"上一頁",next:"下一頁",copyPage:"複製頁面",viewAsMarkdown:"以 Markdown 格式檢視",openInChatGPT:"在 ChatGPT 中開啟",openInClaude:"在 Claude 中開啟",openInPerplexity:"在 Perplexity 中開啟",copyPageAsMarkdown:"將頁面以 Markdown 格式複製給 LLMs",viewPageAsMarkdown:"以純文字檢視此頁面",askQuestionsAboutPage:"詢問有關此頁面的問題",copyMCPServer:"Copy MCP Server",copyMCPServerDescription:"複製 MCP Server URL 到剪貼板",connectToCursor:"Connect to Cursor",installMCPServerOnCursor:"在 Cursor 中安裝 MCP Server",connectToVSCode:"Connect to VSCode",installMCPServerOnVSCode:"在 VSCode 中安裝 MCP Server",assistant:"助手",askAQuestion:"提出問題...",askAIAssistant:"詢問 AI 助手",canYouTellMeAbout:"您能告訴我關於",recentSearches:"最近搜索"},x=e=>{switch(e){case"en":default:return s;case"cn":case"zh":case"zh-Hans":return y;case"zh-Hant":return b;case"es":return i;case"ja":case"jp":return p;case"pt":return h;case"fr":return o;case"fr-CA":return l;case"pt-BR":return f;case"de":return n;case"ko":return g;case"it":return d;case"ru":return m;case"id":return u;case"ar":return a;case"tr":return v;case"hi":return c}},k=e=>{switch(e){case"en":default:return"en-US";case"es":return"es-ES";case"cn":case"zh":case"zh-Hans":case"zh-Hant":return"zh-CN";case"jp":case"ja":return"ja-JP";case"pt":return"pt-PT";case"fr":return"fr-FR";case"fr-CA":return"fr-CA";case"pt-BR":return"pt-BR";case"de":return"de-DE";case"ko":return"ko-KR";case"it":return"it-IT";case"ru":return"ru-RU";case"id":return"id-ID";case"ar":return"ar-SA";case"tr":return"tr-TR";case"hi":return"hi-IN"}}},93351:(e,t,r)=>{"use strict";r.d(t,{_:()=>z,NavigationContextController:()=>q,n:()=>U});var a=r(54568),n=r(38668),s=r(98354);function i(e){return"/"===e.charAt(0)?e.substring(1):e}function o(e,t,r){if("pages"in t)"root"in t&&"object"==typeof t.root&&o(e,t.root,r),t.pages.forEach(t=>o(e,t,r));else if("href"in t){let a=i(t.href),n=e.get(a);e.has(a)?n!==r&&void 0!==n&&e.set(a,void 0):e.set(a,r)}}var l=r(62942),c=r(7620),u=r(13592),d=r(97056),p=r(25050),g=r(27622),h=r(92859);function f(e,t){let r=Array.isArray(e.groups)&&e.groups.every(e=>"string"==typeof e)?e.groups:void 0;return!r||r.some(e=>t.has(e))}var m=r(84873),v=r(77573);function y(e,t,r=!1){if(e&&"object"==typeof e){if((0,s.y)(e)){if(r&&t&&!f(e,t))return;return e.href=(0,v.C)(e.href),e}if("pages"in e){if("root"in e&&"object"==typeof e.root){let a=y(e.root,t,r);if(a)return a.href=(0,v.C)(a.href),a}for(let a of e.pages)if("object"==typeof a){let e=y(a,t,r);if(e)return e.href=(0,v.C)(e.href),e}}if("groups"in e)for(let a of e.groups){if(a.hidden)continue;let e=y(a,t,r);if(e)return e.href=(0,v.C)(e.href),e}for(let a of n.J)if(a in e){let n=e[a];if(Array.isArray(n))for(let e of n){if("object"==typeof e&&"hidden"in e&&e.hidden)continue;let a=y(e,t,r);if(a)return a.href=(0,v.C)(a.href),a}}}}var b=r(48836),x=r(37050);let k=["en","cn","zh","zh-Hans","zh-Hant","es","fr","fr-CA","ja","jp","pt","pt-BR","de","ko","it","ru","id","ar","tr","hi"],C={en:"English",cn:"Chinese",zh:"Chinese","zh-Hans":"Simplified Chinese","zh-Hant":"Traditional Chinese",es:"Spanish",fr:"French","fr-CA":"Canadian French",ja:"Japanese",jp:"Japanese",pt:"Portuguese","pt-BR":"Brazilian Portuguese",de:"German",ko:"Korean",it:"Italian",ru:"Russian",id:"Indonesian",ar:"Arabic",tr:"Turkish",hi:"Hindi"},w=Object.keys(C),S=Object.values(C),P=Object.values({en:"\uD83C\uDDFA\uD83C\uDDF8",cn:"\uD83C\uDDE8\uD83C\uDDF3",zh:"\uD83C\uDDE8\uD83C\uDDF3","zh-Hans":"\uD83C\uDDE8\uD83C\uDDF3","zh-Hant":"\uD83C\uDDF9\uD83C\uDDFC",es:"\uD83C\uDDEA\uD83C\uDDF8",fr:"\uD83C\uDDEB\uD83C\uDDF7","fr-CA":"\uD83C\uDDE8\uD83C\uDDE6",ja:"\uD83C\uDDEF\uD83C\uDDF5",jp:"\uD83C\uDDEF\uD83C\uDDF5",pt:"\uD83C\uDDE7\uD83C\uDDF7","pt-BR":"\uD83C\uDDE7\uD83C\uDDF7",de:"\uD83C\uDDE9\uD83C\uDDEA",ko:"\uD83C\uDDF0\uD83C\uDDF7",it:"\uD83C\uDDEE\uD83C\uDDF9",ru:"\uD83C\uDDF7\uD83C\uDDFA",id:"\uD83C\uDDEE\uD83C\uDDE9",ar:"\uD83C\uDDF8\uD83C\uDDE6",tr:"\uD83C\uDDF9\uD83C\uDDF7",hi:"\uD83C\uDDEE\uD83C\uDDF3"}),A=["jp","cn","zh","zh-Hans","zh-Hant"];w.filter(e=>!A.includes(e)),S.filter(e=>"Chinese"!==e),P.filter(e=>"\uD83C\uDDE8\uD83C\uDDF3"!==e);var j=function(e){return e[e.EXACT=0]="EXACT",e[e.PATH=1]="PATH",e[e.GROUP=2]="GROUP",e[e.DIVISION=3]="DIVISION",e[e.NONE=4]="NONE",e}({});function M({type:e,currentPath:t,entry:r,parentGroups:a,nearestDivisionValue:i,isActiveGroup:o,inActiveDivision:l,currentDivisionValue:c,allDivisionValues:u,firstHrefInDivision:d,navigationDivisions:p,isHiddenPage:g}){if("object"==typeof r){if((0,s.y)(r)&&function(e,t,r,a,n,s,i,o,l,c){let u=void 0!==r?[r]:o??[];if((t=t||"/")===s){for(let e of u)l.set(e,{href:t,matchLevel:0});return}if(c&&void 0!==i&&l.set(i,{href:s,matchLevel:0}),void 0!==i&&void 0!==r){let a=l.get(r);(void 0===a||1<a.matchLevel)&&function(e,t,r){let a=e.split("/").filter(Boolean),n=t.split("/").filter(Boolean);if("language"===r){let e=a[0]&&k.includes(a[0]),t=n[0]&&k.includes(n[0]),r=e?a.slice(1):a,s=t?n.slice(1):n;if(r.length!==s.length)return!1;for(let e=0;e<r.length;e++)if(r[e]!==s[e])return!1;return e!==t||e&&t&&a[0]!==n[0]}if(a.length!==n.length||a.length<2||a.at(-1)!==n.at(-1))return!1;let s=!1;for(let e=0;e<a.length-1;e++)if(a[e]!==n[e]){if(s)return!1;s=!0}return!0}(t,s,e)&&l.set(r,{href:t,matchLevel:1})}if(a){for(let e of u){let r=l.get(e);(void 0===r||2<r.matchLevel)&&l.set(e,{href:t,matchLevel:2})}return}if(n){for(let e of u){let r=l.get(e);(void 0===r||3<r.matchLevel)&&l.set(e,{href:t,matchLevel:3})}return}for(let e of u)l.has(e)||l.set(e,{href:t,matchLevel:4})}(e,r.href,i,o,l,t,c,u,d,g),"pages"in r){let n=r.pages.some(e=>(0,s.y)(e)&&(0,x.N)(e.href,t))&&c===i;for(let s of r.pages)"object"==typeof s&&M({type:e,currentPath:t,entry:s,parentGroups:a.length?a:r.pages,nearestDivisionValue:i,isActiveGroup:n,inActiveDivision:l,currentDivisionValue:c,allDivisionValues:u,firstHrefInDivision:d,navigationDivisions:p,isHiddenPage:g})}if("groups"in r)for(let a of r.groups){if("object"!=typeof a)continue;let n=function e(t,r){if((0,s.y)(t))return(0,x.N)(t.href,r);if("pages"in t){for(let a of t.pages)if(e(a,r))return!0}return!1}(a,t)&&c===i;M({type:e,currentPath:t,entry:a,parentGroups:r.groups,nearestDivisionValue:i,isActiveGroup:n,inActiveDivision:l,currentDivisionValue:c,allDivisionValues:u,firstHrefInDivision:d,navigationDivisions:p,isHiddenPage:g})}for(let s of[...n.J,"groups"])if(s in r){let n=r[s];if(Array.isArray(n)){let l=!1;for(let h of(u&&u.length&&("version"===e&&"versions"===s?l=n.every(e=>u.includes(e.version)):"language"===e&&"languages"===s&&(l=n.every(e=>u.includes(e.language)))),n)){let n;"version"===e&&"versions"===s?n="version"in h?h.version:void 0:"language"===e&&"languages"===s&&(n="language"in h?h.language:void 0),M({type:e,currentPath:t,entry:h,parentGroups:"groups"===s?r.groups:a,nearestDivisionValue:n??i,isActiveGroup:o,inActiveDivision:l,currentDivisionValue:c,allDivisionValues:u,firstHrefInDivision:d,navigationDivisions:p,isHiddenPage:g})}}}}}let N={tabs:"tab",anchors:"anchor",versions:"version",languages:"language",dropdowns:"dropdown",menu:"item"},T=({currentPath:e,currentVersion:t,currentLanguage:r,decoratedNav:a,userGroups:i,shouldUseDivisionMatch:o=!0,cache:l})=>{let c={tabs:[],anchors:[],versions:[],languages:[],dropdowns:[],menu:[]},u=new Map,d=new Map,p=[],{page:g,groupsOrPages:h}=function e(a,u,d){if("object"!=typeof u)return{page:void 0,groupsOrPages:void 0};if((0,s.y)(u))return f(u,i)&&(0,x.N)(u.href,a)?{page:u,groupsOrPages:d}:{page:void 0,groupsOrPages:void 0};if("pages"in u){for(let t of u.pages)if("object"==typeof t){let{page:r,groupsOrPages:n}=e(a,t,d.length?d:u.pages.map(e=>E(e,i)).filter(e=>void 0!==e));if(r)return{page:r,groupsOrPages:n}}}if("groups"in u){let t=L(u.groups,i);for(let r of t){let{page:n,groupsOrPages:s}=e(a,r,t);if(n)return{page:n,groupsOrPages:s}}}for(let s of n.J)if(s in u){let n=u[s];if(Array.isArray(n))for(let u of n){if(o&&B({subDivision:u,key:s,currentVersion:t,currentLanguage:r,subDivisions:n}))continue;let{page:p,groupsOrPages:g,menuDivision:h}=e(a,u,d);if(p){let e=n.filter(e=>F(e,i)).map(e=>{let t=H(e,u);if(!e.hidden||t)return I({item:e,isActive:t,cache:l,userGroups:i,divisionKey:s,menuDivision:h})}).filter(Boolean);if("global"in u&&O(c,u.global),e.length)return c[s].push(...e),{page:p,groupsOrPages:g,menuDivision:"menu"===s?e:void 0}}}}return{page:void 0,groupsOrPages:void 0}}("/"===e||""===e?y(a)?.href||"":e,a,[]);if(!g&&a){let{divisions:u,groupsOrPages:d}=function(e,t,r,a,i,o,l){let c,u,d={tabs:[],anchors:[],versions:[],languages:[],dropdowns:[],menu:[]},p=!0;if(!function t(l,d){if("object"==typeof l){if((0,s.y)(l))if(!(0,x.N)(e,l.href)&&f(l,i)){let t=function(e,t){let r=Math.min(e.length,t.length),a=0;for(let n=0;n<r&&e[n]===t[n];n++)a++;return e.substring(0,a)}(e,l.href);(u=t.length>(u?.length||0)?t:u)===t&&(c=d)}else(0,x.N)(e,l.href)&&(p=f(l,i));if("pages"in l)for(let e of l.pages)"object"==typeof e&&t(e,d.length?d:l.pages.map(e=>E(e,i)).filter(e=>void 0!==e));if("groups"in l){let e=L(l.groups,i);for(let r of l.groups)t(r,e)}for(let e of n.J)if(e in l){let n=l[e];if(Array.isArray(n))for(let s of n)o&&B({subDivision:s,key:e,currentVersion:r,currentLanguage:a,subDivisions:n})||t(s,d)}}}(t,[]),c=p?c:[]){let e=e=>JSON.stringify(structuredClone(c))===JSON.stringify(structuredClone(e));!function t(s,c){if("object"!=typeof s)return!1;if("pages"in s)return e(c.length?c:s.pages.map(e=>E(e,i)).filter(e=>void 0!==e));if("groups"in s)return e(L(s.groups,i));for(let e of n.J)if(e in s){let n=s[e];if(Array.isArray(n)){for(let s of n)if(!(o&&B({subDivision:s,key:e,currentVersion:r,currentLanguage:a,subDivisions:n}))&&(t(s,c)||!p)){let t=n.filter(e=>F(e,i)).map(t=>{let r=H(t,s)&&!0===p;if(!t.hidden||r)return I({item:t,isActive:r,cache:l,userGroups:i,divisionKey:e})}).filter(Boolean);return d[e].push(...t),!0}}}return!1}(t,[])}return{divisions:d,groupsOrPages:c||[]}}(e,a,t,r,i,o,l);c=u,p=d}return h&&h.length&&(p=h),c.versions.length&&M({type:"version",currentPath:e,entry:a,parentGroups:[],nearestDivisionValue:void 0,isActiveGroup:!1,inActiveDivision:!1,currentDivisionValue:c.versions.find(e=>e.isActive)?.name,allDivisionValues:c.versions.map(e=>e.name),firstHrefInDivision:u,navigationDivisions:c,isHiddenPage:void 0===g}),c.languages.length&&M({type:"language",currentPath:e,entry:a,parentGroups:[],nearestDivisionValue:void 0,isActiveGroup:!1,inActiveDivision:!1,currentDivisionValue:c.languages.find(e=>e.isActive)?.language,allDivisionValues:c.languages.map(e=>e.language),firstHrefInDivision:d,navigationDivisions:c,isHiddenPage:void 0===g}),a?.global&&O(c,a.global),{tabs:c.tabs,anchors:c.anchors,versions:c.versions,languages:c.languages,dropdowns:c.dropdowns,groupsOrPages:p,firstHrefInVersion:u,firstHrefInLanguage:d}};function O(e,t){t&&Object.entries(e).forEach(([e,r])=>{if(e in t){let a=t[e];if(a){let e=a.filter(e=>!e.hidden);r.push(...e.map(e=>({name:_(e),...e,href:e.href,isActive:!1,isGlobal:!0})))}}})}function I(e){let{item:t,isActive:r,cache:a,userGroups:s,divisionKey:i,menuDivision:o}=e,{dropdownCache:l}=a,c="";if(l.size&&"dropdowns"in t){let e=D(t.dropdowns),r=l.get(e);e&&r&&(c=(0,m.M)(r))}let u=y(t,s,!!s),d=[...n.J,"groups","pages"],p=Object.fromEntries(Object.entries(t).filter(([e])=>!d.includes(e)));c||(c="href"in t&&(0,b.v)(t.href)?t.href:u?.href||"/");let g={name:_(t),...p,href:c,isActive:r,isGlobal:!1};if("tabs"===i&&"menu"in t){if(r&&o?.length)return{...g,menu:o};let e=t.menu.filter(e=>F(e,s||new Set)).map(e=>{if(!e.hidden)return I({item:e,isActive:!1,cache:a,userGroups:s,divisionKey:"menu"})}).filter(e=>void 0!==e);return{...g,menu:e}}return g}function _(e){if("name"in e&&"string"==typeof e.name)return e.name;let t=Object.values(N).find(t=>t in e);if(t){let r=e[t];if("string"==typeof r)return r}return""}function L(e,t){return Array.isArray(e)&&e.length?e.map(e=>E(e,t)).filter(e=>void 0!==e&&!e.hidden):[]}function E(e,t){if("object"!=typeof e)return;if((0,s.y)(e))return f(e,t)?e:void 0;let r={...e};if("pages"in e){let a=e.pages.map(e=>E(e,t)).filter(e=>void 0!==e);if(0===a.length)return;r.pages=a}return r}function F(e,t){if("object"!=typeof e)return!1;if((0,s.y)(e))return f(e,t);if("pages"in e){if(0===e.pages.length)return!1;let r=!1;for(let a of e.pages)if("object"==typeof a&&F(a,t)){r=!0;break}if(!r)return!1}if("groups"in e){if(0===e.groups.length||e.groups.every(e=>e.hidden))return!1;let r=!1;for(let a of e.groups)if(F(a,t)){r=!0;break}if(!r)return!1}for(let r of n.J)if(r in e){let a=e[r];if(a.every(e=>e.hidden)||!a.some(e=>F(e,t)))return!1}return!0}let H=(e,t)=>JSON.stringify(structuredClone(e))===JSON.stringify(structuredClone(t));function B({subDivision:e,key:t,currentVersion:r,currentLanguage:a,subDivisions:n}){let s="versions"===t&&!!r,i="languages"===t&&!!a;if(s){let t=n.map(e=>e.version);return e.version!==r&&t.includes(r)}if(i){let t=n.map(e=>e.language);return e.language!==a&&t.includes(a)}return!1}function D(e){let t=e[0];if(!t)return"";let r=Object.values(N).find(e=>e in t);return r?e.reduce((e,t)=>e+t[r],""):""}function V(e,t,r,a,i){let o=function e(t,r,a,i,o,l){let c="version"===t?"":void 0;if("object"!=typeof r)return c;if((0,s.y)(r))if(!(0,x.N)(r.href,i))return c;else return function(e,t){let r={value:0};return function e(t,r,a){if("object"==typeof t){for(let i of((0,s.y)(t)&&(0,x.N)(t.href,r)&&a.value++,[...n.J,"groups","pages"]))if(i in t){let n=t[i];if(Array.isArray(n))for(let t of n)e(t,r,a)}}}(e,t,r),r.value>1}(a,i)&&o&&o!==l?c:"version"===t?r.version??l:l;for(let s of[...n.J,"groups","pages"])if(s in r){let n=r[s];if(Array.isArray(n))for(let r of n){let n;n="version"===t?"version"in r?r.version:void 0:"language"in r?r.language:void 0;let s=e(t,r,a,i,o,n??l);if(s&&s!==c)return s}}return c}(e,t,t,r,a,void 0),l=a||i;return o||l}var R=r(18764),G=r(71197);let z=(0,c.createContext)({selectedVersion:"",setSelectedVersion:()=>{},navIsOpen:!1,setNavIsOpen:()=>!1,selectedLocale:void 0,setSelectedLocale:()=>{},isUpdatingCache:!1,divisions:{tabs:[],anchors:[],versions:[],languages:[],dropdowns:[],groupsOrPages:[]},hasAdvancedTabs:!1}),q=({children:e})=>{let t=(0,u.G)(),{docsNavWithMetadata:r}=(0,c.useContext)(G.H6),{actualSubdomain:h}=(0,c.useContext)(G.Em),{auth:f,isFetchingUserInfo:m}=(0,c.useContext)(G.cy),{navigationData:v,updateCache:y,isUpdatingCache:b}=(0,p.Y)(r),x=(0,c.useRef)(!0),k=(0,l.useRouter)(),C=(0,g.f)(),[w,S]=(0,c.useState)(!1);(0,d.yY)(h);let P=(0,c.useMemo)(()=>f??void 0,[f]),A=P?.partial,{initialLocale:M,initialVersion:N}=(0,c.useMemo)(()=>{if(!r)return{initialLocale:void 0,initialVersion:void 0};let e=T({currentPath:t,decoratedNav:A?v:r,userGroups:new Set(C),currentVersion:void 0,currentLanguage:void 0,shouldUseDivisionMatch:!1,cache:{dropdownCache:new Map}});if(0===e.languages.length&&0===e.versions.length)return{initialLocale:void 0,initialVersion:void 0};let a=e.versions.find(e=>e.default)?.version,n=e.languages.find(e=>e.default)?.language,s=A?v??r:r,i=V("version",s,t,a,e.versions[0]?.name??"");return{initialLocale:V("language",s,t,n,e.languages[0]?.language??void 0)||void 0,initialVersion:i||""}},[t,r,v,C,A]),O=(0,c.useMemo)(()=>r?function(e){let t=new Map;return function e(t,r,a){if("object"==typeof r){if((0,s.y)(r)){let e=i(r.href),n=t.get(e);t.has(e)?void 0!==n&&n!==a&&t.set(e,void 0):t.set(e,a)}if("pages"in r)for(let n of("root"in r&&"object"==typeof r.root&&e(t,r.root,a),r.pages))"object"==typeof n&&e(t,n,a);for(let s of["groups",...n.J])if(s in r){let n=r[s];if(Array.isArray(n))for(let r of n){let n=a;"versions"===s&&(n=r.version),e(t,r,n)}}}}(t,e),t}(r):new Map,[r]),I=(0,c.useMemo)(()=>r?function(e){let t=new Map;return function e(t,r,a){let s="language"in r?r.language:a;if("pages"in r){"root"in r&&"object"==typeof r.root&&o(t,r.root,s),r.pages.forEach(e=>o(t,e,s));return}for(let a of["groups",...n.J])a in r&&Array.isArray(r[a])&&r[a].forEach(r=>e(t,r,s))}(t,e,void 0),t}(r):new Map,[r]),[_,L]=(0,c.useState)(M),[E,F]=(0,c.useState)(N??""),[H,B]=(0,c.useState)(new Map);(0,c.useEffect)(()=>{m&&(L(M),F(N??""))},[M,N,m]);let q=(0,c.useMemo)(()=>{let e=(0,R.$)(t),a=O.get(e)??E,n=I.get(e)??_;return T({currentPath:t,decoratedNav:A?v:r,userGroups:new Set(C),currentVersion:a,currentLanguage:n,shouldUseDivisionMatch:x.current,cache:{dropdownCache:H}})},[r,C,t,v,E,_,O,I,A,H]),U=(0,c.useRef)(t);(0,c.useEffect)(()=>{let e=(0,R.$)(t),r=O.get(e)??E,a=I.get(e)??_;r!==E&&r&&e!==U.current&&F(r),a!==_&&a&&e!==U.current&&L(a),U.current=e},[t,O,I,E,_]),(0,c.useEffect)(()=>{A&&y(P)},[P,y,A]);let $=(0,c.useCallback)(e=>{if(e===E)return;F(e);let r=q.firstHrefInVersion.get(e),a=r?.href;null!=a&&(a!=t&&(r?.matchLevel||0)<=j.DIVISION?x.current=!1:x.current=!0,k.push(a))},[E,q.firstHrefInVersion,t,k]),W=(0,c.useCallback)(e=>{if(e===_)return;L(e);let r=q.firstHrefInLanguage.get(e),a=r?.href;null!=a&&(a!=t&&(r?.matchLevel||0)<=j.DIVISION?x.current=!1:x.current=!0,k.push(a))},[_,q.firstHrefInLanguage,t,k]);(0,c.useEffect)(()=>{let e=q.versions.length>0&&!q.versions.some(e=>e.name===E),t=q.languages.length>0&&!q.languages.some(e=>e.language===_);if(!m&&!b){if(e){let e=q.versions.find(e=>e.default)?.name,t=q.versions[0]?.name;$(e??t??"")}if(t&&q.languages[0]?.language){let e=q.languages.find(e=>e.default)?.language,t=q.languages[0]?.language;W(e??t)}}},[q.versions,E,q.languages,_,$,W,b,m]),(0,c.useEffect)(()=>{let e=q.dropdowns;if(0===e.length)return;let t=D(e),r=e.find(e=>e.isActive);if(!r||!t)return;let a=(0,R.$)(r.href);if(H.get(t)===a)return;let n=new Map(H);n.set(t,a),B(n)},[q.dropdowns,H,t]),(0,c.useEffect)(()=>{S(!1)},[t]);let Q=(0,c.useMemo)(()=>q.tabs.some(e=>e.menu?.length),[q.tabs]);return(0,a.jsx)(z.Provider,{value:{navIsOpen:w,setNavIsOpen:S,selectedVersion:E,setSelectedVersion:$,selectedLocale:_,setSelectedLocale:W,isUpdatingCache:b,locales:q.languages.map(e=>e.language),divisions:q,hasAdvancedTabs:Q},children:e})};function U(){let{selectedLocale:e}=(0,c.useContext)(z);return(0,c.useMemo)(()=>(0,h.J)(e),[e])}z.displayName="NavigationContext"},95391:(e,t,r)=>{"use strict";r.d(t,{LANGS:()=>l,LINE_DIFF_ADD_CLASS_NAME:()=>s,LINE_DIFF_REMOVE_CLASS_NAME:()=>i,LINE_FOCUS_CLASS_NAME:()=>n,LINE_HIGHLIGHT_CLASS_NAME:()=>a,SHIKI_CLASSNAME:()=>g,THEMES:()=>o,shikiColorReplacements:()=>c,shikiDisplayLangMap:()=>p,shikiLangMap:()=>d,shikiThemeMap:()=>u});let a="line-highlight",n="line-focus",s="line-diff line-add",i="line-diff line-remove",o=["dark-plus","github-light-default"],l=["bash","blade","c","css","c#","c++","dart","diff","go","html","java","javascript","jsx","json","kotlin","log","lua","markdown","mdx","php","powershell","python","ruby","rust","solidity","swift","terraform","typescript","tsx","yaml","shellscript"],c={[o[0]]:{"#1e1e1e":"transparent","#569cd6":"#9cdcfe","#c8c8c8":"#f3f7f6","#d4d4d4":"#f3f7f6"},[o[1]]:{"#fff":"transparent","#ffffff":"transparent"}},u={dark:o[0],light:o[1]},d={curl:"bash",bash:"bash",sh:"bash",shell:"bash",zsh:"bash",shellscript:"bash",powershell:"powershell",psh:"powershell",ps:"powershell",c:"c","c#":"c#",csharp:"c#",cs:"c#","c++":"c++",cpp:"c++",cc:"c++",go:"go",golang:"go",git:"diff",diff:"diff",java:"java",html:"html",css:"css",javascript:"javascript",js:"javascript",jsx:"jsx",reactjs:"jsx","react-js":"jsx",json:"json",jsonc:"json",json5:"json",php:"php",blade:"blade",lua:"lua",luau:"lua",python:"python",py:"python",terraform:"terraform",tf:"terraform",tfvars:"terraform",typescript:"typescript",ts:"typescript",tsx:"tsx",react:"tsx",reactts:"tsx","react-ts":"tsx",ruby:"ruby",rb:"ruby",rust:"rust",rs:"rust",rustc:"rust",swift:"swift",kotlin:"kotlin",kt:"kotlin",dart:"dart",flutter:"dart",solidity:"solidity",solc:"solidity",markdown:"markdown",md:"markdown",mdx:"mdx",yaml:"yaml",yml:"yaml",toml:"yaml",log:"log",logfile:"log",logs:"log",stdout:"log",stderr:"log"},p={bash:"cURL",blade:"Blade",c:"C",css:"CSS","c#":"C#","c++":"C++",dart:"Dart",diff:"Diff",go:"Go",html:"HTML",java:"Java",javascript:"Javascript",jsx:"JSX",json:"JSON",kotlin:"Kotlin",log:"Log",lua:"Lua",markdown:"Markdown",mdx:"MDX",php:"PHP",powershell:"Powershell",python:"Python",ruby:"Ruby",rust:"Rust",solidity:"Solidity",swift:"Swift",terraform:"Terraform",typescript:"Typescript",tsx:"TSX",yaml:"YAML",shellscript:"Shellscript"},g="shiki shiki-themes"},97056:(e,t,r)=>{"use strict";r.d(t,{wC:()=>o,V:()=>c,yY:()=>l});var a=r(7620),n=r(41384),s=r(18764),i=r(13592);async function o(e,t){try{let r=await c(e,t);return await navigator.clipboard.writeText(r),!0}catch(e){return console.error("Failed to copy markdown:",e),!1}}function l(e){let t=(0,i.G)();(0,a.useEffect)(()=>{let r=async r=>{if(r.metaKey&&"c"===r.key){let r=window.getSelection();r&&r.toString().length>0||await o(e,t)}};return window.addEventListener("keydown",r),()=>window.removeEventListener("keydown",r)},[t,e])}async function c(e,t){let r=(0,s.$)(t);return(await fetch(`${n.cD.BASE_PATH}/_mintlify/_markdown/_sites/${e}${r?`/${r}`:""}`)).text()}},98354:(e,t,r)=>{"use strict";r.d(t,{y:()=>a});let a=e=>e.hasOwnProperty("href")&&e.hasOwnProperty("title")}}]);