!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="d7f06362-f29c-4a5f-bac0-ce2a90d6bfde",e._sentryDebugIdIdentifier="sentry-dbid-d7f06362-f29c-4a5f-bac0-ce2a90d6bfde")}catch(e){}}();"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7261],{27261:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return g},useLinkStatus:function(){return m}});let r=n(33378),o=n(54568),u=r._(n(7620)),i=n(85908),a=n(9330),f=n(87533),l=n(77849),c=n(58490),s=n(57720);n(21611);let d=n(43781),p=n(74637),y=n(50529);function h(e){return"string"==typeof e?e:(0,i.formatUrl)(e)}function g(e){let t,n,r,[i,g]=(0,u.useOptimistic)(d.IDLE_LINK_STATUS),m=(0,u.useRef)(null),{href:_,as:P,children:v,prefetch:E=null,passHref:O,replace:j,shallow:T,scroll:C,onClick:N,onMouseEnter:S,onTouchStart:I,legacyBehavior:w=!1,onNavigate:A,ref:L,unstable_dynamicOnHover:x,...M}=e;t=v,w&&("string"==typeof t||"number"==typeof t)&&(t=(0,o.jsx)("a",{children:t}));let U=u.default.useContext(a.AppRouterContext),k=!1!==E,R=null===E||"auto"===E?f.PrefetchKind.AUTO:f.PrefetchKind.FULL,{href:D,as:F}=u.default.useMemo(()=>{let e=h(_);return{href:e,as:P?h(P):e}},[_,P]);w&&(n=u.default.Children.only(t));let K=w?n&&"object"==typeof n&&n.ref:L,B=u.default.useCallback(e=>(null!==U&&(m.current=(0,d.mountLinkInstance)(e,D,U,R,k,g)),()=>{m.current&&((0,d.unmountLinkForCurrentNavigation)(m.current),m.current=null),(0,d.unmountPrefetchableInstance)(e)}),[k,D,U,R,g]),z={ref:(0,l.useMergedRef)(B,K),onClick(e){w||"function"!=typeof N||N(e),w&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),U&&(e.defaultPrevented||function(e,t,n,r,o,i,a){let{nodeName:f}=e.currentTarget;if(!("A"===f.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}if(e.preventDefault(),a){let e=!1;if(a({preventDefault:()=>{e=!0}}),e)return}u.default.startTransition(()=>{(0,y.dispatchNavigateAction)(n||t,o?"replace":"push",null==i||i,r.current)})}}(e,D,F,m,j,C,A))},onMouseEnter(e){w||"function"!=typeof S||S(e),w&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),U&&k&&(0,d.onNavigationIntent)(e.currentTarget,!0===x)},onTouchStart:function(e){w||"function"!=typeof I||I(e),w&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),U&&k&&(0,d.onNavigationIntent)(e.currentTarget,!0===x)}};return(0,c.isAbsoluteUrl)(F)?z.href=F:w&&!O&&("a"!==n.type||"href"in n.props)||(z.href=(0,s.addBasePath)(F)),r=w?u.default.cloneElement(n,z):(0,o.jsx)("a",{...M,...z,children:t}),(0,o.jsx)(b.Provider,{value:i,children:r})}n(76355);let b=(0,u.createContext)(d.IDLE_LINK_STATUS),m=()=>(0,u.useContext)(b);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},58490:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return y},MiddlewareNotFoundError:function(){return m},MissingStaticPage:function(){return b},NormalizeError:function(){return h},PageNotFoundError:function(){return g},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return f},getLocationOrigin:function(){return i},getURL:function(){return a},isAbsoluteUrl:function(){return u},isResSent:function(){return l},loadGetInitialProps:function(){return s},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return _}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,o=Array(r),u=0;u<r;u++)o[u]=arguments[u];return n||(n=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,u=e=>o.test(e);function i(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function a(){let{href:e}=window.location,t=i();return e.substring(t.length)}function f(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function l(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function s(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await s(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&l(n))return r;if(!r)throw Object.defineProperty(Error('"'+f(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class y extends Error{}class h extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class b extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class m extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function _(e){return JSON.stringify({message:e.message,stack:e.stack})}},74637:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return u}});let r=n(58490),o=n(91075);function u(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,o.hasBasePath)(n.pathname)}catch(e){return!1}}},76355:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},77849:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let r=n(7620);function o(e,t){let n=(0,r.useRef)(null),o=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(n.current=u(e,r)),t&&(o.current=u(t,r))},[e,t])}function u(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},80444:(e,t)=>{function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[n,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(n,r(e));else t.set(n,r(o));return t}function u(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return u},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return o}})},85908:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return u},formatWithValidation:function(){return a},urlObjectKeys:function(){return i}});let r=n(33378)._(n(80444)),o=/https?|ftp|gopher|file/;function u(e){let{auth:t,hostname:n}=e,u=e.protocol||"",i=e.pathname||"",a=e.hash||"",f=e.query||"",l=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?l=t+e.host:n&&(l=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(l+=":"+e.port)),f&&"object"==typeof f&&(f=String(r.urlQueryToSearchParams(f)));let c=e.search||f&&"?"+f||"";return u&&!u.endsWith(":")&&(u+=":"),e.slashes||(!u||o.test(u))&&!1!==l?(l="//"+(l||""),i&&"/"!==i[0]&&(i="/"+i)):l||(l=""),a&&"#"!==a[0]&&(a="#"+a),c&&"?"!==c[0]&&(c="?"+c),""+u+l+(i=i.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+a}let i=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(e){return u(e)}}}]);