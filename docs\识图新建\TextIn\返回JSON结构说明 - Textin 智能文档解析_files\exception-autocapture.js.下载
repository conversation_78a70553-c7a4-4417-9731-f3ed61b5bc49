!function(){"use strict";function r(){return r=Object.assign?Object.assign.bind():function(r){for(var n=1;n<arguments.length;n++){var e=arguments[n];for(var t in e)({}).hasOwnProperty.call(e,t)&&(r[t]=e[t])}return r},r.apply(null,arguments)}var n="undefined"!=typeof window?window:void 0,e="undefined"!=typeof globalThis?globalThis:n,t=null==e?void 0:e.navigator;null==e||e.document,null==e||e.location,null==e||e.fetch,null!=e&&e.XMLHttpRequest&&"withCredentials"in new e.XMLHttpRequest&&e.XMLHttpRequest,null==e||e.AbortController,null==t||t.userAgent;var i=null!=n?n:{},o=["fatal","error","warning","log","info","debug"],a=Array.isArray,u=Object.prototype.toString,l=a||function(r){return"[object Array]"===u.call(r)},c=r=>"function"==typeof r,v=r=>r===Object(r)&&!l(r),f=r=>void 0===r,d=r=>"[object String]"==u.call(r),s=r=>d(r)&&0===r.trim().length,p=r=>null===r;function y(r){return!f(Event)&&w(r,Event)}function w(r,n){try{return r instanceof n}catch(r){return!1}}function E(r){return p(r)||!v(r)&&!c(r)}function b(r){switch(Object.prototype.toString.call(r)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object DOMError]":return!0;default:return w(r,Error)}}function h(r,n){return Object.prototype.toString.call(r)==="[object "+n+"]"}function x(r){return h(r,"DOMError")}var g=/\(error: (.*)\)/,j=50,O="?";function m(r,n,e,t){var i={platform:"web:javascript",filename:r,function:"<anonymous>"===n?O:n,in_app:!0};return f(e)||(i.lineno=e),f(t)||(i.colno=t),i}var _=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,A=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,M=/\((\S*)(?::(\d+))(?::(\d+))\)/,$=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,S=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,D=function(){for(var n=arguments.length,e=new Array(n),t=0;t<n;t++)e[t]=arguments[t];var i=e.sort(((r,n)=>r[0]-n[0])).map((r=>r[1]));return function(n,e){void 0===e&&(e=0);for(var t=[],o=n.split("\n"),a=e;a<o.length;a++){var u=o[a];if(!(u.length>1024)){var l=g.test(u)?u.replace(g,"$1"):u;if(!l.match(/\S*Error: /)){for(var c of i){var v=c(l);if(v){t.push(v);break}}if(t.length>=j)break}}}return function(n){if(!n.length)return[];var e=Array.from(n);return e.reverse(),e.slice(0,j).map((n=>r({},n,{filename:n.filename||R(e).filename,function:n.function||O})))}(t)}}(...[[30,r=>{var n=_.exec(r);if(n){var[,e,t,i]=n;return m(e,O,+t,+i)}var o=A.exec(r);if(o){if(o[2]&&0===o[2].indexOf("eval")){var a=M.exec(o[2]);a&&(o[2]=a[1],o[3]=a[2],o[4]=a[3])}var[u,l]=z(o[1]||O,o[2]);return m(l,u,o[3]?+o[3]:void 0,o[4]?+o[4]:void 0)}}],[50,r=>{var n=$.exec(r);if(n){if(n[3]&&n[3].indexOf(" > eval")>-1){var e=S.exec(n[3]);e&&(n[1]=n[1]||"eval",n[3]=e[1],n[4]=e[2],n[5]="")}var t=n[3],i=n[1]||O;return[i,t]=z(i,t),m(t,i,n[4]?+n[4]:void 0,n[5]?+n[5]:void 0)}}]]);function R(r){return r[r.length-1]||{}}var T,k,U,z=(r,n)=>{var e=-1!==r.indexOf("safari-extension"),t=-1!==r.indexOf("safari-web-extension");return e||t?[-1!==r.indexOf("@")?r.split("@")[0]:O,e?"safari-extension:"+n:"safari-web-extension:"+n]:[r,n]};var H=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i;function P(r,n){void 0===n&&(n=0);var e=r.stacktrace||r.stack||"",t=function(r){if(r&&C.test(r.message))return 1;return 0}(r);try{var i=D,o=function(r,n){var e=function(r){var n=globalThis._posthogChunkIds;if(!n)return{};var e=Object.keys(n);return U&&e.length===k?U:(k=e.length,U=e.reduce(((e,t)=>{T||(T={});var i=T[t];if(i)e[i[0]]=i[1];else for(var o=r(t),a=o.length-1;a>=0;a--){var u=o[a],l=null==u?void 0:u.filename,c=n[t];if(l&&c){e[l]=c,T[t]=[l,c];break}}return e}),{}))}(n);return r.forEach((r=>{r.filename&&(r.chunk_id=e[r.filename])})),r}(i(e,t),i);return o.slice(0,o.length-n)}catch(r){}return[]}var C=/Minified React error #\d+;/i;function I(r,n){var e,t,i=P(r),o=null===(e=null==n?void 0:n.handled)||void 0===e||e,a=null!==(t=null==n?void 0:n.synthetic)&&void 0!==t&&t;return{type:null!=n&&n.overrideExceptionType?n.overrideExceptionType:r.name,value:function(r){var n=r.message;if(n.error&&"string"==typeof n.error.message)return String(n.error.message);return String(n)}(r),stacktrace:{frames:i,type:"raw"},mechanism:{handled:o,synthetic:a}}}function N(r,n){var e=I(r,n);return r.cause&&b(r.cause)&&r.cause!==r?[e,...N(r.cause,{handled:null==n?void 0:n.handled,synthetic:null==n?void 0:n.synthetic})]:[e]}function L(r,n){return{$exception_list:N(r,n),$exception_level:"error"}}function W(r,n){var e,t,i,o=null===(e=null==n?void 0:n.handled)||void 0===e||e,a=null===(t=null==n?void 0:n.synthetic)||void 0===t||t,u={type:null!=n&&n.overrideExceptionType?n.overrideExceptionType:null!==(i=null==n?void 0:n.defaultExceptionType)&&void 0!==i?i:"Error",value:r||(null==n?void 0:n.defaultExceptionMessage),mechanism:{handled:o,synthetic:a}};if(null!=n&&n.syntheticException){var l=P(n.syntheticException,1);l.length&&(u.stacktrace={frames:l,type:"raw"})}return{$exception_list:[u],$exception_level:"error"}}function Y(r,n){var e,t,i,a=null===(e=null==n?void 0:n.handled)||void 0===e||e,u=null===(t=null==n?void 0:n.synthetic)||void 0===t||t,l=null!=n&&n.overrideExceptionType?n.overrideExceptionType:y(r)?r.constructor.name:"Error",c="Non-Error 'exception' captured with keys: "+function(r,n){void 0===n&&(n=40);var e=Object.keys(r);if(e.sort(),!e.length)return"[object has no keys]";for(var t=e.length;t>0;t--){var i=e.slice(0,t).join(", ");if(!(i.length>n))return t===e.length||i.length<=n?i:i.slice(0,n)+"..."}return""}(r),v={type:l,value:c,mechanism:{handled:a,synthetic:u}};if(null!=n&&n.syntheticException){var f=P(null==n?void 0:n.syntheticException,1);f.length&&(v.stacktrace={frames:f,type:"raw"})}return{$exception_list:[v],$exception_level:(i=r.level,d(i)&&!s(i)&&o.indexOf(i)>=0?r.level:"error")}}function q(n,e){var{error:t,event:i}=n,o={$exception_list:[]},a=t||i;if(x(a)||function(r){return h(r,"DOMException")}(a)){var u=a;if(function(r){return"stack"in r}(a))o=L(a,e);else{var l=u.name||(x(u)?"DOMError":"DOMException"),c=u.message?l+": "+u.message:l;o=W(c,r({},e,{overrideExceptionType:x(u)?"DOMError":"DOMException",defaultExceptionMessage:c}))}return"code"in u&&(o.$exception_DOMException_code=""+u.code),o}if(function(r){return h(r,"ErrorEvent")}(a)&&a.error)return L(a.error,e);if(b(a))return L(a,e);if(function(r){return h(r,"Object")}(a)||y(a))return Y(a,e);if(f(t)&&d(i)){var v="Error",s=i,p=i.match(H);return p&&(v=p[1],s=p[2]),W(s,r({},e,{overrideExceptionType:v,defaultExceptionMessage:s}))}return W(a,e)}function B(r){var[n]=r,e=function(r){if(E(r))return r;try{if("reason"in r)return r.reason;if("detail"in r&&"reason"in r.detail)return r.detail.reason}catch(r){}return r}(n);return E(e)?W("Non-Error promise rejection captured with value: "+String(e),{handled:!1,synthetic:!1,overrideExceptionType:"UnhandledRejection"}):q({event:e},{handled:!1,overrideExceptionType:"UnhandledRejection",defaultExceptionMessage:String(e)})}var F=r=>{var e={t:function(e){if(n&&i.POSTHOG_DEBUG&&!f(n.console)&&n.console){for(var t=("__rrweb_original__"in n.console[e]?n.console[e].__rrweb_original__:n.console[e]),o=arguments.length,a=new Array(o>1?o-1:0),u=1;u<o;u++)a[u-1]=arguments[u];t(r,...a)}},info:function(){for(var r=arguments.length,n=new Array(r),t=0;t<r;t++)n[t]=arguments[t];e.t("log",...n)},warn:function(){for(var r=arguments.length,n=new Array(r),t=0;t<r;t++)n[t]=arguments[t];e.t("warn",...n)},error:function(){for(var r=arguments.length,n=new Array(r),t=0;t<r;t++)n[t]=arguments[t];e.t("error",...n)},critical:function(){for(var n=arguments.length,e=new Array(n),t=0;t<n;t++)e[t]=arguments[t];console.error(r,...e)},uninitializedWarning:r=>{e.error("You must initialize PostHog before calling "+r)},createLogger:n=>F(r+" "+n)};return e},G=(0,F("[PostHog.js]").createLogger)("[ExceptionAutocapture]"),J={wrapOnError:r=>{var e=n;e||G.info("window not available, cannot wrap onerror");var t=e.onerror;return e.onerror=function(){for(var n,e=arguments.length,i=new Array(e),o=0;o<e;o++)i[o]=arguments[o];var a=q({event:i[0],error:i[4]});return r(a),null!==(n=null==t?void 0:t(...i))&&void 0!==n&&n},e.onerror.__POSTHOG_INSTRUMENTED__=!0,()=>{var r;null==(r=e.onerror)||delete r.__POSTHOG_INSTRUMENTED__,e.onerror=t}},wrapUnhandledRejection:r=>{var e=n;e||G.info("window not available, cannot wrap onUnhandledRejection");var t=e.onunhandledrejection;return e.onunhandledrejection=function(){for(var n,i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];var u=B(o);return r(u),null!==(n=null==t?void 0:t.apply(e,o))&&void 0!==n&&n},e.onunhandledrejection.__POSTHOG_INSTRUMENTED__=!0,()=>{var r;null==(r=e.onunhandledrejection)||delete r.__POSTHOG_INSTRUMENTED__,e.onunhandledrejection=t}},wrapConsoleError:r=>{var n=console;n||G.info("console not available, cannot wrap console.error");var e=n.error;return n.error=function(){for(var n=arguments.length,t=new Array(n),i=0;i<n;i++)t[i]=arguments[i];var o=t.join(" "),a=t.find((r=>r instanceof Error)),u=a?q({event:o,error:a}):q({event:o},{syntheticException:new Error("PostHog syntheticException")});return r(u),null==e?void 0:e(...t)},n.error.__POSTHOG_INSTRUMENTED__=!0,()=>{var r;null==(r=n.error)||delete r.__POSTHOG_INSTRUMENTED__,n.error=e}}};i.__PosthogExtensions__=i.__PosthogExtensions__||{},i.__PosthogExtensions__.errorWrappingFunctions=J,i.posthogErrorWrappingFunctions=J}();
//# sourceMappingURL=exception-autocapture.js.map
