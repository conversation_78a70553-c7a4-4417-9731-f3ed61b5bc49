package com.sandu.xinye.api.v2.ocr;

import com.jfinal.kit.LogKit;
import com.jfinal.kit.StrKit;
import com.sandu.xinye.api.v2.ocr.dto.OcrResponse;
import com.sandu.xinye.api.v2.ocr.dto.TextInResponse;
import com.sandu.xinye.api.v2.ocr.util.CoordinateUtils;
import com.google.zxing.BinaryBitmap;
import com.google.zxing.MultiFormatReader;
import com.google.zxing.Result;
import com.google.zxing.client.j2se.BufferedImageLuminanceSource;
import com.google.zxing.common.HybridBinarizer;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 元素转换器
 * 将TextIn API的识别结果转换为XPrinter标准的元素数据格式
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class ElementConverter {
    
    public static final ElementConverter me = new ElementConverter();
    
    /**
     * 转换TextIn识别结果为XPrinter格式
     * 
     * @param textInResponse TextIn API响应
     * @param imageFile 原始图片文件
     * @param imageWidth 图片宽度
     * @param imageHeight 图片高度
     * @return OcrResponse 转换后的响应数据
     */
    public OcrResponse convertToXPrinterFormat(TextInResponse textInResponse, 
                                              File imageFile, 
                                              int imageWidth, 
                                              int imageHeight) {
        LogKit.info("开始转换TextIn识别结果为XPrinter格式");
        
        OcrResponse response = new OcrResponse();
        
        // 设置图片信息
        String format = getImageFormat(imageFile.getName());
        response.setImageInfo(imageWidth, imageHeight, format);
        
        if (textInResponse.getData() != null) {
            // 转换文本元素
            convertTextElements(textInResponse.getData().getTexts(), response);
            
            // 转换表格元素
            convertTableElements(textInResponse.getData().getTables(), response);
            
            // 转换条码元素
            convertBarcodeElements(textInResponse.getData().getBarcodes(), response);
            
            // 尝试识别图片中的二维码/条形码（使用ZXing作为补充）
            detectBarcodesWithZXing(imageFile, response);
        }
        
        LogKit.info("转换完成，共识别到 " + response.getElements().size() + " 个元素");
        return response;
    }
    
    /**
     * 转换文本元素
     */
    private void convertTextElements(List<TextInResponse.TextResult> texts, OcrResponse response) {
        if (texts == null || texts.isEmpty()) {
            return;
        }
        
        LogKit.info("转换文本元素，数量: " + texts.size());
        
        for (TextInResponse.TextResult text : texts) {
            if (text.getBbox() == null || StrKit.isBlank(text.getText())) {
                continue;
            }
            
            TextInResponse.BoundingBox bbox = text.getBbox();
            
            // 转换坐标（像素转毫米）
            String x = CoordinateUtils.pixelsToMm(bbox.getX());
            String y = CoordinateUtils.pixelsToMm(bbox.getY());
            String width = CoordinateUtils.pixelsToMm(bbox.getWidth());
            String height = CoordinateUtils.pixelsToMm(bbox.getHeight());
            
            // 计算字体大小
            double charWidth = CoordinateUtils.calculateCharWidth(bbox.getWidth(), text.getText().length());
            String textSize = CoordinateUtils.calculateFontSize(charWidth);
            
            // 获取文本样式
            String bold = "false";
            String italic = "false";
            if (text.getStyle() != null) {
                bold = String.valueOf(text.getStyle().isBold());
                italic = String.valueOf(text.getStyle().isItalic());
            }
            
            // 添加文本元素
            response.addTextElement(x, y, width, height, text.getText(), textSize, bold, italic);
        }
    }
    
    /**
     * 转换表格元素
     */
    private void convertTableElements(List<TextInResponse.TableResult> tables, OcrResponse response) {
        if (tables == null || tables.isEmpty()) {
            return;
        }
        
        LogKit.info("转换表格元素，数量: " + tables.size());
        
        for (TextInResponse.TableResult table : tables) {
            if (table.getBbox() == null) {
                continue;
            }
            
            TextInResponse.BoundingBox bbox = table.getBbox();
            
            // 转换坐标
            String x = CoordinateUtils.pixelsToMm(bbox.getX());
            String y = CoordinateUtils.pixelsToMm(bbox.getY());
            String width = CoordinateUtils.pixelsToMm(bbox.getWidth());
            String height = CoordinateUtils.pixelsToMm(bbox.getHeight());
            
            // 转换单元格数据
            List<Map<String, Object>> cells = new ArrayList<>();
            if (table.getCells() != null) {
                for (TextInResponse.CellResult cell : table.getCells()) {
                    Map<String, Object> cellData = new HashMap<>();
                    cellData.put("row", String.valueOf(cell.getRow()));
                    cellData.put("col", String.valueOf(cell.getCol()));
                    cellData.put("content", cell.getText() != null ? cell.getText() : "");
                    cellData.put("rowSpan", "0");
                    cellData.put("colSpan", "0");
                    cellData.put("fontType", "13");
                    cellData.put("textSize", "10");
                    cellData.put("hAlignment", "1");
                    cellData.put("italic", "false");
                    cellData.put("bold", "false");
                    cellData.put("automaticHeightCalculation", "true");
                    cellData.put("wordSpace", "0.11139240506329115");
                    cellData.put("linesSpace", "0");
                    cellData.put("blackWhiteReflection", "false");
                    cellData.put("strikethrough", "false");
                    cellData.put("lineWrap", "true");
                    cellData.put("horizontalAlignment", "true");
                    cellData.put("underline", "false");
                    
                    cells.add(cellData);
                }
            }
            
            // 添加表格元素
            response.addTableElement(x, y, width, height, table.getRows(), table.getCols(), cells);
        }
    }
    
    /**
     * 转换条码元素
     */
    private void convertBarcodeElements(List<TextInResponse.BarcodeResult> barcodes, OcrResponse response) {
        if (barcodes == null || barcodes.isEmpty()) {
            return;
        }
        
        LogKit.info("转换条码元素，数量: " + barcodes.size());
        
        for (TextInResponse.BarcodeResult barcode : barcodes) {
            if (barcode.getBbox() == null || StrKit.isBlank(barcode.getText())) {
                continue;
            }
            
            TextInResponse.BoundingBox bbox = barcode.getBbox();
            
            // 转换坐标
            String x = CoordinateUtils.pixelsToMm(bbox.getX());
            String y = CoordinateUtils.pixelsToMm(bbox.getY());
            String width = CoordinateUtils.pixelsToMm(bbox.getWidth());
            String height = CoordinateUtils.pixelsToMm(bbox.getHeight());
            
            // 判断是条形码还是二维码
            if (isQRCode(barcode.getType())) {
                // 添加二维码元素
                response.addQrCodeElement(x, y, width, height, barcode.getText());
            } else {
                // 添加条形码元素
                String barcodeType = mapBarcodeType(barcode.getType());
                response.addBarcodeElement(x, y, width, height, barcode.getText(), barcodeType);
            }
        }
    }
    
    /**
     * 使用ZXing检测条码（作为TextIn的补充）
     */
    private void detectBarcodesWithZXing(File imageFile, OcrResponse response) {
        try {
            LogKit.info("使用ZXing检测条码作为补充");
            
            BufferedImage image = ImageIO.read(imageFile);
            if (image == null) {
                return;
            }
            
            BinaryBitmap bitmap = new BinaryBitmap(new HybridBinarizer(
                new BufferedImageLuminanceSource(image)));
            
            MultiFormatReader reader = new MultiFormatReader();
            Result result = reader.decode(bitmap);
            
            if (result != null && !StrKit.isBlank(result.getText())) {
                LogKit.info("ZXing检测到条码: " + result.getBarcodeFormat() + " - " + result.getText());
                
                // 由于ZXing没有提供精确的位置信息，这里使用估算的位置
                // 在实际应用中，可能需要更复杂的算法来确定条码位置
                String x = "10";
                String y = "10";
                String width = "50";
                String height = "20";
                
                if (isQRCodeFormat(result.getBarcodeFormat().toString())) {
                    response.addQrCodeElement(x, y, width, height, result.getText());
                } else {
                    String barcodeType = mapZXingBarcodeType(result.getBarcodeFormat().toString());
                    response.addBarcodeElement(x, y, width, height, result.getText(), barcodeType);
                }
            }
            
        } catch (Exception e) {
            LogKit.warn("ZXing条码检测失败: " + e.getMessage());
            // 不抛出异常，因为这只是补充检测
        }
    }
    
    /**
     * 判断是否为二维码类型
     */
    private boolean isQRCode(String type) {
        return type != null && (type.toLowerCase().contains("qr") || 
                               type.toLowerCase().contains("datamatrix"));
    }
    
    /**
     * 判断ZXing格式是否为二维码
     */
    private boolean isQRCodeFormat(String format) {
        return format != null && (format.contains("QR_CODE") || 
                                 format.contains("DATA_MATRIX"));
    }
    
    /**
     * 映射条码类型到XPrinter格式
     */
    private String mapBarcodeType(String textInType) {
        if (textInType == null) {
            return "4"; // 默认CODE_128
        }
        
        switch (textInType.toLowerCase()) {
            case "code128":
            case "code_128":
                return "4";
            case "code39":
            case "code_39":
                return "1";
            case "ean13":
            case "ean_13":
                return "8";
            case "ean8":
            case "ean_8":
                return "9";
            default:
                return "4"; // 默认CODE_128
        }
    }
    
    /**
     * 映射ZXing条码类型到XPrinter格式
     */
    private String mapZXingBarcodeType(String zxingFormat) {
        if (zxingFormat == null) {
            return "4";
        }
        
        switch (zxingFormat) {
            case "CODE_128":
                return "4";
            case "CODE_39":
                return "1";
            case "EAN_13":
                return "8";
            case "EAN_8":
                return "9";
            default:
                return "4";
        }
    }
    
    /**
     * 获取图片格式
     */
    private String getImageFormat(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "unknown";
        }
        
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot > 0 && lastDot < fileName.length() - 1) {
            return fileName.substring(lastDot + 1).toLowerCase();
        }
        
        return "unknown";
    }
}
