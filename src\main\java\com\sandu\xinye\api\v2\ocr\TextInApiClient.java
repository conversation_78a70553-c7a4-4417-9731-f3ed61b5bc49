package com.sandu.xinye.api.v2.ocr;

import com.alibaba.fastjson.JSON;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.PropKit;
import com.sandu.xinye.api.v2.ocr.dto.TextInResponse;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;

/**
 * TextIn API客户端
 * 负责与TextIn服务进行通信，发送图片识别请求
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class TextInApiClient {
    
    public static final TextInApiClient me = new TextInApiClient();
    
    // TextIn API配置
    private static final String TEXTIN_API_URL = PropKit.get("textin.api.url", "https://api.textin.com/ai/service/v1/pdf_to_markdown");
    private static final String TEXTIN_API_KEY = PropKit.get("textin.api.key", "");
    private static final String TEXTIN_APP_ID = PropKit.get("textin.app.id", "");
    
    // 请求超时配置
    private static final int CONNECT_TIMEOUT = 30000; // 30秒
    private static final int SOCKET_TIMEOUT = 60000;  // 60秒
    
    /**
     * 调用TextIn API进行图片识别
     * 
     * @param imageFile 图片文件
     * @return TextIn API响应结果
     * @throws Exception 调用异常
     */
    public TextInResponse recognizeImage(File imageFile) throws Exception {
        LogKit.info("开始调用TextIn API进行图片识别");
        
        // 检查配置
        if (TEXTIN_API_KEY.isEmpty() || TEXTIN_APP_ID.isEmpty()) {
            throw new IllegalStateException("TextIn API配置不完整，请检查textin.api.key和textin.app.id配置");
        }
        
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        
        try {
            // 创建HTTP客户端
            httpClient = createHttpClient();
            
            // 创建POST请求
            HttpPost httpPost = new HttpPost(TEXTIN_API_URL);
            
            // 设置请求头
            httpPost.setHeader("x-ti-app-id", TEXTIN_APP_ID);
            httpPost.setHeader("x-ti-secret-code", TEXTIN_API_KEY);
            httpPost.setHeader("Content-Type", "application/octet-stream");

            // 读取文件内容并设置为请求体
            byte[] fileBytes = Files.readAllBytes(imageFile.toPath());
            ByteArrayEntity entity = new ByteArrayEntity(fileBytes);
            httpPost.setEntity(entity);

            // 添加URL参数
            String urlWithParams = TEXTIN_API_URL +
                "?dpi=144" +
                "&get_image=objects" +
                "&markdown_details=1" +
                "&page_count=10" +
                "&parse_mode=auto" +
                "&table_flavor=html";
            httpPost.setURI(java.net.URI.create(urlWithParams));
            
            LogKit.info("发送TextIn API请求: " + TEXTIN_API_URL);
            
            // 执行请求
            response = httpClient.execute(httpPost);
            
            // 检查响应状态
            int statusCode = response.getStatusLine().getStatusCode();
            LogKit.info("TextIn API响应状态码: " + statusCode);
            
            if (statusCode != 200) {
                throw new RuntimeException("TextIn API调用失败，状态码: " + statusCode);
            }
            
            // 读取响应内容
            HttpEntity responseEntity = response.getEntity();
            String responseContent = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);
            
            LogKit.info("TextIn API响应内容长度: " + responseContent.length());
            LogKit.debug("TextIn API响应内容: " + responseContent);
            
            // 解析JSON响应
            TextInResponse textInResponse = parseResponse(responseContent);
            
            LogKit.info("TextIn API调用成功");
            return textInResponse;
            
        } catch (Exception e) {
            LogKit.error("TextIn API调用异常: " + e.getMessage(), e);
            throw new RuntimeException("TextIn API调用失败: " + e.getMessage(), e);
        } finally {
            // 关闭资源
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    LogKit.error("关闭HTTP响应失败: " + e.getMessage(), e);
                }
            }
            if (httpClient != null) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    LogKit.error("关闭HTTP客户端失败: " + e.getMessage(), e);
                }
            }
        }
    }
    
    /**
     * 创建HTTP客户端
     */
    private CloseableHttpClient createHttpClient() {
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIMEOUT)
                .setSocketTimeout(SOCKET_TIMEOUT)
                .setConnectionRequestTimeout(CONNECT_TIMEOUT)
                .build();
        
        return HttpClients.custom()
                .setDefaultRequestConfig(requestConfig)
                .build();
    }
    
    /**
     * 解析TextIn API响应
     */
    private TextInResponse parseResponse(String responseContent) {
        try {
            // 使用FastJSON解析响应
            TextInResponse response = JSON.parseObject(responseContent, TextInResponse.class);
            
            if (response == null) {
                throw new RuntimeException("无法解析TextIn API响应");
            }
            
            // 检查响应状态
            if (response.getCode() != 200) {
                throw new RuntimeException("TextIn API返回错误: " + response.getMessage());
            }
            
            return response;
            
        } catch (Exception e) {
            LogKit.error("解析TextIn API响应失败: " + e.getMessage(), e);
            throw new RuntimeException("解析TextIn API响应失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 测试TextIn API连接
     */
    public boolean testConnection() {
        try {
            // 创建一个临时的小图片文件进行测试
            // 这里可以使用一个1x1像素的测试图片
            LogKit.info("测试TextIn API连接...");
            
            // TODO: 实现连接测试逻辑
            
            return true;
        } catch (Exception e) {
            LogKit.error("TextIn API连接测试失败: " + e.getMessage(), e);
            return false;
        }
    }
}
