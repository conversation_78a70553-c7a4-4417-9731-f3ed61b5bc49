package com.sandu.xinye.api.v2.ocr.dto;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * OCR识别响应数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class OcrResponse {
    
    /**
     * 图片信息
     */
    private Map<String, Object> imageInfo;
    
    /**
     * 识别到的元素列表
     */
    private List<Map<String, Object>> elements;
    
    public OcrResponse() {
        this.imageInfo = new HashMap<>();
        this.elements = new ArrayList<>();
    }
    
    /**
     * 设置图片信息
     */
    public void setImageInfo(int width, int height, String format) {
        this.imageInfo.put("width", width);
        this.imageInfo.put("height", height);
        this.imageInfo.put("format", format);
    }
    
    /**
     * 添加文本元素
     */
    public void addTextElement(String x, String y, String width, String height, 
                              String content, String textSize, String bold, String italic) {
        Map<String, Object> element = new HashMap<>();
        element.put("elementType", "1");
        element.put("x", x);
        element.put("y", y);
        element.put("width", width);
        element.put("height", height);
        element.put("content", content);
        element.put("textSize", textSize);
        element.put("bold", bold);
        element.put("italic", italic);
        element.put("hAlignment", "1");
        element.put("rotationAngle", "0");
        element.put("underline", "false");
        element.put("strikethrough", "false");
        element.put("wordSpace", "0.0");
        element.put("linesSpace", "0.0");
        element.put("fontType", "-2");
        element.put("takePrint", "true");
        element.put("mirrorImage", "false");
        element.put("blackWhiteReflection", "false");
        element.put("automaticHeightCalculation", "true");
        element.put("lineWrap", "true");
        element.put("flipX", "false");
        
        this.elements.add(element);
    }
    
    /**
     * 添加条形码元素
     */
    public void addBarcodeElement(String x, String y, String width, String height,
                                 String content, String barcodeType) {
        Map<String, Object> element = new HashMap<>();
        element.put("elementType", "2");
        element.put("x", x);
        element.put("y", y);
        element.put("width", width);
        element.put("height", height);
        element.put("content", content);
        element.put("barcodeType", barcodeType);
        element.put("rotationAngle", "0");
        element.put("showText", "3");
        element.put("textAlignment", "1");
        element.put("horizontalAlignment", "true");
        element.put("takePrint", "true");
        element.put("mirrorImage", "false");
        
        this.elements.add(element);
    }
    
    /**
     * 添加二维码元素
     */
    public void addQrCodeElement(String x, String y, String width, String height, String content) {
        Map<String, Object> element = new HashMap<>();
        element.put("elementType", "7");
        element.put("x", x);
        element.put("y", y);
        element.put("width", width);
        element.put("height", height);
        element.put("content", content);
        element.put("rotationAngle", "0");
        element.put("whiteMargin", "0");
        element.put("errorCorrectionLevel", "M");
        element.put("takePrint", "true");
        element.put("mirrorImage", "false");
        
        this.elements.add(element);
    }
    
    /**
     * 添加表格元素
     */
    public void addTableElement(String x, String y, String width, String height,
                               int rows, int cols, List<Map<String, Object>> cells) {
        Map<String, Object> element = new HashMap<>();
        element.put("elementType", "10");
        element.put("x", x);
        element.put("y", y);
        element.put("width", width);
        element.put("height", height);
        element.put("rowCount", rows);
        element.put("colCount", cols);
        element.put("cells", cells);
        element.put("borderWidth", "0.3");
        element.put("rotationAngle", "0");
        element.put("takePrint", "true");
        element.put("mirrorImage", "false");
        
        // 计算行高和列宽
        List<Double> rowHeights = new ArrayList<>();
        List<Double> columnWidths = new ArrayList<>();
        
        double avgRowHeight = Double.parseDouble(height) / rows;
        double avgColWidth = Double.parseDouble(width) / cols;
        
        for (int i = 0; i < rows; i++) {
            rowHeights.add(avgRowHeight);
        }
        for (int i = 0; i < cols; i++) {
            columnWidths.add(avgColWidth);
        }
        
        element.put("rowHeights", rowHeights);
        element.put("columnWidths", columnWidths);
        
        this.elements.add(element);
    }
    
    // Getters and Setters
    public Map<String, Object> getImageInfo() {
        return imageInfo;
    }
    
    public void setImageInfo(Map<String, Object> imageInfo) {
        this.imageInfo = imageInfo;
    }
    
    public List<Map<String, Object>> getElements() {
        return elements;
    }
    
    public void setElements(List<Map<String, Object>> elements) {
        this.elements = elements;
    }
}
