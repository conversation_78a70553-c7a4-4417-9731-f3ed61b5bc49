package com.sandu.xinye.api.v2.ocr.dto;

import java.util.List;
import java.util.Map;

/**
 * TextIn API响应数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class TextInResponse {
    
    /**
     * 响应状态码
     */
    private int code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 识别结果数据
     */
    private Data data;
    
    public static class Data {
        /**
         * 文本识别结果
         */
        private List<TextResult> texts;
        
        /**
         * 表格识别结果
         */
        private List<TableResult> tables;
        
        /**
         * 条码识别结果
         */
        private List<BarcodeResult> barcodes;
        
        // Getters and Setters
        public List<TextResult> getTexts() {
            return texts;
        }
        
        public void setTexts(List<TextResult> texts) {
            this.texts = texts;
        }
        
        public List<TableResult> getTables() {
            return tables;
        }
        
        public void setTables(List<TableResult> tables) {
            this.tables = tables;
        }
        
        public List<BarcodeResult> getBarcodes() {
            return barcodes;
        }
        
        public void setBarcodes(List<BarcodeResult> barcodes) {
            this.barcodes = barcodes;
        }
    }
    
    /**
     * 文本识别结果
     */
    public static class TextResult {
        private String text;
        private BoundingBox bbox;
        private double confidence;
        private TextStyle style;
        
        // Getters and Setters
        public String getText() {
            return text;
        }
        
        public void setText(String text) {
            this.text = text;
        }
        
        public BoundingBox getBbox() {
            return bbox;
        }
        
        public void setBbox(BoundingBox bbox) {
            this.bbox = bbox;
        }
        
        public double getConfidence() {
            return confidence;
        }
        
        public void setConfidence(double confidence) {
            this.confidence = confidence;
        }
        
        public TextStyle getStyle() {
            return style;
        }
        
        public void setStyle(TextStyle style) {
            this.style = style;
        }
    }
    
    /**
     * 表格识别结果
     */
    public static class TableResult {
        private BoundingBox bbox;
        private int rows;
        private int cols;
        private List<CellResult> cells;
        
        // Getters and Setters
        public BoundingBox getBbox() {
            return bbox;
        }
        
        public void setBbox(BoundingBox bbox) {
            this.bbox = bbox;
        }
        
        public int getRows() {
            return rows;
        }
        
        public void setRows(int rows) {
            this.rows = rows;
        }
        
        public int getCols() {
            return cols;
        }
        
        public void setCols(int cols) {
            this.cols = cols;
        }
        
        public List<CellResult> getCells() {
            return cells;
        }
        
        public void setCells(List<CellResult> cells) {
            this.cells = cells;
        }
    }
    
    /**
     * 表格单元格结果
     */
    public static class CellResult {
        private int row;
        private int col;
        private String text;
        private BoundingBox bbox;
        
        // Getters and Setters
        public int getRow() {
            return row;
        }
        
        public void setRow(int row) {
            this.row = row;
        }
        
        public int getCol() {
            return col;
        }
        
        public void setCol(int col) {
            this.col = col;
        }
        
        public String getText() {
            return text;
        }
        
        public void setText(String text) {
            this.text = text;
        }
        
        public BoundingBox getBbox() {
            return bbox;
        }
        
        public void setBbox(BoundingBox bbox) {
            this.bbox = bbox;
        }
    }
    
    /**
     * 条码识别结果
     */
    public static class BarcodeResult {
        private String text;
        private String type;
        private BoundingBox bbox;
        
        // Getters and Setters
        public String getText() {
            return text;
        }
        
        public void setText(String text) {
            this.text = text;
        }
        
        public String getType() {
            return type;
        }
        
        public void setType(String type) {
            this.type = type;
        }
        
        public BoundingBox getBbox() {
            return bbox;
        }
        
        public void setBbox(BoundingBox bbox) {
            this.bbox = bbox;
        }
    }
    
    /**
     * 边界框
     */
    public static class BoundingBox {
        private int x;
        private int y;
        private int width;
        private int height;
        
        // Getters and Setters
        public int getX() {
            return x;
        }
        
        public void setX(int x) {
            this.x = x;
        }
        
        public int getY() {
            return y;
        }
        
        public void setY(int y) {
            this.y = y;
        }
        
        public int getWidth() {
            return width;
        }
        
        public void setWidth(int width) {
            this.width = width;
        }
        
        public int getHeight() {
            return height;
        }
        
        public void setHeight(int height) {
            this.height = height;
        }
    }
    
    /**
     * 文本样式
     */
    public static class TextStyle {
        private boolean bold;
        private boolean italic;
        private double fontSize;
        private String fontFamily;
        
        // Getters and Setters
        public boolean isBold() {
            return bold;
        }
        
        public void setBold(boolean bold) {
            this.bold = bold;
        }
        
        public boolean isItalic() {
            return italic;
        }
        
        public void setItalic(boolean italic) {
            this.italic = italic;
        }
        
        public double getFontSize() {
            return fontSize;
        }
        
        public void setFontSize(double fontSize) {
            this.fontSize = fontSize;
        }
        
        public String getFontFamily() {
            return fontFamily;
        }
        
        public void setFontFamily(String fontFamily) {
            this.fontFamily = fontFamily;
        }
    }
    
    // Main class getters and setters
    public int getCode() {
        return code;
    }
    
    public void setCode(int code) {
        this.code = code;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public Data getData() {
        return data;
    }
    
    public void setData(Data data) {
        this.data = data;
    }
}
