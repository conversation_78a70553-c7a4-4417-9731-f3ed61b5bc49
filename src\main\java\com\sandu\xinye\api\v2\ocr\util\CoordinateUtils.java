package com.sandu.xinye.api.v2.ocr.util;

import java.text.DecimalFormat;

/**
 * 坐标转换工具类
 * 处理像素坐标与毫米坐标之间的转换
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class CoordinateUtils {
    
    // 默认DPI值，用于像素到毫米的转换
    private static final double DEFAULT_DPI = 300.0;
    
    // 毫米到英寸的转换系数
    private static final double MM_PER_INCH = 25.4;
    
    // 数字格式化器，保留合适的小数位数
    private static final DecimalFormat DECIMAL_FORMAT = new DecimalFormat("#.######");
    
    /**
     * 将像素坐标转换为毫米坐标
     * 
     * @param pixels 像素值
     * @param dpi DPI值，如果为null则使用默认值300
     * @return 毫米值的字符串表示
     */
    public static String pixelsToMm(int pixels, Double dpi) {
        double actualDpi = dpi != null ? dpi : DEFAULT_DPI;
        double mm = (pixels * MM_PER_INCH) / actualDpi;
        return DECIMAL_FORMAT.format(mm);
    }
    
    /**
     * 将像素坐标转换为毫米坐标（使用默认DPI）
     * 
     * @param pixels 像素值
     * @return 毫米值的字符串表示
     */
    public static String pixelsToMm(int pixels) {
        return pixelsToMm(pixels, null);
    }
    
    /**
     * 将毫米坐标转换为像素坐标
     * 
     * @param mm 毫米值
     * @param dpi DPI值，如果为null则使用默认值300
     * @return 像素值
     */
    public static int mmToPixels(double mm, Double dpi) {
        double actualDpi = dpi != null ? dpi : DEFAULT_DPI;
        return (int) Math.round((mm * actualDpi) / MM_PER_INCH);
    }
    
    /**
     * 将毫米坐标转换为像素坐标（使用默认DPI）
     * 
     * @param mm 毫米值
     * @return 像素值
     */
    public static int mmToPixels(double mm) {
        return mmToPixels(mm, null);
    }
    
    /**
     * 根据字符宽度计算字体大小
     * 这是一个经验公式，可能需要根据实际情况调整
     * 
     * @param charWidthPx 字符宽度（像素）
     * @return 字体大小的字符串表示
     */
    public static String calculateFontSize(double charWidthPx) {
        // 经验公式：字体大小 ≈ 字符宽度 * 0.75
        double fontSize = charWidthPx * 0.75;
        
        // 限制字体大小范围
        if (fontSize < 6.0) {
            fontSize = 6.0;
        } else if (fontSize > 72.0) {
            fontSize = 72.0;
        }
        
        return DECIMAL_FORMAT.format(fontSize);
    }
    
    /**
     * 计算文本区域的平均字符宽度
     * 
     * @param textWidth 文本区域宽度（像素）
     * @param textLength 文本长度（字符数）
     * @return 平均字符宽度（像素）
     */
    public static double calculateCharWidth(int textWidth, int textLength) {
        if (textLength <= 0) {
            return 0.0;
        }
        return (double) textWidth / textLength;
    }
    
    /**
     * 格式化数字为字符串，去除不必要的小数位
     * 
     * @param value 数值
     * @return 格式化后的字符串
     */
    public static String formatNumber(double value) {
        return DECIMAL_FORMAT.format(value);
    }
    
    /**
     * 检查坐标是否在合理范围内
     * 
     * @param x X坐标
     * @param y Y坐标
     * @param width 宽度
     * @param height 高度
     * @param imageWidth 图片宽度
     * @param imageHeight 图片高度
     * @return 是否在合理范围内
     */
    public static boolean isValidBounds(int x, int y, int width, int height, 
                                       int imageWidth, int imageHeight) {
        return x >= 0 && y >= 0 && 
               x + width <= imageWidth && 
               y + height <= imageHeight &&
               width > 0 && height > 0;
    }
}
