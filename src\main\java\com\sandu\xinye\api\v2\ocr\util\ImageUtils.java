package com.sandu.xinye.api.v2.ocr.util;

import com.jfinal.kit.LogKit;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

/**
 * 图片处理工具类
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class ImageUtils {
    
    /**
     * 获取图片尺寸信息
     * 
     * @param imageFile 图片文件
     * @return 包含宽度和高度的数组 [width, height]，失败返回null
     */
    public static int[] getImageDimensions(File imageFile) {
        try {
            BufferedImage image = ImageIO.read(imageFile);
            if (image != null) {
                return new int[]{image.getWidth(), image.getHeight()};
            }
        } catch (IOException e) {
            LogKit.error("获取图片尺寸失败: " + e.getMessage(), e);
        }
        return null;
    }
    
    /**
     * 检查图片是否有效
     * 
     * @param imageFile 图片文件
     * @return 是否有效
     */
    public static boolean isValidImage(File imageFile) {
        try {
            BufferedImage image = ImageIO.read(imageFile);
            return image != null;
        } catch (IOException e) {
            LogKit.error("验证图片有效性失败: " + e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 获取图片格式
     * 
     * @param fileName 文件名
     * @return 图片格式，如jpg、png等
     */
    public static String getImageFormat(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "unknown";
        }
        
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot > 0 && lastDot < fileName.length() - 1) {
            return fileName.substring(lastDot + 1).toLowerCase();
        }
        
        return "unknown";
    }
    
    /**
     * 检查是否为支持的图片格式
     * 
     * @param fileName 文件名
     * @return 是否支持
     */
    public static boolean isSupportedImageFormat(String fileName) {
        String format = getImageFormat(fileName);
        return "jpg".equals(format) || "jpeg".equals(format) || 
               "png".equals(format) || "webp".equals(format) || 
               "bmp".equals(format);
    }
    
    /**
     * 计算图片文件的MD5值，用于缓存
     * 
     * @param imageFile 图片文件
     * @return MD5值
     */
    public static String calculateImageMD5(File imageFile) {
        // TODO: 实现MD5计算，用于缓存机制
        return String.valueOf(imageFile.length() + imageFile.lastModified());
    }
}
