package com.sandu.xinye.common.routes.v2;

import com.jfinal.config.Routes;
import com.sandu.xinye.api.v2.about.AboutController;
import com.sandu.xinye.api.v2.aliyun.BarcodeController;
import com.sandu.xinye.api.v2.app_version.AppVersionController;
import com.sandu.xinye.api.v2.banner.BannerApiController;
import com.sandu.xinye.api.v2.cloudfile.CloudfileController;
import com.sandu.xinye.api.v2.common.CommonController;
import com.sandu.xinye.api.v2.datarecovery.DataRecoveryController;
import com.sandu.xinye.api.v2.feedback.FeedbackController;
import com.sandu.xinye.api.v2.font.FontController;
import com.sandu.xinye.api.v2.help.HelpController;
import com.sandu.xinye.api.v2.login.UserLoginController;
import com.sandu.xinye.api.v2.logo.LogoController;
import com.sandu.xinye.api.v2.machine.MachineController;
import com.sandu.xinye.api.v2.ocr.OcrController;
import com.sandu.xinye.api.v2.oss.OssController;
import com.sandu.xinye.api.v2.survey.SurveyApiController;
import com.sandu.xinye.api.v2.templet.GoodsTempletController;
import com.sandu.xinye.api.v2.templet.TempletBusiController;
import com.sandu.xinye.api.v2.templet.TempletController;
import com.sandu.xinye.api.v2.user.UserController;
import com.sandu.xinye.api.v2.user.UserGoodsController;
import com.sandu.xinye.api.v2.app_config.AppConfigController;
import com.sandu.xinye.common.interceptor.AppUserInterceptor;
import com.sandu.xinye.common.model.Cloudfile;

public class ApiRoutes extends Routes {

	@Override
	public void config() {
		this.addInterceptor(new AppUserInterceptor());

		// 登录
		this.add("/api/v2/login", UserLoginController.class);
		// 设备
		this.add("/api/v2/device", MachineController.class);
		// banner
		this.add("/api/v2/banner", BannerApiController.class);
		// logo
		this.add("/api/v2/logo", LogoController.class);
		// 关于公司
		this.add("/api/v2/about", AboutController.class);
		// 帮助中心
		this.add("/api/v2/help", HelpController.class);
		// 字体
		this.add("/api/v2/font", FontController.class);
		// 意见反馈
		this.add("/api/v2/feedback", FeedbackController.class);
		// 通用
		this.add("/api/v2/common", CommonController.class);
		// 自定义模板
		this.add("/api/v2/templet", TempletController.class);
		// 行业模板
		this.add("/api/v2/templetbusi", TempletBusiController.class);
		// 版本
		this.add("/api/v2/appVersion", AppVersionController.class);
		// 问卷调查
		this.add("/api/v2/survey", SurveyApiController.class);
		// oss相关接口
		this.add("/api/v2/oss", OssController.class);
		// 我的文件相关接口
		this.add("/api/v2/cloudfile", CloudfileController.class);
		// 用户相关接口
		this.add("/api/v2/user", UserController.class);
		// 用户商品相关接口
		this.add("/api/v2/user/goods", UserGoodsController.class);
		// 阿里云条码查询
		this.add("/api/v2/aliyun/barcode", BarcodeController.class);
		// 商品模板
		this.add("/api/v2/goods/templet", GoodsTempletController.class);
		// app_config
		this.add("/api/v2/app_config", AppConfigController.class);
		// 数据恢复
		this.add("/api/v2/datarecovery", DataRecoveryController.class);
		// OCR识图
		this.add("/api/v2/ocr", OcrController.class);
	}

}
