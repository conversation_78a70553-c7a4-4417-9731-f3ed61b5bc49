package com.sandu.xinye.api.v2.ocr;

import com.sandu.xinye.api.v2.ocr.dto.OcrResponse;
import com.sandu.xinye.api.v2.ocr.dto.TextInResponse;
import org.junit.Before;
import org.junit.Test;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 元素转换器单元测试
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class ElementConverterTest {
    
    private ElementConverter converter;
    private File mockImageFile;
    
    @Before
    public void setUp() {
        converter = ElementConverter.me;
        mockImageFile = mock(File.class);
        when(mockImageFile.getName()).thenReturn("test.jpg");
    }
    
    @Test
    public void testConvertToXPrinterFormat_NullData() {
        // 测试空数据转换
        TextInResponse response = new TextInResponse();
        response.setData(null);
        
        OcrResponse result = converter.convertToXPrinterFormat(response, mockImageFile, 800, 600);
        
        assertNotNull(result);
        assertNotNull(result.getImageInfo());
        assertEquals(800, result.getImageInfo().get("width"));
        assertEquals(600, result.getImageInfo().get("height"));
        assertEquals("jpg", result.getImageInfo().get("format"));
        assertTrue(result.getElements().isEmpty());
    }
    
    @Test
    public void testConvertToXPrinterFormat_WithTextElements() {
        // 测试文本元素转换
        TextInResponse response = createTextInResponseWithText();
        
        OcrResponse result = converter.convertToXPrinterFormat(response, mockImageFile, 800, 600);
        
        assertNotNull(result);
        assertEquals(1, result.getElements().size());
        
        Map<String, Object> element = result.getElements().get(0);
        assertEquals("1", element.get("elementType")); // 文本类型
        assertEquals("Hello World", element.get("content"));
        assertNotNull(element.get("x"));
        assertNotNull(element.get("y"));
        assertNotNull(element.get("width"));
        assertNotNull(element.get("height"));
        assertNotNull(element.get("textSize"));
        assertEquals("false", element.get("bold"));
        assertEquals("false", element.get("italic"));
    }
    
    @Test
    public void testConvertToXPrinterFormat_WithBarcodeElements() {
        // 测试条码元素转换
        TextInResponse response = createTextInResponseWithBarcode();
        
        OcrResponse result = converter.convertToXPrinterFormat(response, mockImageFile, 800, 600);
        
        assertNotNull(result);
        assertEquals(1, result.getElements().size());
        
        Map<String, Object> element = result.getElements().get(0);
        assertEquals("2", element.get("elementType")); // 条码类型
        assertEquals("1234567890", element.get("content"));
        assertEquals("4", element.get("barcodeType")); // CODE_128
    }
    
    @Test
    public void testConvertToXPrinterFormat_WithQRCodeElements() {
        // 测试二维码元素转换
        TextInResponse response = createTextInResponseWithQRCode();
        
        OcrResponse result = converter.convertToXPrinterFormat(response, mockImageFile, 800, 600);
        
        assertNotNull(result);
        assertEquals(1, result.getElements().size());
        
        Map<String, Object> element = result.getElements().get(0);
        assertEquals("7", element.get("elementType")); // 二维码类型
        assertEquals("https://example.com", element.get("content"));
    }
    
    @Test
    public void testConvertToXPrinterFormat_WithTableElements() {
        // 测试表格元素转换
        TextInResponse response = createTextInResponseWithTable();
        
        OcrResponse result = converter.convertToXPrinterFormat(response, mockImageFile, 800, 600);
        
        assertNotNull(result);
        assertEquals(1, result.getElements().size());
        
        Map<String, Object> element = result.getElements().get(0);
        assertEquals("10", element.get("elementType")); // 表格类型
        assertEquals(2, element.get("rowCount"));
        assertEquals(2, element.get("colCount"));
        
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> cells = (List<Map<String, Object>>) element.get("cells");
        assertNotNull(cells);
        assertEquals(2, cells.size());
        
        Map<String, Object> cell1 = cells.get(0);
        assertEquals("0", cell1.get("row"));
        assertEquals("0", cell1.get("col"));
        assertEquals("Cell 1", cell1.get("content"));
    }
    
    @Test
    public void testConvertToXPrinterFormat_MixedElements() {
        // 测试混合元素转换
        TextInResponse response = createTextInResponseWithMixedElements();
        
        OcrResponse result = converter.convertToXPrinterFormat(response, mockImageFile, 800, 600);
        
        assertNotNull(result);
        assertEquals(3, result.getElements().size()); // 文本 + 条码 + 二维码
        
        // 验证不同类型的元素都被正确转换
        boolean hasText = false, hasBarcode = false, hasQRCode = false;
        for (Map<String, Object> element : result.getElements()) {
            String type = (String) element.get("elementType");
            if ("1".equals(type)) hasText = true;
            else if ("2".equals(type)) hasBarcode = true;
            else if ("7".equals(type)) hasQRCode = true;
        }
        
        assertTrue("应该包含文本元素", hasText);
        assertTrue("应该包含条码元素", hasBarcode);
        assertTrue("应该包含二维码元素", hasQRCode);
    }
    
    // 辅助方法：创建包含文本的TextIn响应
    private TextInResponse createTextInResponseWithText() {
        TextInResponse response = new TextInResponse();
        TextInResponse.Data data = new TextInResponse.Data();
        
        List<TextInResponse.TextResult> texts = new ArrayList<>();
        TextInResponse.TextResult text = new TextInResponse.TextResult();
        text.setText("Hello World");
        
        TextInResponse.BoundingBox bbox = new TextInResponse.BoundingBox();
        bbox.setX(100);
        bbox.setY(200);
        bbox.setWidth(200);
        bbox.setHeight(30);
        text.setBbox(bbox);
        
        TextInResponse.TextStyle style = new TextInResponse.TextStyle();
        style.setBold(false);
        style.setItalic(false);
        text.setStyle(style);
        
        texts.add(text);
        data.setTexts(texts);
        response.setData(data);
        
        return response;
    }
    
    // 辅助方法：创建包含条码的TextIn响应
    private TextInResponse createTextInResponseWithBarcode() {
        TextInResponse response = new TextInResponse();
        TextInResponse.Data data = new TextInResponse.Data();
        
        List<TextInResponse.BarcodeResult> barcodes = new ArrayList<>();
        TextInResponse.BarcodeResult barcode = new TextInResponse.BarcodeResult();
        barcode.setText("1234567890");
        barcode.setType("code128");
        
        TextInResponse.BoundingBox bbox = new TextInResponse.BoundingBox();
        bbox.setX(50);
        bbox.setY(300);
        bbox.setWidth(300);
        bbox.setHeight(80);
        barcode.setBbox(bbox);
        
        barcodes.add(barcode);
        data.setBarcodes(barcodes);
        response.setData(data);
        
        return response;
    }
    
    // 辅助方法：创建包含二维码的TextIn响应
    private TextInResponse createTextInResponseWithQRCode() {
        TextInResponse response = new TextInResponse();
        TextInResponse.Data data = new TextInResponse.Data();
        
        List<TextInResponse.BarcodeResult> barcodes = new ArrayList<>();
        TextInResponse.BarcodeResult qrcode = new TextInResponse.BarcodeResult();
        qrcode.setText("https://example.com");
        qrcode.setType("qr_code");
        
        TextInResponse.BoundingBox bbox = new TextInResponse.BoundingBox();
        bbox.setX(400);
        bbox.setY(100);
        bbox.setWidth(150);
        bbox.setHeight(150);
        qrcode.setBbox(bbox);
        
        barcodes.add(qrcode);
        data.setBarcodes(barcodes);
        response.setData(data);
        
        return response;
    }
    
    // 辅助方法：创建包含表格的TextIn响应
    private TextInResponse createTextInResponseWithTable() {
        TextInResponse response = new TextInResponse();
        TextInResponse.Data data = new TextInResponse.Data();
        
        List<TextInResponse.TableResult> tables = new ArrayList<>();
        TextInResponse.TableResult table = new TextInResponse.TableResult();
        table.setRows(2);
        table.setCols(2);
        
        TextInResponse.BoundingBox bbox = new TextInResponse.BoundingBox();
        bbox.setX(100);
        bbox.setY(400);
        bbox.setWidth(400);
        bbox.setHeight(200);
        table.setBbox(bbox);
        
        List<TextInResponse.CellResult> cells = new ArrayList<>();
        TextInResponse.CellResult cell1 = new TextInResponse.CellResult();
        cell1.setRow(0);
        cell1.setCol(0);
        cell1.setText("Cell 1");
        
        TextInResponse.CellResult cell2 = new TextInResponse.CellResult();
        cell2.setRow(0);
        cell2.setCol(1);
        cell2.setText("Cell 2");
        
        cells.add(cell1);
        cells.add(cell2);
        table.setCells(cells);
        
        tables.add(table);
        data.setTables(tables);
        response.setData(data);
        
        return response;
    }
    
    // 辅助方法：创建包含混合元素的TextIn响应
    private TextInResponse createTextInResponseWithMixedElements() {
        TextInResponse response = new TextInResponse();
        TextInResponse.Data data = new TextInResponse.Data();
        
        // 添加文本
        List<TextInResponse.TextResult> texts = new ArrayList<>();
        TextInResponse.TextResult text = new TextInResponse.TextResult();
        text.setText("Mixed Content");
        TextInResponse.BoundingBox textBbox = new TextInResponse.BoundingBox();
        textBbox.setX(10);
        textBbox.setY(10);
        textBbox.setWidth(100);
        textBbox.setHeight(20);
        text.setBbox(textBbox);
        texts.add(text);
        data.setTexts(texts);
        
        // 添加条码和二维码
        List<TextInResponse.BarcodeResult> barcodes = new ArrayList<>();
        
        // 条码
        TextInResponse.BarcodeResult barcode = new TextInResponse.BarcodeResult();
        barcode.setText("123456");
        barcode.setType("code128");
        TextInResponse.BoundingBox barcodeBbox = new TextInResponse.BoundingBox();
        barcodeBbox.setX(10);
        barcodeBbox.setY(50);
        barcodeBbox.setWidth(200);
        barcodeBbox.setHeight(50);
        barcode.setBbox(barcodeBbox);
        barcodes.add(barcode);
        
        // 二维码
        TextInResponse.BarcodeResult qrcode = new TextInResponse.BarcodeResult();
        qrcode.setText("QR Content");
        qrcode.setType("qr_code");
        TextInResponse.BoundingBox qrBbox = new TextInResponse.BoundingBox();
        qrBbox.setX(250);
        qrBbox.setY(50);
        qrBbox.setWidth(100);
        qrBbox.setHeight(100);
        qrcode.setBbox(qrBbox);
        barcodes.add(qrcode);
        
        data.setBarcodes(barcodes);
        response.setData(data);
        
        return response;
    }
}
