package com.sandu.xinye.api.v2.ocr;

import com.jfinal.upload.UploadFile;
import com.sandu.xinye.common.kit.RetKit;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * OCR服务单元测试
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class OcrServiceTest {
    
    private OcrService ocrService;
    
    @Mock
    private UploadFile mockUploadFile;
    
    @Mock
    private File mockFile;
    
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        ocrService = OcrService.me;
    }
    
    @Test
    public void testRecognizeImage_NullFile() {
        // 测试空文件情况
        RetKit result = ocrService.recognizeImage(null, null, null);
        
        assertNotNull(result);
        assertFalse(result.isTrue("success"));
        assertTrue(result.getStr("msg").contains("请上传图片文件"));
    }
    
    @Test
    public void testRecognizeImage_InvalidFile() {
        // 模拟无效文件
        when(mockUploadFile.getFile()).thenReturn(null);
        
        RetKit result = ocrService.recognizeImage(mockUploadFile, null, null);
        
        assertNotNull(result);
        assertFalse(result.isTrue("success"));
    }
    
    @Test
    public void testRecognizeImage_FileTooLarge() {
        // 模拟文件过大
        when(mockUploadFile.getFile()).thenReturn(mockFile);
        when(mockFile.exists()).thenReturn(true);
        when(mockFile.length()).thenReturn(11L * 1024 * 1024); // 11MB
        when(mockUploadFile.getFileName()).thenReturn("test.jpg");
        
        RetKit result = ocrService.recognizeImage(mockUploadFile, null, null);
        
        assertNotNull(result);
        assertFalse(result.isTrue("success"));
        assertTrue(result.getStr("msg").contains("图片文件过大"));
    }
    
    @Test
    public void testRecognizeImage_UnsupportedFormat() {
        // 模拟不支持的文件格式
        when(mockUploadFile.getFile()).thenReturn(mockFile);
        when(mockFile.exists()).thenReturn(true);
        when(mockFile.length()).thenReturn(1024L); // 1KB
        when(mockUploadFile.getFileName()).thenReturn("test.txt");
        
        RetKit result = ocrService.recognizeImage(mockUploadFile, null, null);
        
        assertNotNull(result);
        assertFalse(result.isTrue("success"));
        assertTrue(result.getStr("msg").contains("不支持的图片格式"));
    }
    
    @Test
    public void testRecognizeImage_ValidJpgFile() throws IOException {
        // 创建一个临时的测试图片文件
        Path tempFile = createTestImageFile("test.jpg");
        
        try {
            when(mockUploadFile.getFile()).thenReturn(tempFile.toFile());
            when(mockUploadFile.getFileName()).thenReturn("test.jpg");
            
            // 注意：这个测试需要真实的TextIn API配置才能完全通过
            // 在没有配置的情况下，会因为API调用失败而返回错误
            RetKit result = ocrService.recognizeImage(mockUploadFile, 800, 600);
            
            assertNotNull(result);
            // 由于没有真实的TextIn API配置，这里主要测试文件验证逻辑
            
        } finally {
            // 清理临时文件
            Files.deleteIfExists(tempFile);
        }
    }
    
    @Test
    public void testRecognizeImage_ValidPngFile() throws IOException {
        // 创建一个临时的测试图片文件
        Path tempFile = createTestImageFile("test.png");
        
        try {
            when(mockUploadFile.getFile()).thenReturn(tempFile.toFile());
            when(mockUploadFile.getFileName()).thenReturn("test.png");
            
            RetKit result = ocrService.recognizeImage(mockUploadFile, 1024, 768);
            
            assertNotNull(result);
            
        } finally {
            // 清理临时文件
            Files.deleteIfExists(tempFile);
        }
    }
    
    /**
     * 创建测试用的图片文件
     */
    private Path createTestImageFile(String fileName) throws IOException {
        Path tempFile = Paths.get(System.getProperty("java.io.tmpdir"), fileName);
        
        // 创建一个简单的测试图片文件（实际上是空文件，但足够测试文件验证逻辑）
        Files.write(tempFile, new byte[1024]); // 1KB的测试数据
        
        return tempFile;
    }
    
    @Test
    public void testValidateImageFile_EmptyFile() {
        // 测试空文件
        when(mockUploadFile.getFile()).thenReturn(mockFile);
        when(mockFile.exists()).thenReturn(true);
        when(mockFile.length()).thenReturn(0L);
        when(mockUploadFile.getFileName()).thenReturn("test.jpg");
        
        RetKit result = ocrService.recognizeImage(mockUploadFile, null, null);
        
        assertNotNull(result);
        assertFalse(result.isTrue("success"));
        assertTrue(result.getStr("msg").contains("图片文件为空"));
    }
    
    @Test
    public void testValidateImageFile_BlankFileName() {
        // 测试空文件名
        when(mockUploadFile.getFile()).thenReturn(mockFile);
        when(mockFile.exists()).thenReturn(true);
        when(mockFile.length()).thenReturn(1024L);
        when(mockUploadFile.getFileName()).thenReturn("");
        
        RetKit result = ocrService.recognizeImage(mockUploadFile, null, null);
        
        assertNotNull(result);
        assertFalse(result.isTrue("success"));
        assertTrue(result.getStr("msg").contains("图片文件名不能为空"));
    }
    
    @Test
    public void testValidateImageFile_ValidFile() {
        // 测试有效文件
        when(mockUploadFile.getFile()).thenReturn(mockFile);
        when(mockFile.exists()).thenReturn(true);
        when(mockFile.length()).thenReturn(1024L); // 1KB
        when(mockUploadFile.getFileName()).thenReturn("test.jpg");
        
        // 这个测试主要验证文件验证逻辑通过，但会在TextIn API调用时失败
        RetKit result = ocrService.recognizeImage(mockUploadFile, 800, 600);
        
        assertNotNull(result);
        // 文件验证应该通过，但API调用会失败
    }
}
